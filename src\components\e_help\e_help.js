import { useEffect, useState } from "react";
import { Pressable, ScrollView, StyleSheet, Text, View } from "react-native";
import SelfHelpCards from "./view/selfhelp_card";
import SelfHelpContent from "./view/_self-help-content";
import { GlobalStyles } from "../app/global-styles";
import { selfhelpFAQService } from "./modal/_faqs-service";
import { config } from "../../environment";

export default function Help(props) {
  const [labels, setLabels] = useState([]);
  const [FAQData, setFAQS] = useState([]);
  const [UserguideData, setUserguide] = useState([]);
  const [LinksData, setLinksData] = useState([]);
  const [LinkDescription, setLinksDescription] = useState();
  const [CallUSDescription, setCallUSDescription] = useState();
  const [ImageURL, setImageURL] = useState();

  useEffect(() => {
    selfhelpFAQService
      .Labels()
      .then(res => {
        if (res.data) {
          setLabels(res.data.getLabels.E_ENERGY_SAVING_TIPS);
        }
      })
      .catch(err => {
        console.log("err", err);
      });
  }, []);
  useEffect(() => {
    if (!LinkDescription) {
      let description = "E_SELF_HELP";
      selfhelpFAQService
        .FAQs(description)
        .then(res => {
          if (res.data) {
            res.data.getContents.E_SELF_HELP.forEach(element => {
              if (element.contentCode === "E_SELF_HELP_LINKS_DESCRIPTION") {
                setLinksDescription(element.answer);
              }
              if (element.contentCode === "E_SELF_HELP_CALL_US_DESCRIPTION") {
                setCallUSDescription(element.answer);
              }
            });
          }
        });
    }
    selfhelpFAQService
      .FAQs("E_FAQS")
      .then(res => {
        if (res?.data?.getContents?.E_FAQS.length>0) {
          setFAQS(res.data.getContents.E_FAQS);
        }else{
          setFAQS("NODATA")
        }
      })
      .catch(err => {
        setFAQS("NODATA")
      });

    selfhelpFAQService
      .FAQs("E_USER_GUIDES")
      .then(res => {
        if (res.data) {
          setUserguide(res.data.getContents.E_USER_GUIDES);
        }else{
          setUserguide("NODATA")
        }
      })
      .catch(err => {
        setUserguide("NODATA")
      });
    
    selfhelpFAQService
      .FAQs("E_LINKS")
      .then(res => {
        if (res.data) {
          setLinksData(res.data.getContents.E_LINKS);
          setImageURL(
            config.urls.ASSEST_URL_ENDPOINT + "/e_self_help_links_image.png",
          );
        }else{
          setLinksData("NODATA")
        }
      })
      .catch(err => {
        setLinksData("NODATA")
      });
  }, []);
  return (
    <>
      <View style={styles.pressable} android_ripple={{ color: "#ccc" }}>
        <View style={styles.content}>
          <View style={styles.spaceAroundCard}>
            <SelfHelpCards />
          </View>
          <ScrollView style={styles.contentCard}>
            <SelfHelpContent
              labels={labels}
              FAQData={FAQData}
              UserguideData={UserguideData}
              LinksData={LinksData}
              CallUSDescription={CallUSDescription}
              ImageURL={ImageURL}
              LinkDescription={LinkDescription}
            />
          </ScrollView>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  pressable: {
    flex: 1,
    justifyContent: "center",
  },
  spaceAroundCard: {
    width: "100%",
    marginVertical: 15,
  },
  contentCard: {
    height: "100%",
  },
  content: {
    flex: 1,
    overflow: "scroll",
    marginBottom: 5,
  },
});

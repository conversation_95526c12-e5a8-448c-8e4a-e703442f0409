import axios from "axios";
import { config } from "../../../environment";
import AsyncStorage from "@react-native-async-storage/async-storage";

export const selfhelpFAQService = {
  Labels,
  FAQs,
  Tips,
};

async function Labels() {
  const rawBearer = await AsyncStorage.getItem("bearer");
  const bearer = JSON.parse(rawBearer);
  if (bearer) {
    const labels = `{
      getLabels(input: {languageCode: "en"})
    }`;
    let acessToken = bearer.acessToken;
    let headers = {
      acessToken: acessToken,
      tenantCode: config.constants.BASE_TENANT_CODE,
    };
    return new Promise((resolve, reject) => {
      axios
        .post(
          config.urls.ADMIN_SERVICE_BASE_URL,
          {
            query: labels,
          },
          {
            headers: headers,
          },
        )
        .then(function (response) {
          resolve(response.data);
        })
        .catch(function (error) {
          reject(error);
        });
    });
  }
}

async function FAQs(type) {
  const rawBearer = await AsyncStorage.getItem("bearer");
  const bearer = JSON.parse(rawBearer);
  if (bearer) {
    const faqs =
      `query {
            getContents(input: {
                categoryCode:"` +
      type +
      `"
            })
        }`;
    let acessToken = bearer.acessToken;
    let headers = {
      acessToken: acessToken,
      tenantCode: config.constants.BASE_TENANT_CODE,
    };
    return new Promise((resolve, reject) => {
      axios
        .post(
          config.urls.ADMIN_SERVICE_BASE_URL,
          {
            query: faqs,
          },
          {
            headers: headers,
          },
        )
        .then(function (response) {
          resolve(response.data);
        })
        .catch(function (error) {
          reject(error);
        });
    });
  }
}

async function Tips(customerClassCode, languageCode) {
  const rawBearer = await AsyncStorage.getItem("bearer");

  const bearer = JSON.parse(rawBearer);
  if (bearer) {
    const tips =
      `query{
      getTips(input: {tenantCode: "` +
      config.constants.BASE_TENANT_CODE +
      `", customerClassCode: "` +
      customerClassCode +
      `", languageCode: "` +
      languageCode +
      `"}) {
        tipsDetails {
          tipId
          tipTitle
          tipShortDescription
          tipLongDescription
          tipSequence
          tipIconName
          tipCriteriaParameter
          tipTenantCode
        }
      }
    }`;
    let acessToken = bearer.acessToken;
    let headers = {
      acessToken: acessToken,
      tenantCode: config.constants.BASE_TENANT_CODE,
    };
    return new Promise((resolve, reject) => {
      axios
        .post(
          config.urls.COMMUNICATION_SERVICE_BASE_URL,
          {
            query: tips,
          },
          {
            headers: headers,
          },
        )
        .then(function (response) {
          resolve(response?.data);
        })
        .catch(function (error) {
          reject(error);
        });
    });
  }
}

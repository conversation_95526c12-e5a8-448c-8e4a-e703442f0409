import { createSlice } from "@reduxjs/toolkit";

const alertsPreferenceReducer = createSlice({
    name: "alertsPreference",
    initialState: {
        alertsPreference :'',
    },
    reducers: {
        alertsPreference: (state, action) => {
            state.alertsPreference= action.payload
        },
    
    }
})

export const alertsPreference = alertsPreferenceReducer.actions.alertsPreference;
export default alertsPreferenceReducer.reducer;
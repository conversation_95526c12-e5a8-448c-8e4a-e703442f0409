import React, { useState, useEffect, useContext } from "react";
import {
  StyleSheet,
  View,
  Pressable,
  Dimensions,
  Keyboard,
  TouchableWithoutFeedback,
  Platform,
} from "react-native";
import { Card, Text, TextInput, ActivityIndicator } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import { DatePickerModal } from "react-native-paper-dates";
import { useSelector, useDispatch } from "react-redux";
import moment from "moment";
import { ticketService } from "../model/ticket_service";
import Button from "../../common/_button";
import TurnOnOff from "../../e_workactivities/view/confirm-modal";
import TicketWarning from "../createdTicket_warning/ticket_warning";
import { ticketContext } from "../e_services";
import { ticketID } from "../../../redux/slices/pastTicketId";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import CalendarPicker from "../../common/calendar";
import { config } from "../../../environment";
import CalendarPickerWO from "../../common/calendar_wo";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { stackContext } from "../../app/get_stack";

const windowHeight = Dimensions.get("window").height;
export default function TurnONOFF({ requiredHeight, requiredCardHeight }) {
  const { workModelType } = React.useContext(stackContext);
  const context = workModelType === "WA" ? drawerContext : drawerContextWO;

  const {
    openDrawer,
    setOpenMenu,
    menuFlag,
    setmenuFlag,
    submitLoader,
    setSubmitLoader,
  } = useContext(context);

  const { setShowPopup, showPopup, setTitlepopup, setTicketNumber } =
    useContext(context);

  const [activeTicket, setTickets] = React.useState(false);
  const [completeTickets, setCompleteTickets] = React.useState(false);

  const [ticketNumber, setticketNumber] = React.useState();
  const [loading, setLoading] = React.useState(false);
  const [date, setDate] = useState(new Date());
  const [open, setOpen] = useState(false);
  const [errorOff, setErrorOff] = useState(false);
  const [errorOffPast, setErrorOffPast] = useState(false);
  const [errorOnPast, setErrorOnPast] = useState(false);
  const [errorOnoff, setErrorOnOff] = useState(false);
  const [errorOn, setErrorOn] = useState(false);
  const [disableSubmit, setDisableSubmit] = useState(true);
  const [err, setErr] = useState();
  const dispatch = useDispatch();
  const pastTicketID = useSelector(store => store?.ticketID.ticketID);
  const turnOnOffID = useSelector(store => store?.turnOnOffID.turnOnOffID);
  const [pastTicketDetails, setPastTicketDetails] = useState();
  const [readOnly, setReadOnly] = useState(true);
  const [isLoading, setALoading] = useState(false);
  const [disableCancle, setDisableCancle] = useState(true);
  const [offCalendarDate, setOffCalendarDate] = useState(null);
  const [onCalendarDate, setOnCalendarDate] = useState(null);
  const [OnAvailableDates, setOnAvailableDates] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [showCalendar, setShowCalendar] = useState(false);
  const [disableCalendar, setDisableCalendar] = useState(true);
  const [editable, setEditable] = useState(true);
  const [availableOndates, setAvailableOndates] = useState(null);
  const [calOrText, setCalorText] = useState(false);
  const [textDateOff, setTextDateOff] = useState(null);
  const [textDateOn, setTextDateOn] = useState(null);
  const [apiCallStatus, setApiCallStatus] = useState(false);

  const today = new Date();
  const OffAvailableDates = today.toISOString().split("T")[0];

  let saId = useSelector(
    store => store?.meterDetails?.meterDetails?.getSaInfo?.saId,
  );
  let accountId = useSelector(
    store => store?.meterDetails?.meterDetails?.accountId,
  );

  let saDate = useSelector(
    store => store?.meterDetails?.meterDetails?.getSaInfo?.saStartDate,
  );

  useEffect(() => {
    if (pastTicketID) {
      let ticket = null;
      dispatch(ticketID(ticket));
    }
    if (turnOnOffID) {
      setReadOnly(false);
    }
  }, [pastTicketID, turnOnOffID]);

  useEffect(() => {
    let dateFrom = moment().format("DD-MM-YYYY");
    let dateTo = moment(saDate).format("DD-MM-YYYY");
    let ticketType = "CM-TURNONOFF";
    setLoading(true);
    ticketService
      .getUserServiceTickets(dateTo, dateFrom, ticketType, accountId, saId)
      .then(res => {
        if (res) {
          let tempActive =
            res?.data.getUserServiceTickets.serviceTicketList.filter(
              ticket =>
                ticket?.ticketTypeCd === "CM-TURNONOFF" &&
                ticket?.ticketStatusCd === "ACTIVE" &&
                ticket?.userSubmittedValues.SA_ID === saId,
            );
          let tempAll =
            res?.data.getUserServiceTickets.serviceTicketList.filter(
              ticket => ticket?.ticketTypeCd === "CM-TURNONOFF",
              // &&
              // ticket?.ticketStatusCd === "ACTIVE"
            );
          if (tempAll.length > 0) {
            tempAll?.forEach(element => {
              if (element.ticketId === turnOnOffID) {
                setPastTicketDetails(element);
              }
            });
          }
          if (tempActive.length > 0) {
            setTickets(true);
            setCompleteTickets(false);
            setLoading(false);
          } else {
            setTickets(false);
            setCompleteTickets(true);
            setLoading(false);
          }
          setLoading(false);
        } else {
          setTickets(false);
          setCompleteTickets(true);
          setLoading(false);
        }
      })
      .catch(err => setLoading(false));
  }, [accountId, saDate, saId, showPopup, turnOnOffID]);

  useEffect(() => {
    if (availableOndates) {
      const onavailable = new Date(availableOndates);
      onavailable.setDate(onavailable.getDate() + 1);
      setOnAvailableDates(onavailable.toISOString().split("T")[0]);
    }
    if (textDateOff) {
      let regexdate = `^(3[01]|[12][0-9]|0[1-9])-(1[0-2]|0[1-9])-[0-9]{4}$`;
      var dateRegex = new RegExp(regexdate);
      var dateTest = dateRegex.test(textDateOff);
      let off_date = moment(textDateOff, "DD-MM-YYYY");
      let on_date = moment(textDateOn, "DD-MM-YYYY");
      let current_date = moment();
      if (dateTest === true) {
        setOffCalendarDate(moment(off_date).format("DD-MM-YYYY"));
        setAvailableOndates(moment(off_date).format("YYYY-MM-DD"));
        setErrorOff(false);
        if (off_date.isBefore(current_date)) {
          if (
            off_date.format("DD-MM-YYYY") === current_date.format("DD-MM-YYYY")
          ) {
            if (off_date.isSame(on_date)) {
              setErrorOff(false);
              setErrorOffPast(false);
              setErrorOnOff(true);
            } else {
              setErrorOff(false);
              setErrorOffPast(false);
              setErrorOnOff(false);
            }
          } else {
            setErrorOff(false);
            setErrorOffPast(true);
            setErrorOnOff(false);
            return;
          }
        } else {
        }
      } else {
        setErrorOff(true);
        setDisableSubmit(true);
      }
    } else {
      setDisableSubmit(true);
    }
    if (textDateOn) {
      let regexdate = `^(3[01]|[12][0-9]|0[1-9])-(1[0-2]|0[1-9])-[0-9]{4}$`;
      var dateRegex = new RegExp(regexdate);
      var dateTest = dateRegex.test(textDateOn);
      let on_date = moment(textDateOn, "DD-MM-YYYY");
      let off_date = moment(textDateOff, "DD-MM-YYYY");
      let current_date = moment();
      if (dateTest === true) {
        setOnCalendarDate(moment(on_date).format("DD-MM-YYYY"));
        if (on_date.isSameOrBefore(off_date)) {
          setErrorOnOff(true);
          setErrorOn(false);
          return;
        } else {
        }
        setErrorOn(false);
        setErrorOnOff(false);
      } else {
        setErrorOn(true);
        setErrorOnOff(false);
        return;
      }
    }
    if (offCalendarDate && calOrText) {
      let off_date = moment(offCalendarDate, "DD-MM-YYYY");
      let current_date = moment();
      let on_date = moment(onCalendarDate, "DD-MM-YYYY");
      if (off_date.isBefore(current_date)) {
        if (
          off_date.format("DD-MM-YYYY") === current_date.format("DD-MM-YYYY")
        ) {
          if (off_date.isSame(on_date)) {
            setErrorOff(false);
            setErrorOffPast(false);
            setErrorOnOff(true);
          } else {
            setErrorOff(false);
            setErrorOffPast(false);
            setErrorOnOff(false);
          }
        } else {
          setErrorOff(false);
          setErrorOffPast(true);
          setErrorOnOff(false);
        }
      } else {
        if (off_date && textDateOn) {
          if (off_date.isBefore(on_date)) {
            setErrorOff(false);
            setErrorOffPast(false);
            setErrorOnOff(true);
          } else {
            setErrorOff(false);
            setErrorOffPast(false);
            setErrorOnOff(true);
          }
        } else {
          setErrorOff(false);
          setErrorOffPast(false);
          setErrorOn(false);
          setErrorOnOff(false);
        }
      }
    } else {
      setErrorOffPast(false);
      setErrorOnOff(false);
    }
    if (offCalendarDate && onCalendarDate) {
      if (offCalendarDate > onCalendarDate) {
        setErrorOnOff(true);
      } else {
        setErrorOnOff(false);
        setDisableSubmit(false);
      }
    }

    if (offCalendarDate || onCalendarDate || textDateOff || textDateOn) {
      setDisableCancle(false);
    }
  }, [
    offCalendarDate,
    onCalendarDate,
    availableOndates,
    textDateOff,
    textDateOn,
    calOrText,
  ]);

  useEffect(() => {
    if (errorOff || errorOffPast || errorOn || errorOnPast || errorOnoff) {
      setDisableSubmit(true);
    }
  }, [errorOff, errorOffPast, errorOn, errorOnPast, errorOnoff]);

  useEffect(() => {
    if (apiCallStatus) {
      setOffCalendarDate("");
      setOnCalendarDate("");
      setTextDateOff("");
      setTextDateOn("");
      setErr();
      setErrorOff(false);
      setErrorOn(false);
      setLoading(false);
      setSubmitLoader(false);
      setErrorOnOff(false);
      setDisableCancle(true);
      setDisableSubmit(true);
    }
  }, [apiCallStatus]);

  const closeMenu = () => {
    setOffCalendarDate("");
    setOnCalendarDate("");
    setTextDateOff("");
    setTextDateOn("");
    setErr();
    setErrorOff(false);
    setErrorOn(false);
    setLoading(false);
    setSubmitLoader(false);
    setErrorOnOff(false);
    setDisableCancle(true);
    setDisableSubmit(true);
  };

  const okClick = () => {
    if (offCalendarDate !== undefined && onCalendarDate !== undefined) {
      const offdate = offCalendarDate;
      const ondate = onCalendarDate;
      let ticketType = "CM-TURNONOFF";
      setALoading(true);
      setSubmitLoader(true);
      ticketService
        .getUserServiceTicketSubmit(
          offdate,
          ondate,
          ticketType,
          accountId,
          saId,
        )
        .then(async res => {
          setApiCallStatus(true);
          setTitlepopup("Turn On/Off - Service Request");
          setTicketNumber(res.data.createTurnOffServiceTicket.ticketId);
          setShowPopup(true);
        })
        .catch(err => {
          setErr(err?.response?.data?.errors?.[0]?.message);
          setALoading(false);
          setSubmitLoader(false);
        });
    }
  };

  const pastticketsFn = () => {
    setOpenMenu(true);
    setmenuFlag(false);
  };

  return (
    <>
      <View style={{ height: requiredHeight }}>
        <View style={styles.aroundMargin}>
          {loading ? (
            <ActivityIndicator
              size="large"
              color={GlobalStyles.colors.ePrimary.base}
            />
          ) : !turnOnOffID && activeTicket ? (
            <TicketWarning />
          ) : (
            <>
              <Card style={([styles.card], { height: requiredCardHeight })}>
                <View style={styles.container}>
                  <Text style={styles.titleCard}>
                    Turn Off Date<Text style={styles.titleCardStar}>*</Text>:
                  </Text>
                  {pastTicketDetails ? (
                    <TextInput
                      value={
                        pastTicketDetails
                          ? pastTicketDetails?.userSubmittedValues?.START_DATE
                          : offDate
                      }
                      //value= {offDate}
                      placeholder="DD-MM-YYYY"
                      mode="outlined"
                      dense
                      returnKeyType="done"
                      outlineColor={GlobalStyles.colors.eOutline.base}
                      //keyboardType="numeric"
                      keyboardType={
                        pastTicketDetails
                          ? Platform.OS === "ios"
                            ? "default"
                            : "text"
                          : Platform.OS === "ios"
                          ? "default"
                          : "numeric"
                      }
                      // right={
                      //   <TextInput.Icon
                      //     name="calendar"
                      //     color={GlobalStyles.colors.ePrimary.base}
                      //   />
                      // }
                      style={[
                        styles.inputcls,
                        {
                          shadowColor: "#000",
                          shadowOffset: { width: 0, height: 4 },
                          shadowOpacity: 0.1,
                          shadowRadius: 3,
                        },
                      ]}
                      activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
                      theme={{
                        colors: { text: GlobalStyles.colors.eDark.base },
                      }}
                      editable={readOnly}
                    />
                  ) : workModelType == "WA" ? (
                    <CalendarPicker
                      minDate={OffAvailableDates}
                      setCalendarDate={setOffCalendarDate}
                      selectedDate={selectedDate}
                      setSelectedDate={setOffCalendarDate}
                      showCalendar={showCalendar}
                      setShowCalendar={setShowCalendar}
                      calendarDate={offCalendarDate}
                      requiredCardHeight={requiredHeight}
                      editable={editable}
                      setErrorOn={setErrorOn}
                      setAvailableOndates={setAvailableOndates}
                      setCalorText={setCalorText}
                      setTextDate={setTextDateOff}
                    />
                  ) : (
                    <CalendarPickerWO
                      minDate={OffAvailableDates}
                      setCalendarDate={setOffCalendarDate}
                      selectedDate={selectedDate}
                      setSelectedDate={setOffCalendarDate}
                      showCalendar={showCalendar}
                      setShowCalendar={setShowCalendar}
                      calendarDate={offCalendarDate}
                      requiredCardHeight={requiredHeight}
                      editable={editable}
                      setErrorOn={setErrorOn}
                      setAvailableOndates={setAvailableOndates}
                      setCalorText={setCalorText}
                      setTextDate={setTextDateOff}
                    />
                  )}
                  {errorOff ? (
                    <Text style={styles.errorCls}>
                      Please Enter Valid Turn Off date
                    </Text>
                  ) : null}
                  {errorOffPast ? (
                    <Text style={styles.errorCls}>
                      Please Enter Future Turn Off Date
                    </Text>
                  ) : null}
                  <Text style={styles.titleCardON}>
                    Turn On Date<Text style={styles.textStar}>*</Text>:
                  </Text>
                  {pastTicketDetails ? (
                    <TextInput
                      value={
                        pastTicketDetails
                          ? pastTicketDetails?.userSubmittedValues?.END_DATE
                          : onDate
                      }
                      //value= {onDate}
                      mode="outlined"
                      returnKeyType="done"
                      dense
                      outlineColor={GlobalStyles.colors.eOutline.base}
                      keyboardType={
                        pastTicketDetails
                          ? Platform.OS === "ios"
                            ? "default"
                            : "text"
                          : Platform.OS === "ios"
                          ? "default"
                          : "numeric"
                      }
                      placeholder="DD-MM-YYYY"
                      // right={
                      //   <TextInput.Icon
                      //     name="calendar"
                      //     color={GlobalStyles.colors.ePrimary.base}
                      //   />
                      // }
                      style={[
                        styles.inputcls,
                        {
                          shadowColor: "#000",
                          shadowOffset: { width: 0, height: 4 },
                          shadowOpacity: 0.1,
                          shadowRadius: 3,
                        },
                      ]}
                      activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
                      theme={{
                        colors: { text: GlobalStyles.colors.eDark.base },
                      }}
                      editable={readOnly}
                    />
                  ) : workModelType == "WA" ? (
                    <CalendarPicker
                      minDate={OnAvailableDates}
                      setCalendarDate={setOnCalendarDate}
                      selectedDate={selectedDate}
                      setSelectedDate={setOnCalendarDate}
                      showCalendar={showCalendar}
                      setShowCalendar={setShowCalendar}
                      calendarDate={onCalendarDate}
                      requiredCardHeight={requiredHeight}
                      editable={editable}
                      setErrorOn={setErrorOff}
                      setAvailableOndates={setAvailableOndates}
                      setCalorText={setCalorText}
                      setTextDate={setTextDateOn}
                    />
                  ) : (
                    <CalendarPickerWO
                      minDate={OnAvailableDates}
                      setCalendarDate={setOnCalendarDate}
                      selectedDate={selectedDate}
                      setSelectedDate={setOnCalendarDate}
                      showCalendar={showCalendar}
                      setShowCalendar={setShowCalendar}
                      calendarDate={onCalendarDate}
                      requiredCardHeight={requiredHeight}
                      editable={editable}
                      setErrorOn={setErrorOff}
                      setAvailableOndates={setAvailableOndates}
                      setCalorText={setCalorText}
                      setTextDate={setTextDateOn}
                    />
                  )}
                  {errorOn ? (
                    <Text style={styles.errorCls}>
                      Please Enter Valid Turn On date
                    </Text>
                  ) : null}

                  {errorOnPast ? (
                    <Text style={styles.errorCls}>
                      Please Enter Future Turn On Date
                    </Text>
                  ) : null}
                  {errorOnoff ? (
                    <Text style={styles.errorCls}>
                      Date must not be before turn off date
                    </Text>
                  ) : null}
                </View>
                {err && <Text style={styles.alertErr}>{err}</Text>}
              </Card>
              {pastTicketDetails ? (
                <>
                  <View style={styles.centerButtons}>
                    <Button
                      buttonbgColor={styles.backBg}
                      textColor={styles.backText}
                      onPress={pastticketsFn}>
                      Back to past tickets
                    </Button>
                  </View>
                </>
              ) : (
                <>
                  <View style={styles.centerButtons}>
                    <Button
                      onPress={closeMenu}
                      buttonbgColor={[
                        styles.cancelBg,
                        disableCancle && styles.disabledCancleStyle,
                      ]}
                      textColor={[
                        disableCancle ? styles.disableColor : styles.cancelText,
                      ]}
                      disabled={disableCancle}>
                      Cancel
                    </Button>
                    <Button
                      textColor={styles.textColorSubmit}
                      disabled={disableSubmit}
                      onPress={okClick}
                      buttonbgColor={[
                        styles.buttonBgColor,
                        disableSubmit && styles.disabledStyle,
                      ]}>
                      Submit
                      {isLoading && (
                        <ActivityIndicator
                          align="center"
                          size={13}
                          color={GlobalStyles.colors.eWhite.base}
                        />
                      )}
                    </Button>
                  </View>
                </>
              )}
            </>
          )}
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 20,
    paddingHorizontal: 15,
    overflow: "scroll",
  },
  disabledStyle: {
    opacity: 0.5,
    backgroundColor: GlobalStyles.colors.eBackground.selected,
  },
  disabledCancleStyle: {
    opacity: 0.5,
    backgroundColor: GlobalStyles.colors.ePage.base,
    borderColor: GlobalStyles.colors.eLight.base,
    borderWidth: 1,
  },
  disableColor: {
    color: GlobalStyles.colors.eLight.selected,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  aroundMargin: {
    marginLeft: "5%",
    marginRight: "5%",
  },
  card: {
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    width: "100%",
    padding: "6%",
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
  },
  titleCard: {
    color: GlobalStyles.colors.eDark.base,
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
    width: windowHeight / 2.2,
  },
  titleCardStar: {
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eDanger.dark,
  },
  titleCardON: {
    color: GlobalStyles.colors.eDark.base,
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    marginTop: 43,
  },
  textStar: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    marginTop: 43,
    color: GlobalStyles.colors.eDanger.dark,
  },
  errorCls: {
    color: GlobalStyles.colors.eDanger.dark,
    fontSize: 10,
  },
  title: {
    color: GlobalStyles.colors.eRich.base,
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
    textAlign: "left",
  },
  content: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
  },
  label: {
    margin: 8,
  },
  centerButtons: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 15,
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontFamily: "NotoSans-Medium",
  },
  bgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    marginLeft: 10,
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginLeft: 10,
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 10,
  },
  cancelText: {
    color: GlobalStyles.colors.eSecondary.base,
    fontFamily: "NotoSans-Medium",
    fontSize: 12,
  },
  textColorSubmit: {
    color: GlobalStyles.colors.eWhite.base,
    fontFamily: "NotoSans-Medium",
    fontSize: 12,
  },
  inputcls: {
    color: GlobalStyles.colors.eDark.base,
    backgroundColor: GlobalStyles.colors.eBackground.base,
    fontSize: 14,
  },
  enableCls: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    color: GlobalStyles.colors.eSecondary.base,
    outlineColor: GlobalStyles.colors.eSecondary.base,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: GlobalStyles.colors.eSecondary.base,
    // height: 30,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
    // marginTop: -10
  },
  diableCls: {
    borderRadius: 10,
    borderWidth: 2,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
    // marginTop: -10
  },
  submit: {
    marginLeft: 12,
    borderRadius: 10,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
    // paddingBottom: 5
  },
  submitdisable: {
    marginLeft: 12,
    borderRadius: 10,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
    paddingBottom: 5,
    // height: 30,
  },
  backBg: {
    borderColor: GlobalStyles.colors.ePrimary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 10,
  },
  backText: {
    color: GlobalStyles.colors.ePrimary.base,
  },
});

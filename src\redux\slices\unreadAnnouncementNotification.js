import { createSlice } from "@reduxjs/toolkit";

const unreadAnnNotificationReducer = createSlice({
    name: "unreadAnnouncementInfo",
    initialState: {
        unreadAnnouncementInfo:[]
    },
    reducers: {
        unreadAnnouncementInfo:(state,action)=>{
            state.unreadAnnouncementInfo=action.payload
        }
    }
})
export const unreadAnnouncementInfo = unreadAnnNotificationReducer.actions.unreadAnnouncementInfo;
export default unreadAnnNotificationReducer.reducer;
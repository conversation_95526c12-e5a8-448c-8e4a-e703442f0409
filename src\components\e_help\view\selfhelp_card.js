import { ScrollView, View, StyleSheet, Dimensions } from "react-native";
import SelfHelpSingleCard from "./_self-help-single-card";
import { useEffect, useState } from "react";
import { CARDS } from "../constants";

export default function SelfHelpCards({ pastDisplay }) {
  const windowWidth = Dimensions.get("window").width;
  const [cardWidth, setCardWidth] = useState(400);

  useEffect(() => {
    if (windowWidth) {
      if (windowWidth < 500) {
        setCardWidth(windowWidth - 20);
      } else {
        setCardWidth(400);
      }
    }
  }, [windowWidth]);

  const selfHelpCards = [{ name: "SELF HELP", data: CARDS }];
  return (
    <ScrollView
      horizontal={true}
      showsHorizontalScrollIndicator={false}
      pagingEnabled={true}>
      {selfHelpCards.map((item, index) => {
        return (
          <View
            style={[
              index != 0 && styles.rightSpace,
              styles.card1,
              { width: cardWidth },
            ]}
            key={item.path || index}>
            <SelfHelpSingleCard
              title={item.name}
              data={item.data}
              pastDisplay={pastDisplay}
            />
          </View>
        );
      })}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  card1: {
    paddingLeft: 15,
  },
  rightSpace: {
    paddingRight: 15,
  },
});

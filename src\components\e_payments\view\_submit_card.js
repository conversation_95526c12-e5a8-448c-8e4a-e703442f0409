import React, { useState, useEffect, useContext } from "react";
import { StyleSheet, View } from "react-native";
import { Card, Text } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import { useDispatch, useSelector } from "react-redux";
import Ra<PERSON>payCheckout from "react-native-razorpay";
import { PaymentContext } from "../e_payments";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Button from "../../common/_button";
import { TextInput } from "react-native-paper";
import { paymentService } from "../model/payment_service";
import { meterDetails } from "../../../redux/slices/selectedAccount";
import logo from "../../../../assets/OUUG-logo.png";
import { accountDetailsInfo } from "../../../redux/slices/accountDetails";
import Input from "../../common/_input";
import Config from "react-native-config";

export default function SubmitCard({ totalValue, onBlur, onFocus }) {
  const [disable, setDisable] = useState(true);
  const [account, setAccount] = useState([]);
  const [bearer, setbearer] = useState();
  const [saId, setSaId] = useState(null);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState(null);
  const dispatch = useDispatch();
  const [text, setText] = useState("");
  const CURRENCY_REGEX = /^\s*-?[0-9]\d*(\.\d{1,2})?\s*$/;

  const accountData = useSelector(
    state => state.accountDetails?.accountDetails,
  );
  const {
    setConfirmScreen,
    disabled,
    setPaymentCurrency,
    setAccountDetailsScreen,
    setErrorScreen,
    selectedAccount,
    currency,
    setCurrency,
    payableAmount,
    setPay,
    setPayId,
  } = useContext(PaymentContext);

  useEffect(() => {
    if (accountData?.accountSummary) {
      setCurrency(accountData?.accountSummary?.accountDetails[0]?.curSymbol);
    }
    setPaymentCurrency(currency);
  }, [currency]);

  useEffect(() => {
    if (accountData) {
      setName(
        accountData?.accountSummary?.personDetailList?.[
          "C1-Person"
        ]?.personName?.entityName
          ?.replaceAll("-", " ")
          .replaceAll(",", " "),
      );
      let emailAdd = accountData?.accountSummary?.personDetailList?.[
        "C1-Person"
      ]?.emailAddress
        ? accountData?.accountSummary?.personDetailList?.["C1-Person"]
            ?.emailAddress
        : accountData?.accountSummary?.personDetailList?.[
            "C1-Person"
          ]?.personContactDetail.filter(
            item =>
              item.personContactType === "PRIMARYEMAIL" &&
              item.contactDetailValue,
          );
      setEmail(emailAdd?.[0]?.contactDetailValue);
      let mobile = accountData?.accountSummary?.personDetailList?.[
        "C1-Person"
      ]?.personContactDetail.filter(
        item =>
          item.personContactType === "CELLPHONE" && item.contactDetailValue,
      );
      setPhone(mobile?.[0]?.contactDetailValue);
    }
  }, [accountData]);
  useEffect(() => {
    if (totalValue > 0) {
      setDisable(false);
      setPay(totalValue.toFixed(2));
    } else {
      setDisable(true);
      setPay(totalValue);
    }
  }, [totalValue]);

  useEffect(() => {
    if (selectedAccount && Array.isArray(selectedAccount)) {
      let tempAcc = [];
      selectedAccount.map(item => {
        if (item.currentBalance > 0) {
          tempAcc.push({
            accountId: item.accountId,
            amount: item.currentBalance,
          });
        }
      });
      setAccount(JSON.stringify(tempAcc));
    }
  }, [selectedAccount]);

  const handleChangePay = e => {
    setPay(e);
    if (Number(e) > totalValue || e <= 0) {
      setDisable(true);
    } else {
      setDisable(false);
    }
  };
  const updateUsageBill = async account => {
    try {
      const response = await paymentService.getAccountDetails(account, email);
      const bearer = await AsyncStorage.getItem("bearer");
      const parsedToken = JSON.parse(bearer);
      parsedToken.accountDetails = response;
      dispatch(accountDetailsInfo(response));
      const updatedToken = JSON.stringify(parsedToken);
      await AsyncStorage.setItem("bearer", updatedToken);
      let getFirstMeter =
        response?.accountSummary?.personAcccountDetail?.personAccountList.find(
          singleAcc => !singleAcc.isDisabled,
        );
      const saId = getFirstMeter?.accountSaList[0];
      const sampleObj = { getSaInfo: saId };
      getFirstMeter = { ...getFirstMeter, ...sampleObj };
      dispatch(meterDetails(getFirstMeter));
    } catch (error) {
      console.log(error);
    }
  };

  const payNow = async () => {
    setAccountDetailsScreen(false);
    let accountDetails = [];
    if (selectedAccount.length > 1) {
      selectedAccount.map(item =>
        accountDetails.push({
          accountId: item.accountId,
          amount: item.currentBalance,
        }),
      );
    } else {
      accountDetails = [
        {
          accountId: selectedAccount?.[0]?.accountId,
          amount: Number(payableAmount),
        },
      ];
    }

    await AsyncStorage.getItem("bearer").then(async bearer => {
      await paymentService
        .prepayment(
          payableAmount,
          selectedAccount?.[0]?.accountId,
          selectedAccount?.[0]?.saDetails?.[0]?.saId,
          bearer,
          email,
        )
        .then(async res => {
          let orderData = res?.data?.data?.prePaymentDetailsRazorPay;
          if (orderData) {
            let curr = orderData?.currency ? orderData?.currency : "INR";
            let orderId = orderData?.id;
            var options = {
              currency: curr,
              key: Config.RAZORPAY_KEY, // Enter the Key ID generated from the Dashboard
              amount: payableAmount * 100,
              name: "Abjayon",
              order_id: orderId, //Replace this with an order_id created using Orders API.
              prefill: {
                email: email,
                contact: phone,
                name: name,
              },
              theme: { color: GlobalStyles.colors.ePrimary.base },
            };

            RazorpayCheckout.open(options)
              .then(async data => {
                if (accountDetails && orderData) {
                  await paymentService
                    .postPaymentProcessing(
                      orderData,
                      data,
                      accountDetails,
                      false,
                      email,
                    )
                    .then(res => {
                      let response =
                        res?.data?.data?.postPaymentDetailsRazorPay;
                      if (
                        response?.genericErrorInformation?.isInError === "false"
                      ) {
                        if (response?.mainData.length > 1) {
                          if (
                            response?.mainData?.[0]?.errorInformation
                              ?.isInError === "false"
                          ) {
                            response?.mainData?.[0]?.paymentInfo?.output
                              ?.customOutput?.field1?.name === "payId" &&
                              setPayId(
                                response?.mainData?.[0]?.paymentInfo?.output
                                  ?.customOutput?.field1?.value,
                              );
                            updateUsageBill(selectedAccount?.[0]?.accountId);
                            setAccountDetailsScreen(false);
                            setConfirmScreen(true);
                            setErrorScreen(false);
                          } else {
                            if (
                              response?.mainData?.[0]?.paymentInfo?.output
                                ?.customOutput?.field1?.name === "payId"
                            ) {
                              setPayId(
                                response?.mainData?.[0]?.paymentInfo?.output
                                  ?.customOutput?.field1?.value,
                              );
                              setAccountDetailsScreen(false);
                              setConfirmScreen(false);
                              setErrorScreen(true);
                            }else{
                              setAccountDetailsScreen(false);
                              setConfirmScreen(false);
                              setErrorScreen(true);
                            }
                          }
                        } else {
                          if (
                            response?.mainData?.errorInformation?.isInError ===
                            "false"
                          ) {
                            res?.data?.data?.postPaymentDetailsRazorPay
                              ?.mainData?.paymentInfo?.output?.customOutput
                              ?.field1?.name === "payId" &&
                              setPayId(
                                response?.mainData?.paymentInfo?.output
                                  ?.customOutput?.field1?.value,
                              );
                            updateUsageBill(selectedAccount?.[0]?.accountId);
                            setAccountDetailsScreen(false);
                            setConfirmScreen(true);
                            setErrorScreen(false);
                          } else {
                            if (
                              response?.mainData?.paymentInfo?.output
                                ?.customOutput?.field1?.name === "payId"
                            ) {
                              setPayId(
                                response?.mainData?.paymentInfo?.output
                                  ?.customOutput?.field1?.value,
                              );
                              setAccountDetailsScreen(false);
                              setConfirmScreen(false);
                              setErrorScreen(true);
                            }else{
                              setAccountDetailsScreen(false);
                              setConfirmScreen(false);
                              setErrorScreen(true);
                            }
                          }
                        }
                      } else {
                        setAccountDetailsScreen(false);
                        setConfirmScreen(false);
                        setErrorScreen(true);
                      }
                    })
                    .catch(err => {
                      setAccountDetailsScreen(false);
                      setConfirmScreen(false);
                      setErrorScreen(true);
                    });
                }
              })
              .catch(error => {
                setAccountDetailsScreen(false);
                setConfirmScreen(false);
                setErrorScreen(true);
              });
          }
        })
        .catch(err => {
          setAccountDetailsScreen(false);
          setConfirmScreen(false);
          setErrorScreen(true);
        });
    });
  };

  const isValidAmount =
    CURRENCY_REGEX.test(payableAmount) && payableAmount.length < 8;

  return (
    <View>
      <Card style={styles.card}>
        <View style={styles.aroundMargin}>
          <View style={styles.content}>
            <View style={styles.leftView}>
              {!selectedAccount ||
              selectedAccount.length === 0 ||
              totalValue <= 0 ||
              selectedAccount.length > 1 ? (
                <Text style={styles.textStyleZero}>
                  {currency}
                  {payableAmount}
                </Text>
              ) : (
                <View style={{ bottom: 3 }}>
                  <TextInput
                    theme={{
                      colors: {
                        text: GlobalStyles.colors.eDark.base,
                        primary: GlobalStyles.colors.eDark.base,
                        accent: GlobalStyles.colors.eDark.base,
                      },
                    }}
                    dense
                    mode="outlined"
                    onFocus={onFocus}
                    onBlur={onBlur}
                    outlineColor={GlobalStyles.colors.eWhite.base}
                    outlineStyle={{
                      borderColor: isValidAmount
                        ? GlobalStyles.colors.eWhite.base
                        : GlobalStyles.colors.eDanger.dark,
                    }}
                    scrollEnabled={false}
                    textAlignVertical={"auto"}
                    keyboardType="numeric"
                    returnKeyType="done"
                    activeOutlineColor={GlobalStyles.colors.eWhite.hover}
                    maxLength={10}
                    style={styles.paymentAmount}
                    value={payableAmount}
                    onChangeText={handleChangePay}
                    disabled={
                      !selectedAccount ||
                      selectedAccount.length === 0 ||
                      totalValue <= 0
                    }
                    selectionColor="black"
                  />
                </View>
              )}
              {!selectedAccount ||
              selectedAccount.length === 0 ||
              totalValue <= 0 ||
              selectedAccount.length > 1 ? (
                <Text style={styles.paymentLabel}>Amount Payable</Text>
              ) : null}
            </View>

            <View style={styles.rightView}>
              <Button
                buttonbgColor={
                  disable || !isValidAmount
                    ? styles.buttonBgColorDisable
                    : styles.buttonBgColor
                }
                textColor={disable ? styles.blueTextdisable : styles.blueText}
                disabled={disable || !isValidAmount}
                onPress={payNow}>
                PAY NOW
              </Button>
            </View>
          </View>
        </View>
      </Card>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    width: "100%",
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    color: GlobalStyles.colors.eWhite.base,
  },
  aroundMargin: {
    marginLeft: 5,
    marginRight: 5,
  },
  paymentAmount: {
    marginTop: 13,
    marginBottom: 13,
    textAlign: "right",
    fontFamily: "NotoSans-Bold",
    // height: 28,
    width: 120,
    fontSize: 16,
    textAlign: "center",
    marginLeft: "7%",
    overflow: "hidden",
  },
  paymentLabel: {
    fontSize: 12,
    textAlign: "right",
    fontWeight: 600,
    color: GlobalStyles.colors.eWhite.base,
    marginTop: 0,
    marginBottom: 12,
  },
  leftView: {
    float: "left",
    marginLeft: 4,
    alignSelf: "center",
    width: "27%",
    //marginTop: 30,
  },
  rightView: {
    float: "right",
    width: "40%",
    marginRight: "7%",
  },
  content: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
    justifyContent: "space-between",
    alignItems: "center",
    margin: 5,
  },
  diableCls: {
    backgroundColor: GlobalStyles.colors.eLight.base,
    color: GlobalStyles.colors.eMedium.base,
    fontFamily: "NotoSans-Bold",
    width: "85%",
  },
  buttonBgColor: {
    width: "80%",
    paddingTop: 10,
    paddingBottom: 10,
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    paddingVertical: "4%",
    alignSelf: "flex-end",
  },
  buttonBgColorDisable: {
    width: "80%",
    backgroundColor: GlobalStyles.colors.eLight.base,
    paddingTop: 10,
    paddingBottom: 10,
    alignSelf: "flex-end",
    paddingVertical: "4%",
  },
  blueText: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 13,
    fontFamily: "NotoSans-Bold",
  },
  blueTextdisable: {
    color: GlobalStyles.colors.eMedium.base,
    fontSize: 13,
    fontFamily: "NotoSans-Bold",
  },
  textStyleZero: {
    fontSize: 16,
    textAlign: "right",
    color: GlobalStyles.colors.eWhite.base,
    fontFamily: "NotoSans-Bold",
    marginTop: 23,
    marginBottom: -5,
  },
});

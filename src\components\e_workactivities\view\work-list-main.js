import React, { useState, useEffect, useContext } from "react";
import {
  StyleSheet,
  View,
  Button,
  TextInput,
  TouchableOpacity,
  Pressable,
  ScrollView,
} from "react-native";
import { Card, Text, Checkbox, List } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import AllWorkActivites from "./all-work-activities";
import WorkOrderList from "./work-order";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import { useNavigation } from "@react-navigation/native";
import workOrderContext from "../../app/authenticated/authenticated_footer";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import ConsumerIndex from "../../e_consumer-indexing/view/new-consumer-index";
import { config } from "../../../environment";
import WorkOrderListWO from "./work-order_wo";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import AllWorkActivitesWO from "./all-work-activities_wo";
import { stackContext } from "../../app/get_stack";
//import { WorkOrderService } from "../model/work-order-service";

export default function WorkActivityList() {
  // const [workOrderList, setWorkOrderList] = useState([]);
  const { workModelType } = React.useContext(stackContext);
  const context = workModelType === "WA" ? drawerContext : drawerContextWO;

  const {
    setSingleWorkOrder,
    allActivities,
    setAllActivities,
    singleWorkOrder,
    setWorkOrder,
    workOrder,
    consumerIndex,
    setConsumerIndex,
    confirmationModal,
    setConfirmationModal,
    setTempWorkOrder,
  } = useContext(context);

  const navigation = useNavigation();

  useEffect(() => {}, [
    singleWorkOrder,
    consumerIndex,
    allActivities,
    confirmationModal,
  ]);
  useEffect(() => {
    setTempWorkOrder(tempCIData);
  }, [singleWorkOrder]);
  // const [loading, setLoading] = useState(false);
  // const [reviewScreen, setReviewScreen] = useState(false);
  // const [tempCIData, setTempCIData] = useState();
  // const [filled, setfilled] = useState(false);

  // const fetchWorkOrderList = async () => {
  //   console.log('getAllCIList.....');
  //   try {
  //     setLoading(true);
  //     const res = await WorkOrderService.getAllWorkOrderList();
  //     console.log('res.........',res);
  //     setWorkOrderList(res);
  //   } catch (err) {
  //     console.log("Error in fetching work order data");
  //   } finally {
  //     setLoading(false);
  //   }
  // };
  // useEffect(() => {
  //     console.log('calling.....useEffect');
  //     fetchWorkOrderList();
  // }, []);

  return (
    <>
      {allActivities ? (
        workModelType === "WA" ? (
          <AllWorkActivites />
        ) : (
          <AllWorkActivitesWO />
        )
      ) : singleWorkOrder ? (
        workModelType === "WA" ? (
          <WorkOrderList />
        ) : (
          <WorkOrderListWO />
        )
      ) : (
        <ConsumerIndex />
      )}
    </>
  );
}

const styles = StyleSheet.create({
  card: {
    borderTopEndRadius: 10,
    borderTopStartRadius: 10,
    borderBottomEndRadius: 0,
    borderBottomStartRadius: 0,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    padding: 15,
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    margin: 10,
  },
  cardDetails: {
    borderTopEndRadius: 10,
    borderTopStartRadius: 10,
    borderBottomEndRadius: 0,
    borderBottomStartRadius: 0,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    padding: 15,
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    margin: 10,
  },
  titleCard: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontWeight: "700",
    fontFamily: "NotoSans-Bold",
  },
  cardHeight: {
    alignSelf: "center",
    marginTop: "8%",
    fontSize: 12,
  },
  wrapDirection: {
    flexDirection: "row",
    flexWrap: "wrap",
    alignContent: "space-between",
  },
  listView: {
    marginHorizontal: 20,
  },
  backButtonWrapper: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 3,
  },
  paddingRight: {
    paddingRight: 10,
  },
});

import { GlobalStyles } from "../app/global-styles";
import {
  View,
  Text,
  Dimensions,
  StyleSheet,
  ScrollView,
  Image,
  Platform,
} from "react-native";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import Ionicons from "react-native-vector-icons/Ionicons";
import SelectDropdown from "react-native-select-dropdown";
import React, { useEffect, useRef } from "react";
import Icon from "../icon";

export default function DropDown({
  data,
  defaultvalue,
  selectedAccount,
  onChange,
  title,
  afterSelection,
  close,
  setClose,
  canRenderIcon,
  customSelectButton = false,
  options,
  disabled,
  dropdownType,
}) {
  const dropdownRef = useRef({});

  useEffect(() => {
    if (close === true) {
      dropdownRef.current.reset();
      setClose(false);
    }
  }, [close]);

  const renderCustomizeBtnChild = (selectedItem, index) => {
    return canRenderIcon ? (
      <View style={styles.dropdown3BtnChildStyle}>
        {selectedItem ? (
          <>
            {options === "keyValuePair" ? (
              <View
                style={{
                  flexDirection: "row",
                  display: "flex",
                  flex: 1,
                  alignItems: "center",
                  justifyContent: "space-between",
                }}>
                <View
                  style={
                    dropdownType === "account"
                      ? styles.leftItem
                      : styles.leftItemsai
                  }>
                  <Text style={styles.selectedValue}>{afterSelection}</Text>
                </View>
                <View
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    gap: 4,
                  }}>
                  {selectedItem && selectedItem.image && (
                    <Icon
                      name={selectedItem.image}
                      color={GlobalStyles.colors.ePrimary.base}
                      size={14}
                    />
                  )}
                  <Text style={styles.selectedItem}>{selectedItem?.value}</Text>
                </View>
              </View>
            ) : (
              <Text style={styles.item}>{selectedItem?.value}</Text>
            )}
          </>
        ) : (
          <View>
            <View>
              <Ionicons name="md-earth-sharp" color={"#444"} size={32} />
            </View>
            <Text style={styles.dropdown3BtnTxt}>
              {/* {selectedItem.title} */}
            </Text>
            <View>
              <FontAwesome name="chevron-down" color={"#444"} size={12} />
            </View>
          </View>
        )}
      </View>
    ) : null;
  };
  const renderButtonTextAfterSelection = (selectedItem, index) => {
    return customSelectButton ? (
      <View style={styles.btnTextContainer}>
        <View
          style={
            dropdownType === "account" ? styles.leftItem : styles.leftItemsai
          }>
          <Text style={styles.selectedValue}>{afterSelection} </Text>
        </View>
        <View style={styles.rightItem}>
          <Text style={styles.selectedItem}>
            {selectedItem?.value || selectedItem}
          </Text>
        </View>
      </View>
    ) : options === "keyValuePair" ? (
      <View style={styles.rightItem}>
        <Text style={styles.item}>{selectedItem.value}</Text>
      </View>
    ) : (
      <View
        style={{
          display: "flex",
          flexDirection: "row",
          alignItems: "center",
        }}>
        <View style={{ paddingRight: 5 }}></View>
        <View>
          <Text style={styles.singleItem}>{selectedItem}</Text>
        </View>
      </View>
    );
    // text represented after item is selected
    // if data array is an array of objects then return selectedItem.property to render after item is selected
  };

  const handleKeyValuePair = (item, index) => {
    return (
      <View style={{ fontSize: 14, fontFamily: "NotoSans-SemiBold" }}>
        <Text>{item?.value}</Text>
      </View>
    );
  };
  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      alwaysBounceVertical={false}
      contentContainerStyle={styles.scrollViewContainer}>
      <SelectDropdown
        disabled={disabled}
        data={data}
        onSelect={(selectedItem, index) => {
          onChange(selectedItem);
        }}
        ref={dropdownRef}
        buttonTextAfterSelection={
          customSelectButton
            ? renderButtonTextAfterSelection
            : options === "keyValuePair"
            ? handleKeyValuePair
            : false
        }
        rowTextForSelection={(item, index) => {
          // text represented for each item in dropdown
          // if data array is an array of objects then return item.property to represent item in dropdown
          return options === "keyValuePair" ? item.value : item;
        }}
        renderCustomizedButtonChild={
          customSelectButton ? renderCustomizeBtnChild : false
        }
        renderDropdownIcon={isOpened => {
          return (
            <FontAwesome
              name={isOpened ? "chevron-up" : "chevron-down"}
              color={GlobalStyles.colors.eRich.hover}
              size={12}
              style={dropdownType === "account" ? styles.dropdownArrow : null}
            />
          );
        }}
        dropdownIconPosition={"right"}
        defaultValue={defaultvalue || null}
        defaultButtonText={title}
        buttonStyle={styles.dropdownBtnStyleBeforeSelection}
        buttonTextStyle={styles.btnTextStyle}
        dropdownStyle={styles.dropdownStyle}
        rowStyle={styles.rowStyle}
        renderCustomizedRowChild={
          canRenderIcon
            ? (item, index) => {
                return (
                  <View style={styles.dropdownInsideContent}>
                    {item?.image && (
                      <Icon
                        name={item.image}
                        color={GlobalStyles.colors.ePrimary.base}
                        style={styles.closeIcon}
                        size={14}
                      />
                    )}
                    <Text style={styles.item}>{item?.value}</Text>
                  </View>
                );
              }
            : null
        }
        rowTextStyle={
          customSelectButton
            ? styles.dropdown4RowTxtStyle
            : styles.genericRowTxtStyle
        }
        dropdownOverlayColor="No"
        dropdownBackgroundColor="No"
      />
    </ScrollView>
  );
}
const styles = StyleSheet.create({
  singleId: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    backgroundColor: GlobalStyles.colors.eWhite.base,
    borderRadius: 0,
    zIndex: 0,
    width: "100%",
    marginTop: 0,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    borderWidth: 0,
    padding: 12,
  },
  scrollViewContainer: {
    flexGrow: 1,
    justifyContent: "space-between",
    alignItems: "center",
  },
  dropdownStyle: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginTop: Platform.OS === "ios" ?  0: -29,
    borderTopLeftRadius:5,
    borderTopRightRadius: 5,
    borderBottomLeftRadius: 2,
    borderBottomEndRadius: 2,
    paddingHorizontal: 2,
    elevation: 1,
  },
  dropdown3BtnImage: { width: 45, height: 45, resizeMode: "cover" },
  dropdown3BtnTxt: {
    color: "#444",
    textAlign: "center",
    fontFamily: "NotoSans-Bold",
    fontSize: 12,
    marginHorizontal: 12,
  },
  dropdownBtnStyleBeforeSelection: {
    width: "100%",
    height: 30,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    borderRadius: 2,
    borderColor: GlobalStyles.colors.eOutline.base,
    fontSize: 12,
    fontFamily: "NotoSans-Regular",
    color: GlobalStyles.colors.eRich.hover,
  },
  btnTextStyle: {
    color: GlobalStyles.colors.eDark.base,
    textAlign: "left",
    fontSize: 14,
    fontFamily: "NotoSans-SemiBold",
  },
  rowStyle: {
    backgroundColor: GlobalStyles.colors.eBackground.base,
    borderBottomColor: GlobalStyles.colors.eBackground.base,
    paddingHorizontal: -3,
    height: 40,
  },
  dropdown4RowTxtStyle: {
    textAlign: "left",
    color: GlobalStyles.colors.eRich.hover,
    fontSize: 14,
    fontFamily: "NotoSans-SemiBold",
  },
  genericRowTxtStyle: {
    textAlign: "left",
    fontSize: 14,
    fontFamily: "NotoSans-SemiBold",
    opacity: 0.8,
  },
  dropdown3BtnChildStyle: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 10,
    gap: 16,
  },
  selectedItem: {
    color: GlobalStyles.colors.eRich.hover,
    fontSize: 12,
    fontFamily: "NotoSans-Regular",
    marginRight: 10,
    top: -2,
  },
  item: {
    color: GlobalStyles.colors.eRich.hover,
    fontSize: 12,
    fontFamily: "NotoSans-Regular",
  },

  singleItem: {
    fontSize: 14,
    fontFamily: "NotoSans-SemiBold",
  },
  selectedValue: {
    color: GlobalStyles.colors.eRich.base,
    fontSize: 14,
    fontFamily: "NotoSans-SemiBold",
  },
  dropdownRowImage: { width: 15, height: 15, resizeMode: "cover" },
  dropdownInsideContent: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "center",
    paddingHorizontal: 14,
    gap: 6,
  },
  dropdown3RowTxt: {
    marginLeft: 10,
    fontSize: 16,
  },
  dropdownArrow: {
    marginBottom: Platform.OS === "ios" ? 2 : 0,
  },
  btnTextContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
});

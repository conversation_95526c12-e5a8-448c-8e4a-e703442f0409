import { useEffect, useState } from "react";
import { View } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { defaultAlertsPreference } from "../../../redux/slices/defaultAlertsPreference";
import { alertsPreference } from "../../../redux/slices/handleChangeAlertsPreference";
import { billPreferenceService } from "../bills/model/billPreferenceService";
import BillAlertsNotifications from "../bills/view/_bill_alerts_notifications";
import SaveButton from "../bills/view/_save_button";
import AccountBalance from "./view/_account_balance";

export default function PrepaidAccountPreference() {
  let accountId = useSelector(
    (store) => store?.meterDetails?.meterDetails?.accountId
  );
  const [billRoutingLoading, setBillRoutingLoading] = useState(false);
  const [billAlertsError, setBillAlertsError] = useState();
  const dispatch = useDispatch();
  useEffect(() => {
    if (accountId) {
      dispatch(defaultAlertsPreference());
      dispatch(alertsPreference());
      setBillRoutingLoading(true);
      billPreferenceService
        .getBillNotifyPreference(accountId)
        .then((response) => {
          if (response?.data?.getBillNotifyPreference?.notificationTypes) {
            let data =
              response?.data?.getBillNotifyPreference?.notificationTypes.filter(
                (i) => i.applicableFor === "PREPAID"
              );
            dispatch(defaultAlertsPreference(data));
            dispatch(alertsPreference(data));
            setBillAlertsError();
            setBillRoutingLoading(false);
          }
        })
        .catch((err) => {
          setBillRoutingLoading(false);
          setBillAlertsError(err);
          dispatch(defaultAlertsPreference());
          dispatch(alertsPreference());
        });
    }
  }, [accountId]);
  return (
    <View style={{marginBottom:"8%"}}>
      <AccountBalance />
      <View style={{ margin: "2%" }} />
      {billAlertsError ? null : (
        <BillAlertsNotifications billRoutingLoading={billRoutingLoading} />
      )}
      <SaveButton />
      <View style={{ margin: "3%" }} />
    </View>
  );
}

import AsyncStorage from "@react-native-async-storage/async-storage";
import moment from "moment";
import { useEffect } from "react";
import { useState } from "react";
import { ActivityIndicator, StyleSheet } from "react-native";
import { Card } from "react-native-paper";
import { useDispatch, useSelector } from "react-redux";
import { unreadAnnouncementInfo } from "../../../redux/slices/unreadAnnouncementNotification";
import { unreadBillInfo } from "../../../redux/slices/unreadBillNotification";
import { unreadMeterInfo } from "../../../redux/slices/unreadMeterNotification";
import { GlobalStyles } from "../../app/global-styles";
import meterAlertsCall from "../model/notification_service";
import ContentDisplay from "./_contentDisplay";
import FilterData from "./_filterData";

export default function NotificationCard({
  meterRank,
  billRank,
  announceRank,
  setMeterUnread,
  setBillUnread,
  setAnnouncementUnread,
}) {
  const [startDate, setStartDate] = useState();
  const [endDate, setEndDate] = useState();
  const [announcementEndDate, setAnnouncementEndDate] = useState();
  const [readData, setReadData] = useState();
  const [allData, setData] = useState([]);
  const [loadingAlerts, setLoadingAlerts] = useState(true);
  const [loadingAnn, setLoadingAnn] = useState(true);
  const [finalData, setFinalData] = useState();
  const dispatch = useDispatch();
  const accountId = useSelector(
    state => state?.meterDetails?.meterDetails?.accountId,
  );

  const customerClass = useSelector(
    state => state?.meterDetails?.meterDetails?.customerClassCd,
  );
  const languageCode = useSelector(
    state =>
      state?.parameter?.parameter?.ParameterLookup?.TENANT_PRIMARY_LANGUAGE,
  );
  const ANNOUNCEMENT_VALUE = "ANNOUNCEMENT";
  const backendDataFormat = useSelector(
    state => state?.parameter?.parameter?.ParameterLookup?.YEAR_DATE_FORMAT,
  );
  const saStartDate = useSelector(
    state => state?.meterDetails?.meterDetails?.getSaInfo?.saStartDate,
  );

  useEffect(() => {
    if (backendDataFormat) {
      setStartDate(moment(saStartDate).format(backendDataFormat));
      setEndDate(moment().format(backendDataFormat));
    }
  }, [backendDataFormat]);
  useEffect(() => {
    if (endDate && backendDataFormat) {
      setAnnouncementEndDate(
        moment(endDate).add(1, "day").format(backendDataFormat),
      );
    }
  }, [endDate]);

  useEffect(() => {
    if (
      startDate &&
      endDate &&
      customerClass &&
      accountId &&
      announcementEndDate &&
      languageCode
    ) {
      setLoadingAlerts(true);
      setData([]);
      meterAlertsCall(
        startDate,
        endDate,
        "",
        accountId,
        customerClass,
        announcementEndDate,
        languageCode,
      )
        .then(res => {
          if (meterRank) {
            setData(prev => [
              ...prev,
              {
                alertsData: res?.data?.getAlerts?.Meter,
                path: "METER",
                widgetRank: meterRank,
                widgetValue: meterRank ? true : false,
              },
            ]);
          }
          if (billRank) {
            setData(prev => [
              ...prev,
              {
                alertsData: res?.data?.getAlerts?.Bill,
                path: "BILL",
                widgetRank: billRank,
                widgetValue: billRank ? true : false,
              },
            ]);
          }
          let unreadDataBill = res?.data?.getAlerts?.Bill.filter(
            i => i.IsRead === 0,
          );
          let unreadDataMeter = res?.data?.getAlerts?.Meter.filter(
            i => i.IsRead === 0,
          );

          setMeterUnread(unreadDataMeter.length);
          setBillUnread(unreadDataBill.length);
          setLoadingAlerts(false);
        })
        .catch(err => setLoadingAlerts(false));
      setLoadingAnn(true);
      meterAlertsCall(
        startDate,
        endDate,
        ANNOUNCEMENT_VALUE,
        accountId,
        customerClass,
        announcementEndDate,
        languageCode,
      )
        .then(res => {
          setLoadingAnn(false);
          {
            if (announceRank) {
              setData(prev => [
                ...prev,
                {
                  alertsData: res?.data?.getAnnouncementList,
                  path: "ANNOUNCEMENT",
                  widgetRank: announceRank,
                  widgetValue: announceRank ? true : false,
                },
              ]);
              let unreadData = res?.data?.getAnnouncementList.filter(
                i => i.IsRead === 0,
              );

              setAnnouncementUnread(unreadData.length);
            }
          }
        })
        .catch(err => setLoadingAnn(false));
    }
  }, [
    startDate,
    endDate,
    customerClass,
    accountId,
    announcementEndDate,
    announceRank,
    meterRank,
    billRank,
    languageCode,
  ]);

  useEffect(() => {
    let tempAllData = [];
    if (
      allData &&
      allData.length > 0 &&
      loadingAlerts === false &&
      loadingAnn === false
    ) {
      allData.forEach(item => {
        let correct;
        if (item.alertsData.length === 0) {
          if (tempAllData.length > 0) {
            tempAllData.forEach(
              ele => item?.path !== ele?.path && tempAllData.push(item),
            );
          } else {
            correct = true;
          }
        } else {
          item.alertsData.forEach(ele => {
            if (
              moment(
                item.path === "ANNOUNCEMENT"
                  ? ele?.CreateDateTime
                  : ele.InsertedDateTime,
              ).isBetween(
                moment(startDate),
                moment(
                  item.path === "ANNOUNCEMENT" ? announcementEndDate : endDate,
                ),
                "day",
                "[]",
              ) ||
              moment(
                item.path === "ANNOUNCEMENT"
                  ? ele?.CreateDateTime
                  : ele.InsertedDateTime,
              ).isSame(startDate, "day") ||
              moment(
                item.path === "ANNOUNCEMENT"
                  ? ele?.CreateDateTime
                  : ele.InsertedDateTime,
              ).isSame(
                item.path === "ANNOUNCEMENT" ? announcementEndDate : endDate,
                "day",
              )
            ) {
              correct = true;
            } else {
              correct = false;
            }
          });
        }
        if (correct === true) {
          tempAllData.push(item);
        }
      });
      setFinalData(tempAllData.sort((a, b) => a.widgetRank - b.widgetRank));
    }
  }, [allData, loadingAlerts, loadingAnn, startDate, endDate]);

  useEffect(() => {
    if (finalData && finalData.length > 0 && !loadingAlerts && !loadingAnn) {
      finalData.map(item => {
        if (item.path === "METER") {
          dispatch(unreadMeterInfo(item.alertsData));
        }
        if (item.path === "BILL") {
          dispatch(unreadBillInfo(item.alertsData));
        }
        if (item.path === "ANNOUNCEMENT") {
          dispatch(unreadAnnouncementInfo(item.alertsData));
        }
      });
    }
  }, [finalData, loadingAlerts, loadingAnn]);

  return (
    <>
      {!loadingAlerts && !loadingAnn ? (
        <>
          <FilterData
            setStartDate={setStartDate}
            setEndDate={setEndDate}
            startDate={startDate}
            endDate={endDate}
            readData={readData}
            setReadData={setReadData}
          />
          <ContentDisplay
            readData={readData}
            setMeterUnread={setMeterUnread}
            setBillUnread={setBillUnread}
            setAnnouncementUnread={setAnnouncementUnread}
          />
        </>
      ) : (
        <ActivityIndicator color={GlobalStyles.colors.ePrimary.base} />
      )}
    </>
  );
}

const styles = StyleSheet.create({
  cardStyles: {
    marginHorizontal: "4%",
    borderRadius: 10,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    boxShadow: "rgb(0 0 0 / 5%) 1px 5px 13px 0px",
    position: "relative",
    padding: "4%",
  },
});

import axios from "axios";
import { config } from "../../../environment";

export const DashboardServiceWO = {
  getTodayList,
  getTaskCount,
  getPlaceFromCoordinates,
};

async function getTaskCount() {
  try {
    const response = await axios.get(
      "https://impresa-fieldwork-demo.abjayon.com/work-management/work-order/user-workOrder-count?resourceId=2",
    );

    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function getTodayList() {
  try {
    const response = await axios.get(
      "https://impresa-fieldwork-demo.abjayon.com/work-management/work-order?pageNumber=1&pageSize=1000&getWorkActivities=true&orgHierarchyId=9&crewResourceId=2&getActivityChecklist=true&workStatus=['I','C','O']",
      // Add your request data here if needed
    );
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function getAllWorkActivities() {
  try {
    const response = await axios.get(
      "https://impresa-fieldwork-demo.abjayon.com/work-management/work-order?workOrderId=8&pageNumber=1&pageSize=1000&getWorkActivities=true&getWorkOrderResources=true&getActivityChecklist=true",
    );

    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function getAddress() {
  try {
    const response = await axios.get(
      "https://impresa-fieldwork-demo.abjayon.com/field-setup/consumer-indexing?meterNumber=Meter002",
    );
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function getPlaceFromCoordinates(latitude, longitude, apiKey) {
  try {
    const response = await axios.get(
      `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}`,
    );
    if (response.data.results.length > 0) {
      let formattedAddress = response.data.results[0].formatted_address;
      if (formattedAddress) {
        let addressParts = formattedAddress.split(",");
        // Remove the last two elements
        addressParts = addressParts.slice(0, -2);

        // Join the remaining parts back into a string
        formattedAddress = addressParts.join(", ");
      } else {
        formattedAddress = "";
      }
      return formattedAddress;
    } else {
      return "Location not found";
    }
  } catch (error) {
    console.error("Error fetching location:", error);
    return "Error fetching location";
  }
}

export default getPlaceFromCoordinates;

import { Card, Text, TextInput } from "react-native-paper";
import {
  View,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Dimensions,
  Platform,
} from "react-native";
import React, { useEffect, useState, useContext } from "react";
import { GlobalStyles } from "../../app/global-styles";
import UploadImage from "../../common/_upload_image";
import Button from "../../common/_button";
import getTicketReferenceNumber, { complaintsService } from "./model/service";
import { useSelector, useDispatch } from "react-redux";
import { ticketContext } from "../e_services";
import { ticketID } from "../../../redux/slices/pastTicketId";
import { turnOnOffID } from "../../../redux/slices/pastTurnOnOff";
import DropDown from "../../common/_dropdown";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import { config } from "../../../environment";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { stackContext } from "../../app/get_stack";

const windowHeight = Dimensions.get("window").height;
const windowWidth = Dimensions.get("window").width;
export default function Complaints({
  name,
  requiredHeight,
  requiredCardHeight,
}) {
  const { workModelType } = React.useContext(stackContext);
  const [comment, setComment] = useState();
  const [selectedItem, setSelected] = useState();
  const [data, setData] = useState();
  const [allData, setAllData] = useState();
  const [category, setCategory] = useState();
  const [dropdownError, setDropdownError] = useState(false);
  const [fileData, setFileData] = useState([]);
  const [ticketTypeErr, setTicketTypeError] = useState(false);
  const [commentErr, setCommentErr] = useState(false);
  let setShowPopup,
    setTitlepopup,
    setTicketNumber,
    submitLoader,
    setSubmitLoader;

  if (workModelType === "WA") {
    ({ setShowPopup, setTitlepopup, setTicketNumber } =
      useContext(drawerContext));
    ({ submitLoader, setSubmitLoader } = useContext(drawerContext));
  } else {
    ({ setShowPopup, setTitlepopup, setTicketNumber } =
      useContext(drawerContextWO));
    ({ submitLoader, setSubmitLoader } = useContext(drawerContextWO));
  }

  const [isLoading, setLoading] = useState(false);
  const [ticketErr, setTicketErr] = useState();
  const [options, setOptions] = useState();
  const dispatch = useDispatch();
  const pastTicketID = useSelector(store => store?.ticketID.ticketID);
  const pastturnOnOffID = useSelector(store => store?.turnOnOffID.turnOnOffID);
  const [close, setClose] = useState(false);
  const { height } = Dimensions.get("window");

  const [disableSubmit, setDisableSubmit] = useState(true);
  const [disableCancle, setDisableCancle] = useState(true);

  const param = useSelector(
    state =>
      state?.parameter?.parameter?.ParameterLookup?.TENANT_PRIMARY_LANGUAGE,
  );

  const accountId = useSelector(
    state => state?.meterDetails?.meterDetails?.accountId,
  );

  useEffect(() => {
    if (pastTicketID || pastturnOnOffID) {
      let ticket = null;
      dispatch(ticketID(ticket));
      dispatch(turnOnOffID(ticket));
    }
    if (param) {
      complaintsService
        .getTicketDropdowns(param)
        .then(res => setAllData(res?.data))
        .catch(err => setDropdownError(true));
    }
  }, [param, pastTicketID]);

  useEffect(() => {
    if (selectedItem || comment || comment?.trim().length > 0) {
      setDisableCancle(false);
    } else {
      setDisableCancle(true);
    }
    if (selectedItem && comment && comment?.trim().length > 0) {
      setDisableSubmit(false);
    } else {
      setDisableSubmit(true);
    }
  }, [selectedItem, comment]);

  useEffect(() => {
    setComment("");
    setClose(true);
    if (allData?.getTicketDropdowns && name) {
      const option = [],
        newData = [];
      allData?.getTicketDropdowns?.[name]?.map(item => {
        option.push(item.ticketTypeValue);
        newData.push({
          value: item.ticketTypeValue,
          key: item.ticketTypeCode,
        });
      });
      setOptions(option);
      setData(newData);
      setCategory(allData?.getTicketDropdowns?.[name]?.[0]?.ticketCategoryCode);
    }
  }, [name, allData]);

  const selectClick = val => {
    setSelected(val);
  };

  const submitClick = async () => {
    if (selectedItem) {
      setTicketTypeError(false);
      if (comment) {
        setCommentErr(false);
        setLoading(true);
        setSubmitLoader(true);
        setTicketErr();
        const selectedCode = data.filter(
          item => item.value === selectedItem.value,
        );
        await complaintsService
          .getTicketReferenceNumber(
            accountId,
            selectedCode[0].key,
            comment,
            fileData,
            category,
          )
          .then(res => {
            setLoading(false);
            setSubmitLoader(false);
            setShowPopup(true);
            setTicketNumber(res?.data?.createTicket?.ticketId);
            cancelClick();
            setTitlepopup(name + " - Complaints");
            setTicketErr();
          })
          .catch(err => {
            setLoading(false);
            setSubmitLoader(false);
            setShowPopup(false);
            setTicketErr(err);
          });
      } else {
        setCommentErr(true);
      }
    } else {
      setTicketTypeError(true);
    }
  };

  const cancelClick = () => {
    setComment("");
    setClose(true);
    setLoading(false);
    setSubmitLoader(false);
    setSelected();
    setDisableCancle(true);
  };

  return (
    <>
      <View style={{ height: requiredHeight }}>
        <Card style={[styles.cardStyle, { height: requiredCardHeight }]}>
          <ScrollView style={styles.scrollStyle}>
            <View style={styles.content}>
              <Text style={styles.text}>
                Choose Ticket Type<Text style={styles.textStar}>*</Text>:
              </Text>
              <View
                style={{
                  paddingVertical: 10,
                }}>
                {data && data.length > 0 ? (
                  <DropDown
                    data={data}
                    onChange={selectClick}
                    defaultvalue={selectedItem}
                    close={close}
                    setClose={setClose}
                    options="keyValuePair"
                    customSelectButton={false}
                    title="Select Option"
                  />
                ) : (
                  <ActivityIndicator
                    size="large"
                    color={GlobalStyles.colors.ePrimary.base}
                  />
                )}
                {ticketTypeErr && (
                  <Text
                    style={{
                      color: GlobalStyles.colors.eDanger.dark,
                      fontSize: 10,
                    }}>
                    Ticket Type is require
                  </Text>
                )}
              </View>
            </View>
            <View>
              <Text style={styles.text}>
                Comments<Text style={styles.textStar}>*</Text>:
              </Text>
              <TextInput
                multiline
                numberOfLines={4}
                placeholderTextColor="#5C5E60"
                mode="outlined"
                maxLength={2000}
                value={comment}
                enablesReturnKeyAutomatically
                onChangeText={comm => setComment(comm)}
                placeholder="Enter reason or Comments"
                style={styles.commentsText}
                outlineColor={GlobalStyles.colors.eDark.hover}
                activeOutlineColor={GlobalStyles.colors.ePrimary.base}
                persistentScrollbar={true}
              />
              <View
                style={{ flex: 1, flexDirection: "row", marginBottom: "2%" }}>
                {commentErr && (
                  <Text
                    style={{
                      color: GlobalStyles.colors.eDanger.dark,
                      textAlign: "left",
                      flex: 1,
                      fontSize: 10,
                    }}>
                    Comments are required
                  </Text>
                )}

                <Text style={{ textAlign: "right", flex: 1 }}>
                  {comment ? comment.length : "0"}/2000
                </Text>
              </View>
            </View>

            <Text style={styles.text}>Upload Photo/Document:</Text>
            <View style={{ paddingVertical: 10 }}>
              <UploadImage />
            </View>
          </ScrollView>
          {ticketErr && (
            <Text
              style={{ color: "red", textAlign: "center", marginTop: "4%" }}>
              Something went wrong
            </Text>
          )}
        </Card>
        <View style={styles.centerButtons}>
          <Button
            onPress={cancelClick}
            buttonbgColor={[
              styles.cancelBg,
              disableCancle && styles.disabledCancleStyle,
            ]}
            textColor={[
              disableCancle ? styles.disableColor : styles.cancelText,
            ]}
            disabled={disableCancle}>
            Cancel
          </Button>
          <Button
            onPress={submitClick}
            buttonbgColor={[
              styles.buttonBgColor,
              disableSubmit && styles.disabledStyle,
            ]}
            textColor={styles.textColor}
            disabled={disableSubmit}>
            Submit
            {isLoading && (
              <ActivityIndicator
                align="center"
                size={13}
                color={GlobalStyles.colors.eWhite.base}
              />
            )}
          </Button>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  cardStyle: {
    marginHorizontal: 20,
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    paddingVertical: 20,
    width: Platform.OS === "ios" ? windowWidth - 40 : windowHeight / 2.2,
  },
  disabledStyle: {
    opacity: 0.5,
    backgroundColor: GlobalStyles.colors.eBackground.selected,
  },
  disabledCancleStyle: {
    opacity: 0.5,
    backgroundColor: GlobalStyles.colors.ePage.base,
    borderColor: GlobalStyles.colors.eLight.base,
    borderWidth: 1,
  },
  scrollStyle: {
    paddingHorizontal: 20,
  },
  text: {
    fontFamily: "NotoSans-SemiBold",
    fontSize: 12,
    color: GlobalStyles.colors.eRich.base,
  },
  textStar: {
    fontFamily: "NotoSans-SemiBold",
    fontSize: 12,
    color: GlobalStyles.colors.eDanger.dark,
  },
  content: {
    paddingVertical: 10,
    paddingBottom: 10,
    overflow: "scroll",
    zIndex: 100,
  },
  centerButtons: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 15,
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  disableColor: {
    color: GlobalStyles.colors.eLight.selected,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  bgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    marginLeft: 10,
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginLeft: 10,
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.ePage.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 10,
  },
  cancelText: {
    color: GlobalStyles.colors.eSecondary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  commentsText: {
    fontSize: 14,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    height: 110,
  },
});

import { View, StyleSheet, Platform, StatusBar } from "react-native";
import { Text } from "react-native-paper";
import { GlobalStyles } from "../../../app/global-styles";
import React, { useContext } from "react";
import { drawerContext } from "../../../app/authenticated/authenticated_layout";
import Icon from "../../../icon";
import { useDispatch } from "react-redux";
import { ticketID } from "../../../../redux/slices/pastTicketId";
import { turnOnOffID } from "../../../../redux/slices/pastTurnOnOff";
import {
  SafeAreaFrameContext,
  SafeAreaView,
} from "react-native-safe-area-context";
import { drawerContextWO } from "../../../app/authenticated/authenticated_layout_wo";
import { config } from "../../../../environment";
import { stackContext } from "../../../app/get_stack";

export default function PastTicketTitle() {
  const { workModelType } = React.useContext(stackContext);
  const { setOpenMenu } =
    workModelType === "WA"
      ? useContext(drawerContext)
      : useContext(drawerContextWO);

  const dispatch = useDispatch();

  const closeMenu = () => {
    dispatch(ticketID(null));
    dispatch(turnOnOffID(null));
    setOpenMenu(false);
  };
  return (
    <>
      {Platform.OS === "android" && (
        <StatusBar
          barStyle="light-content"
          hidden={false}
          backgroundColor={GlobalStyles.colors.eBlack.base}
          translucent={false}
        />
      )}
      <View style={styles.container}>
        <View style={styles.leftContainer}>
          <View>
            <Icon
              name="Close-icon-stroke"
              color={GlobalStyles.colors.eWhite.base}
              onPress={closeMenu}
              style={styles.filterIcon}
              size={28}
            />
          </View>
          <View>
            <Text style={styles.pastTicketLabel}>VIEW PAST TICKETS</Text>
          </View>
        </View>
        <View>
          <Icon
            name="Filter-fill-icon"
            color={GlobalStyles.colors.eWhite.base}
            size={17}
          />
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: "3%",
    paddingTop: Platform.OS === "ios" ? "13%" : 5,
  },
  leftContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    gap: 10,
  },
  pastTicketLabel: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    textAlign: "center",
  },
  filterIcon: {
    fontFamily: "NotoSans-Bold",
  },
});

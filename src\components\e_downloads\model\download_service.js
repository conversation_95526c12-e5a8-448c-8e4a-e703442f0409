import axios from "axios";
import { config } from "../../../environment";

export default function downloadData(
  accountId,
  code,
  billId,
  paymentId,
  userName,
  startDate,
  endDate,
  languageCode,
) {
  let downloadHeader = "";
  let tenantCode = config.constants.BASE_TENANT_CODE;
  if (billId) {
    downloadHeader = {
      tenantCode: tenantCode,
      accountId: accountId,
      billId: billId,
    };
    primaryKeyType = "billId";
  } else if (paymentId) {
    downloadHeader = {
      tenantCode: tenantCode,
      accountId: accountId,
      payId: paymentId,
    };
    primaryKeyType = "payId";
  } else {
    downloadHeader = {
      tenantCode: tenantCode,
      accountId: accountId,
    };
    primaryKeyType = "accountId";
  }

  const APIquery =
    `query{ 
    getBase64Document(input: {
        documentCode:"` +
    code +
    `"
        accountId:"` +
    accountId +
    `",
        tenantCode:"` +
    tenantCode +
    `",
        languageCode:"` +
    languageCode +
    `",
        username:"` +
    userName +
    `",
        billId:"` +
    billId +
    `",
        payId:"` +
    paymentId +
    `",
        startDate:"` +
    startDate +
    `",
        endDate:"` +
    endDate +
    `",
      })
    }`;
  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.USAGE_SERVICE_BASE_URL,
        {
          query: APIquery,
        },
        {
          headers: downloadHeader,
        },
      )
      .then(function (response) {
        resolve(response);
      })
      .catch(function (error) {
        reject(error);
      });
  });
}

import React, { useContext } from "react";
import { StyleSheet, View } from "react-native";
import { Card, Text } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import { PaymentContext } from "../e_payments";
import moment from "moment";
import Icon from "../../icon";
import Button from "../../common/_button";
import { useDispatch } from "react-redux";
import { servicePath } from "../../../redux/slices/servicePath";

export default function PaymentConfirm({ onBack = () => {} }) {
  const {
    selectedAccount = [],
    payableAmount,
    paymentCurrency,
    payId,
  } = useContext(PaymentContext);

  const paymentinfo = {
    accountIds: selectedAccount.map(item => item.accountId).join(","),
    receiptNumber: payId,
    transactionDate: moment().format("MMM DD,YYYY"),
    amount: Number(payableAmount),
    currency: paymentCurrency,
    key: 1,
  };

  return (
    <View>
      <Card style={styles.card}>
        <Text style={styles.titleCard}>PAYMENT CONFIRMATION</Text>
        <View style={styles.lineStyle} />
        <View style={styles.imgViewCls}>
          <Icon
            name="Sucess-icon-effects"
            color={GlobalStyles.colors.eSecondary.base}
            size={180}
          />
          <Icon
            name="Sucess-icon"
            style={styles.closeIcon}
            color={GlobalStyles.colors.eSecondary.base}
            size={80}
          />
          <View style={styles.contentTicket}>
            <Text style={styles.subtitle}>Payment Successful</Text>
            <Text style={styles.subtitle2}>Thank You</Text>
          </View>
        </View>
      </Card>
      <Card style={styles.card2}>
        <Text style={styles.titleCard}>PAYMENTS INFORMATION</Text>
        <View style={styles.lineStyle2} />
        <View style={styles.imgViewCls2}>
          <View style={styles.accountHeaderContent}>
            <View style={{ flex: 1 }}>
              <Text style={styles.singleHeader}>Account ID</Text>
            </View>
            <View style={{ flex: 1 }}>
              <Text style={styles.accountId}>{paymentinfo?.accountIds}</Text>
            </View>
          </View>
          <View style={styles.accountHeaderContent}>
            <View style={{ flex: 1 }}>
              <Text style={styles.singleHeader}>Receipt Number</Text>
            </View>
            <View style={{ flex: 1 }}>
              <Text style={styles.accountId}>{paymentinfo.receiptNumber}</Text>
            </View>
          </View>
          <View style={styles.accountHeaderContent}>
            <View style={{ flex: 1 }}>
              <Text style={styles.singleHeader}>Transaction Date</Text>
            </View>
            <View style={{ flex: 1 }}>
              <Text style={styles.accountId}>
                {paymentinfo.transactionDate}
              </Text>
            </View>
          </View>
          <View style={styles.paymentAmount}>
            <View style={{ flex: 1, marginVertical: 5 }}>
              <Text style={styles.singleHeaderAmount}>Amount</Text>
            </View>
            <View style={{ flex: 1 }}>
              <Text style={styles.amount}>
                {paymentinfo.currency && paymentinfo.currency.length > 1
                  ? (paymentinfo.amount === 0
                      ? paymentinfo.amount
                      : paymentinfo.amount.toFixed(2)) +
                    " " +
                    paymentinfo.currency
                  : paymentinfo.currency +
                    "" +
                    (paymentinfo.amount === 0
                      ? paymentinfo.amount
                      : paymentinfo.amount.toFixed(2))}
              </Text>
            </View>
          </View>
        </View>
      </Card>
      <View style={styles.btnContainer}>
        <Button
          buttonbgColor={styles.backBg}
          textColor={styles.backText}
          onPress={onBack}>
          Back to Payment
        </Button>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 20,
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
  },
  titleCard: {
    color: GlobalStyles.colors.ePrimary.base,
    fontFamily: "NotoSans-Bold",
    fontSize: 12,
    textAlign: "left",
    borderBottomColor: GlobalStyles.colors.eFaint.base,
  },
  card2: {
    marginHorizontal: 20,
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    marginTop: 10,
  },
  imgViewCls: {
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: -20,
    marginBottom: -20,
    backgroundColor: GlobalStyles.colors.eBackground2.base,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  imgViewCls2: {
    justifyContent: "center",
    alignItems: "center",
    marginHorizontal: -20,
    marginBottom: -20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  greenBg: {
    marginTop: 20,
    backgroundColor: GlobalStyles.colors.eSecondary.base,
  },
  contentTicket: {
    paddingTop: 10,
    paddingBottom: 10,
    paddingLeft: 20,
    paddingRight: 20,
  },
  subtitle: {
    textAlign: "center",
    color: GlobalStyles.colors.eSecondary.base,
    fontSize: 16,
    fontFamily: "NotoSans-Bold",
  },
  subtitle2: {
    textAlign: "center",
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 16,
    fontFamily: "NotoSans-SemiBold",
    marginVertical: 10,
  },
  lineStyle: {
    borderWidth: 0.5,
    borderColor: GlobalStyles.colors.eLight.base,
    marginTop: 10,
    marginHorizontal: -20,
    width: "115%",
  },
  lineStyle2: {
    borderWidth: 0.4,
    opacity: 0.3,
    borderColor: GlobalStyles.colors.ePrimary.base,
    marginTop: 10,
    marginHorizontal: -18,
    width: "111%",
  },
  accountHeaderContent: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
    borderRadius: 4,
    justifyContent: "flex-start",
    width: "100%",
    textAlign: "right",
    textColor: GlobalStyles.colors.eWhite.base,
  },
  paymentAmount: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
    borderRadius: 4,
    justifyContent: "flex-start",
    width: "100%",
    textAlign: "right",
    textColor: GlobalStyles.colors.eWhite.base,
    backgroundColor: GlobalStyles.colors.ePastelColor2.hover,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    marginTop: 10,
  },
  singleHeader: {
    color: GlobalStyles.colors.eBlack.base,
    textAlign: "left",
    fontFamily: "NotoSans-Medium",
    fontSize: 12,
    marginTop: 10,
    marginLeft: 20,
    justifyContent: "flex-start",
  },
  singleHeaderAmount: {
    color: GlobalStyles.colors.ePrimary.base,
    textAlign: "left",
    fontFamily: "NotoSans-Bold",
    fontSize: 12,
    marginLeft: 20,
    justifyContent: "flex-start",
    marginVertical: 5,
  },
  amount: {
    color: GlobalStyles.colors.eSecondary.base,
    textAlign: "left",
    fontFamily: "NotoSans-Bold",
    fontSize: 12,
    marginTop: 10,
    marginLeft: 20,
    justifyContent: "flex-start",
  },
  accountId: {
    color: GlobalStyles.colors.ePrimary.base,
    textAlign: "left",
    fontFamily: "NotoSans-SemiBold",
    fontSize: 12,
    marginTop: 10,
    marginLeft: 20,
    justifyContent: "flex-start",
  },
  closeIcon: {
    marginTop: 0,
    position: "absolute",
  },
  btnContainer: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 18,
  },
  backBg: {
    borderColor: GlobalStyles.colors.ePrimary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 10,
  },
  backText: {
    color: GlobalStyles.colors.ePrimary.base,
  },
});

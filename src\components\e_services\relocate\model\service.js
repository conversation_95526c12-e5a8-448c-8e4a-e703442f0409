import { config } from "../../../../environment";
import axios from "axios";

export default function createRelocateServiceTicket(
  email,
  mobileNo,
  address1,
  address2,
  address3,
  country,
  state,
  city,
  zipCode,
  accountId,
  saId,
  personId
) {

  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.COMMUNICATION_SERVICE_BASE_URL,
        {
          query:
            `query{
            createRelocateServiceTicket(
              input: {
              ticketTypeCd: "CM-RELOCSVC",
              email: "`+ email + `",
              mobileNumber: "`+ mobileNo + `",
              country: "`+ country + `",
              state: "`+ state + `",
              city: "`+ city + `",
              zip: "`+ zipCode + `",
              address1: "`+ address1 + `",
              address2: "`+ address2 + `",
              address3: "`+ address3 + `",
            })
                  {
                  ticketId
                  }
                }
            `
        },
        {
          headers: {
            accountId: accountId,
            saId: saId,
            personId: personId,
            tenantCode: config.constants.BASE_TENANT_CODE,
          }
        }
      )
      .then((response) => {
        resolve(response?.data?.data?.createRelocateServiceTicket?.ticketId);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

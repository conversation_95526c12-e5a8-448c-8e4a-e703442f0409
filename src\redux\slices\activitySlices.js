import { createSlice } from "@reduxjs/toolkit";

const activitySlice = createSlice({
  name: "activity",
  initialState: {
    selectedActivity: {},
    allActivities: [],
    isCurrentChecklistCompleted: false,
    snackBarData: {
      canShowSnackbar: false,
      message: "",
    },
  },
  reducers: {
    setCurrentActivity: (state, action) => {
      state.selectedActivity = action.payload;
    },
    setActivities: (state, action) => {
      state.allActivities = action.payload;
    },
    setIsCurrentChecklistCompleted: (state, action) => {
      state.isCurrentChecklistCompleted = action.payload;
    },
    setSnackbarData: (state, action) => {
      state.snackBarData = action.payload;
    },
  },
});

export const {
  setCurrentActivity,
  setActivities,
  setIsCurrentChecklistCompleted,
  setSnackbarData,
} = activitySlice.actions;
export default activitySlice.reducer;

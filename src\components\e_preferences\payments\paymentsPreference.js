import { useEffect, useState } from "react";
import { ActivityIndicator } from "react-native";
import { Text } from "react-native";
import { ScrollView, View } from "react-native";
import { useSelector } from "react-redux";
import { GlobalStyles } from "../../app/global-styles";
import { EAutoPayServices } from "./model/auto_pay_service";
import AutoPay from "./view/_auto_pay";
import AutoPayment from "./view/_auto_payment_checkbox";
import AutoPayData from "./view/_auto_pay_data";

export default function PaymentsPreference() {
  const [autoData, setAutoData] = useState([]);
  const [checked, setChecked] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const accountId = useSelector(
    state => state?.meterDetails?.meterDetails?.accountId,
  );

  let AutoPayTenentParameter = useSelector(
    state => state?.parameter?.parameter?.ParameterLookup?.AUTO_PAY_ENABLED,
  );

  useEffect(() => {
    if (accountId) {
      setIsLoading(true);
      EAutoPayServices.getPaymentPreference(accountId).then(res => {
        if (
          res?.data?.getAutoPayStatus?.status &&
          res?.data?.getAutoPayStatus?.status === "true"
        ) {
          setChecked(true);
          setIsLoading(false);
        } else {
          setChecked(false);
          setIsLoading(false);
        }
      });
    }
  }, [accountId]);

  return (
    <View style={{ marginBottom: "8%" }}>
      {isLoading ? (
        <ActivityIndicator color={GlobalStyles.colors.ePrimary.base} />
      ) : (
        autoData.length > 0 &&
        AutoPayTenentParameter &&
        AutoPayTenentParameter === "true" && (
          <AutoPayment
            autoData={autoData}
            checked={checked}
            setChecked={setChecked}
          />
        )
      )}
      <View style={{ margin: "2%" }} />
      <AutoPay setAutoData={setAutoData} autoData={autoData} />
      <View style={{ margin: "2%" }} />
      <AutoPayData
        autoData={autoData}
        setAutoData={setAutoData}
        setChecked={setChecked}
        checked={checked}
      />
      <View style={{ margin: "2%" }} />
    </View>
  );
}

import React, { useState, useContext } from "react";
import { Text, Card, TextInput } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import { useSelector, useDispatch } from "react-redux";
import {
  View,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Dimensions,
} from "react-native";
import Button from "../../common/_button";
import UploadImage from "../../common/_upload_image";
import DropDown from "../../common/_dropdown";
import { ticketContext } from "../e_services";
import { useEffect } from "react";
import { ticketID } from "../../../redux/slices/pastTicketId";
import { turnOnOffID } from "../../../redux/slices/pastTurnOnOff";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import { config } from "../../../environment";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { stackContext } from "../../app/get_stack";

export default function ReportOutage({ requiredHeight, requiredCardHeight }) {
  const { workModelType } = React.useContext(stackContext);
  const outageType = [
    "All Power Out",
    "Partial Out",
    "Area Out",
    "Power On",
    "Blinking",
    "Own Lights",
    "Bright Lights",
    "N/A",
  ];

  const causeOfOutage = [
    "All Power Out",
    "Partial Out",
    "Area Out",
    "Power On",
    "Blinking",
    "Own Lights",
    "Bright Lights",
    "N/A",
  ];
  const [close, setClose] = useState(false);
  const { height } = Dimensions.get("window");

  const [comment, setComment] = useState();
  const [selectedOutageType, setSelectedOutageType] = useState();
  const [selectedCauseOfOutage, setSelectedCauseOfOutage] = useState();
  const [commentErr, setCommentErr] = useState();
  const {
    setShowPopup,
    setTitlepopup,
    setTicketNumber,
    submitLoader,
    setSubmitLoader,
  } =
    workModelType === "WA"
      ? useContext(drawerContext)
      : useContext(drawerContextWO);

  const [disableSubmit, setDisableSubmit] = useState(true);
  const [outageTypeErr, setOutageTypeErr] = useState(false);
  const [causeOfOutageErr, setCauseOfOutage] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [disableCancle, setDisableCancle] = useState(true);
  const param = useSelector(
    state =>
      state?.parameter?.parameter?.ParameterLookup?.TENANT_PRIMARY_LANGUAGE,
  );

  const accountId = useSelector(
    state => state?.meterDetails?.meterDetails?.accountId,
  );
  const dispatch = useDispatch();
  const pastTicketID = useSelector(store => store?.ticketID.ticketID);
  const pastturnOnOffID = useSelector(store => store?.turnOnOffID.turnOnOffID);
  useEffect(() => {
    if (pastTicketID || pastturnOnOffID) {
      let ticket = null;
      dispatch(ticketID(ticket));
      dispatch(turnOnOffID(ticket));
    }
  }, [pastTicketID, pastturnOnOffID]);

  useEffect(() => {
    if (
      selectedOutageType ||
      selectedCauseOfOutage ||
      comment ||
      comment?.trim().length > 0
    ) {
      setDisableCancle(false);
    } else {
      setDisableCancle(true);
    }
    if (
      selectedOutageType &&
      selectedCauseOfOutage &&
      comment &&
      comment.trim().length > 0
    ) {
      setDisableSubmit(false);
    } else {
      setDisableSubmit(true);
    }
  }, [selectedOutageType, selectedCauseOfOutage, comment]);

  const selectOutageTypeClick = val => {
    setSelectedOutageType(val);
    setOutageTypeErr(false);
  };

  const causeOfOutageClick = val => {
    setSelectedCauseOfOutage(val);
    setCauseOfOutage(false);
  };

  const submitClick = () => {
    // setLoading(true);
    // setSubmitLoader(true);
    if (selectedOutageType) {
      setOutageTypeErr(false);
      if (selectedCauseOfOutage) {
        setCauseOfOutage(false);
        if (comment) {
          setLoading(true);
          setSubmitLoader(true);
          setCommentErr(false);
          setShowPopup(true);
          setTicketNumber("**********");
          cancelClick();
          setTitlepopup("Report Outage - Service Request");

          // complaintsService
          //   .getTicketReferenceNumber(
          //     accountId,
          //     selectedItem,
          //     comment,
          //     fileData,
          //     category
          //   )
          //   .then((res) => setTicketNumber(res?.data?.createTicket?.ticketId));
        } else {
          setCommentErr(true);
          setLoading(false);
          setSubmitLoader(false);
        }
      } else {
        setCauseOfOutage(true);
      }
    } else {
      setOutageTypeErr(true);
      //   setTicketTypeError(true);
    }
  };

  const cancelClick = () => {
    setClose(true);
    setComment("");
    setSelectedOutageType();
    setCauseOfOutage(false);
    setOutageTypeErr(false);
    setCommentErr(false);
    setSelectedCauseOfOutage("");
    setLoading(false);
    setSubmitLoader(false);
    setDisableCancle(true);
  };

  return (
    <>
      <View style={{ height: requiredHeight }}>
        <Card style={[styles.cardStyle, { height: requiredCardHeight }]}>
          <ScrollView style={styles.scrollStyle}>
            <View style={styles.content}>
              <Text style={styles.text}>
                Choose Outage Type<Text style={styles.textStar}>*</Text>:
              </Text>
              <View style={{ paddingVertical: 10 }}>
                {outageType && outageType.length > 0 ? (
                  <DropDown
                    data={outageType}
                    onChange={setSelectedOutageType}
                    title="Select Option"
                    selectedAccount=""
                    close={close}
                    setClose={setClose}
                    afterSelection=""
                  />
                ) : (
                  <ActivityIndicator
                    size="large"
                    color={GlobalStyles.colors.ePrimary.base}
                  />
                )}
                {outageTypeErr && (
                  <Text style={styles.errorCls}>Select Outage Type</Text>
                )}
              </View>
            </View>
            <View style={styles.content}>
              <Text style={styles.text}>
                Cause Of Outage<Text style={styles.textStar}>*</Text>:
              </Text>
              <View style={{ paddingVertical: 10 }}>
                {causeOfOutage && causeOfOutage.length > 0 ? (
                  <DropDown
                    data={causeOfOutage}
                    onChange={setSelectedCauseOfOutage}
                    // value={selectedCauseOfOutage}
                    title="Select Option"
                    selectedAccount=""
                    close={close}
                    setClose={setClose}
                    afterSelection=""
                  />
                ) : (
                  <ActivityIndicator
                    size="large"
                    color={GlobalStyles.colors.ePrimary.base}
                  />
                )}
                <Text
                  style={{
                    color: GlobalStyles.colors.eTertiary.base,
                    fontSize: 10,
                  }}>
                  Cause of Outage not listed in this drop down? Briefly explain
                  it in the comment box.
                </Text>
                {causeOfOutageErr && (
                  <Text style={styles.errorCls}>Select Outage Type</Text>
                )}
              </View>
            </View>
            <View>
              <Text style={styles.text}>
                Comments<Text style={styles.textStar}>*</Text>:
              </Text>
              <TextInput
                multiline
                numberOfLines={4}
                placeholderTextColor="#5C5E60"
                mode="outlined"
                value={comment}
                onChangeText={comm => {
                  setComment(comm);
                  setCommentErr(false);
                }}
                placeholder="Enter reason or Comments"
                maxLength={2000}
                outlineColor={GlobalStyles.colors.eDark.hover}
                activeOutlineColor={GlobalStyles.colors.ePrimary.base}
                style={styles.inputcls}
              />
              <View
                style={{ flex: 1, flexDirection: "row", marginBottom: "2%" }}>
                {commentErr && (
                  <Text style={styles.errorCls}>Comments are required</Text>
                )}
                <Text style={{ textAlign: "right", flex: 1 }}>
                  {comment ? comment.length : "0"}/2000
                </Text>
              </View>
            </View>

            <Text style={styles.text}>Upload Photo/Document:</Text>
            <View style={{ paddingVertical: 10 }}>
              <UploadImage />
            </View>
          </ScrollView>
        </Card>
        <View style={styles.btnContainer}>
          <Button
            onPress={cancelClick}
            buttonbgColor={[
              styles.cancelBg,
              disableCancle && styles.disabledCancleStyle,
            ]}
            textColor={[
              disableCancle ? styles.disableColor : styles.cancelText,
            ]}
            disabled={disableCancle}>
            Cancel
          </Button>
          <Button
            onPress={submitClick}
            buttonbgColor={[
              styles.buttonBgStyle,
              disableSubmit && styles.disabledStyle,
            ]}
            textColor={styles.textColor}
            disabled={disableSubmit}>
            Submit
            {isLoading && (
              <ActivityIndicator
                align="center"
                size={13}
                color={GlobalStyles.colors.eWhite.base}
              />
            )}
          </Button>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  cardStyle: {
    marginHorizontal: 20,
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    paddingVertical: 20,
  },
  disabledStyle: {
    opacity: 0.5,
    backgroundColor: GlobalStyles.colors.eBackground.selected,
  },
  disableColor: {
    color: GlobalStyles.colors.eLight.selected,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  disabledCancleStyle: {
    opacity: 0.5,
    backgroundColor: GlobalStyles.colors.ePage.base,
    borderColor: GlobalStyles.colors.eLight.base,
    borderWidth: 1,
  },
  scrollStyle: {
    paddingHorizontal: 20,
  },
  errorCls: {
    color: GlobalStyles.colors.eDanger.dark,
    fontSize: 10,
    marginTop: 3,
  },
  inputcls: {
    backgroundColor: GlobalStyles.colors.eFaint.base,
    height: 120,
  },
  text: {
    fontFamily: "NotoSans-SemiBold",
    fontSize: 12,
    color: GlobalStyles.colors.eRich.base,
  },
  textStar: {
    fontFamily: "NotoSans-SemiBold",
    fontSize: 12,
    color: GlobalStyles.colors.eRich.base,
  },
  content: {
    paddingVertical: 10,
    paddingBottom: 10,
    overflow: "scroll",
    zIndex: 100,
  },
  btnContainer: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 15,
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  bgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    marginLeft: 10,
  },
  buttonBgStyle: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginLeft: 10,
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 10,
  },
  cancelText: {
    color: GlobalStyles.colors.eSecondary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  disableText: {
    color: GlobalStyles.colors.eLight.selected,
  },
  borderColor: {
    borderColor: GlobalStyles.colors.ePrimary.base,
  },
});

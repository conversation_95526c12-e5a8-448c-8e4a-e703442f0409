import React, { useState } from "react";
import { Card } from "react-native-paper";
import { GlobalStyles } from "../../../app/global-styles";
import {
  StyleSheet,
  View,
  Dimensions,
  ActivityIndicator,
  Text,
  Platform,
} from "react-native";
import FlatButton from "../../../common/_flat_button";
import NumbersPopup from "./_numbers-popup";
import Icon from "../../../icon";

export default function SupportCard({ CallUSDescription, ImageURL }) {
  const [openPopUP, setOpenPopUp] = useState(false);

  const getNumbers = () => {
    setOpenPopUp(prev => !prev);
  };

  const closePopup = () => {
    setOpenPopUp(false);
  };

  return (
    <Card style={[styles.headerCard, { height:  Platform.OS === "ios" ? 180: 185 }]}>
      {CallUSDescription ? (
        <>
        <View style={styles.labelContainer}>
          <Text style={styles.titleStyleCallUS}>TALK TO SUPPORT</Text>
        </View>
          <View style={styles.scrollStyleCAllUS}>
            <View style={styles.content}>
              <View style={styles.leftViewSupport}>
                <View style={styles.image}>
                  <Icon
                    name="Callus-icon"
                    color={GlobalStyles.colors.ePrimary.base}
                    size={100}
                  />
                </View>
              </View>
              <View style={styles.rightViewSupport}>
                <View>
                  <Text style={styles.cardTitle1}>
                    Out experts will guide you
                  </Text>
                  <Text style={styles.cardTitle}>{CallUSDescription}</Text>
                </View>
                <View
                  style={{
                    backgroundColor: GlobalStyles.colors.eBackground3.dark,
                    height: 55,
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    width: "100%",
                  }}>
                  <View
                    style={{
                      backgroundColor: GlobalStyles.colors.eWhite.base,
                      width: "90%",
                      borderRadius: 10,
                    }}>
                    <View style={styles.contentShowNumber}>
                      <View style={styles.leftViewShowNumber}>
                        <Icon
                          name="Contact-phone-icon"
                          color={GlobalStyles.colors.eSecondary.base}
                          size={15}
                          onPress={getNumbers}
                        />
                      </View>
                      <View style={styles.rightViewShowNumber}>
                        <FlatButton
                          textStyles={styles.showNumbers}
                          onPress={() => getNumbers()}>
                          Show Numbers
                        </FlatButton>
                      </View>
                      <View style={styles.rightViewShowIcon}>
                        <Icon
                          name="down-arrow-icon"
                          color={GlobalStyles.colors.ePastelColor2.selected}
                          size={15}
                          onPress={getNumbers}
                        />
                      </View>
                    </View>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </>
      ) : (
        <ActivityIndicator
          size="large"
          color={GlobalStyles.colors.ePrimary.base}
        />
      )}
      {openPopUP ? <NumbersPopup hideModel={closePopup} /> : null}
    </Card>
  );
}
const styles = StyleSheet.create({
  aroundMargin: {
    marginLeft: "5%",
    marginRight: "5%",
    height: "50%",
  },
  titleStyle: {
    color: GlobalStyles.colors.eRich.base,
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    textAlign: "left",
    backgroundColor: GlobalStyles.colors.eBackground.base,
  },
  headerCard: {
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    width: "100%",
    marginVertical: 10,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  titleStyleCallUS: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    textAlign: "left",
  },

  scrollStyleCAllUS: {
    backgroundColor: GlobalStyles.colors.eBackground3.base,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  leftViewSupport: {
    float: "left",
    marginTop: 10,
    alignSelf: "stretch",
    width: "40%",
  },
  image: {
    marginTop: 10,
  },
  rightViewSupport: {
    float: "right",
    width: "60%",
  },
  cardTitle: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    textAlign: "center",
    paddingVertical: -5,
    marginBottom: "5%",
    marginTop: "5%",
  },
  cardTitle1: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 10,
    textAlign: "center",
    paddingVertical: -5,
    marginTop: 10,
    fontFamily: "NotoSans-Italic",
  },
  contentShowNumber: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
  },
  leftViewShowNumber: {
    float: "left",
    marginTop: 10,
    marginLeft: 10,
    marginRight: 3,
  },
  rightViewShowNumber: {
    float: "right",
  },
  rightViewShowIcon: {
    marginVertical: 10,
    marginLeft: 7,
    marginRight: 0,
    paddingTop: 0,
  },
  contentcallus: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
    paddingHorizontal: "6%",
    paddingVertical: "1%",
  },
  showNumbers: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    // paddingVertical: "2%",
    // paddingHorizontal: "2%",
    textAlign: "center",
    marginTop: 10,
    marginLeft: 3,
  },
  content: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
    paddingHorizontal: "6%",
    paddingVertical: "1%",
    paddingBottom: "4%",
  },
  labelContainer: { 
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    paddingVertical: 10,
    paddingLeft: 20,
    borderTopRightRadius:20,
    borderTopLeftRadius: 20,
  }
});

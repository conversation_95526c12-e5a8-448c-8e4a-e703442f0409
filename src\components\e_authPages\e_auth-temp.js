import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  Image,
  ImageBackground,
  Dimensions,
  Pressable,
  Platform,
} from "react-native";
import { GlobalStyles } from "../app/global-styles";
import LoginPage from "../e_login/e_login_page";
import Register from "../e_register/e_register";
import CopyRight from "./view/_copy_right";
import AuthButtons from "./view/_auth_buttons";
import Icon from "../icon";
import { Card, Text } from "react-native-paper";
import RegisterModal from "../e_register/view/e_register-modal";
import RegisterSuccess from "../e_register/view/e_register_confirm";
import OTPVerification from "../e_register/view/e_register_OTP";
import VerificationScreen from "../e_register/view/e_verification-screen";
import { StatusBar } from "react-native";

const width = Dimensions.get("window").width;
const { height } = Dimensions.get("window");

export const registerContext = React.createContext();
export default function AuthPages() {
  const [verificationScreen, setVerificationScreen] = React.useState(false);
  const [verificationDetails, setVerifyDetails] = React.useState([]);
  const [showRegisterModal, setRegisterModal] = React.useState(false);
  const [showRegisterSuccess, setRegisterSuccess] = React.useState(false);
  const [email, setEmailUsername] = React.useState("");
  const [mobileNumber, setmobileNumber] = React.useState();
  const [closePopup, setClosePopup] = React.useState();
  const [isLoginPage, setIsLoginPage] = useState("Login");
  // const [dimensions, setDimensions] = useState({ window, screen });
  const { width } = Dimensions.get("window");
  const { height } = Dimensions.get("window");

  // useEffect(() => {
  //   const subscription = Dimensions.addEventListener(
  //     "change",
  //     ({ window, screen }) => {
  //       setDimensions({ window, screen });
  //     }
  //   );
  //   return () => subscription?.remove();
  // });
  const backHandler = () => {
    if (showRegisterModal) {
      setRegisterModal(false);
    }
    if (verificationScreen) {
      setVerificationScreen(false);
    }
  };
  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="light-content"
        hidden={false}
        backgroundColor={GlobalStyles.colors.eBlack.base}
        translucent={true}
      />
      <registerContext.Provider
        value={{
          showRegisterModal,
          setRegisterModal,
          showRegisterSuccess,
          setRegisterSuccess,
          setIsLoginPage,
          email,
          setEmailUsername,
          setVerificationScreen,
          setVerifyDetails,
          isLoginPage,
          setmobileNumber,
          mobileNumber,
        }}>
        <View
          style={{
            backgroundColor: GlobalStyles.colors.eWhite.base,
            height: height,

            // top: "2%",
            // position: "relative",
          }}>
          <Icon
            name="Login-Top-curve-graphic"
            size={width / 2.9}
            color={GlobalStyles.colors.eSecondary.base}
            style={{
              position: "absolute",
              top: -10,
              right: 0,
              left: 0,
              backgroundColor: GlobalStyles.colors.eWhite.base,
              flex: 1,
              justifyContent: "center",
            }}
          />

          <Icon
            name="Login-Top-curve-graphic"
            size={width / 2.9}
            color={GlobalStyles.colors.ePrimary.base}
            style={{
              position: "absolute",
              top: -30,
              right: 0,
              left: 0,
              flex: 1,
              justifyContent: "center",
            }}
          />
          {showRegisterModal || verificationScreen ? (
            <Icon
              name="back"
              style={styles.blueBg}
              color={GlobalStyles.colors.eWhite.base}
              size={18}
              onPress={backHandler}
            />
          ) : null}
          <View style={styles.authPages}>
            {isLoginPage === "Login" && (
              <>
                <View style={styles.clientLogoView}>
                  <Image
                    style={styles.clientLogo}
                    source={require("../../../assets/e_client_logo.png")}
                  />
                </View>
                <View style={styles.headingView}>
                  <AuthButtons />
                </View>
                <View style={styles.loginPage}>
                  <LoginPage />
                </View>
                <View style={styles.appLogoView}>
                  <Image
                    style={styles.appLogo}
                    source={require("../../../assets/impressaLogo.png")}
                  />
                </View>
                <View>
                  <Icon
                    name="Login-Bottom-curve-graphic"
                    size={width / 3.6}
                    color={GlobalStyles.colors.eSecondary.base}
                    style={{
                      //bottom: 130,
                      bottom: 20,
                      //Platform.OS === "ios" ? 106 : 110,
                      position: "absolute",
                    }}
                  />
                  <Icon
                    name="Login-Bottom-curve-graphic"
                    size={width / 3.6}
                    color={GlobalStyles.colors.ePrimary.base}
                    style={{
                      //bottom: 110,
                      bottom: 0,
                      //Platform.OS === "ios" ? 86 : 90,
                      position: "absolute",
                    }}
                  />
                  <View style={styles.copyRight}>
                    <CopyRight />
                  </View>
                </View>
              </>
            )}
            {isLoginPage === "Register" &&
              !showRegisterModal &&
              !verificationScreen && (
                <>
                  <View style={styles.clientLogoView}>
                    <Image
                      style={styles.clientLogo}
                      source={require("../../../assets/e_client_logo.png")}
                    />
                  </View>
                  <View style={styles.headingView}>
                    <AuthButtons
                      isLoginPage={isLoginPage}
                      setIsLoginPage={setIsLoginPage}
                    />
                  </View>
                  <View style={styles.registerPage}>
                    <Register />
                  </View>
                  <View style={styles.appLogoView}>
                    <Image
                      style={styles.appLogo}
                      source={require("../../../assets/impressaLogo.png")}
                    />
                  </View>
                  <View>
                    <Icon
                      name="Login-Bottom-curve-graphic"
                      size={width / 3.6}
                      color={GlobalStyles.colors.eSecondary.base}
                      style={{
                        bottom: 20,
                        position: "absolute",
                      }}
                    />
                    <Icon
                      name="Login-Bottom-curve-graphic"
                      size={width / 3.6}
                      color={GlobalStyles.colors.ePrimary.base}
                      style={{
                        bottom: 0,
                        //  Platform.OS === "ios" ? 86 : 90,
                        position: "absolute",
                      }}
                    />
                    <View style={styles.copyRightRegis}>
                      <CopyRight />
                    </View>
                  </View>
                </>
              )}
            {showRegisterModal ? (
              <View style={styles.OTPPage}>
                <OTPVerification />
              </View>
            ) : null}

            {verificationScreen ? (
              <>
                <View style={styles.verificationPage}>
                  <VerificationScreen
                    verificationDetails={verificationDetails}
                  />
                </View>
                <Icon
                  name="Login-Bottom-curve-graphic"
                  size={width / 3.6}
                  color={GlobalStyles.colors.eSecondary.base}
                  style={{
                    bottom: -40,
                    // Platform.OS === "ios" ? 106 : 110,
                    position: "absolute",
                  }}
                />
                <Icon
                  name="Login-Bottom-curve-graphic"
                  size={width / 3.6}
                  color={GlobalStyles.colors.ePrimary.base}
                  style={{
                    bottom: -60,
                    //  Platform.OS === "ios" ? 86 : 90,
                    position: "absolute",
                  }}
                />
                {/* <View style={styles.copyRight}>
                  <CopyRight />
                </View> */}
              </>
            ) : null}
          </View>

          {showRegisterSuccess ? (
            <>
              <Pressable
                style={styles.successDrawerOpacity}
                onPress={() => setShowpopup(false)}></Pressable>
              <Card style={[styles.accountStyle, { minHeight: height / 4 }]}>
                <RegisterSuccess
                  showProfilePopup={showRegisterModal}
                  hideModel={closePopup}
                />
              </Card>
            </>
          ) : null}
        </View>
      </registerContext.Provider>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    position: "relative",
    marginTop: Platform.OS === "ios" ? -20 : 0,
  },
  bgImage: {
    flex: 1,
    justifyContent: "center",
    width: width,
    position: "absolute",
    top: 0,
    bottom: 0,
  },
  blueBg: {
    marginTop: 45,
    // backgroundColor: GlobalStyles.colors.eWhite.base,
    position: "absolute",
    top: 0,
    right: 0,
    left: 20,
  },
  authPages: {
    position: "relative",
    height: height,
    marginTop: height / 4,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    flex: 1,
    justifyContent: "center",
    alignContent: "center",
  },
  loginPage: {
    marginTop: height / 19,
    paddingBottom: height / 5,
    minHeight: height / 1.45,
    alignContent: "center",
    flex: 1,
    justifyContent: "center",
  },
  registerPage: {
    marginTop: height / 19,
    paddingBottom: height / 5,
    minHeight: height / 1.45,
    alignContent: "center",
    flex: 1,
    justifyContent: "center",
  },
  OTPPage: {
    marginTop: height / 19,
    paddingBottom: height / 5,
    minHeight: height / 1.9,
    alignContent: "center",
    flex: 1,
    justifyContent: "center",
  },
  verificationPage: {
    marginTop: height / 19,
    paddingBottom: height / 5,
    minHeight: height / 1.45,
    alignContent: "center",
    flex: 1,
    justifyContent: "center",
  },
  copyRight: {
    position: "absolute",
    justifyContent: "center",
    //bottom: Platform.OS === "ios" ? "14%" : "15%",
    bottom: Platform.OS === "ios" ? "14%" : 35,
    width: width,
  },
  copyRightRegis: {
    position: "absolute",
    justifyContent: "center",
    //bottom: Platform.OS === "ios" ? "14%" : "15%",
    bottom: Platform.OS === "ios" ? "14%" : 35,
    width: width,
  },
  clientLogoView: {
    alignItems: "center",
    height: height / 7,
  },
  clientLogo: {
    height: 43,
    width: 248,
  },
  appLogoView: {
    height: height / 3.3,
    alignItems: "flex-end",
    marginTop: "10%",
    right: "10%",
    paddingTop: height / 8,
    position: "absolute",
    bottom: 0,
    flex: 1,
    justifyContent: "center",
  },
  appBottomLogo: {
    //top:"10%",
    position: "absolute",
    bottom: 0,
  },
  appLogo: {
    height: height / 30,
    width: 165,
  },
  headingView: {
    alignItems: "center",
    flex: 1,
    marginTop: "-2%",
    marginBottom: "1%",
    justifyContent: "center",
  },
  iconTop: {
    content: "e93c",
    color: GlobalStyles.colors.eSecondary.base,
  },
  accountDrawerOpacity: {
    height: height - 1000,
    // width: "100%",
    backgroundColor: "black",
    opacity: 0.5,
  },
  accountStyle: {
    position: "absolute",
    width: width,
    bottom: 0,
    borderTopEndRadius: 25,
    borderTopLeftRadius: 25,
    backgroundColor: GlobalStyles.colors.eFaint.base,
  },
  successDrawerOpacity: {
    height: "100%",
    width: width,
    backgroundColor: "black",
    opacity: 0.5,
    position: "absolute",
  },
  successStyle: {
    position: "absolute",
    height: height - 300,
    width: width,
    bottom: 0,
    borderTopEndRadius: 25,
    borderTopLeftRadius: 25,
    backgroundColor: GlobalStyles.colors.eFaint.base,
  },
});

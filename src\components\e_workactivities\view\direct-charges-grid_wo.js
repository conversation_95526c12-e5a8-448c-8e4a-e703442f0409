import * as React from "react";
import { DataTable, Divider, Text, TextInput } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import { ScrollView } from "react-native-gesture-handler";
import { View } from "react-native";
import { useTranslation } from 'react-i18next';

const DirectChargesGridWO = () => {
  const { t } = useTranslation();
  const [page, setPage] = React.useState(0);
  const [numberOfItemsPerPageList] = React.useState([2, 3, 4]);
  const [itemsPerPage, onItemsPerPageChange] = React.useState(
    numberOfItemsPerPageList[0],
  );

  const [amount, setAmount] = React.useState("");
  const [remarks, setRemarks] = React.useState("");

  const [items] = React.useState([
    {
      key: 1,
      category: "Misc",
      UOM: "USD",
      remarks: "For consumables",
    },
  ]);

  const from = page * itemsPerPage;
  const to = Math.min((page + 1) * itemsPerPage, items.length);

  React.useEffect(() => {
    setPage(0);
  }, [itemsPerPage]);

  return (
    <>
      <View>
        <Text
          style={{
            paddingLeft: 16,
          }}>
          {t('DIRECT_CHARGES')}
        </Text>
      </View>
      <DataTable
        style={{
          paddingBottom: 10,
          marginBottom: 10,
        }}>
        <Divider />
        <ScrollView
          horizontal
          persistentScrollbar
          contentContainerStyle={{ flexDirection: "column" }}>
          <DataTable.Header>
            <DataTable.Title style={{ width: 80 }}>{t('CATEGORY')}</DataTable.Title>
            <DataTable.Title style={{ width: 80 }}>{t('AMOUNT')}</DataTable.Title>
            <DataTable.Title style={{ width: 60 }}>{t('UOM')}</DataTable.Title>
            <DataTable.Title style={{ width: 300 }}>{t('REMARKS')}</DataTable.Title>
          </DataTable.Header>

          {items.slice(from, to).map(item => (
            <DataTable.Row
              key={item.key}
              style={{ flex: 1, width: "100%", height: 50 }}>
              <DataTable.Cell style={{ width: 80 }}>
                {item.category}
              </DataTable.Cell>
              <DataTable.Cell
                style={{
                  width: 80,
                }}>
                <TextInput
                  placeholderTextColor="gray"
                  mode="outlined"
                  value={amount}
                  enablesReturnKeyAutomatically
                  onChangeText={amount => setAmount(amount)}
                  placeholder="0"
                  outlineColor={GlobalStyles.colors.eDark.hover}
                  activeOutlineColor={GlobalStyles.colors.ePrimary.base}
                  persistentScrollbar={true}
                  style={{
                    height: 25,
                  }}
                />
              </DataTable.Cell>
              <DataTable.Cell style={{ width: 60 }}>{item.UOM}</DataTable.Cell>
              <DataTable.Cell style={{ width: 300 }}>
                <View>
                  <TextInput
                    placeholderTextColor="gray"
                    mode="outlined"
                    value={remarks}
                    enablesReturnKeyAutomatically
                    onChangeText={remarks => setRemarks(remarks)}
                    placeholder=" "
                    outlineColor={GlobalStyles.colors.eDark.hover}
                    activeOutlineColor={GlobalStyles.colors.ePrimary.base}
                    persistentScrollbar={true}
                    style={{
                      height: 25,
                      width: 300,
                    }}
                  />
                </View>
              </DataTable.Cell>
            </DataTable.Row>
          ))}
        </ScrollView>
      </DataTable>
    </>
  );
};

export default DirectChargesGridWO;

import * as React from "react";
import AuthenticatedTabs from "./auntenticated_tabs";
import AuthenticatedHeader from "./authenticated_header";
import AuthenticatedFooter from "./authenticated_footer";
import AsyncStorage from "@react-native-async-storage/async-storage";
import getParametersWidgets from "./model/parameter-service";
import ModalPopup from "../../e_preferences/_modal_popup";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { StyleSheet, Dimensions, View, Pressable } from "react-native";
import { useDispatch } from "react-redux";
import { Card, Text } from "react-native-paper";
import { GlobalStyles } from "../global-styles";
import { setSnackbarData } from "../../../redux/slices/activitySlices";
import { ActivityIndicator, Snackbar } from "react-native-paper";
import {
  CHECKLIST,
  ROUTES,
  SNACKBAR_TIMEOUT_DURATION,
  URLS,
} from "../../common/constants";
import { useIsFocused } from "@react-navigation/native";
import axios from "axios";
import { config } from "../../../environment";
import OTPModalScreen from "../../e_workactivities/view/otp-confirm";
import ConfirmModalScreen from "../../e_workactivities/view/confirm-modal";
import { NotificationService } from "./model/getNotification_service";
import moment from "moment";
import { NotificationServiceWO } from "./model/getNotification_service_wo";
import AuthenticatedTabWO from "./auntenticated_tabs_wo";
import AuthenticatedFooterWO from "./authenticated_footer_wo";
import AuthenticatedHeaderWO from "./authenticated_header_wo";
import { stackContext } from "../get_stack";

export const drawerContextWO = React.createContext();
export default function AuthenticatedLayoutWO() {
  const { workModelType } = React.useContext(stackContext);
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useDispatch();
  const [openDrawer, setOpenMenu] = React.useState(false);
  const [menuFlag, setmenuFlag] = React.useState(false);
  const [accountSwitcher, setAccountSwitcher] = React.useState(false);
  let bearer = AsyncStorage.getItem("bearer");
  const [popup, setPopup] = useState(false);
  const [title, setTitle] = useState("");
  const [content, setContent] = useState();
  const [error, setError] = useState("");
  const [button, setButton] = useState(false);
  const [notificationError, setNotificatioError] = useState(false);
  const [iconName, setIcon] = useState();
  const { height } = Dimensions.get("window");
  const [popupCode, setPopupCode] = useState();
  const [yesDone, setYesDone] = useState(false);
  const [submitLoader, setSubmitLoader] = useState(false);
  const [showPopup, setShowPopup] = React.useState(false);
  const [closePopup, setClosePopup] = React.useState(false);
  const [ticketNumber, setTicketNumber] = React.useState(false);
  const [titlepopup, setTitlepopup] = React.useState(null);
  const focused = useIsFocused();
  const { canShowSnackbar = false, message = "Your action is now complete!" } =
    useSelector(state => state.activity.snackBarData);
  const [allActivities, setAllActivities] = useState(true);
  const [singleWorkOrder, setSingleWorkOrder] = useState(false);
  const [workOrder, setWorkOrder] = useState();
  const [consumerIndex, setConsumerIndex] = useState(false);
  const [OTPModal, setOTPModal] = useState(false);
  const [confirmationModal, setConfirmationModal] = useState(false);
  const [reviewClose, setReviewClose] = useState(false);
  const [CIList, setCIList] = useState(true);
  const [singleCI, setSingleCI] = useState(false);
  const [confirmNote, setconfirmNote] = useState("");
  const [confirmModalType, setConfirmModalType] = useState();
  const [updateCI, setupdateCI] = useState(false);
  const [allCIList, setAllCIList] = useState([]);
  const [createCI, setCreateCI] = useState(false);
  const [WOList, setWOList] = useState(true);
  const [singleWO, setSingleWO] = useState(false);
  const [allWOList, setAllWOList] = useState([]);
  const [customerNumber, setCustomerNumber] = useState();
  const [OTPVerification, setOTPVerification] = useState(false);
  const [updateWAObj, setUpdateWAObj] = useState([]);
  const [OTPError, setOTPError] = useState(false);
  const [singleWODetails, setSingleWODetails] = useState({});
  const [OTPConfirmationWO, setOTPConfirmationWO] = useState(false);
  const [tempWorkOrder, setTempWorkOrder] = useState({});
  const [notifications, setNotifications] = useState([]);
  const [newNotification, setNewNotification] = useState(false);
  const [selectedItem, setSelectedItem] = useState("Home");
  const [newWorkOrder, setNewWorkOrder] = useState({});
  const [newWorkOrderExists, setNewWorkOrderExists] = useState(false);

  const fetchNotifications = async () => {
    try {
      //setLoading(true);
      let res;

      res = await NotificationServiceWO.getAllWorkOrderList();
      //const res = await NotificationService.getAllWorkOrderList();

      res.workOrders = res?.workOrders?.filter(
        each => each["WorkOrderType"] != null,
      );
      res?.workOrders.sort((a, b) => {
        return moment(b.PlannedStartDate).diff(moment(a.PlannedStartDate));
      });
      setNotifications(prev => {
        if (prev.length != 0 && prev.length < res.workOrders.length) {
          console.log("New notifications available!", prev);
          console.log("old", res.workOrders);
          const prevIds = new Set(prev.map(item => item.WorkOrderId));
          const newItems = res.workOrders.filter(item => {
            console.log("", item);
            return !prevIds.has(item.WorkOrderId);
          });

          console.log("prevIds", prevIds);
          console.log("newItems", newItems[0]);
          setNewWorkOrder(newItems[0]);
          setNewNotification(true);
          setAllWOList(res);
        }
        return res.workOrders;
      });
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
    }
  };

  useEffect(() => {}, [notifications]);

  useEffect(() => {}, [newNotification]);

  useEffect(() => {}, [newWorkOrder]);

  useEffect(() => {
    fetchNotifications();
    const interval = setInterval(() => {
      fetchNotifications();
    }, 10000); // Fetch notifications every 30 seconds

    return () => clearInterval(interval); // Cleanup interval on component unmount
  }, []);

  const yesClick = () => {
    setYesDone(true);
  };

  return (
    <View style={{ flex: 1 }}>
      {workModelType && bearer ? (
        <drawerContextWO.Provider
          value={{
            openDrawer,
            submitLoader,
            setOpenMenu,
            setAccountSwitcher,
            menuFlag,
            setmenuFlag,
            accountSwitcher,
            setPopup,
            setTitle,
            setContent,
            setError,
            setButton,
            setIcon,
            popupCode,
            popup,
            setPopupCode,
            yesDone,
            setYesDone,
            notificationError,
            setNotificatioError,
            submitLoader,
            setSubmitLoader,
            showPopup,
            setShowPopup,
            setClosePopup,
            ticketNumber,
            setTicketNumber,
            titlepopup,
            setTitlepopup,
            allActivities,
            setAllActivities,
            singleWorkOrder,
            setSingleWorkOrder,
            workOrder,
            setWorkOrder,
            consumerIndex,
            setConsumerIndex,
            confirmationModal,
            setConfirmationModal,
            OTPModal,
            setOTPModal,
            CIList,
            setCIList,
            singleCI,
            setSingleCI,
            confirmNote,
            setconfirmNote,
            updateCI,
            setupdateCI,
            allCIList,
            setAllCIList,
            reviewClose,
            setReviewClose,
            createCI,
            setCreateCI,
            WOList,
            setWOList,
            singleWO,
            setSingleWO,
            allWOList,
            setAllWOList,
            customerNumber,
            setCustomerNumber,
            OTPVerification,
            setOTPVerification,
            updateWAObj,
            setUpdateWAObj,
            confirmModalType,
            setConfirmModalType,
            OTPError,
            setOTPError,
            singleWODetails,
            setSingleWODetails,
            OTPConfirmationWO,
            setOTPConfirmationWO,
            tempWorkOrder,
            setTempWorkOrder,
            newNotification,
            setNewNotification,
            selectedItem,
            setSelectedItem,
            newWorkOrder,
            newWorkOrderExists,
            setNewWorkOrderExists,
          }}>
          <>
            {workModelType == "WA" ? (
              <AuthenticatedHeader />
            ) : (
              <AuthenticatedHeaderWO />
            )}
            {workModelType == "WA" ? (
              <AuthenticatedTabs />
            ) : (
              <AuthenticatedTabWO />
            )}
            <View style={styles.snackBarWrapper}>
              <Snackbar
                style={styles.snackbarStyle}
                visible={canShowSnackbar}
                onDismiss={() =>
                  dispatch(
                    setSnackbarData({ canShowSnackbar: false, message: "" }),
                  )
                }
                duration={SNACKBAR_TIMEOUT_DURATION}>
                {message}
              </Snackbar>
            </View>
            {workModelType == "WA" ? (
              <AuthenticatedFooter />
            ) : (
              <AuthenticatedFooterWO />
            )}
          </>
          {OTPModal ? (
            <View>
              <Pressable
                style={styles.accountDrawerOpacity}
                onPress={() => setShowPopup(false)}></Pressable>
              <Card style={[styles.accountStyle, { minHeight: height / 3 }]}>
                <OTPModalScreen
                  showPopup={showPopup}
                  hideModel={closePopup}
                  ticketNumber={ticketNumber}
                  title={titlepopup}
                />
              </Card>
            </View>
          ) : null}

          {confirmationModal ? (
            <View>
              <Pressable
                style={styles.accountDrawerOpacity}
                onPress={() => setShowPopup(false)}></Pressable>
              <Card style={[styles.accountStyle, { minHeight: height / 3 }]}>
                <ConfirmModalScreen
                  showPopup={showPopup}
                  hideModel={closePopup}
                  ticketNumber={ticketNumber}
                  title={titlepopup}
                  confirmNote={""}
                />
              </Card>
            </View>
          ) : null}
        </drawerContextWO.Provider>
      ) : (
        <Text>BearerToken issue</Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  accountDrawerOpacity: {
    height: "100%",
    width: "100%",
    backgroundColor: "black",
    opacity: 0.5,
    position: "absolute",
  },
  accountStyle: {
    position: "absolute",
    width: "100%",
    bottom: 0,
    borderTopEndRadius: 25,
    borderTopLeftRadius: 25,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    backgroundColor: GlobalStyles.colors.eFaint.base,
  },
});

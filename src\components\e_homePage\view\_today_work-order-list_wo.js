import {
  StyleSheet,
  View,
  Text,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import { ScrollView } from "react-native-gesture-handler";
import { GlobalStyles } from "../../app/global-styles";
import Icon from "../../icon";
import FontAwesome5Icon from "react-native-vector-icons/FontAwesome";
import { useDispatch, useSelector } from "react-redux";
import {
  setActivities,
  setCurrentActivity,
} from "../../../redux/slices/activitySlices";
import { Switch, TouchableRipple } from "react-native-paper";
import React, { useContext, useEffect, useState } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  DATE_FORMATS,
  ROUTES,
  WORK_ACTIVITY_STATUS,
} from "../../common/constants";
import _ from "lodash";
import moment from "moment/moment";
import { useIsFocused } from "@react-navigation/native";
import { homeContext } from "../e_home";
import { servicePath } from "../../../redux/slices/servicePath";
import { useNavigation } from "@react-navigation/native";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import { DashboardService } from "../model/service";
import axios from "axios";
import { config } from "../../../environment";
import { DashboardServiceWO } from "../model/service_wo";
import { AddressTextFromLatLong } from "./_today_work-order-list";
import { homeContextWO } from "../e_home_wo";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { stackContext } from "../../app/get_stack";
const GOOGLE_MAPS_APIKEY = "AIzaSyCEki8XOhlK7BkZXxyIBxgruZS5c9PMcwI";

export const AddressTextFromLatLongWO = ({ latitude, longitude }) => {
  const { workModelType } = React.useContext(stackContext);
  const [address, setAddress] = useState("");
  useEffect(() => {
    if (latitude && longitude) {
      if (workModelType == "WA") {
        DashboardService.getPlaceFromCoordinates(
          latitude,
          longitude,
          GOOGLE_MAPS_APIKEY,
        )
          .then(response => {
            setAddress(response);
          })
          .catch(error => {
            console.log(error);
            setAddress("");
          });
      } else {
        DashboardServiceWO.getPlaceFromCoordinates(
          latitude,
          longitude,
          GOOGLE_MAPS_APIKEY,
        )
          .then(response => {
            setAddress(response);
          })
          .catch(error => {
            console.log(error);
            setAddress("");
          });
      }
    }
  }, []);

  return <Text>📌&nbsp;{address}</Text>;
};

export default function TodayWorkOrderListWO() {
  const { workModelType } = React.useContext(stackContext);
  const context = workModelType === "WA" ? homeContext : homeContextWO;
  const { todayList, markers } = useContext(context);

  const [todayListWithTime, setTodayListWithTime] = useState(null);
  const pathName = useSelector(state => state?.servicePath?.servicePath);
  const SHIFT_START_TIME = moment().startOf("day").add(10, "hours");
  let setAllActivities,
    setSingleWorkOrder,
    setWorkOrder,
    setConsumerIndex,
    allWOList,
    setWOList,
    setSingleWO,
    customerNumber,
    setSingleWODetails,
    singleWODetails,
    setOTPConfirmationWO;

  if (workModelType === "WA") {
    ({
      setAllActivities,
      setSingleWorkOrder,
      setWorkOrder,
      setConsumerIndex,
      allWOList,
      setWOList,
      setSingleWO,
      customerNumber,
      setSingleWODetails,
      singleWODetails,
      setOTPConfirmationWO,
    } = useContext(drawerContext));
  } else {
    ({
      setAllActivities,
      setSingleWorkOrder,
      setWorkOrder,
      setConsumerIndex,
      allWOList,
      setWOList,
      setSingleWO,
      customerNumber,
      setSingleWODetails,
      singleWODetails,
      setOTPConfirmationWO,
    } = useContext(drawerContextWO));
  }

  useEffect(() => {
    console.log("todayList", todayList);
  }, [todayList]);
  const dispatch = useDispatch();
  const navigation = useNavigation();
  //TODO: use hash or hash map for look up
  const getValueFromMap = status => {
    let value = null;
    switch (status) {
      case "Cancelled":
      case "Closed":
        value = {
          color: "green",
          icon: "Sucess-icon",
        };
        break;
      case "Overdue":
        value = {
          color: "red",
          icon: "exclamation-triangle",
        };
        break;
      case "Open":
      case "In Progress":
        value = {
          color: "orange",
          icon: "Hourglass-icon-new",
        };
        break;
      default:
        value = {
          color: "green",
          icon: "Sucess-icon",
        };
    }
    return value;
  };
  const onPressTicket = option => {
    console.log("option", option);
    // setWOList(true);
    // setSingleWO(false);

    if (option.WorkOrderType === "Consumer Survey") {
      console.log("yesssss", option);
      setSingleWODetails(option);
      // //setCustomerNumber(option.ConsumerPhoneNumber);
      setWOList(false);
      setSingleWO(true);
      setOTPConfirmationWO(false);
    } else if (option.WorkOrderType === "Consumer Meter Replacement") {
      setSingleWODetails(option);
      setWOList(false);
      setSingleWO(true);
      setOTPConfirmationWO(true);
    } else {
      setOTPConfirmationWO(true);
    }
    dispatch(servicePath("WorkActivities"));
    navigation.navigate("WorkActivities");
  };

  const getTravelTime = async (origin, destination) => {
    const url = `https://maps.googleapis.com/maps/api/distancematrix/json?origins=${origin}&destinations=${destination}&key=${GOOGLE_MAPS_APIKEY}`;
    try {
      const response = await axios.get(url);
      const travelTime = response.data.rows[0].elements[0].duration.value; // Travel time in seconds
      return travelTime;
    } catch (error) {
      console.error("Error fetching travel time:", error);
      return 0; // Default to 0 if there's an error
    }
  };

  // const updatedTodayList = async todayList => {
  //   const SHIFT_START_TIME = moment(); // Example, replace with actual SHIFT_START_TIME
  //   let list = JSON.parse(JSON.stringify(todayList)); // Deep copy to avoid mutating the original array
  //   console.log(JSON.stringify(list, null, 2), "test...");
  //   for (let i = 0; i < list.length; i++) {
  //     if (i === 0) {
  //       list[i].startTime = moment(SHIFT_START_TIME); // Initialize start time with SHIFT_START_TIME
  //       list[i].endTime = moment(SHIFT_START_TIME).add(
  //         list[i].PlannedDuration ? list[i].PlannedDuration : 5,
  //         "hours",
  //       );
  //     } else {
  //       const origin = `${list[i - 1].lat},${list[i - 1].lng}`;
  //       const destination = `${list[i].lat},${list[i].lng}`;
  //       const travelTimeInSeconds = await getTravelTime(origin, destination);
  //       const travelTimeInMinutes = travelTimeInSeconds / 60;

  //       list[i].startTime = moment(list[i - 1].endTime).add(
  //         travelTimeInMinutes,
  //         "minutes",
  //       );
  //       list[i].endTime = moment(list[i].startTime).add(
  //         list[i].PlannedDuration ? list[i].PlannedDuration : 5,
  //         "hours",
  //       );
  //     }
  //   }

  //   console.log(list, "list");
  //   if (!list) {
  //     list = [];
  //   }
  //   return list;
  // };

  const updatedTodayList = todayList => {
    const SHIFT_START_TIME = moment().startOf("day").add(10, "hours"); // Example, replace with actual SHIFT_START_TIME
    let list = JSON.parse(JSON.stringify(todayList)); // Deep copy to avoid mutating the original array
    list = list.map((wo, index) => {
      if (index === 0) {
        wo.startTime = moment(SHIFT_START_TIME); // Initialize start time with SHIFT_START_TIME
        wo.endTime = moment(SHIFT_START_TIME).add(
          wo.PlannedDuration ? wo.PlannedDuration : 5,
          "hours",
        );
      } else {
        wo.startTime = moment(list[index - 1].endTime).add(32, "minutes");
        wo.endTime = moment(wo.startTime).add(
          wo.PlannedDuration ? wo.PlannedDuration : 5,
          "hours",
        );
      }
      return wo;
    });
    console.log(list, "list");
    return list;
  };

  useEffect(() => {
    if (todayList && Array.isArray(todayList)) {
      let todayTrueList = todayList.filter(wo =>
        moment(wo.PlannedStartDate, "YYYY-MM-DD HH:mm:ss").isSame(
          moment(),
          "day",
        ),
      );
      const list = updatedTodayList(todayTrueList);
      console.log(JSON.stringify(list, null, 2), "listwithtime");
      setTodayListWithTime(list);
    }
  }, [todayList]);
  return (
    <View>
      <ScrollView style={styles.container}>
        {!_.isEmpty(todayList) ? (
          <>
            <View>
              {(todayListWithTime || []).map((option, index) => {
                return (
                  <TouchableRipple
                    onPress={() => onPressTicket(option)}
                    key={option.workorderId}>
                    <>
                      <View>
                        <View style={styles.activityItem}>
                          <View style={styles.activityItemInner}>
                            <View style={styles.iconContainer}>
                              {/* {option.Status === "CO" ? (
                                <FontAwesome5Icon
                                  name={"exclamation-triangle"}
                                  color={GlobalStyles.colors.eSecondary.base}
                                  size={24}
                                />
                              ) : option.Status === "I" ? (
                                <FontAwesome5Icon
                                  name={"exclamation-triangle"}
                                  color={GlobalStyles.colors.ePrimary.base}
                                  size={24}
                                />
                              ) : (
                                <FontAwesome5Icon
                                  name={"exclamation-triangle"}
                                  color={GlobalStyles.colors.eTertiary.base}
                                  size={24}
                                />
                              )} */}
                              <Text style={styles.routeNumberStyle}>
                                {String.fromCharCode(97 + index).toUpperCase()}
                              </Text>
                            </View>
                            <View style={styles.contentContainer}>
                              <View
                                style={[
                                  styles.displayFlex,
                                  { gap: 10, paddingVertical: 3 },
                                ]}>
                                <Text style={styles.headerStyleNumber}>
                                  {option.WorkOrderId}
                                </Text>
                                <Text style={styles.headerStyleNumber}>
                                  {option?.Title}
                                </Text>
                              </View>
                              <View style={styles.subHeaderRow}>
                                <Text style={[styles.subHeaderRowMinWidth]}>
                                  Start Time:{" "}
                                  {moment(option.startTime).format(
                                    "DD-MM-YYYY hh:mm A",
                                  )}
                                </Text>
                              </View>
                              <View style={styles.subHeaderRow}>
                                <Text style={[styles.subHeaderRowMinWidth]}>
                                  End Time:{" "}
                                  {moment(option.endTime).format(
                                    "DD-MM-YYYY hh:mm A",
                                  )}
                                </Text>
                              </View>
                              <View style={styles.subHeaderRow}>
                                {workModelType == "WA" ? (
                                  <AddressTextFromLatLong
                                    latitude={
                                      option && option.LatitudeLongitude
                                        ? option.LatitudeLongitude.split(",")[0]
                                        : ""
                                    }
                                    longitude={
                                      option
                                        ? option.LatitudeLongitude.split(",")[1]
                                        : ""
                                    }
                                  />
                                ) : (
                                  <AddressTextFromLatLongWO
                                    latitude={
                                      option && option.LatitudeLongitude
                                        ? option.LatitudeLongitude.split(",")[0]
                                        : ""
                                    }
                                    longitude={
                                      option
                                        ? option.LatitudeLongitude.split(",")[1]
                                        : ""
                                    }
                                  />
                                )}
                              </View>
                              <View style={styles.subHeaderRowStatus}>
                                <Text style={[styles.subHeaderStatus]}>
                                  {option.Priority === "H"
                                    ? "High"
                                    : option.Priority === "L"
                                    ? "Low"
                                    : "Medium"}
                                </Text>
                                {/* <Text
                                  style={[styles.subHeaderRowMinWidth]}></Text> */}
                                <Text style={[styles.subHeaderStatus]}>
                                  {option.Status === "C" ||
                                  option.Status === "O"
                                    ? "Open"
                                    : option.Status === "I"
                                    ? "Inprogress"
                                    : option.Status === "CO"
                                    ? "Complete"
                                    : option.Status === "R"
                                    ? "Rejected"
                                    : option.Status === "CA"
                                    ? "Cancelled"
                                    : option.Status}
                                </Text>
                              </View>
                            </View>
                          </View>
                          <View style={styles.arrowIconStyle}>
                            <FontAwesome5Icon
                              name="chevron-right"
                              color={GlobalStyles.colors.eMedium.base}
                              size={12}
                              fontFamily="NotoSans-Bold"
                            />
                          </View>
                        </View>
                      </View>
                      <View style={styles.lineStyle} />
                    </>
                  </TouchableRipple>
                );
              })}
            </View>
          </>
        ) : (
          <View
            style={[
              styles.noDateWrapper,
              { height: Dimensions.get("window").height },
            ]}>
            <View style={{ height: Dimensions.get("window").height - 500 }}>
              <Text
                style={{
                  fontSize: 19,
                  color: GlobalStyles.colors.ePrimary.base,
                }}>
                No Work Orders Available.
              </Text>
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  activityWrapper: {
    // paddingTop: 8,
    // paddingBottom: 8,
    // borderBottomColor: GlobalStyles.colors.eBackground.base,
    // borderBottomWidth: 1,
  },
  lineStyle: {
    borderWidth: 1,
    borderColor: GlobalStyles.colors.eBackground.base,
    //marginTop: 10,
    //marginBottom: 6,
    width: "150%",
  },
  activityItem: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 10,
    // marginTop: -5,
  },
  mainView: {
    marginVertical: 5,
  },
  activityItemInner: {
    display: "flex",
    flexDirection: "row",
    flex: 1,
    gap: 20,
  },
  arrowIconStyle: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  displayFlex: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  headerStyle: {
    fontSize: 15,
    fontFamily: "NotoSans-SemiBold",
  },
  headerStyleNumber: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eRich.base,
  },
  iconContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 10,
    backgroundColor: "#eee",
    padding: 10,
  },
  routeNumberStyle: {
    color: "#333",
  },
  subHeaderRowMinWidth: {
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderPriority: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eRich.base,
    textTransform: "capitalize",
    marginTop: -6,
    textAlign: "right",
  },
  subHeaderStatus: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eRich.base,
    textTransform: "capitalize",
    // marginTop: -10,
    paddingRight: 10,
    textAlign: "right",
  },
  subHeaderRow: {
    display: "flex",
    flexDirection: "row",
    paddingVertical: 1,
    justifyContent: "space-between",
    paddingRight: 20,
    marginTop: -5,
    // textAlign: 'left'
  },
  subHeaderRowStatus: {
    display: "flex",
    flexDirection: "column",
    paddingVertical: 1,
    justifyContent: "flex-end",
    paddingRight: 10,
  },
  container: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    height: "95%",
    marginHorizontal: 15,
  },
  contentContainer: {
    flex: 1,
  },
  noDateWrapper: {
    flex: 1,
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
  },
  profilePicView: {
    height: 60,
    width: 60,
    borderRadius: 100,
    marginLeft: 0,
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    borderWidth: 10,
    borderColor: GlobalStyles.colors.eWhite.base,
  },
});

import React, { useEffect } from "react";
import { useState } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  Keyboard,
  Animated,
} from "react-native";
import AccountDetails from "./view/_account_details";
import PaymentMethods from "./view/_payment_methods";
import SubmitCard from "./view/_submit_card";
import PaymentConfirm from "./view/_payment_confirm";
import PaymentError from "./view/_payment_error";
import DownloadCard from "./view/_download";
import { useSelector } from "react-redux";
// import * as Linking from "expo-linking";
import { paymentService } from "./model/payment_service";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { meterDetails } from "../../redux/slices/selectedAccount";
import { servicePath } from "../../redux/slices/servicePath";
import { GlobalStyles } from "../app/global-styles";
import { Text, TextInput } from "react-native-paper";
import Button from "../common/_button";

export const PaymentContext = React.createContext();
export default function Payments(props) {
  const [totalValue, setTotalValue] = useState(0);
  const [currency, setCurrency] = useState("$");
  const [accountDetailsScreen, setAccountDetailsScreen] = useState(true);
  const [confirmScreen, setConfirmScreen] = useState(false);
  const [errorScreen, setErrorScreen] = useState(false);
  const [disabled, setPaymentDisabled] = useState(true);
  const [selectedAccount, setSelectedPayments] = useState();
  const [paymentCurrency, setPaymentCurrency] = useState();
  const [payId, setPayId] = useState();
  const [tempAcc, setTempAcc] = useState();
  const [data, setData] = useState({});
  const [payableAmount, setPay] = useState();
  const [showKeyboard, setShowKeyboard] = useState(false);

  let servicePathName = useSelector(state => state?.servicePath?.servicePath);

  useEffect(() => {
    if (servicePathName === "Payments" && data && data?.queryParams) {
    } else {
      setAccountDetailsScreen(true);
      setConfirmScreen(false);
      setErrorScreen(false);
    }
  }, [servicePathName]);

  // useEffect(() => {
  //   const { queryParams } = data;
  //   const { account, amount, paymentStatus, payId } = queryParams || {};

  //   if (payId) {
  //     setPayId(payId);
  //   }

  //   if (account && !Array.isArray(account)) {
  //     setTempAcc(account);
  //     setSelectedPayments(account);
  //   }

  //   if (amount) {
  //     setTotalValue(Number(amount));
  //   }

  //   if (paymentStatus === "success") {
  //     setAccountDetailsScreen(false);
  //     setConfirmScreen(true);
  //     setErrorScreen(false);
  //     updateUsageBill(account);
  //     setData({});
  //   } else if (paymentStatus === "fail") {
  //     setAccountDetailsScreen(false);
  //     setConfirmScreen(false);
  //     setErrorScreen(true);
  //     setData({});
  //   }
  // }, [data]);

  const backToPayment = () => {
    setAccountDetailsScreen(true);
    setErrorScreen(false);
    setConfirmScreen(false);
    setShowKeyboard(false);
  };
  const [shift] = useState(new Animated.Value(0));
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      handleKeyboardDidShow,
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      handleKeyboardDidHide,
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);
  const handleKeyboardDidShow = () => {
    Animated.timing(shift, {
      toValue: -30, // Adjust the value as needed
      duration: 100,
      useNativeDriver: true,
    }).start();
  };

  const handleKeyboardDidHide = () => {
    Animated.timing(shift, {
      toValue: 0,
      duration: 100,
      useNativeDriver: true,
    }).start();
  };
  return (
    <>
      <PaymentContext.Provider
        value={{
          confirmScreen,
          setConfirmScreen,
          setPaymentDisabled,
          disabled,
          selectedAccount,
          setSelectedPayments,
          totalValue,
          paymentCurrency,
          setPaymentCurrency,
          setAccountDetailsScreen,
          setErrorScreen,
          currency,
          setCurrency,
          payId,
          payableAmount,
          setPay,
          setPayId,
        }}>
        {confirmScreen ? (
          <>
            <ScrollView style={styles.pressable}>
              <View style={styles.spaceAroundCard}>
                <PaymentConfirm onBack={backToPayment} />
              </View>
            </ScrollView>
            <View style={styles.spaceAroundCardSubmit}>
              <DownloadCard totalValue={totalValue} />
            </View>
          </>
        ) : errorScreen ? (
          <>
            <ScrollView style={styles.pressable}>
              <View style={styles.spaceAroundCard}>
                <PaymentError />
              </View>
            </ScrollView>
            <View style={styles.spaceAroundCardSubmit}>
              <DownloadCard
                totalValue={totalValue}
                onRetryPayment={() => setShowKeyboard(false)}
              />
            </View>
          </>
        ) : accountDetailsScreen ? (
          <>
            <ScrollView style={styles.pressable}>
              <View style={styles.spaceAroundCard}>
                <AccountDetails
                  totalValue={totalValue}
                  setTotalValue={setTotalValue}
                  currency={currency}
                  //setCurrency={setCurrency}
                />
              </View>
              <View style={styles.spaceAroundCard}>
                <PaymentMethods />
              </View>
            </ScrollView>
            {Platform.OS === "ios" ? (
              <KeyboardAvoidingView
                behavior={Platform.OS === "ios" ? "padding" : ""}>
                <View style={styles.spaceAroundCardSubmit}>
                  <SubmitCard
                    totalValue={totalValue}
                    onFocus={() => setShowKeyboard(true)}
                    onBlur={() => setShowKeyboard(false)}
                  />
                </View>
                {/* logic to move the submit card to the top */}
                {showKeyboard && Platform.OS === "ios" && (
                  <View style={{ height: 100 }}></View>
                )}
              </KeyboardAvoidingView>
            ) : (
              <Animated.View style={[{ transform: [{ translateY: shift }] }]}>
                <SubmitCard
                  totalValue={totalValue}
                  onFocus={() => {
                    setShowKeyboard(true);
                  }}
                  onBlur={() => {
                    setShowKeyboard(false);
                  }}
                />
              </Animated.View>
            )}
          </>
        ) : (
          <ActivityIndicator
            size="large"
            color={GlobalStyles.colors.ePrimary.base}
            style={{ marginTop: 100 }}
          />
        )}
      </PaymentContext.Provider>
    </>
  );
}

const styles = StyleSheet.create({
  content: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  pressable: {
    flex: 1,
    marginTop: 20,
  },
  spaceAroundCard: {
    marginBottom: 10,
    width: "100%",
  },
  spaceAroundCardSubmit: {
    width: "100%",
  },
});

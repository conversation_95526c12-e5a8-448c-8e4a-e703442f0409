import { StyleSheet, View, Image } from "react-native";
import { GlobalStyles } from "../../app/global-styles";
import { Text, IconButton } from "react-native-paper";
import React, { useContext } from "react";
import { registerContext } from "../../e_authPages/e_auth_pages";
import TextLink from "react-native-text-link";
import { useNavigation } from "@react-navigation/native";
import Icon from "../../icon";
import { config } from "../../../environment";
import { registerContextWO } from "../../e_authPages/e_auth_pages_wo";
import { stackContext } from "../../app/get_stack";

export default function RegisterConfirmModal() {
  const { workModelType } = React.useContext(stackContext);
  let setRegisterSuccess,
    setRegisterModal,
    setIsLoginPage,
    setVerificationScreen;

  if (workModelType === "WA") {
    ({
      setRegisterSuccess,
      setRegisterModal,
      setIsLoginPage,
      setVerificationScreen,
    } = useContext(registerContext));
  } else {
    ({ setIsLoginPage } = useContext(registerContextWO));
  }

  const navigation = useNavigation();

  const closeMenu = () => {
    setIsLoginPage("Login");
    setRegisterSuccess(false);
    setRegisterModal(false);
    setVerificationScreen(false);
  };

  const okClick = () => {
    console.log("ok");
  };

  return (
    <>
      <View style={styles.container}>
        <View style={styles.content}>
          <Text style={styles.title}>confirmation</Text>
          <Icon
            name="Close-icon-stroke"
            color={GlobalStyles.colors.eBlack.base}
            onPress={closeMenu}
            style={styles.closeIcon}
            size={24}
          />
        </View>
        <View style={styles.lineStyleTop}></View>
        <View style={styles.imgViewCls}>
          <Icon
            name="Sucess-icon"
            style={styles.greenBg}
            color={GlobalStyles.colors.eSecondary.base}
            size={53}
          />
        </View>

        <View style={styles.contentTicket}>
          <Text style={styles.subtitle}>
            You have successfully registered with us!
          </Text>
        </View>

        <View style={styles.lineStyleBottom}></View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flext: 1,
    position: "relative",
  },
  content: {
    padding: 20,
  },
  contentLast: {
    marginBottom: 10,
  },
  contentTicket: {
    padding: 25,
  },
  title: {
    textAlign: "center",
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 16,
    fontFamily: "NotoSans-Bold",
  },
  subtitle: {
    textAlign: "center",
    color: GlobalStyles.colors.eSecondary.base,
    fontSize: 14,
    fontFamily: "NotoSans-SemiBold",
  },
  ticketReference: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 16,
    fontFamily: "NotoSans-Bold",
    padding: 10,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: GlobalStyles.colors.ePrimary.base,
    marginTop: 10,
  },
  subText: {
    textAlign: "center",
    color: GlobalStyles.colors.eBlack.base,
    fontSize: 11,
  },
  subText2: {
    textAlign: "center",
    color: GlobalStyles.colors.eBlack.base,
    fontSize: 11,
  },
  subLink: {
    textAlign: "center",
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 11,
    paddingTop: 10,
    paddingBottom: 10,
  },
  closeIcon: {
    position: "absolute",
    top: 13,
    right: 18,
    backgroundColor: GlobalStyles.colors.eFaint.selected,
    borderRadius: 20,
    padding: 5,
  },
  imgCls: {
    height: 60,
    width: 60,
    padding: 5,
  },
  imgViewCls: {
    justifyContent: "center",
    alignItems: "center",
    marginTop: 30,
    // marginBottom: -5,
  },
  lineStyleBottom: {
    borderWidth: 0.5,
    borderColor: GlobalStyles.colors.eOutline.base,
    marginTop: 10,
  },
  lineStyleTop: {
    borderWidth: 0.5,
    borderColor: "#DEE2E4",
    borderColor: GlobalStyles.colors.ePastelColor2.hover,
    marginLeft: -3,
    marginBottom: 2,
    marginRight: -3,
    // marginTop: -20,
  },
  greenBg: {
    backgroundColor: GlobalStyles.colors.eFaint.base,
  },
});

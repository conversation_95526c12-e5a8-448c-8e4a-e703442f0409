import axios from "axios";
import { config } from "../../../environment";
import AsyncStorage from "@react-native-async-storage/async-storage";

export const registerService = {
  register,
  OTPSubmitDetails,
  ResendOTPDetails,
};

async function register(user, tenantNumber, FLName) {
  let APIquery = {};
  if (FLName) {
    APIquery = {
      firstName: user.firstname,
      lastName: user.lastname,
      password: user.password,
      email: user.registeredEmail,
      phoneNumber: user.registeredCell,
      identities: [
        {
          identityValue: user.accountNumber,
          identityCode: tenantNumber,
        },
      ],
      tenantCode: config.parameters.TENANT_CODE,
      createdBy: 0,
    };
  } else {
    APIquery = {
      password: user.password,
      email: user.registeredEmail,
      phoneNumber: user.registeredCell,
      identities: [
        {
          identityValue: user.accountNumber,
          identityCode: tenantIdNo,
        },
      ],
      tenantCode: config.parameters.TENANT_CODE,
      createdBy: 0,
    };
  }

  return await axios.post(config.urls.SIGNUP, APIquery);
}
async function OTPSubmitDetails(otp, flowType, email) {
  const validateOTPObject = {};
  if (flowType === "LOGIN_OTPVERIFIED" || flowType === "LOGIN") {
    validateOTPObject.password = password;
    validateOTPObject.otp = Number(otp);
    validateOTPObject.flowType = flowType;
  } else {
    validateOTPObject.otp = Number(otp);
    validateOTPObject.flowType = flowType;
  }
  return await axios.post(config.urls.VALIDATEOTP, validateOTPObject, {
    headers: { userName: email.toLowerCase() },
  });
}

async function ResendOTPDetails(email) {
    return await axios.post(config.urls.SIGNUP_RESENDOTP, {
      flowType: "SIGNUP",
    },{
      headers: { userName: email.toLowerCase() },
    });
}

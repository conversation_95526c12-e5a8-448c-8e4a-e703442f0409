import { GlobalStyles } from "../app/global-styles";
import { StyleSheet, View, Image } from "react-native";
import { Text, IconButton, Card } from "react-native-paper";
import Icon from "../icon";
import Button from "../common/_button";
import AsyncStorage from "@react-native-async-storage/async-storage";

export default function ModalPopup({
  setPopup,
  title,
  content,
  error,
  button,
  iconName,
  yesClick,
  popupCode,
  notificationError,
}) {
  const closeMenu = () => {
    setPopup(false);
    AsyncStorage.removeItem("startDate");
    AsyncStorage.removeItem("endDate");
  };

  const noClick = () => {
    setPopup(false);
    AsyncStorage.removeItem("startDate");
    AsyncStorage.removeItem("endDate");
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>{title}</Text>
        <IconButton
          icon={() => <Icon
            name="Close-icon-stroke"
            color={GlobalStyles.colors.eDark.base}
            style={{fontFamily: "NotoSans-Bold"}}
            size={24}
          />}
          onPress={closeMenu}
          style={styles.closeIcon}
          size={15}
        />
      </View>
      <View style={[styles.horizontalLineFirst, { marginTop: "2%" }]} />
      <View style={styles.contentView}>
        <View style={styles.imgViewCls}>
          {iconName && (
            <Icon
              name={iconName}
              color={
                error === "ERROR"
                  ? GlobalStyles.colors.eDanger.dark
                  : error === "SUCCESS"
                  ? GlobalStyles.colors.eSecondary.base
                  : GlobalStyles.colors.ePrimary.base
              }
              size={50}
            />
          )}
        </View>
        {popupCode === "NOTIFICATION_FILTER" ? (
          <View>
              {content}
          </View>
        ) : (
          <View>
            <Text
              style={[
                styles.subtitle,
                error === "SUCCESS"
                  ? { color: GlobalStyles.colors.eSecondary.base }
                  : error === "ERROR"
                  ? { color: GlobalStyles.colors.eDanger.dark }
                  : error === "WARNING" && {
                      color: GlobalStyles.colors.ePrimary.base,
                    },
              ]}>
              {content}
            </Text>
          </View>
        )}
      </View>
      {button && (
        <View style={styles.bottomButtons}>
          <View style={[styles.horizontalLineLast, { marginBottom: "2%" }]} />
          <View style={styles.centerButtons}>
            <Button
              buttonbgColor={styles.cancelBg}
              textColor={styles.cancelText}
              onPress={noClick}>
              {popupCode === "NOTIFICATION_FILTER" ? "Cancel" : "No"}
            </Button>
            <Button
              textColor={styles.textColor}
              onPress={yesClick}
              disabled={notificationError}
              buttonbgColor={[
                styles.buttonBgColor,
                notificationError ? styles.disabledStyle : null,
              ]}>
              {popupCode === "NOTIFICATION_FILTER" ? "Ok" : "Yes"}
            </Button>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    paddingBottom: 35,
  },
  content: {
    paddingTop:8,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    height:70
  },

  title: {
    textAlign: "center",
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 16,
    fontFamily: "NotoSans-Bold",
    fontWeight: 600,
    textTransform: "capitalize",
  },
  subtitle: {
    textAlign: "center",
    fontSize: 14,
    fontFamily: "NotoSans-SemiBold",
    marginVertical: "10%",
  },
  imgViewCls: {
    justifyContent: "center",
    alignItems: "center",
    paddingTop: "12%",
  },
  closeIcon: {
    position: "absolute",
    top:18,
    right: 18,
    backgroundColor: GlobalStyles.colors.eFaint.selected,
  },
  greenBg: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
  },
  centerButtons: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 18,
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 10,
  },
  cancelText: {
    color: GlobalStyles.colors.eSecondary.base,
    fontFamily: "NotoSans-Medium",
    fontSize: 12,
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontFamily: "NotoSans-Medium",
    fontSize: 12,
    width: 45,
    paddingTop: 1,
    paddingBottom: 1,
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginLeft: 10,
  },
  bottomButtons: {
    position: "absolute",
    bottom: "7%",
    width: "100%",
  },
  horizontalLine: {
    borderBottomWidth: 0.6,
    width: "100%",
    borderColor: GlobalStyles.colors.eSeparationLine.base,
  },
  horizontalLineFirst: {
    borderBottomWidth: 0.6,
    width: "100%",
    borderColor: GlobalStyles.colors.eBackground3.base,
  },
  horizontalLineLast: {
    borderBottomWidth: 0.3,
    width: "100%",
    borderColor: GlobalStyles.colors.eOutline.selected,
  },
  disabledStyle: {
    opacity: 0.5,
  },
  contentView: {
    minHeight: 200,
    display: "flex",
    alignItems: "center",
  },
});

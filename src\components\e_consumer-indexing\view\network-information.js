import React, { useEffect, useMemo, useState, useContext } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { ScrollView } from "react-native-gesture-handler";
import CustomTextInput from "./CustomTextInput";
import { consumerPaginationContext } from "./new-consumer-index";
import { useDispatch, useSelector } from "react-redux";
import { getInitialConsumerIndexingValues } from "../../common/util";
import _ from "lodash";
import { GlobalStyles } from "../../app/global-styles";
import { Dimensions } from "react-native";
import { consumerIndexContext } from "../e_consumer-index";
import { useTranslation } from 'react-i18next';

const windowWidth = Dimensions.get("window").width;
const windowHeight = Dimensions.get("window").height;
const NetworkInfoPage = () => {
  const { t } = useTranslation();
  const { IndexArray, currentPage, setIndexArray } = useContext(
    consumerPaginationContext,
  );
  const { tempCIData, revert, setRevert, newCIData, setNewCIData } =
    useContext(consumerIndexContext);

  useEffect(() => {
    if (newCIData) {
      if (
        !newCIData.FeederCode ||
        !newCIData.DTRName ||
        !newCIData.poleNo ||
        !newCIData.FeederName ||
        !newCIData.DTRating ||
        !newCIData.NoOfMetersOnPole ||
        !newCIData.DTRCode ||
        !newCIData.CTRatioOfDT
      ) {
        const updatedIndexArray = IndexArray.map((item, index) =>
          index === currentPage - 1 ? { ...item, filled: false } : item,
        );
        setIndexArray(updatedIndexArray);
      } else {
        const updatedIndexArray = IndexArray.map((item, index) =>
          index === currentPage - 1 ? { ...item, filled: true } : item,
        );
        setIndexArray(updatedIndexArray);
      }
    }
  }, [newCIData, currentPage]);

  useEffect(() => {
    if (revert) {
      setNewCIData(tempCIData);
      setRevert(false);
    }
  }, [revert]);

  return (
    <View style={styles.container}>
      <View style={styles.section}>
        <View style={styles.headerWrapper}>
          <Text style={styles.sectionHeaderText}>
            {IndexArray[currentPage - 1].name}
          </Text>
        </View>
        <View style={styles.sectionWrapper}>
          <ScrollView
            persistentScrollbar={true}
            contentContainerStyle={{ flexGrow: 1 }}>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t('FEEDER_CODE')}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.FeederCode ? `${newCIData?.FeederCode}` : ""
                }
                value={newCIData?.FeederCode ? `${newCIData?.FeederCode}` : ""}
                placeholder={t('ENTER_FEEDER_CODE')}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, FeederCode: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t('DTR_NAME')}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={newCIData?.DTRName ? `${newCIData?.DTRName}` : ""}
                value={newCIData?.DTRName ? `${newCIData?.DTRName}` : ""}
                placeholder={t('ENTER_DTR_NAME')}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, DTRName: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t('POLE_NO')}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={newCIData?.poleNo ? `${newCIData?.poleNo}` : ""}
                value={newCIData?.poleNo ? `${newCIData?.poleNo}` : ""}
                placeholder={t('ENTER_POLE_NO')}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, poleNo: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t('FEEDER_NAME')}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.FeederName ? `${newCIData?.FeederName}` : ""
                }
                placeholder={t('ENTER_FEEDER_NAME')}
                value={newCIData?.FeederName ? `${newCIData?.FeederName}` : ""}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, FeederName: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t('DT_RATING')}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.DTRating ? `${newCIData?.DTRating}` : ""
                }
                placeholder={t('ENTER_DT_RATING')}
                value={newCIData?.DTRating ? `${newCIData?.DTRating}` : ""}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, DTRating: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t('ENTER_No_METER_ON_POLE')}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.NoOfMetersOnPole
                    ? `${newCIData?.NoOfMetersOnPole}`
                    : ""
                }
                placeholder={t('ENTER_No_METER_ON_POLE')}
                value={
                  newCIData?.NoOfMetersOnPole
                    ? `${newCIData?.NoOfMetersOnPole}`
                    : ""
                }
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, NoOfMetersOnPole: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t('DTR_CODE')}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={newCIData?.DTRCode ? `${newCIData?.DTRCode}` : ""}
                value={newCIData?.DTRCode ? `${newCIData?.DTRCode}` : ""}
                placeholder={t('ENTER_DTR_CODE')}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, DTRCode: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t('ENTER_DT_CT_RATIO')}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.CTRatioOfDT ? `${newCIData?.CTRatioOfDT}` : ""
                }
                placeholder={t('ENTER_DT_CT_RATIO')}
                value={
                  newCIData?.CTRatioOfDT ? `${newCIData?.CTRatioOfDT}` : ""
                }
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, CTRatioOfDT: value }));
                }}
              />
            </View>
          </ScrollView>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingLeft: 10,
  },
  extendFlex: {
    width: windowWidth - 40,
    paddingBottom: 10,
  },
  headerWrapper: {
    paddingBottom: 20,
  },
  flexRowCenter: {
    paddingHorizontal: 40,
  },
  section: {},
  sectionHeaderText: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.ePrimary.base,
  },
  sectionItemHeader: {
    width: "100%",
  },
  sectionItemInputStyle: {
    height: 35,
  },
  sectionItemLabel: {
    fontFamily: "NotoSans-Medium",
    fontSize: 10,
    color: GlobalStyles.colors.eRich.hover,
  },
  sectionWrapper: {
    height: windowHeight - 450,
  },
  submitBtnWrapper: {
    paddingVertical: 16,
    marginBottom: 10,
  },
});

export default NetworkInfoPage;

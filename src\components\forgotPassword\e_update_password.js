import { ScrollView, StyleSheet, View } from "react-native";
import Input from "../common/_input";
import { GlobalStyles } from "../app/global-styles";
import React, { useContext, useEffect, useState } from "react";
import Button from "../common/_button";
import FlatButton from "../common/_flat_button";
import { loginService } from "../e_login/model/_login_service";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { registerContext } from "../e_authPages/e_auth_pages";
import { ActivityIndicator, Text } from "react-native-paper";
import { registerContextWO } from "../e_authPages/e_auth_pages_wo";
import { config } from "../../environment";
import { stackContext } from "../app/get_stack";

export default function UpdatePassword() {
  const { workModelType } = React.useContext(stackContext);
  const { setIsLoginPage } = useContext(
    workModelType === "WA" ? registerContext : registerContextWO,
  );

  const [password, setpassword] = useState();
  const [confirmPassword, setConfirmPassword] = useState();
  const [submitError, setSubmitError] = useState(false);
  const [submitErrMsg, setSubmitErrorMsg] = useState("");
  const [passwordRequire, setPasswordRequire] = useState(false);
  const [passwordError, setpasswordError] = useState(false);
  const [confirmPasswordRequire, setConfirmPasswordRequire] = useState(false);
  const [confirmPasswordError, setConfirmPasswordErr] = useState();
  const [isLoading, setisLoading] = useState(false);
  const [email, setEmail] = useState();
  const [sessionId, setSessionId] = useState();

  useEffect(() => {
    AsyncStorage.getItem("email").then(res => setEmail(res));
    AsyncStorage.getItem("sessionId").then(res => setSessionId(res));
  }, [AsyncStorage.getItem("email")]);

  const updateInputValueHandler = (inputType, enteredValue) => {
    setSubmitError(false);
    setSubmitErrorMsg("");
    switch (inputType) {
      case "password":
        setpassword(enteredValue);
        break;
      case "confirmPassword":
        setConfirmPassword(enteredValue);
        break;
    }
  };

  const passwordBlur = () => {
    if (password) {
      setPasswordRequire(false);
      var regData =
        "^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{6,})";
      var strongRegex = new RegExp(regData);
      var test = strongRegex.test(password);
      if (test) {
        setpasswordError(false);
      } else {
        setpasswordError(true);
      }
    } else {
      setPasswordRequire(true);
    }
  };

  const confirmPasswordBlur = () => {
    if (confirmPassword) {
      setConfirmPasswordRequire(false);
    } else {
      setConfirmPasswordRequire(true);
    }
    if (password && confirmPassword) {
      if (password !== confirmPassword) {
        setConfirmPasswordErr(true);
      } else {
        setConfirmPasswordErr(false);
      }
    }
  };

  const getpasswordErrorMsg = () => {
    if (passwordRequire) {
      return "Please enter new password";
    } else if (passwordError) {
      return "Password is case-sensitive, it must contain a minimum of six characters with atleast one capital letter, one small letter, one number and one special character.";
    } else return;
  };

  const getConfirmPasswordErrorMsg = () => {
    if (confirmPasswordRequire) {
      return "Please enter confirm Password";
    } else if (confirmPasswordError) {
      return "New password and Confirm password should be same";
    } else return;
  };

  const submitClick = () => {
    passwordBlur();
    confirmPasswordBlur();
    if (
      password &&
      confirmPassword &&
      !confirmPasswordError &&
      !confirmPasswordRequire &&
      !passwordError &&
      !passwordRequire
    ) {
      setisLoading(true);
      loginService
        .updatepassword(password, sessionId, email)
        .then(res => {
          setisLoading(false);
          AsyncStorage.removeItem("email");
          AsyncStorage.removeItem("sessionId");
          if (res?.data?.isOk === true || res?.isOk === true) {
            setIsLoginPage("updatePasswordSuccess");
          }
        })
        .catch(error => {
          setisLoading(false);
          if (error?.response?.status === 400) {
            if (error?.response?.data?.message === "SERVICE_NOT_WORKING") {
              setSubmitError(true);
              setSubmitErrorMsg("Service is not working");
            } else if (
              error?.response?.data?.message === "FORGOT_SESSION_EXPIRED"
            ) {
              setSubmitError(true);
              setSubmitErrorMsg(
                "Your session to forgot password is expired. Please try again",
              );
            } else if (error?.response?.data?.message === "INVALID_ACTION") {
              setSubmitError(true);
              setSubmitErrorMsg("Inavalid action");
            } else if (error?.response?.data?.message) {
              setSubmitError(true);
              setSubmitErrorMsg(error?.response?.data?.message);
            } else {
              setSubmitError(true);
              setSubmitErrorMsg(
                "Something went wrong. Please try again after sometime.",
              );
            }
          } else if (error?.response?.status === 401) {
            if (error?.response?.data?.message === "NOT_ALLOWED") {
              setSubmitError(true);
              setSubmitErrorMsg("not allowed");
            } else if (error?.response?.data?.message) {
              setSubmitError(true);
              setSubmitErrorMsg(error?.response?.data?.message);
            } else {
              setSubmitError(true);
              setSubmitErrorMsg(
                "Something went wrong. Please try again after sometime.",
              );
            }
          } else if (error?.response?.status === 404) {
            if (error?.response?.data?.message === "USER_NOT_FOUND") {
              setSubmitError(true);
              setSubmitErrorMsg("Username not found");
            } else if (error?.response?.data?.message) {
              setSubmitError(true);
              setSubmitErrorMsg(error?.response?.data?.message);
            } else {
              setSubmitError(true);
              setSubmitErrorMsg(
                "Something went wrong. Please try again after sometime.",
              );
            }
          } else {
            setSubmitError(true);
            setSubmitErrorMsg(
              "Something went wrong. Please try again after sometime.",
            );
          }
        });
    }
  };

  const cancelClick = () => {
    setIsLoginPage("Login");
  };

  return (
    <>
      <ScrollView style={styles.scrollStyle}>
        <View style={styles.textStyle}>
          <Text variant="headlineMedium" style={styles.textColorOTP}>
            Update Password
          </Text>
        </View>
        <View style={styles.container}>
          <Input
            onUpdateValue={enteredVal =>
              updateInputValueHandler("password", enteredVal)
            }
            value={password}
            onBlur={passwordBlur}
            isInvalid={passwordRequire || passwordError}
            errorMsg={getpasswordErrorMsg}
            secure={true}
            placeholder="Enter New Password"
            name="lock"
          />
          <Input
            onUpdateValue={enteredVal =>
              updateInputValueHandler("confirmPassword", enteredVal)
            }
            secure={true}
            value={confirmPassword}
            onBlur={confirmPasswordBlur}
            isInvalid={confirmPasswordRequire || confirmPasswordError}
            errorMsg={getConfirmPasswordErrorMsg}
            name="lock"
            placeholder="Enter Confirm Password"
          />
        </View>
        {submitError ? (
          <ScrollView style={styles.errorContainer}>
            <Text style={styles.errorStyle}>{submitErrMsg}</Text>
          </ScrollView>
        ) : (
          <View style={{ padding: "4%" }} />
        )}
      </ScrollView>
      <View style={styles.buttons}>
        <Button
          onPress={!isLoading && submitClick}
          buttonbgColor={styles.buttonbgColor}
          textColor={styles.textColor}>
          Submit{" "}
          {isLoading && (
            <ActivityIndicator
              align="center"
              size={13}
              color={GlobalStyles.colors.eWhite.base}
              style={{ paddingTop: 6 }}
            />
          )}
        </Button>
        <FlatButton onPress={cancelClick} textStyles={styles.flatButtonOtp}>
          Cancel
        </FlatButton>
      </View>
      <View style={styles.captionStyle}>
        <Text style={styles.textColor} variant="bodyMedium">
          Important Instructions{" "}
        </Text>
        <Text style={styles.textColorSmall}>
          {" "}
          1.Passwords are case-sensitive and must contain atleast six
          characters.
        </Text>
        <Text style={styles.textColorSmall}>
          2.Your password should contain atleast one capital letter, one small
          letter, one number and atleast one special character.
        </Text>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  textStyle: {
    alignItems: "center",
    marginTop: "3%",
  },
  textColorOTP: {
    color: GlobalStyles.colors.eWhite.base,
    textAlign: "center",
  },

  container: {
    flex: 1,
    width: "100%",
    // paddingBottom: 10,
    paddingHorizontal: 12,
    elevation: 2,
    marginTop: 3,
    alignContent: "center",
    justifyContent: "center",
  },
  flatButtonOtp: {
    textAlign: "center",
    color: GlobalStyles.colors.eWhite.base,
    textDecorationLine: "underline",
    paddingVertical: "1%",
    paddingHorizontal: 12,
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
  },
  buttons: {
    paddingHorizontal: "10%",
  },

  buttonbgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    paddingVertical: "3%",
    paddingHorizontal: "3%",
    width: "100%",
    alignItems: "center",
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 16,
    fontFamily: "NotoSans-Medium",
  },
  textColorSmall: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 14,
    fontFamily: "NotoSans-Medium",
  },
  errorContainer: {
    width: "92%",
    backgroundColor: GlobalStyles.colors.eDanger.light,
    maxHeight: 48,
    marginLeft: "4%",
    marginBottom: "2%",
    overflow: "scroll",
  },
  errorStyle: {
    paddingVertical: 10,
    paddingHorizontal: 38,
    fontSize: 12,
    color: GlobalStyles.colors.eDanger.dark,
  },
  scrollStyle: {
    height: "100%",
    paddingHorizontal: "2%",
  },
  captionStyle: {
    marginTop: "5%",
    alignContent: "center",
    marginHorizontal: "10%",
  },
});

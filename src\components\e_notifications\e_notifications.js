import React, { useEffect, useState } from "react";
import { ActivityIndicator, StyleSheet, View } from "react-native";
import BlueCard from "../common/blueCard/blueCard";
import { useSelector } from "react-redux";
import { GlobalStyles } from "../app/global-styles";
import NotificationCard from "./view/_card";

export default function Notifications() {
  const widgets = useSelector(
    state => state?.parameter?.parameter?.WidgetLookUp,
  );
  const [childWidget, setChildWidget] = useState([]);
  const [allData, setData] = useState([]);
  const [meterRank, setMeterRank] = useState();
  const [billRank, setBillRank] = useState();
  const [announceRank, setAnnRank] = useState();
  const [meterUnread, setMeterUnread] = useState(0);
  const [billUnread, setBillUnread] = useState(0);
  const [announcementUnread, setAnnouncementUnread] = useState(0);

  useEffect(() => {
    widgets &&
      widgets.map(item =>
        item.widgetCode === "E_SIDEBAR.ALERTS" &&
        item.widgetDefaultValue === "true"
          ? setChildWidget(item.children)
          : null,
      );
  }, [widgets]);
  useEffect(() => {
    if (childWidget) {
      childWidget.map(i => {
        if (
          i.widgetCode === "E_METER_ALERTS" &&
          i.widgetDefaultValue === "true"
        ) {
          setMeterRank(i.widgetRank);
        } else if (
          i.widgetCode === "E_BILL_NOTIFICATION" &&
          i.widgetDefaultValue === "true"
        ) {
          setBillRank(i.widgetRank);
        } else if (
          i.widgetCode === "E_ANNOUNCEMENT" &&
          i.widgetDefaultValue === "true"
        ) {
          setAnnRank(i.widgetRank);
        }
      });
    }
  }, [childWidget]);

  useEffect(() => {
    let titleData = [
      {
        label: "Alerts",
        path: "METER",
        icon: "Exclamationmark-stroke-icon",
        fillIcon: "Exclamationmark-fill-icon",
        widgetRank: meterRank,
        widgetValue: meterRank ? true : false,
      },
      {
        label: "Notifications",
        path: "BILL",
        widgetRank: billRank,
        widgetValue: billRank ? true : false,
        icon: "MyNotifications-stroke-icon",
        fillIcon: "MyNotifications-fill-icon",
      },
      {
        label: "Announcements",
        path: "ANNOUNCEMENT",
        widgetRank: announceRank,
        widgetValue: announceRank ? true : false,
        icon: "Announcement-stroke-icon",
        fillIcon: "Announcement-fill-icon",
      },
    ];
    let tempAllData = [];
    if (titleData.length > 0) {
      titleData.forEach(item => {
        let duplicate;
        tempAllData.forEach(ele => {
          if (item?.path === ele?.path) {
            duplicate = true;
          }
        });
        if (!duplicate) {
          tempAllData.push(item);
        }
      });
      setData(tempAllData.sort((a, b) => a.widgetRank - b.widgetRank));
    }
  }, [meterRank, billRank, announceRank]);

  let name = "MY NOTIFICATIONS";

  return (
    <View style={{ position: "relative" }}>
      {allData.length > 0 ? (
        <>
          <View style={styles.spaceAroundCard}>
            <BlueCard
              title={name}
              data={allData}
              defaultSelected={allData[0]?.path}
              meterUnread={meterUnread}
              billUnread={billUnread}
              announcementUnread={announcementUnread}
            />
          </View>
          <NotificationCard
            meterRank={meterRank}
            billRank={billRank}
            announceRank={announceRank}
            setMeterUnread={setMeterUnread}
            setBillUnread={setBillUnread}
            setAnnouncementUnread={setAnnouncementUnread}
          />
        </>
      ) : (
        <ActivityIndicator color={GlobalStyles.colors.ePrimary.base} />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  spaceAroundCard: {
    width: "94%",
    margin: "4%",
  },
});

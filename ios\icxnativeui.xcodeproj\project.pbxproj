// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		092E3792909C43DAAFD96CE2 /* NotoSans-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B2D07C0BA82B470AA4B7E891 /* NotoSans-Bold.ttf */; };
		0BA97127A5CF4050BDB3F259 /* NotoSans-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8ACAF0E1D26247AA80A3E51F /* NotoSans-MediumItalic.ttf */; };
		0C80B921A6F3F58F76C31292 /* BuildFile in Frameworks */ = {isa = PBXBuildFile; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		1F4B4E6D4D0E449589B0C38E /* NotoSans-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 83CAD68A37434476BBAEFDB8 /* NotoSans-SemiBoldItalic.ttf */; };
		200B445FBAF64E63B62C17F3 /* NotoSans-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4746D470DFE241039AE8CA1A /* NotoSans-Thin.ttf */; };
		259190AB442440A38D954B43 /* NotoSans-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 070F0C1AA5DB4B6C9419FD9F /* NotoSans-SemiBold.ttf */; };
		36435060FA0845B3AA436968 /* NotoSans-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 149858F65B9A488DB80AEDA3 /* NotoSans-LightItalic.ttf */; };
		3F6F60D3CA914C9BAF81CE7C /* NotoSans-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5968B5FCB0E04E1FA0D811BE /* NotoSans-Regular.ttf */; };
		4193FA5ACA834D798947014A /* custom-icons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D73C737828664A76A3B45503 /* custom-icons.ttf */; };
		4F772D3A0997B2E7D5E8A470 /* libPods-icxnativeui-icxnativeui-dev.a in Frameworks */ = {isa = PBXBuildFile; fileRef = A7B5172D5795D8F7B479DC12 /* libPods-icxnativeui-icxnativeui-dev.a */; };
		5A208F9061064135A2F2C2DB /* NotoSans-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = FB8E4944CD964B40882F84D8 /* NotoSans-Light.ttf */; };
		5AFEE2FD1C0249C4B54301DC /* selection.json in Resources */ = {isa = PBXBuildFile; fileRef = ACB330A2E79147FC86CAC534 /* selection.json */; };
		67805C18216AB71A425C9BC1 /* libPods-icxnativeui.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 34E6C6900B17CACE373001E9 /* libPods-icxnativeui.a */; };
		714758BF2A13A87A00343AC4 /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		714758C02A13A87A00343AC4 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		714758C42A13A87A00343AC4 /* FontAwesome.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7183D1D829D3232A00042BB9 /* FontAwesome.ttf */; };
		714758C52A13A87A00343AC4 /* MaterialCommunityIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7183D1D229D192FD00042BB9 /* MaterialCommunityIcons.ttf */; };
		714758C62A13A87A00343AC4 /* MaterialIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7183D1D329D192FD00042BB9 /* MaterialIcons.ttf */; };
		714758C72A13A87A00343AC4 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		714758C82A13A87A00343AC4 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		714758C92A13A87A00343AC4 /* NotoSans-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D9D274730DE840CEA029E1F6 /* NotoSans-Black.ttf */; };
		714758CA2A13A87A00343AC4 /* NotoSans-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 683B0E46BEAD44A28B6CA844 /* NotoSans-BlackItalic.ttf */; };
		714758CB2A13A87A00343AC4 /* NotoSans-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B2D07C0BA82B470AA4B7E891 /* NotoSans-Bold.ttf */; };
		714758CC2A13A87A00343AC4 /* NotoSans-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B1CCA7980C164AC494CEA61C /* NotoSans-BoldItalic.ttf */; };
		714758CD2A13A87A00343AC4 /* NotoSans-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 49C064F03E5D4D89BAC3BAB7 /* NotoSans-ExtraBold.ttf */; };
		714758CE2A13A87A00343AC4 /* NotoSans-ExtraBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 07A52D688C23491CAC79CFF1 /* NotoSans-ExtraBoldItalic.ttf */; };
		714758CF2A13A87A00343AC4 /* NotoSans-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B9A0326DBADF4E64878B88E8 /* NotoSans-ExtraLight.ttf */; };
		714758D02A13A87A00343AC4 /* NotoSans-ExtraLightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CC5AEB8529D941189B518189 /* NotoSans-ExtraLightItalic.ttf */; };
		714758D12A13A87A00343AC4 /* NotoSans-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4D30E638D4064F4EA9FED85D /* NotoSans-Italic.ttf */; };
		714758D22A13A87A00343AC4 /* NotoSans-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = FB8E4944CD964B40882F84D8 /* NotoSans-Light.ttf */; };
		714758D32A13A87A00343AC4 /* NotoSans-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 149858F65B9A488DB80AEDA3 /* NotoSans-LightItalic.ttf */; };
		714758D42A13A87A00343AC4 /* NotoSans-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CB0F786F306E4855BEA41DED /* NotoSans-Medium.ttf */; };
		714758D52A13A87A00343AC4 /* NotoSans-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8ACAF0E1D26247AA80A3E51F /* NotoSans-MediumItalic.ttf */; };
		714758D62A13A87A00343AC4 /* NotoSans-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5968B5FCB0E04E1FA0D811BE /* NotoSans-Regular.ttf */; };
		714758D72A13A87A00343AC4 /* NotoSans-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 070F0C1AA5DB4B6C9419FD9F /* NotoSans-SemiBold.ttf */; };
		714758D82A13A87A00343AC4 /* NotoSans-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 83CAD68A37434476BBAEFDB8 /* NotoSans-SemiBoldItalic.ttf */; };
		714758D92A13A87A00343AC4 /* NotoSans-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4746D470DFE241039AE8CA1A /* NotoSans-Thin.ttf */; };
		714758DA2A13A87A00343AC4 /* NotoSans-ThinItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CCD3C942B692499287896B55 /* NotoSans-ThinItalic.ttf */; };
		714758DB2A13A87A00343AC4 /* custom-icons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D73C737828664A76A3B45503 /* custom-icons.ttf */; };
		714758DC2A13A87A00343AC4 /* selection.json in Resources */ = {isa = PBXBuildFile; fileRef = ACB330A2E79147FC86CAC534 /* selection.json */; };
		716C07762A13BB9200AF001A /* Ionicons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 716C07752A13BB9200AF001A /* Ionicons.ttf */; };
		716C07772A13BB9200AF001A /* Ionicons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 716C07752A13BB9200AF001A /* Ionicons.ttf */; };
		716C07782A13BB9200AF001A /* Ionicons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 716C07752A13BB9200AF001A /* Ionicons.ttf */; };
		716C07792A13BB9200AF001A /* Ionicons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 716C07752A13BB9200AF001A /* Ionicons.ttf */; };
		7179591529FA2FE700766BC9 /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		7179591629FA2FE700766BC9 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		7179591A29FA2FE700766BC9 /* FontAwesome.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7183D1D829D3232A00042BB9 /* FontAwesome.ttf */; };
		7179591B29FA2FE700766BC9 /* MaterialCommunityIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7183D1D229D192FD00042BB9 /* MaterialCommunityIcons.ttf */; };
		7179591C29FA2FE700766BC9 /* MaterialIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7183D1D329D192FD00042BB9 /* MaterialIcons.ttf */; };
		7179591D29FA2FE700766BC9 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		7179591E29FA2FE700766BC9 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		7179591F29FA2FE700766BC9 /* NotoSans-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D9D274730DE840CEA029E1F6 /* NotoSans-Black.ttf */; };
		7179592029FA2FE700766BC9 /* NotoSans-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 683B0E46BEAD44A28B6CA844 /* NotoSans-BlackItalic.ttf */; };
		7179592129FA2FE700766BC9 /* NotoSans-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B2D07C0BA82B470AA4B7E891 /* NotoSans-Bold.ttf */; };
		7179592229FA2FE700766BC9 /* NotoSans-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B1CCA7980C164AC494CEA61C /* NotoSans-BoldItalic.ttf */; };
		7179592329FA2FE700766BC9 /* NotoSans-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 49C064F03E5D4D89BAC3BAB7 /* NotoSans-ExtraBold.ttf */; };
		7179592429FA2FE700766BC9 /* NotoSans-ExtraBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 07A52D688C23491CAC79CFF1 /* NotoSans-ExtraBoldItalic.ttf */; };
		7179592529FA2FE700766BC9 /* NotoSans-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B9A0326DBADF4E64878B88E8 /* NotoSans-ExtraLight.ttf */; };
		7179592629FA2FE700766BC9 /* NotoSans-ExtraLightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CC5AEB8529D941189B518189 /* NotoSans-ExtraLightItalic.ttf */; };
		7179592729FA2FE700766BC9 /* NotoSans-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4D30E638D4064F4EA9FED85D /* NotoSans-Italic.ttf */; };
		7179592829FA2FE700766BC9 /* NotoSans-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = FB8E4944CD964B40882F84D8 /* NotoSans-Light.ttf */; };
		7179592929FA2FE700766BC9 /* NotoSans-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 149858F65B9A488DB80AEDA3 /* NotoSans-LightItalic.ttf */; };
		7179592A29FA2FE700766BC9 /* NotoSans-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CB0F786F306E4855BEA41DED /* NotoSans-Medium.ttf */; };
		7179592B29FA2FE700766BC9 /* NotoSans-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8ACAF0E1D26247AA80A3E51F /* NotoSans-MediumItalic.ttf */; };
		7179592C29FA2FE700766BC9 /* NotoSans-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5968B5FCB0E04E1FA0D811BE /* NotoSans-Regular.ttf */; };
		7179592D29FA2FE700766BC9 /* NotoSans-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 070F0C1AA5DB4B6C9419FD9F /* NotoSans-SemiBold.ttf */; };
		7179592E29FA2FE700766BC9 /* NotoSans-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 83CAD68A37434476BBAEFDB8 /* NotoSans-SemiBoldItalic.ttf */; };
		7179592F29FA2FE700766BC9 /* NotoSans-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4746D470DFE241039AE8CA1A /* NotoSans-Thin.ttf */; };
		7179593029FA2FE700766BC9 /* NotoSans-ThinItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CCD3C942B692499287896B55 /* NotoSans-ThinItalic.ttf */; };
		7179593129FA2FE700766BC9 /* custom-icons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D73C737828664A76A3B45503 /* custom-icons.ttf */; };
		7179593229FA2FE700766BC9 /* selection.json in Resources */ = {isa = PBXBuildFile; fileRef = ACB330A2E79147FC86CAC534 /* selection.json */; };
		7179593F29FA300C00766BC9 /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		7179594029FA300C00766BC9 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		7179594429FA300C00766BC9 /* FontAwesome.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7183D1D829D3232A00042BB9 /* FontAwesome.ttf */; };
		7179594529FA300C00766BC9 /* MaterialCommunityIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7183D1D229D192FD00042BB9 /* MaterialCommunityIcons.ttf */; };
		7179594629FA300C00766BC9 /* MaterialIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7183D1D329D192FD00042BB9 /* MaterialIcons.ttf */; };
		7179594729FA300C00766BC9 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		7179594829FA300C00766BC9 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		7179594929FA300C00766BC9 /* NotoSans-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D9D274730DE840CEA029E1F6 /* NotoSans-Black.ttf */; };
		7179594A29FA300C00766BC9 /* NotoSans-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 683B0E46BEAD44A28B6CA844 /* NotoSans-BlackItalic.ttf */; };
		7179594B29FA300C00766BC9 /* NotoSans-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B2D07C0BA82B470AA4B7E891 /* NotoSans-Bold.ttf */; };
		7179594C29FA300C00766BC9 /* NotoSans-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B1CCA7980C164AC494CEA61C /* NotoSans-BoldItalic.ttf */; };
		7179594D29FA300C00766BC9 /* NotoSans-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 49C064F03E5D4D89BAC3BAB7 /* NotoSans-ExtraBold.ttf */; };
		7179594E29FA300C00766BC9 /* NotoSans-ExtraBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 07A52D688C23491CAC79CFF1 /* NotoSans-ExtraBoldItalic.ttf */; };
		7179594F29FA300C00766BC9 /* NotoSans-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B9A0326DBADF4E64878B88E8 /* NotoSans-ExtraLight.ttf */; };
		7179595029FA300C00766BC9 /* NotoSans-ExtraLightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CC5AEB8529D941189B518189 /* NotoSans-ExtraLightItalic.ttf */; };
		7179595129FA300C00766BC9 /* NotoSans-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4D30E638D4064F4EA9FED85D /* NotoSans-Italic.ttf */; };
		7179595229FA300C00766BC9 /* NotoSans-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = FB8E4944CD964B40882F84D8 /* NotoSans-Light.ttf */; };
		7179595329FA300C00766BC9 /* NotoSans-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 149858F65B9A488DB80AEDA3 /* NotoSans-LightItalic.ttf */; };
		7179595429FA300C00766BC9 /* NotoSans-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CB0F786F306E4855BEA41DED /* NotoSans-Medium.ttf */; };
		7179595529FA300C00766BC9 /* NotoSans-MediumItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 8ACAF0E1D26247AA80A3E51F /* NotoSans-MediumItalic.ttf */; };
		7179595629FA300C00766BC9 /* NotoSans-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 5968B5FCB0E04E1FA0D811BE /* NotoSans-Regular.ttf */; };
		7179595729FA300C00766BC9 /* NotoSans-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 070F0C1AA5DB4B6C9419FD9F /* NotoSans-SemiBold.ttf */; };
		7179595829FA300C00766BC9 /* NotoSans-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 83CAD68A37434476BBAEFDB8 /* NotoSans-SemiBoldItalic.ttf */; };
		7179595929FA300C00766BC9 /* NotoSans-Thin.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4746D470DFE241039AE8CA1A /* NotoSans-Thin.ttf */; };
		7179595A29FA300C00766BC9 /* NotoSans-ThinItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CCD3C942B692499287896B55 /* NotoSans-ThinItalic.ttf */; };
		7179595B29FA300C00766BC9 /* custom-icons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D73C737828664A76A3B45503 /* custom-icons.ttf */; };
		7179595C29FA300C00766BC9 /* selection.json in Resources */ = {isa = PBXBuildFile; fileRef = ACB330A2E79147FC86CAC534 /* selection.json */; };
		7183D1D629D1941E00042BB9 /* MaterialIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7183D1D329D192FD00042BB9 /* MaterialIcons.ttf */; };
		7183D1D729D1942200042BB9 /* MaterialCommunityIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7183D1D229D192FD00042BB9 /* MaterialCommunityIcons.ttf */; };
		7183D1DC29D323A800042BB9 /* FontAwesome.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 7183D1D829D3232A00042BB9 /* FontAwesome.ttf */; };
		734D13ED21A84740B9BF13FC /* NotoSans-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 683B0E46BEAD44A28B6CA844 /* NotoSans-BlackItalic.ttf */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		84A73F75780D410289C1AFC6 /* NotoSans-ThinItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CCD3C942B692499287896B55 /* NotoSans-ThinItalic.ttf */; };
		86DBFFFE471B46B4A3801A3F /* NotoSans-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B9A0326DBADF4E64878B88E8 /* NotoSans-ExtraLight.ttf */; };
		926BD29A851349F88D60694B /* NotoSans-ExtraBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 07A52D688C23491CAC79CFF1 /* NotoSans-ExtraBoldItalic.ttf */; };
		9B2C5E2B64DDEFFD1F9412FD /* libPods-icxnativeui-icxnativeui-qa.a in Frameworks */ = {isa = PBXBuildFile; fileRef = AB87CD9C9B466C87BB91E47D /* libPods-icxnativeui-icxnativeui-qa.a */; };
		9B540B23BE20113B8032F213 /* libPods-icxnativeui-icxnativeui-stage.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 19C7557E52413AC92424DC63 /* libPods-icxnativeui-icxnativeui-stage.a */; };
		9EAC8A69EEFD4D458538A1A2 /* NotoSans-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 4D30E638D4064F4EA9FED85D /* NotoSans-Italic.ttf */; };
		AD382B953C4947BDB5F16BDE /* NotoSans-ExtraLightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CC5AEB8529D941189B518189 /* NotoSans-ExtraLightItalic.ttf */; };
		ADA893389FC94C80BCFA2964 /* NotoSans-ExtraBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 49C064F03E5D4D89BAC3BAB7 /* NotoSans-ExtraBold.ttf */; };
		DCFEEFCD6B044EF881C56E0F /* NotoSans-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = CB0F786F306E4855BEA41DED /* NotoSans-Medium.ttf */; };
		F1757D4A25B7437C983D3CFE /* NotoSans-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = B1CCA7980C164AC494CEA61C /* NotoSans-BoldItalic.ttf */; };
		F3D287966FE34A109036CFB6 /* NotoSans-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = D9D274730DE840CEA029E1F6 /* NotoSans-Black.ttf */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* icxnativeuiTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = icxnativeuiTests.m; sourceTree = "<group>"; };
		070F0C1AA5DB4B6C9419FD9F /* NotoSans-SemiBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "NotoSans-SemiBold.ttf"; path = "../assets/fonts/NotoSans-SemiBold.ttf"; sourceTree = "<group>"; };
		07A52D688C23491CAC79CFF1 /* NotoSans-ExtraBoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "NotoSans-ExtraBoldItalic.ttf"; path = "../assets/fonts/NotoSans-ExtraBoldItalic.ttf"; sourceTree = "<group>"; };
		07BE7FBBA5C0CA547A50539B /* Pods-icxnativeui-icxnativeui-stage.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-icxnativeui-icxnativeui-stage.debug.xcconfig"; path = "Target Support Files/Pods-icxnativeui-icxnativeui-stage/Pods-icxnativeui-icxnativeui-stage.debug.xcconfig"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* icxnativeui.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = icxnativeui.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = icxnativeui/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = icxnativeui/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = icxnativeui/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = icxnativeui/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = icxnativeui/main.m; sourceTree = "<group>"; };
		149858F65B9A488DB80AEDA3 /* NotoSans-LightItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "NotoSans-LightItalic.ttf"; path = "../assets/fonts/NotoSans-LightItalic.ttf"; sourceTree = "<group>"; };
		19C7557E52413AC92424DC63 /* libPods-icxnativeui-icxnativeui-stage.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-icxnativeui-icxnativeui-stage.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		3479C1EF7D3C699236D9BC85 /* Pods-icxnativeui-icxnativeui-dev.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-icxnativeui-icxnativeui-dev.release.xcconfig"; path = "Target Support Files/Pods-icxnativeui-icxnativeui-dev/Pods-icxnativeui-icxnativeui-dev.release.xcconfig"; sourceTree = "<group>"; };
		34E6C6900B17CACE373001E9 /* libPods-icxnativeui.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-icxnativeui.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		4746D470DFE241039AE8CA1A /* NotoSans-Thin.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "NotoSans-Thin.ttf"; path = "../assets/fonts/NotoSans-Thin.ttf"; sourceTree = "<group>"; };
		49C064F03E5D4D89BAC3BAB7 /* NotoSans-ExtraBold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "NotoSans-ExtraBold.ttf"; path = "../assets/fonts/NotoSans-ExtraBold.ttf"; sourceTree = "<group>"; };
		4D30E638D4064F4EA9FED85D /* NotoSans-Italic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "NotoSans-Italic.ttf"; path = "../assets/fonts/NotoSans-Italic.ttf"; sourceTree = "<group>"; };
		5968B5FCB0E04E1FA0D811BE /* NotoSans-Regular.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "NotoSans-Regular.ttf"; path = "../assets/fonts/NotoSans-Regular.ttf"; sourceTree = "<group>"; };
		636AED1C95B473375AEC7846 /* Pods-icxnativeui-icxnativeui-qa.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-icxnativeui-icxnativeui-qa.release.xcconfig"; path = "Target Support Files/Pods-icxnativeui-icxnativeui-qa/Pods-icxnativeui-icxnativeui-qa.release.xcconfig"; sourceTree = "<group>"; };
		683B0E46BEAD44A28B6CA844 /* NotoSans-BlackItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "NotoSans-BlackItalic.ttf"; path = "../assets/fonts/NotoSans-BlackItalic.ttf"; sourceTree = "<group>"; };
		714758E32A13A87A00343AC4 /* icxnativeui-qa.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "icxnativeui-qa.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		714758E42A13A87A00343AC4 /* icxnativeui qa-info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "icxnativeui qa-info.plist"; path = "/Users/<USER>/Projects/Abjyon/icx-native-ui/ios/icxnativeui qa-info.plist"; sourceTree = "<absolute>"; };
		716C07752A13BB9200AF001A /* Ionicons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = Ionicons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/Ionicons.ttf"; sourceTree = "<group>"; };
		7179593929FA2FE700766BC9 /* icxnativeui-dev.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "icxnativeui-dev.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		7179593A29FA2FE700766BC9 /* icxnativeui dev-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "icxnativeui dev-Info.plist"; path = "/Users/<USER>/Projects/Abjayon/icx-native-ui/ios/icxnativeui dev-Info.plist"; sourceTree = "<absolute>"; };
		7179596329FA300C00766BC9 /* icxnativeui-stage.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "icxnativeui-stage.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		7179596429FA300C00766BC9 /* icxnativeui stage-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "icxnativeui stage-Info.plist"; path = "/Users/<USER>/Projects/Abjayon/icx-native-ui/ios/icxnativeui stage-Info.plist"; sourceTree = "<absolute>"; };
		7183D1D229D192FD00042BB9 /* MaterialCommunityIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = MaterialCommunityIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/MaterialCommunityIcons.ttf"; sourceTree = "<group>"; };
		7183D1D329D192FD00042BB9 /* MaterialIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = MaterialIcons.ttf; path = "../node_modules/react-native-vector-icons/Fonts/MaterialIcons.ttf"; sourceTree = "<group>"; };
		7183D1D829D3232A00042BB9 /* FontAwesome.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = FontAwesome.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome.ttf"; sourceTree = "<group>"; };
		7183D1D929D3233400042BB9 /* FontAwesome5_Brands.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = FontAwesome5_Brands.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Brands.ttf"; sourceTree = "<group>"; };
		7183D1DA29D3234000042BB9 /* FontAwesome5_Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = FontAwesome5_Regular.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Regular.ttf"; sourceTree = "<group>"; };
		7183D1DB29D3234600042BB9 /* FontAwesome5_Solid.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; name = FontAwesome5_Solid.ttf; path = "../node_modules/react-native-vector-icons/Fonts/FontAwesome5_Solid.ttf"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = icxnativeui/LaunchScreen.storyboard; sourceTree = "<group>"; };
		83CAD68A37434476BBAEFDB8 /* NotoSans-SemiBoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "NotoSans-SemiBoldItalic.ttf"; path = "../assets/fonts/NotoSans-SemiBoldItalic.ttf"; sourceTree = "<group>"; };
		844158FE2C22E17C0015E6B0 /* main.jsbundle */ = {isa = PBXFileReference; lastKnownFileType = text; path = main.jsbundle; sourceTree = "<group>"; };
		8ACAF0E1D26247AA80A3E51F /* NotoSans-MediumItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "NotoSans-MediumItalic.ttf"; path = "../assets/fonts/NotoSans-MediumItalic.ttf"; sourceTree = "<group>"; };
		9378203630EE362E036409BC /* Pods-icxnativeui-icxnativeui-dev.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-icxnativeui-icxnativeui-dev.debug.xcconfig"; path = "Target Support Files/Pods-icxnativeui-icxnativeui-dev/Pods-icxnativeui-icxnativeui-dev.debug.xcconfig"; sourceTree = "<group>"; };
		99E9852ECE3C8407050AD928 /* Pods-icxnativeui.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-icxnativeui.debug.xcconfig"; path = "Target Support Files/Pods-icxnativeui/Pods-icxnativeui.debug.xcconfig"; sourceTree = "<group>"; };
		A7B5172D5795D8F7B479DC12 /* libPods-icxnativeui-icxnativeui-dev.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-icxnativeui-icxnativeui-dev.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		AB87CD9C9B466C87BB91E47D /* libPods-icxnativeui-icxnativeui-qa.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-icxnativeui-icxnativeui-qa.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		ACB330A2E79147FC86CAC534 /* selection.json */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = selection.json; path = ../assets/fonts/selection.json; sourceTree = "<group>"; };
		AFB5AD617DDE06042C501AF9 /* Pods-icxnativeui-icxnativeui-stage.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-icxnativeui-icxnativeui-stage.release.xcconfig"; path = "Target Support Files/Pods-icxnativeui-icxnativeui-stage/Pods-icxnativeui-icxnativeui-stage.release.xcconfig"; sourceTree = "<group>"; };
		B1CCA7980C164AC494CEA61C /* NotoSans-BoldItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "NotoSans-BoldItalic.ttf"; path = "../assets/fonts/NotoSans-BoldItalic.ttf"; sourceTree = "<group>"; };
		B2D07C0BA82B470AA4B7E891 /* NotoSans-Bold.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "NotoSans-Bold.ttf"; path = "../assets/fonts/NotoSans-Bold.ttf"; sourceTree = "<group>"; };
		B59C31B6BECCE1F1B4A34629 /* Pods-icxnativeui-icxnativeui-qa.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-icxnativeui-icxnativeui-qa.debug.xcconfig"; path = "Target Support Files/Pods-icxnativeui-icxnativeui-qa/Pods-icxnativeui-icxnativeui-qa.debug.xcconfig"; sourceTree = "<group>"; };
		B9A0326DBADF4E64878B88E8 /* NotoSans-ExtraLight.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "NotoSans-ExtraLight.ttf"; path = "../assets/fonts/NotoSans-ExtraLight.ttf"; sourceTree = "<group>"; };
		CB0F786F306E4855BEA41DED /* NotoSans-Medium.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "NotoSans-Medium.ttf"; path = "../assets/fonts/NotoSans-Medium.ttf"; sourceTree = "<group>"; };
		CC5AEB8529D941189B518189 /* NotoSans-ExtraLightItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "NotoSans-ExtraLightItalic.ttf"; path = "../assets/fonts/NotoSans-ExtraLightItalic.ttf"; sourceTree = "<group>"; };
		CCD3C942B692499287896B55 /* NotoSans-ThinItalic.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "NotoSans-ThinItalic.ttf"; path = "../assets/fonts/NotoSans-ThinItalic.ttf"; sourceTree = "<group>"; };
		D73C737828664A76A3B45503 /* custom-icons.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "custom-icons.ttf"; path = "../assets/fonts/custom-icons.ttf"; sourceTree = "<group>"; };
		D9D274730DE840CEA029E1F6 /* NotoSans-Black.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "NotoSans-Black.ttf"; path = "../assets/fonts/NotoSans-Black.ttf"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		F9B8AE0C1D8F5B1822746531 /* Pods-icxnativeui.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-icxnativeui.release.xcconfig"; path = "Target Support Files/Pods-icxnativeui/Pods-icxnativeui.release.xcconfig"; sourceTree = "<group>"; };
		FB8E4944CD964B40882F84D8 /* NotoSans-Light.ttf */ = {isa = PBXFileReference; explicitFileType = undefined; fileEncoding = 9; includeInIndex = 0; lastKnownFileType = unknown; name = "NotoSans-Light.ttf"; path = "../assets/fonts/NotoSans-Light.ttf"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0C80B921A6F3F58F76C31292 /* BuildFile in Frameworks */,
				67805C18216AB71A425C9BC1 /* libPods-icxnativeui.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		714758C12A13A87A00343AC4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9B2C5E2B64DDEFFD1F9412FD /* libPods-icxnativeui-icxnativeui-qa.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7179591729FA2FE700766BC9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4F772D3A0997B2E7D5E8A470 /* libPods-icxnativeui-icxnativeui-dev.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7179594129FA300C00766BC9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9B540B23BE20113B8032F213 /* libPods-icxnativeui-icxnativeui-stage.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* icxnativeuiTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* icxnativeuiTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = icxnativeuiTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* icxnativeui */ = {
			isa = PBXGroup;
			children = (
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
			);
			name = icxnativeui;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				34E6C6900B17CACE373001E9 /* libPods-icxnativeui.a */,
				A7B5172D5795D8F7B479DC12 /* libPods-icxnativeui-icxnativeui-dev.a */,
				AB87CD9C9B466C87BB91E47D /* libPods-icxnativeui-icxnativeui-qa.a */,
				19C7557E52413AC92424DC63 /* libPods-icxnativeui-icxnativeui-stage.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				844158FE2C22E17C0015E6B0 /* main.jsbundle */,
				13B07FAE1A68108700A75B9A /* icxnativeui */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* icxnativeuiTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				868E7EA738874ADAA23F01FC /* Resources */,
				7179593A29FA2FE700766BC9 /* icxnativeui dev-Info.plist */,
				7179596429FA300C00766BC9 /* icxnativeui stage-Info.plist */,
				714758E42A13A87A00343AC4 /* icxnativeui qa-info.plist */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* icxnativeui.app */,
				7179593929FA2FE700766BC9 /* icxnativeui-dev.app */,
				7179596329FA300C00766BC9 /* icxnativeui-stage.app */,
				714758E32A13A87A00343AC4 /* icxnativeui-qa.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		868E7EA738874ADAA23F01FC /* Resources */ = {
			isa = PBXGroup;
			children = (
				716C07752A13BB9200AF001A /* Ionicons.ttf */,
				7183D1DB29D3234600042BB9 /* FontAwesome5_Solid.ttf */,
				7183D1DA29D3234000042BB9 /* FontAwesome5_Regular.ttf */,
				7183D1D929D3233400042BB9 /* FontAwesome5_Brands.ttf */,
				7183D1D829D3232A00042BB9 /* FontAwesome.ttf */,
				7183D1D229D192FD00042BB9 /* MaterialCommunityIcons.ttf */,
				7183D1D329D192FD00042BB9 /* MaterialIcons.ttf */,
				D9D274730DE840CEA029E1F6 /* NotoSans-Black.ttf */,
				683B0E46BEAD44A28B6CA844 /* NotoSans-BlackItalic.ttf */,
				B2D07C0BA82B470AA4B7E891 /* NotoSans-Bold.ttf */,
				B1CCA7980C164AC494CEA61C /* NotoSans-BoldItalic.ttf */,
				49C064F03E5D4D89BAC3BAB7 /* NotoSans-ExtraBold.ttf */,
				07A52D688C23491CAC79CFF1 /* NotoSans-ExtraBoldItalic.ttf */,
				B9A0326DBADF4E64878B88E8 /* NotoSans-ExtraLight.ttf */,
				CC5AEB8529D941189B518189 /* NotoSans-ExtraLightItalic.ttf */,
				4D30E638D4064F4EA9FED85D /* NotoSans-Italic.ttf */,
				FB8E4944CD964B40882F84D8 /* NotoSans-Light.ttf */,
				149858F65B9A488DB80AEDA3 /* NotoSans-LightItalic.ttf */,
				CB0F786F306E4855BEA41DED /* NotoSans-Medium.ttf */,
				8ACAF0E1D26247AA80A3E51F /* NotoSans-MediumItalic.ttf */,
				5968B5FCB0E04E1FA0D811BE /* NotoSans-Regular.ttf */,
				070F0C1AA5DB4B6C9419FD9F /* NotoSans-SemiBold.ttf */,
				83CAD68A37434476BBAEFDB8 /* NotoSans-SemiBoldItalic.ttf */,
				4746D470DFE241039AE8CA1A /* NotoSans-Thin.ttf */,
				CCD3C942B692499287896B55 /* NotoSans-ThinItalic.ttf */,
				D73C737828664A76A3B45503 /* custom-icons.ttf */,
				ACB330A2E79147FC86CAC534 /* selection.json */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				99E9852ECE3C8407050AD928 /* Pods-icxnativeui.debug.xcconfig */,
				F9B8AE0C1D8F5B1822746531 /* Pods-icxnativeui.release.xcconfig */,
				9378203630EE362E036409BC /* Pods-icxnativeui-icxnativeui-dev.debug.xcconfig */,
				3479C1EF7D3C699236D9BC85 /* Pods-icxnativeui-icxnativeui-dev.release.xcconfig */,
				B59C31B6BECCE1F1B4A34629 /* Pods-icxnativeui-icxnativeui-qa.debug.xcconfig */,
				636AED1C95B473375AEC7846 /* Pods-icxnativeui-icxnativeui-qa.release.xcconfig */,
				07BE7FBBA5C0CA547A50539B /* Pods-icxnativeui-icxnativeui-stage.debug.xcconfig */,
				AFB5AD617DDE06042C501AF9 /* Pods-icxnativeui-icxnativeui-stage.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* icxnativeui */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "icxnativeui" */;
			buildPhases = (
				E101CE2BE418541E81300E9E /* [CP] Check Pods Manifest.lock */,
				FD10A7F022414F080027D42C /* Start Packager */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				A56A88039273D4A7DD78ABDA /* [CP] Embed Pods Frameworks */,
				9A86D4BC84769D9017C63480 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = icxnativeui;
			productName = icxnativeui;
			productReference = 13B07F961A680F5B00A75B9A /* icxnativeui.app */;
			productType = "com.apple.product-type.application";
		};
		714758BB2A13A87A00343AC4 /* icxnativeui-qa */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 714758E02A13A87A00343AC4 /* Build configuration list for PBXNativeTarget "icxnativeui-qa" */;
			buildPhases = (
				E8C7ECBA8EAA6D02B3A323C3 /* [CP] Check Pods Manifest.lock */,
				714758BD2A13A87A00343AC4 /* Start Packager */,
				714758BE2A13A87A00343AC4 /* Sources */,
				714758C12A13A87A00343AC4 /* Frameworks */,
				714758C32A13A87A00343AC4 /* Resources */,
				714758DD2A13A87A00343AC4 /* Bundle React Native code and images */,
				DFF17FFF48BB52AA63E6D9E4 /* [CP] Embed Pods Frameworks */,
				408E13B71452EFD6976115AC /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "icxnativeui-qa";
			productName = icxnativeui;
			productReference = 714758E32A13A87A00343AC4 /* icxnativeui-qa.app */;
			productType = "com.apple.product-type.application";
		};
		7179591129FA2FE700766BC9 /* icxnativeui-dev */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7179593629FA2FE700766BC9 /* Build configuration list for PBXNativeTarget "icxnativeui-dev" */;
			buildPhases = (
				FE7AC8B1B927E15133126272 /* [CP] Check Pods Manifest.lock */,
				7179591329FA2FE700766BC9 /* Start Packager */,
				7179591429FA2FE700766BC9 /* Sources */,
				7179591729FA2FE700766BC9 /* Frameworks */,
				7179591929FA2FE700766BC9 /* Resources */,
				7179593329FA2FE700766BC9 /* Bundle React Native code and images */,
				00E343CD40B92FD692281444 /* [CP] Embed Pods Frameworks */,
				AFE33553DC8B668DF22416BD /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "icxnativeui-dev";
			productName = icxnativeui;
			productReference = 7179593929FA2FE700766BC9 /* icxnativeui-dev.app */;
			productType = "com.apple.product-type.application";
		};
		7179593B29FA300C00766BC9 /* icxnativeui-stage */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7179596029FA300C00766BC9 /* Build configuration list for PBXNativeTarget "icxnativeui-stage" */;
			buildPhases = (
				F4877976EF0811A1692916F3 /* [CP] Check Pods Manifest.lock */,
				7179593D29FA300C00766BC9 /* Start Packager */,
				7179593E29FA300C00766BC9 /* Sources */,
				7179594129FA300C00766BC9 /* Frameworks */,
				7179594329FA300C00766BC9 /* Resources */,
				7179595D29FA300C00766BC9 /* Bundle React Native code and images */,
				D3B792ECD4EA24C326F2A476 /* [CP] Embed Pods Frameworks */,
				064E6B53BF15EDBBE9A55F25 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "icxnativeui-stage";
			productName = icxnativeui;
			productReference = 7179596329FA300C00766BC9 /* icxnativeui-stage.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						DevelopmentTeam = 55LFX39W35;
						LastSwiftMigration = 1120;
						ProvisioningStyle = Manual;
					};
					714758BB2A13A87A00343AC4 = {
						DevelopmentTeam = 55LFX39W35;
						ProvisioningStyle = Manual;
					};
					7179591129FA2FE700766BC9 = {
						DevelopmentTeam = 55LFX39W35;
						ProvisioningStyle = Manual;
					};
					7179593B29FA300C00766BC9 = {
						DevelopmentTeam = 55LFX39W35;
						ProvisioningStyle = Manual;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "icxnativeui" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* icxnativeui */,
				7179591129FA2FE700766BC9 /* icxnativeui-dev */,
				7179593B29FA300C00766BC9 /* icxnativeui-stage */,
				714758BB2A13A87A00343AC4 /* icxnativeui-qa */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				716C07762A13BB9200AF001A /* Ionicons.ttf in Resources */,
				7183D1DC29D323A800042BB9 /* FontAwesome.ttf in Resources */,
				7183D1D729D1942200042BB9 /* MaterialCommunityIcons.ttf in Resources */,
				7183D1D629D1941E00042BB9 /* MaterialIcons.ttf in Resources */,
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				F3D287966FE34A109036CFB6 /* NotoSans-Black.ttf in Resources */,
				734D13ED21A84740B9BF13FC /* NotoSans-BlackItalic.ttf in Resources */,
				092E3792909C43DAAFD96CE2 /* NotoSans-Bold.ttf in Resources */,
				F1757D4A25B7437C983D3CFE /* NotoSans-BoldItalic.ttf in Resources */,
				ADA893389FC94C80BCFA2964 /* NotoSans-ExtraBold.ttf in Resources */,
				926BD29A851349F88D60694B /* NotoSans-ExtraBoldItalic.ttf in Resources */,
				86DBFFFE471B46B4A3801A3F /* NotoSans-ExtraLight.ttf in Resources */,
				AD382B953C4947BDB5F16BDE /* NotoSans-ExtraLightItalic.ttf in Resources */,
				9EAC8A69EEFD4D458538A1A2 /* NotoSans-Italic.ttf in Resources */,
				5A208F9061064135A2F2C2DB /* NotoSans-Light.ttf in Resources */,
				36435060FA0845B3AA436968 /* NotoSans-LightItalic.ttf in Resources */,
				DCFEEFCD6B044EF881C56E0F /* NotoSans-Medium.ttf in Resources */,
				0BA97127A5CF4050BDB3F259 /* NotoSans-MediumItalic.ttf in Resources */,
				3F6F60D3CA914C9BAF81CE7C /* NotoSans-Regular.ttf in Resources */,
				259190AB442440A38D954B43 /* NotoSans-SemiBold.ttf in Resources */,
				1F4B4E6D4D0E449589B0C38E /* NotoSans-SemiBoldItalic.ttf in Resources */,
				200B445FBAF64E63B62C17F3 /* NotoSans-Thin.ttf in Resources */,
				84A73F75780D410289C1AFC6 /* NotoSans-ThinItalic.ttf in Resources */,
				4193FA5ACA834D798947014A /* custom-icons.ttf in Resources */,
				5AFEE2FD1C0249C4B54301DC /* selection.json in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		714758C32A13A87A00343AC4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				716C07792A13BB9200AF001A /* Ionicons.ttf in Resources */,
				714758C42A13A87A00343AC4 /* FontAwesome.ttf in Resources */,
				714758C52A13A87A00343AC4 /* MaterialCommunityIcons.ttf in Resources */,
				714758C62A13A87A00343AC4 /* MaterialIcons.ttf in Resources */,
				714758C72A13A87A00343AC4 /* LaunchScreen.storyboard in Resources */,
				714758C82A13A87A00343AC4 /* Images.xcassets in Resources */,
				714758C92A13A87A00343AC4 /* NotoSans-Black.ttf in Resources */,
				714758CA2A13A87A00343AC4 /* NotoSans-BlackItalic.ttf in Resources */,
				714758CB2A13A87A00343AC4 /* NotoSans-Bold.ttf in Resources */,
				714758CC2A13A87A00343AC4 /* NotoSans-BoldItalic.ttf in Resources */,
				714758CD2A13A87A00343AC4 /* NotoSans-ExtraBold.ttf in Resources */,
				714758CE2A13A87A00343AC4 /* NotoSans-ExtraBoldItalic.ttf in Resources */,
				714758CF2A13A87A00343AC4 /* NotoSans-ExtraLight.ttf in Resources */,
				714758D02A13A87A00343AC4 /* NotoSans-ExtraLightItalic.ttf in Resources */,
				714758D12A13A87A00343AC4 /* NotoSans-Italic.ttf in Resources */,
				714758D22A13A87A00343AC4 /* NotoSans-Light.ttf in Resources */,
				714758D32A13A87A00343AC4 /* NotoSans-LightItalic.ttf in Resources */,
				714758D42A13A87A00343AC4 /* NotoSans-Medium.ttf in Resources */,
				714758D52A13A87A00343AC4 /* NotoSans-MediumItalic.ttf in Resources */,
				714758D62A13A87A00343AC4 /* NotoSans-Regular.ttf in Resources */,
				714758D72A13A87A00343AC4 /* NotoSans-SemiBold.ttf in Resources */,
				714758D82A13A87A00343AC4 /* NotoSans-SemiBoldItalic.ttf in Resources */,
				714758D92A13A87A00343AC4 /* NotoSans-Thin.ttf in Resources */,
				714758DA2A13A87A00343AC4 /* NotoSans-ThinItalic.ttf in Resources */,
				714758DB2A13A87A00343AC4 /* custom-icons.ttf in Resources */,
				714758DC2A13A87A00343AC4 /* selection.json in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7179591929FA2FE700766BC9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				716C07772A13BB9200AF001A /* Ionicons.ttf in Resources */,
				7179591A29FA2FE700766BC9 /* FontAwesome.ttf in Resources */,
				7179591B29FA2FE700766BC9 /* MaterialCommunityIcons.ttf in Resources */,
				7179591C29FA2FE700766BC9 /* MaterialIcons.ttf in Resources */,
				7179591D29FA2FE700766BC9 /* LaunchScreen.storyboard in Resources */,
				7179591E29FA2FE700766BC9 /* Images.xcassets in Resources */,
				7179591F29FA2FE700766BC9 /* NotoSans-Black.ttf in Resources */,
				7179592029FA2FE700766BC9 /* NotoSans-BlackItalic.ttf in Resources */,
				7179592129FA2FE700766BC9 /* NotoSans-Bold.ttf in Resources */,
				7179592229FA2FE700766BC9 /* NotoSans-BoldItalic.ttf in Resources */,
				7179592329FA2FE700766BC9 /* NotoSans-ExtraBold.ttf in Resources */,
				7179592429FA2FE700766BC9 /* NotoSans-ExtraBoldItalic.ttf in Resources */,
				7179592529FA2FE700766BC9 /* NotoSans-ExtraLight.ttf in Resources */,
				7179592629FA2FE700766BC9 /* NotoSans-ExtraLightItalic.ttf in Resources */,
				7179592729FA2FE700766BC9 /* NotoSans-Italic.ttf in Resources */,
				7179592829FA2FE700766BC9 /* NotoSans-Light.ttf in Resources */,
				7179592929FA2FE700766BC9 /* NotoSans-LightItalic.ttf in Resources */,
				7179592A29FA2FE700766BC9 /* NotoSans-Medium.ttf in Resources */,
				7179592B29FA2FE700766BC9 /* NotoSans-MediumItalic.ttf in Resources */,
				7179592C29FA2FE700766BC9 /* NotoSans-Regular.ttf in Resources */,
				7179592D29FA2FE700766BC9 /* NotoSans-SemiBold.ttf in Resources */,
				7179592E29FA2FE700766BC9 /* NotoSans-SemiBoldItalic.ttf in Resources */,
				7179592F29FA2FE700766BC9 /* NotoSans-Thin.ttf in Resources */,
				7179593029FA2FE700766BC9 /* NotoSans-ThinItalic.ttf in Resources */,
				7179593129FA2FE700766BC9 /* custom-icons.ttf in Resources */,
				7179593229FA2FE700766BC9 /* selection.json in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7179594329FA300C00766BC9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				716C07782A13BB9200AF001A /* Ionicons.ttf in Resources */,
				7179594429FA300C00766BC9 /* FontAwesome.ttf in Resources */,
				7179594529FA300C00766BC9 /* MaterialCommunityIcons.ttf in Resources */,
				7179594629FA300C00766BC9 /* MaterialIcons.ttf in Resources */,
				7179594729FA300C00766BC9 /* LaunchScreen.storyboard in Resources */,
				7179594829FA300C00766BC9 /* Images.xcassets in Resources */,
				7179594929FA300C00766BC9 /* NotoSans-Black.ttf in Resources */,
				7179594A29FA300C00766BC9 /* NotoSans-BlackItalic.ttf in Resources */,
				7179594B29FA300C00766BC9 /* NotoSans-Bold.ttf in Resources */,
				7179594C29FA300C00766BC9 /* NotoSans-BoldItalic.ttf in Resources */,
				7179594D29FA300C00766BC9 /* NotoSans-ExtraBold.ttf in Resources */,
				7179594E29FA300C00766BC9 /* NotoSans-ExtraBoldItalic.ttf in Resources */,
				7179594F29FA300C00766BC9 /* NotoSans-ExtraLight.ttf in Resources */,
				7179595029FA300C00766BC9 /* NotoSans-ExtraLightItalic.ttf in Resources */,
				7179595129FA300C00766BC9 /* NotoSans-Italic.ttf in Resources */,
				7179595229FA300C00766BC9 /* NotoSans-Light.ttf in Resources */,
				7179595329FA300C00766BC9 /* NotoSans-LightItalic.ttf in Resources */,
				7179595429FA300C00766BC9 /* NotoSans-Medium.ttf in Resources */,
				7179595529FA300C00766BC9 /* NotoSans-MediumItalic.ttf in Resources */,
				7179595629FA300C00766BC9 /* NotoSans-Regular.ttf in Resources */,
				7179595729FA300C00766BC9 /* NotoSans-SemiBold.ttf in Resources */,
				7179595829FA300C00766BC9 /* NotoSans-SemiBoldItalic.ttf in Resources */,
				7179595929FA300C00766BC9 /* NotoSans-Thin.ttf in Resources */,
				7179595A29FA300C00766BC9 /* NotoSans-ThinItalic.ttf in Resources */,
				7179595B29FA300C00766BC9 /* custom-icons.ttf in Resources */,
				7179595C29FA300C00766BC9 /* selection.json in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n \n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		00E343CD40B92FD692281444 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-icxnativeui-icxnativeui-dev/Pods-icxnativeui-icxnativeui-dev-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-icxnativeui-icxnativeui-dev/Pods-icxnativeui-icxnativeui-dev-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-icxnativeui-icxnativeui-dev/Pods-icxnativeui-icxnativeui-dev-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		064E6B53BF15EDBBE9A55F25 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-icxnativeui-icxnativeui-stage/Pods-icxnativeui-icxnativeui-stage-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-icxnativeui-icxnativeui-stage/Pods-icxnativeui-icxnativeui-stage-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-icxnativeui-icxnativeui-stage/Pods-icxnativeui-icxnativeui-stage-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		408E13B71452EFD6976115AC /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-icxnativeui-icxnativeui-qa/Pods-icxnativeui-icxnativeui-qa-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-icxnativeui-icxnativeui-qa/Pods-icxnativeui-icxnativeui-qa-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-icxnativeui-icxnativeui-qa/Pods-icxnativeui-icxnativeui-qa-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		714758BD2A13A87A00343AC4 /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		714758DD2A13A87A00343AC4 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		7179591329FA2FE700766BC9 /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		7179593329FA2FE700766BC9 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		7179593D29FA300C00766BC9 /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		7179595D29FA300C00766BC9 /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		9A86D4BC84769D9017C63480 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-icxnativeui/Pods-icxnativeui-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-icxnativeui/Pods-icxnativeui-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-icxnativeui/Pods-icxnativeui-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		A56A88039273D4A7DD78ABDA /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-icxnativeui/Pods-icxnativeui-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-icxnativeui/Pods-icxnativeui-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-icxnativeui/Pods-icxnativeui-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		AFE33553DC8B668DF22416BD /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-icxnativeui-icxnativeui-dev/Pods-icxnativeui-icxnativeui-dev-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-icxnativeui-icxnativeui-dev/Pods-icxnativeui-icxnativeui-dev-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-icxnativeui-icxnativeui-dev/Pods-icxnativeui-icxnativeui-dev-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		D3B792ECD4EA24C326F2A476 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-icxnativeui-icxnativeui-stage/Pods-icxnativeui-icxnativeui-stage-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-icxnativeui-icxnativeui-stage/Pods-icxnativeui-icxnativeui-stage-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-icxnativeui-icxnativeui-stage/Pods-icxnativeui-icxnativeui-stage-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		DFF17FFF48BB52AA63E6D9E4 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-icxnativeui-icxnativeui-qa/Pods-icxnativeui-icxnativeui-qa-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-icxnativeui-icxnativeui-qa/Pods-icxnativeui-icxnativeui-qa-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-icxnativeui-icxnativeui-qa/Pods-icxnativeui-icxnativeui-qa-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		E101CE2BE418541E81300E9E /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-icxnativeui-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		E8C7ECBA8EAA6D02B3A323C3 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-icxnativeui-icxnativeui-qa-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		F4877976EF0811A1692916F3 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-icxnativeui-icxnativeui-stage-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		FE7AC8B1B927E15133126272 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-icxnativeui-icxnativeui-dev-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		714758BE2A13A87A00343AC4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				714758BF2A13A87A00343AC4 /* AppDelegate.mm in Sources */,
				714758C02A13A87A00343AC4 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7179591429FA2FE700766BC9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7179591529FA2FE700766BC9 /* AppDelegate.mm in Sources */,
				7179591629FA2FE700766BC9 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7179593E29FA300C00766BC9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7179593F29FA300C00766BC9 /* AppDelegate.mm in Sources */,
				7179594029FA300C00766BC9 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 99E9852ECE3C8407050AD928 /* Pods-icxnativeui.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 26;
				DEVELOPMENT_TEAM = 55LFX39W35;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 55LFX39W35;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = icxnativeui/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ImpresaCx;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.business";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = com.abjayon.impresafieldworkdemo;
				PRODUCT_NAME = icxnativeui;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match AppStore com.abjayon.impresafieldworkdemo";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F9B8AE0C1D8F5B1822746531 /* Pods-icxnativeui.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 26;
				DEVELOPMENT_TEAM = 55LFX39W35;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 55LFX39W35;
				INFOPLIST_FILE = icxnativeui/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ImpresaCx;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.business";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = com.abjayon.impresafieldworkdemo;
				PRODUCT_NAME = icxnativeui;
				PROVISIONING_PROFILE = "5045f108-e5b1-4171-9359-d4afdabce37e";
				PROVISIONING_PROFILE_SPECIFIER = "match AppStore com.abjayon.native.ImpresaCxFieldWork";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match AppStore com.abjayon.impresafieldworkdemo";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		714758E12A13A87A00343AC4 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B59C31B6BECCE1F1B4A34629 /* Pods-icxnativeui-icxnativeui-qa.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 5;
				DEVELOPMENT_TEAM = 55LFX39W35;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 55LFX39W35;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "icxnativeui qa-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = ImpresaCx;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = com.abjayon.native.ImpresaCxFieldWork;
				PRODUCT_NAME = "$(TARGET_NAME)";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match Development com.abjayon.native.ImpresaCxFieldWork";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		714758E22A13A87A00343AC4 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 636AED1C95B473375AEC7846 /* Pods-icxnativeui-icxnativeui-qa.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 5;
				DEVELOPMENT_TEAM = 55LFX39W35;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 55LFX39W35;
				INFOPLIST_FILE = "icxnativeui qa-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = ImpresaCx;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = com.abjayon.native.ImpresaCxFieldWork;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "5045f108-e5b1-4171-9359-d4afdabce37e";
				PROVISIONING_PROFILE_SPECIFIER = "match AppStore com.abjayon.native.ImpresaCxFieldWork";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match Development com.abjayon.native.ImpresaCxFieldWork";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		7179593729FA2FE700766BC9 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9378203630EE362E036409BC /* Pods-icxnativeui-icxnativeui-dev.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 5;
				DEVELOPMENT_TEAM = 55LFX39W35;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 55LFX39W35;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "icxnativeui dev-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = ImpresaCx;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = com.abjayon.native.ImpresaCxFieldWork;
				PRODUCT_NAME = "$(TARGET_NAME)";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match Development com.abjayon.native.ImpresaCxFieldWork";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		7179593829FA2FE700766BC9 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3479C1EF7D3C699236D9BC85 /* Pods-icxnativeui-icxnativeui-dev.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 5;
				DEVELOPMENT_TEAM = 55LFX39W35;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 55LFX39W35;
				INFOPLIST_FILE = "icxnativeui dev-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = ImpresaCx;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = com.abjayon.native.ImpresaCxFieldWork;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "5045f108-e5b1-4171-9359-d4afdabce37e";
				PROVISIONING_PROFILE_SPECIFIER = "match AppStore com.abjayon.native.ImpresaCxFieldWork";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match Development com.abjayon.native.ImpresaCxFieldWork";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		7179596129FA300C00766BC9 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 07BE7FBBA5C0CA547A50539B /* Pods-icxnativeui-icxnativeui-stage.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 5;
				DEVELOPMENT_TEAM = 55LFX39W35;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 55LFX39W35;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = "icxnativeui stage-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = ImpresaCx;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.2;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = com.abjayon.native.ImpresaCxFieldWork;
				PRODUCT_NAME = "$(TARGET_NAME)";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match Development com.abjayon.native.ImpresaCxFieldWork";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		7179596229FA300C00766BC9 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AFB5AD617DDE06042C501AF9 /* Pods-icxnativeui-icxnativeui-stage.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 5;
				DEVELOPMENT_TEAM = 55LFX39W35;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 55LFX39W35;
				INFOPLIST_FILE = "icxnativeui stage-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = ImpresaCx;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.2;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				"PRODUCT_BUNDLE_IDENTIFIER[sdk=iphoneos*]" = com.abjayon.native.ImpresaCxFieldWork;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "5045f108-e5b1-4171-9359-d4afdabce37e";
				PROVISIONING_PROFILE_SPECIFIER = "match AppStore com.abjayon.native.ImpresaCxFieldWork";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match Development com.abjayon.native.ImpresaCxFieldWork";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					_LIBCPP_ENABLE_CXX17_REMOVED_UNARY_BINARY_FUNCTION,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 12.4;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "icxnativeui" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		714758E02A13A87A00343AC4 /* Build configuration list for PBXNativeTarget "icxnativeui-qa" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				714758E12A13A87A00343AC4 /* Debug */,
				714758E22A13A87A00343AC4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7179593629FA2FE700766BC9 /* Build configuration list for PBXNativeTarget "icxnativeui-dev" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7179593729FA2FE700766BC9 /* Debug */,
				7179593829FA2FE700766BC9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7179596029FA300C00766BC9 /* Build configuration list for PBXNativeTarget "icxnativeui-stage" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7179596129FA300C00766BC9 /* Debug */,
				7179596229FA300C00766BC9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "icxnativeui" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}

import axios from "axios";
import { config } from "../../../environment";
import AsyncStorage from "@react-native-async-storage/async-storage";

export const UpdateMobileDetails = {
  UpdateMobileService,
  OTPSubmitDetails,
  ResendOTPDetails
};

async function UpdateMobileService(mobileNumber, contactID, accountID) {
  let bearer = await AsyncStorage.getItem("bearer");
  let userName = "";
  if (bearer) {
    bearer = JSON.parse(bearer);
    userName = bearer.userName;
    let APIquery =
      `query{updateMobile(input:{phoneNum:"` +
      mobileNumber +
      `",contactId:"` +
      contactID +
      `"}){isUpdated}}`;
    const flowType = AsyncStorage.setItem("flowType", "UPDATE_MOBILE");
    return new Promise((resolve, reject) => {
      axios({
        url: config.urls.OAUTH_SERVICE_BASE_URL,
        method: "post",
        headers: { "Content-Type": "application/json", accountId: accountID },
        data: {
          query: APIquery,
        },
      })
        .then(function (response) {
          resolve(response.data);
        })

        .catch(function (error) {
          reject(error);
        });
    });
  }
}

async function OTPSubmitDetails(otp) {
  const validateOTPObject = {};
  const flowType = await AsyncStorage.getItem("flowType");
  let bearer = await AsyncStorage.getItem("bearer");
  let userName = "";
  if (bearer) {
    bearer = JSON.parse(bearer);
    userName = bearer.userName;
  }
  validateOTPObject.tenantCode = config.constants.BASE_TENANT_CODE;
  if (flowType === "UPDATE_MOBILE") {
    validateOTPObject.otp = Number(otp);
    validateOTPObject.flowType = flowType;
    let data = await axios
      .request({
        method: "POST",
        url: config.urls.VALIDATEOTP,
        data: validateOTPObject,
      })
      .then((res) => {
        return res;
      })
      .catch((err) => {
        throw err;
      });
    return data;
  }
}

async function ResendOTPDetails(mobile) {
  let bearer = await AsyncStorage.getItem("bearer");
  if (bearer) {
    bearer = JSON.parse(bearer);
    return await axios.post(config.urls.LOGIN_RESENDOTP, {
      flowType: "UPDATE_MOBILE",
    });
  }
}

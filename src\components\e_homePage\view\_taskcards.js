import React, { useState, useEffect, useContext } from "react";
import { Card, Text, Title, Paragraph } from "react-native-paper";
import { StyleSheet, View, TouchableOpacity, Platform } from "react-native";
import { GlobalStyles } from "../../app/global-styles";
import Icon from "../../icon";
import { homeContext } from "../e_home";
import { homeContextWO } from "../e_home_wo";
import { Dimensions } from "react-native";
import { useTranslation } from "react-i18next";
import { config } from "../../../environment";
import { stackContext } from "../../app/get_stack";

const { height, width } = Dimensions.get("window");
const cardWidth = width / 3.45; // Assuming you want 3 cards in a row
const cardHeight = Platform?.OS == "ios" ? height - 752 : height - 850; // Set a fixed height for all cards
export default function TaskCards() {
  const { workModelType } = React.useContext(stackContext);
  let mainData,
    setMarkers,
    taskCount,
    markersCT,
    setMarkersCT,
    markersOT,
    setMarkersOT,
    markersODT,
    setMarkersODT;

  if (workModelType === "WA") {
    ({
      mainData,
      setMarkers,
      taskCount,
      markersCT,
      setMarkersCT,
      markersOT,
      setMarkersOT,
      markersODT,
      setMarkersODT,
    } = useContext(homeContext));
  } else {
    ({
      mainData,
      setMarkers,
      taskCount,
      markersCT,
      setMarkersCT,
      markersOT,
      setMarkersOT,
      markersODT,
      setMarkersODT,
    } = useContext(homeContextWO));
  }

  const { t } = useTranslation();
  const [completedTasks, setCompletedTasks] = React.useState(0);
  const [openTasks, setOpenTasks] = React.useState(0);
  const [overdueTasks, setOverdueTasks] = React.useState(0);

  // useEffect(() => {
  //   if (mainData) {
  //     mainData.forEach(item => {
  //       if (item.name === "Created") {
  //         setOpenTasks(item.count);
  //       }
  //       if (item.name === "OverDue") {
  //         setOverdueTasks(item.count);
  //       }
  //       if (item.name === "Completed") {
  //         setCompletedTasks(item.count);
  //       }
  //       console.log("item", item);
  //     });
  //   }
  // }, [mainData]);

  const completedHandler = option => {
    if (option.name === "Created") {
      setMarkers(markersCT);
    }
    if (option.name === "OverDue") {
      setMarkers(markersODT);
    }
    if (option.name === "Completed") {
      setMarkers(markersOT);
    }
  };

  useEffect(() => {}, [taskCount, markersCT, markersODT, markersOT]);

  const sortedTaskCount = taskCount.sort((a, b) => {
    const order = ["Completed", "Created", "OverDue"];
    return order.indexOf(a.name) - order.indexOf(b.name);
  });

  return (
    <View style={styles.cardContainer}>
      {sortedTaskCount?.map((option, index) => {
        return (
          <>
            <TouchableOpacity
              onPress={() => completedHandler(option)}
              activeOpacity={0.9}
              key={index}>
              <Card style={styles.card}>
                <Card.Content style={styles.cardContent}>
                  <View style={styles.iconTextContainer}>
                    <View
                      style={[
                        styles.iconTextIcon,
                        { backgroundColor: getBackgroundColor(option.name) },
                      ]}>
                      {/* {option?.name === "Created" ? <></> : null} */}
                      <Icon
                        //name="FS-Completed-Tasks-icon"
                        name={getIconName(option.name)}
                        size={25}
                        color={GlobalStyles.colors.eWhite.base}
                        style={{
                          top: 11,
                          right: 0,
                          left: 12,
                        }}
                      />
                    </View>
                    <Paragraph style={styles.iconText}>
                      {option.count}
                    </Paragraph>
                  </View>
                  <Text style={styles.taskText}>
                    {option.name === "Created"
                      ? t("OPEN")
                      : t(option.name.toUpperCase())}{" "}
                    {t("TASKS")}{" "}
                  </Text>
                </Card.Content>
              </Card>
            </TouchableOpacity>
          </>
        );
      })}
    </View>
  );
}

const getIconName = type => {
  // Add your logic to determine the icon name based on the item type
  switch (type) {
    case "Completed":
      return "FS-Completed-Tasks-icon";
    case "Created":
      return "FS-Open-Tasks-icon";
    case "OverDue":
      return "FW-WorkActivities-Overdue-icon";
    default:
      return "default-icon"; // Set a default icon if the type doesn't match any case
  }
};
const getBackgroundColor = name => {
  switch (name) {
    case "Created":
      return GlobalStyles.colors.eTertiary.base;
    case "OverDue":
      return GlobalStyles.colors.eDanger.dark;
    case "Completed":
      return GlobalStyles.colors.eSecondary.base;
    default:
      return GlobalStyles.colors.defaultBackgroundColor;
  }
};
const styles = StyleSheet.create({
  card: {
    height: cardHeight, // Set a fixed height for all cards
    borderRadius: 5,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    width: cardWidth,
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 0px 2px 3px 0px",
    flex: 1,
    marginVertical: 10,
    marginRight: 10,
  },
  cardContent: {
    //flexDirection: "row",
    //justifyContent: "space-between",
  },
  titleCard: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontWeight: "700",
    fontFamily: "NotoSans-Bold",
  },
  cardContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  iconTextContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
  },
  iconTextIcon: {
    height: 70,
    width: 70,
    borderRadius: 100,
    borderWidth: 10,
    borderColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: -16,
    marginTop: -16,
  },
  iconText: {
    flex: 1, // This will make the text take up the available space
    textAlign: "right",
    fontFamily: "NotoSans-SemiBold",
    marginBottom: 10,
    fontSize: 18,
  },
  taskText: {
    color: GlobalStyles.colors.eDark.selected,
    fontSize: 10,
    textAlign: "left",
    fontFamily: "NotoSans-SemiBold",
    marginLeft: -5,
  },
});

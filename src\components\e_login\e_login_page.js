import { useContext, useEffect, useState } from "react";
import { Alert, StyleSheet, View, Image, ImageBackground } from "react-native";
import { GlobalStyles } from "../app/global-styles";
import FlatButton from "../common/_flat_button.js";
import LoginForm from "./view/_login_form";
import { Text } from "react-native-paper";

export default function LoginPage({ onAuthenticate }) {
  const [credentialsInvalid, setCredentialsInvalid] = useState({
    email: false,
    password: false,
  });

  function switchAuthModeHandler() {
    // Todo
  }

  const submitHandler = credentials => {
    let { email, confirmEmail, password, confirmPassword } = credentials;

    email = email.trim();
    password = password.trim();

    const emailIsValid = email.includes("@");
    const passwordIsValid = password.length > 6;
    const emailsAreEqual = email === confirmEmail;
    const passwordsAreEqual = password === confirmPassword;

    if (
      !emailIsValid ||
      !passwordIsValid ||
      (!isLogin && (!emailsAreEqual || !passwordsAreEqual))
    ) {
      Alert.alert("Invalid input", "Please check your entered credentials.");
      setCredentialsInvalid({
        email: !emailIsValid,
        password: !passwordIsValid,
      });
      return;
    }
    onAuthenticate({ email, password });
  };

  return (
    <LoginForm
      onSubmit={submitHandler}
      credentialsInvalid={credentialsInvalid}
    />
  );
}

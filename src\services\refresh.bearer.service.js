import { config } from "../environment";
import axios from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { commonService } from "./common.service";

export const refreshBearerService = {
  refreshBearerToken,
  refreshAuthBearerToken,
};

async function refreshAuthBearerToken() {
  let storedBearer = await AsyncStorage.getItem("authbearer");
  const bearer = JSON.parse(storedBearer);

  if (bearer) {
    await axios
      .post(
        config.urls.REFRESH_BEARER,
        {
          grantType: "refresh_token",
          refreshToken: bearer.refresh_token,
          source: config.confidentialClient.CLIENT_ID_KEYCLOCK,
        },
        {
          headers: {
            clientid: config.confidentialClient.CLIENT_ID_KEYCLOCK,
            clientsecret:
              config.confidentialClient.AUTH_UI_CLIENT_SECRET_KEYCLOCK,
            oldAccessToken: bearer.access_token,
          },
        },
      )
      .then(function (response) {
        bearer.access_token = response.data.access_token;
        bearer.refresh_token = response.data.refresh_token;

        AsyncStorage.setItem("authbearer", JSON.stringify(bearer));
      })
      .catch(function (err) {
        console.log("err", err);
        const status = err.status || (err.response ? err.response.status : 0);
        if (status === 401 || status === 400) {
          commonService.logoutUser();
        }
      });
  }
}

async function refreshBearerToken() {
  let storedBearer = await AsyncStorage.getItem("bearer");
  const bearer = JSON.parse(storedBearer);
  if (bearer) {
    await axios
      .post(
        config.urls.REFRESH_BEARER,
        {
          grantType: "refresh_token",
          refreshToken: bearer.refreshToken,
        },
        {
          headers: {
            oldAccessToken: bearer.acessToken,
          },
        },
      )
      .then(async response => {
        bearer.acessToken = response.data.access_token;
        bearer.refreshToken = response.data.refresh_token;
        await AsyncStorage.setItem("bearer", JSON.stringify(bearer));
      })
      .catch(function (err) {
        const status = err.status || (err.response ? err.response.status : 0);
        if (status === 401 || status === 400 || status === 500) {
          commonService.logoutUser();
        }
      });
  } else {
    commonService.logoutUser();
  }
}

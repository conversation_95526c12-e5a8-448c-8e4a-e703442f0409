// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    // ext {
    //     buildToolsVersion = "33.0.0"
    //     minSdkVersion = 21
    //     compileSdkVersion = 33
    //     targetSdkVersion = 33

    //     // We use NDK 23 which has both M1 support and is the side-by-side NDK version from AGP.
    //     ndkVersion = "23.1.7779620"
    // }
     ext {
        googlePlayServicesVersion = "+" // default: "+"
        firebaseMessagingVersion = "+"
        buildToolsVersion = findProperty('android.buildToolsVersion') ?: '33.0.0'
        minSdkVersion = Integer.parseInt(findProperty('android.minSdkVersion') ?: '29')
        compileSdkVersion = Integer.parseInt(findProperty('android.compileSdkVersion') ?: '33')
        targetSdkVersion = Integer.parseInt(findProperty('android.targetSdkVersion') ?: '33')
        if (findProperty('android.kotlinVersion')) {
            kotlinVersion = findProperty('android.kotlinVersion')
        }
        frescoVersion = findProperty('expo.frescoVersion') ?: '2.5.0'

        if (System.properties['os.arch'] == 'aarch64') {
            // For M1 Users we need to use the NDK 24 which added support for aarch64
            ndkVersion = '24.0.8215888'
        } else {
            // Otherwise we default to the side-by-side NDK version from AGP.
            ndkVersion = '21.4.7075529'
        }
         supportLibVersion = "28.0.0"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:7.3.1")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath 'com.google.gms:google-services:4.3.2'
    }
}

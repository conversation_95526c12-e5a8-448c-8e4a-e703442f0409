import axios from "axios";
import { config } from "../../../../environment";

export const NotificationServiceWO = {
  getAllWorkOrderList,
};

async function getAllWorkOrderList() {
  try {
    let url = config.urls.GET_WORK_ORDERS;
    const response = await axios.get(url, {
      params: {
        pageSize: 2000,
        getWorkActivities: true,
        getWorkOrderResources: true,
        getActivityChecklist: true,
        //orgHierarchyId: 1, - Discom Corporation - Testing Org Id
        //Division 1 - 21 - QA Test 21
        orgHierarchyId: 9,
       // crewResourceId: 2,
      },
    });
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

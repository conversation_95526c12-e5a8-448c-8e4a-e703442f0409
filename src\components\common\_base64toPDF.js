import moment from "moment";
import RNFetchBlob from "rn-fetch-blob";
import { PermissionsAndroid, Alert, Platform } from "react-native";

export const convertBase64ToPDF = async (base64Data, fileType) => {
  try {
    const dateFormat = moment().format("DD-MM-YYYY");
    const path = `${RNFetchBlob.fs.dirs.DocumentDir}/${fileType}_${dateFormat}.pdf`;

    //strip off the base64 part only pull the data
    const pdfData = base64Data.split(";base64,")[1];

    if (Platform.OS === "ios") {
      //write to the path
      await RNFetchBlob.fs.writeFile(path, pdfData, "base64");
      RNFetchBlob.ios.openDocument(path);
      // Replace following with snack barr
    } else if (Platform.OS === "android") {
      await RNFetchBlob.fs.writeFile(path, pdfData, "base64");
      RNFetchBlob.android.actionViewIntent(path, "application/pdf", {
        action: RNFetchBlob.android.actionViewIntent.ACTION_VIEW,
        flags: [
          RNFetchBlob.android.actionViewIntent.FLAG_ACTIVITY_NEW_TASK,
          RNFetchBlob.android.actionViewIntent.FLAG_ACTIVITY_NO_HISTORY,
          RNFetchBlob.android.actionViewIntent.FLAG_ACTIVITY_CLEAR_TOP,
          RNFetchBlob.android.actionViewIntent.FLAG_GRANT_READ_URI_PERMISSION,
        ],
      });
    }
  } catch (err) {
    Alert.alert("Download Error", "Unable to download receipt");
    // TODO: handle error
    // display a snackbar notification saying "Unable to download zero balance certificate"
  }
};

import AsyncStorage from "@react-native-async-storage/async-storage";
import moment from "moment";
import { useEffect, useState } from "react";
import { StyleSheet, Text, View } from "react-native";
import { HelperText, TextInput } from "react-native-paper";
import { useSelector } from "react-redux";
import { GlobalStyles } from "../../app/global-styles";
import Button from "../../common/_button";

export default function FilterPopup({
  startDate,
  endDate,
  readData,
  setNotificatioError,
}) {
  const [startDateRange, setStartDateRange] = useState();
  const [endDateRange, setEndDateRange] = useState();
  const [read, setRead] = useState(false);
  const [unread, setUnread] = useState(false);
  const [startDateErr, setStartDateErr] = useState(false);
  const [endDateErr, setEndDateErr] = useState(false);
  const [last3months, setLast3months] = useState();
  const [last6months, setlast6months] = useState();
  const [last12months, setLast12months] = useState();
  const [previousStart, setPreviousStart] = useState();
  const [prevEnd,setPreviousEnd]=useState();
  const [current_date, setCurrent_date] = useState();
  const [saStart, setSaStart] = useState();
  const [otherFilter, setOtherFilter] = useState("SHOWALL");
  const backendDataFormat = useSelector(
    state => state?.parameter?.parameter?.ParameterLookup?.YEAR_DATE_FORMAT,
  );

  const saStartDate = useSelector(
    state => state?.meterDetails?.meterDetails?.getSaInfo?.saStartDate,
  );

  useEffect(() => {
    if (startDate && endDate) {
      setStartDateRange(moment(startDate).format("DD-MM-YYYY"));
      setEndDateRange(moment(endDate).format("DD-MM-YYYY"));
    }
  }, [startDate, endDate]);

  useEffect(() => {
    if (readData === "READ") {
      setRead(true);
    } else if (readData === "UNREAD") {
      setUnread(true);
    } else {
      setRead(false);
      setUnread(false);
    }
  }, [readData]);

  const startDateValidation = startDateRange => {
    if (startDateRange) {
      let start_date = moment(startDateRange, "DD-MM-YYYY").utcOffset("-07:00");
      let current_date = moment().utcOffset("-07:00");
      if (start_date.isSameOrBefore(current_date)) {
        setStartDateErr(false);
        setNotificatioError(false);
      } else {
        setStartDateErr(true);
        setNotificatioError(true);
      }
    }
  };

  useEffect(() => {
    if (startDateRange && endDateRange) {
      AsyncStorage.setItem("startDate", startDateRange);
      AsyncStorage.setItem("endDate", endDateRange);
    }
  }, [startDateRange, endDateRange, startDate, endDate]);

  const endDateValidation = endDateRange => {
    if (endDateRange) {
      let start_date = moment(startDateRange, "DD-MM-YYYY").utcOffset("-07:00");
      let end_date = moment(endDateRange, "DD-MM-YYYY").utcOffset("-07:00");
      let current_date = moment().utcOffset("-07:00");
      if (
        moment(end_date).isAfter(start_date) &&
        end_date.isSameOrBefore(current_date)
      ) {
        setEndDateErr(false);
        setNotificatioError(false);
      } else {
        setEndDateErr(true);
        setNotificatioError(true);
      }
    } else {
      setEndDateErr(true);
      setNotificatioError(true);
    }
  };

  const unreadPress = () => {
    if (read === true && unread === false) {
      setRead(false);
    }
    setUnread(prev => !prev);
  };

  useEffect(() => {
    if (read === true) {
      AsyncStorage.setItem("read", "READ");
    } else if (unread === true) {
      AsyncStorage.setItem("read", "UNREAD");
    } else if (read === false && unread === false) {
      AsyncStorage.setItem("read", "NA");
    }
  }, [read, unread]);

  const readPress = () => {
    if (unread === true && read === false) {
      setUnread(false);
    }
    setRead(prev => !prev);
  };

  useEffect(() => {
    let last3month = moment().subtract(3, "months").format("DD-MM-YYYY");
    let last6month = moment().subtract(6, "months").format("DD-MM-YYYY");
    let last12month = moment().subtract(1, "year").format("DD-MM-YYYY");
    let previousStart = moment().subtract(1, 'year').startOf('year').format("DD-MM-YYYY");
    let previousEnd = moment().subtract(1, 'year').endOf('year').format("DD-MM-YYYY")
    let current = moment().format("DD-MM-YYYY");
    let sa = moment(saStartDate).format("DD-MM-YYYY");
    setLast3months(last3month);
    setlast6months(last6month);
    setLast12months(last12month);
    setPreviousStart(previousStart);
    setPreviousEnd(previousEnd)
    setCurrent_date(current);
    setSaStart(sa);
  }, [backendDataFormat, saStartDate]);

  useEffect(() => {
    if (
      last3months &&
      last6months &&
      last12months &&
      previousStart &&
      prevEnd &&
      current_date &&
      saStart &&
      endDateRange
    ) {
      if (startDateRange === last3months && endDateRange === current_date) {
        setOtherFilter("LASTTHREEMONTHS");
      } else if (
        startDateRange === last6months &&
        endDateRange === current_date
      ) {
        setOtherFilter("LASTSIXMONTHS");
      } else if (
        startDateRange === last12months &&
        endDateRange === current_date
      ) {
        setOtherFilter("LASTYEAR");
      } else if (
        startDateRange === previousStart &&
        endDateRange === prevEnd
      ) {
        setOtherFilter("PREVIOUSYEAR");
      } else if (startDateRange === saStart && endDateRange === current_date) {
        setOtherFilter("SHOWALL");
      } else {
        setOtherFilter("");
      }
    }
  }, [
    last3months,
    last12months,
    last6months,
    previousStart,
    prevEnd,
    current_date,
    startDateRange,
    endDateRange,
  ]);

  const otherFilterClick = e => {
    if (e === "LASTTHREEMONTHS") {
      setStartDateRange(last3months);
      setEndDateRange(current_date);
    } else if (e === "LASTSIXMONTHS") {
      setStartDateRange(last6months);
      setEndDateRange(current_date);
    } else if (e === "LASTYEAR") {
      setStartDateRange(last12months);
      setEndDateRange(current_date);
    } else if (e === "PREVIOUSYEAR") {
      setStartDateRange(previousStart);
      setEndDateRange(prevEnd);
    } else if (e === "SHOWALL") {
      setStartDateRange(saStart);
      setEndDateRange(current_date);
    }
    setOtherFilter(e);
  };

  return (
    <View style={styles.mainContent}>
      <Text style={styles.textStyle}>Date Range</Text>
      <View style={styles.rowSpace}>
        <View style={{ width: "45%" }}>
          <TextInput
            mode="outlined"
            dense
            outlineColor={GlobalStyles.colors.eFaint.base}
            keyboardType="numeric"
            activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
            placeholder="DD-MM-YYYY"
            maxLength={10}
            style={styles.inputcls}
            value={startDateRange}
            onChangeText={e => {
              if (e.length === 10) {
                startDateValidation(e);
              }
              setStartDateRange(e);
            }}
            error={startDateErr}
            returnKeyType="done"

          />
          <HelperText type="error" visible={startDateErr} padding="none">
            startDate is invalid!
          </HelperText>
        </View>
        <View style={{ width: "45%" }}>
          <TextInput
            mode="outlined"
            dense
            outlineColor={GlobalStyles.colors.eFaint.base}
            keyboardType="numeric"
            activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
            placeholder="DD-MM-YYYY"
            maxLength={10}
            style={styles.inputcls}
            value={endDateRange}
            onChangeText={e => {
              if (e.length === 10) {
                endDateValidation(e);
              }
              setEndDateRange(e);
            }}
            returnKeyType="done"
            error={endDateErr}
          />
          <HelperText type="error" visible={endDateErr} padding="none">
            endDate is invalid!
          </HelperText>
        </View>
      </View>
      <View style={styles.horizontalLine} />
      <View style={styles.content}>
        <View style={styles.rowSpaceButton}>
          <Button
            buttonbgColor={[
              styles.buttonBgColor,
              unread ? styles.selectedColor : styles.unselectedColor,
            ]}
            textColor={styles.buttontextStyle}
            onPress={unreadPress}>
            Unread
          </Button>
          <Button
            buttonbgColor={[
              styles.buttonBgColor,
              read ? styles.selectedColor : styles.unselectedColor,
            ]}
            textColor={styles.buttontextStyle}
            onPress={readPress}>
            Read
          </Button>
        </View>
        <View style={styles.rowSpaceButton}>
          <Button
            buttonbgColor={[
              styles.buttonBgColor,
              otherFilter === "LASTTHREEMONTHS"
                ? styles.selectedColor
                : styles.unselectedColor,
            ]}
            textColor={styles.buttontextStyle}
            onPress={() => otherFilterClick("LASTTHREEMONTHS")}>
            Past 3 Months
          </Button>
          <Button
            buttonbgColor={[
              styles.buttonBgColor,
              otherFilter === "LASTSIXMONTHS"
                ? styles.selectedColor
                : styles.unselectedColor,
            ]}
            textColor={styles.buttontextStyle}
            onPress={() => otherFilterClick("LASTSIXMONTHS")}>
            Past 6 Months
          </Button>
          <Button
            buttonbgColor={[
              styles.buttonBgColor,
              otherFilter === "LASTYEAR"
                ? styles.selectedColor
                : styles.unselectedColor,
            ]}
            textColor={styles.buttontextStyle}
            onPress={() => otherFilterClick("LASTYEAR")}>
            Past 12 Months
          </Button>
        </View>
        <View style={styles.rowSpaceButton}>
          <Button
            buttonbgColor={[
              styles.buttonBgColor,
              otherFilter === "PREVIOUSYEAR"
                ? styles.selectedColor
                : styles.unselectedColor,
            ]}
            textColor={styles.buttontextStyle}
            onPress={() => otherFilterClick("PREVIOUSYEAR")}>
            Previous Year
          </Button>
          <Button
            buttonbgColor={[
              styles.buttonBgColor,
              otherFilter === "SHOWALL"
                ? styles.selectedColor
                : styles.unselectedColor,
            ]}
            textColor={styles.buttontextStyle}
            onPress={() => otherFilterClick("SHOWALL")}>
            Show All
          </Button>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  mainContent: { paddingHorizontal: "5%", height: "100%", paddingBottom: "25%" },
  rowSpace: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  rowSpaceButton: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: "3%",
  },
  inputcls: {
    color: GlobalStyles.colors.eDark.base,
    backgroundColor: GlobalStyles.colors.eBackground.base,
    fontSize: 14,
  },
  textStyle: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    marginBottom: "1%",
  },
  horizontalLine: {
    borderTopWidth: 1,
    borderColor: GlobalStyles.colors.eLight.base,
    width: "100%",
    marginBottom: 25,
  },
  buttonBgColor: {
    marginRight: 10,
    width: "30%",
    paddingHorizontal: "1%",
  },
  unselectedColor: {
    backgroundColor: GlobalStyles.colors.eLight.hover,
  },
  content: {
    marginBottom: "4%",
  },
  buttontextStyle: {
    fontSize: 12,
  },
  selectedColor: {
    backgroundColor: GlobalStyles.colors.ePastelColor2.hover,
  },
});

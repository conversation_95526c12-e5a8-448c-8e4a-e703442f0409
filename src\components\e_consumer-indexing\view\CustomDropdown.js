import React, { useState } from "react";
import { StyleSheet, View } from "react-native";
import { Dropdown } from "react-native-element-dropdown";
import { GlobalStyles } from "../../app/global-styles";
import { useTranslation } from 'react-i18next';

const DropdownComponent = ({
  data,
  disabled = false,
  handleOnChange = () => {},
  selectedValue,
  selectName
}) => {
  const [isFocus, setIsFocus] = useState(false);
  const { t } = useTranslation();
  const handleChange = (value) => {
    handleOnChange(value);
    setIsFocus(false);
  };
  return (
    <View style={styles.container}>
      <Dropdown
        style={[
          styles.dropdown,
          disabled && styles.disabled,
          isFocus && { borderColor: "blue" },
        ]}
        placeholderStyle={styles.placeholderStyle}
        selectedTextStyle={styles.selectedTextStyle}
        inputSearchStyle={styles.inputSearchStyle}
        iconStyle={styles.iconStyle}
        itemTextStyle={styles.itemTextStyle}
        data={data}
        disable={disabled}
        maxHeight={250}
        labelField="label"
        valueField="value"
        placeholder={!isFocus ? selectName : "..."}
        searchPlaceholder={t('SELECT_STATUS')}
        value={selectedValue}
        selectedValue={selectedValue ? selectedValue : ''}
        onFocus={() => setIsFocus(true)}
        onBlur={() => setIsFocus(false)}
        onChange={handleChange}
      />
    </View>
  );
};

export default DropdownComponent;

const styles = StyleSheet.create({
  container: {
    backgroundColor: GlobalStyles.colors.eFaint.base,
  },
  disabled: {
    backgroundColor: GlobalStyles.colors.eFaint.base,
  },
  dropdown: {
    height: 50,
    borderColor: GlobalStyles.colors.eRich.hover,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 8,
  },
  icon: {
    marginRight: 5,
  },
  itemTextStyle: {
    fontSize: 14,
  },
  label: {
    position: "absolute",
    backgroundColor: "white",
    left: 22,
    top: 8,
    zIndex: 999,
    paddingHorizontal: 8,
    fontSize: 10,
  },
  placeholderStyle: {
    fontSize: 14,
  },
  selectedTextStyle: {
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eDark.selected
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 14,
  },
});

import * as React from "react";
import { DataTable, Divider, Text, TextInput } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import { ScrollView } from "react-native-gesture-handler";
import { View } from "react-native";
import { useTranslation } from "react-i18next";

const DirectChargesGrid = data => {
  console.log(data, "NNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNn");
  const { t } = useTranslation();
  const [page, setPage] = React.useState(0);
  const [numberOfItemsPerPageList] = React.useState([2, 3, 4]);
  const [itemsPerPage, onItemsPerPageChange] = React.useState(
    numberOfItemsPerPageList[0],
  );

  const [amount, setAmount] = React.useState("");
  const [remarks, setRemarks] = React.useState("");

  const [items, setItems] = React.useState([]);
  React.useEffect(() => {
    if (data) {
      setItems([]);
      let arr = [];
      data["data"].map(e => {
        if (e?.resourceClass === "OTHER") {
          let obj = {
            StockItemCode: e?.woResourceId,
            Description: e?.resourceType,
            Qantity: `${e?.quantity} ${
              e?.uom?.split("WD-")[1] ? e?.uom?.split("WD-")[1] : "Hrs."
            }`,
          };
          arr.push(obj);
        }
      });
      setItems(arr);
    }
  }, [data]);

  const from = page * itemsPerPage;
  const to = Math.min((page + 1) * itemsPerPage, items.length);

  React.useEffect(() => {
    setPage(0);
  }, [itemsPerPage]);

  return (
    <>
      {/* <View>
        <Text
          style={{
            paddingLeft: 16,
          }}>
          {t('DIRECT_CHARGES')}
        </Text>
      </View> */}
      <DataTable
        style={{
          paddingBottom: 10,
          marginBottom: 10,
        }}>
        <Divider />
        <ScrollView
          horizontal
          persistentScrollbar
          contentContainerStyle={{ flexDirection: "column" }}>
          <DataTable.Header>
            <DataTable.Title style={{ width: 100 }}>
              {t("STOCK_ITEM_ID")}
            </DataTable.Title>
            <DataTable.Title style={{ width: 120 }}>
              {t("DESCRIPTION")}
            </DataTable.Title>
            <DataTable.Title style={{ width: 60 }}>
              {t("QUANTITY")}
            </DataTable.Title>
          </DataTable.Header>
          {/* key: 1,code,type,description,uom,quantity,duration
      category: "Others",
      UOM: "WD-Dollar",
      otherResourceCode: "Hotel",
      quantity: 100 
      remarks: "Hotel charges",*/}

          {items.map(item => (
            <DataTable.Row
              key={item.key}
              style={{ flex: 1, width: "100%", height: 50 }}>
              <DataTable.Cell style={{ width: 100 }}>
                {item.category}
              </DataTable.Cell>
              <DataTable.Cell style={{ width: 120 }}>
                {item.Description}
                {/* <View>
                  <TextInput
                    placeholderTextColor="gray"
                    mode="outlined"
                    value={remarks}
                    enablesReturnKeyAutomatically
                    onChangeText={remarks => setRemarks(remarks)}
                    placeholder=" "
                    outlineColor={GlobalStyles.colors.eDark.hover}
                    activeOutlineColor={GlobalStyles.colors.ePrimary.base}
                    persistentScrollbar={true}
                    style={{
                      height: 25,
                      width: 300,
                    }}
                  />
                </View> */}
              </DataTable.Cell>
              <DataTable.Cell style={{ width: 60, justifyContent: "center", }}>
                {item.Qantity}
              </DataTable.Cell>
            </DataTable.Row>
          ))}
        </ScrollView>
      </DataTable>
    </>
  );
};

export default DirectChargesGrid;

import axios from "axios";
import { config } from "../../../environment";
import { refreshBearerService } from "../../../services/refresh.bearer.service";
import AsyncStorage from "@react-native-async-storage/async-storage";

export const loginService = {
  login,
  loginViaOTP,
  reset,
  resendOTP,
  verifyOTP,
  updatepassword,
};

async function login(username, password) {
  let tenantCode = config.constants.BASE_TENANT_CODE;
  const rawBearer = await AsyncStorage.getItem("authbearer");
  const bearer = JSON.parse(rawBearer);

  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.LOGIN,
        {
          email: username,
          password: password,
        },
        {
          headers: {
            tenantCode: tenantCode,
            accesstoken: bearer?.access_token,
            "x-skip-auth": true,
            isMobile: true,
          },
        },
      )
      .then(response => {
        resolve(response.data);
      })
      .catch(error => {
        console.log(error);
        if (
          error?.response?.status === 401 &&
          error?.response?.data?.message.toLowerCase() === "access denied"
        ) {
          refreshBearerService.refreshAuthBearerToken();
          login(username, password);
        }
        reject(error);
      });
  });
}

async function loginViaOTP(username) {
  let tenantCode = config.constants.BASE_TENANT_CODE;
  const rawBearer = await AsyncStorage.getItem("authbearer");
  const bearer = JSON.parse(rawBearer);
  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.LOGIN_VIA_OTP,
        {
          email: username,
        },
        {
          headers: {
            email: username,
            tenantCode: tenantCode,
            accesstoken: bearer?.access_token,
            "x-skip-auth": true,
          },
        },
      )
      .then(response => {
        resolve(response.data);
      })
      .catch(error => {
        console.log(error);
        if (
          error?.response?.status === 401 &&
          error?.response?.data?.message.toLowerCase() === "access denied"
        ) {
          refreshBearerService.refreshAuthBearerToken();
          loginViaOTP(username);
        }
        reject(error);
      });
  });
}

async function reset(username) {
  let tenantCode = config.constants.BASE_TENANT_CODE;
  const rawBearer = await AsyncStorage.getItem("authbearer");
  const bearer = JSON.parse(rawBearer);

  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.RESET,
        {
          userName: username,
          action: "FORGOT_PASSWORD",
          tenantCode: tenantCode,
        },
        {
          headers: {
            tenantCode: tenantCode,
            accesstoken: bearer?.access_token,
            "x-skip-auth": true,
          },
        },
      )
      .then(response => {
        resolve(response.data);
      })
      .catch(error => {
        console.log(error);
        if (
          error?.response?.status === 401 &&
          error?.response?.data?.message.toLowerCase() === "access denied"
        ) {
          refreshBearerService.refreshAuthBearerToken();
          loginViaOTP(username);
        }
        reject(error);
      });
  });
}

async function resendOTP(username) {
  let tenantCode = config.constants.BASE_TENANT_CODE;
  const rawBearer = await AsyncStorage.getItem("authbearer");
  const bearer = JSON.parse(rawBearer);

  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.RESENDOTP,
        {
          flowType: "FORGOT_PASSWORD",
        },
        {
          headers: {
            username: username,
            tenantCode: tenantCode,
            accesstoken: bearer?.access_token,
            "x-skip-auth": true,
          },
        },
      )
      .then(response => {
        resolve(response.data);
      })
      .catch(error => {
        console.log(error);
        if (
          error?.response?.status === 401 &&
          error?.response?.data?.message.toLowerCase() === "access denied"
        ) {
          refreshBearerService.refreshAuthBearerToken();
          loginViaOTP(username);
        }
        reject(error);
      });
  });
}

async function verifyOTP(otp, username) {
  let tenantCode = config.constants.BASE_TENANT_CODE;
  const rawBearer = await AsyncStorage.getItem("authbearer");
  const bearer = JSON.parse(rawBearer);
  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.VERIFYOTP,
        {
          flowType: "FORGOT_PASSWORD",
          otp: Number(otp),
        },
        {
          headers: {
            username: username,
            tenantCode: tenantCode,
            accesstoken: bearer?.access_token,
            "x-skip-auth": true,
          },
        },
      )
      .then(response => {
        resolve(response.data);
      })
      .catch(error => {
        console.log(error);
        if (
          error?.response?.status === 401 &&
          error?.response?.data?.message.toLowerCase() === "access denied"
        ) {
          refreshBearerService.refreshAuthBearerToken();
          loginViaOTP(username);
        }
        reject(error);
      });
  });
}

async function updatepassword(password, sessionId, username) {
  let tenantCode = config.constants.BASE_TENANT_CODE;
  const rawBearer = await AsyncStorage.getItem("authbearer");
  const bearer = JSON.parse(rawBearer);

  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.RESET,
        {
          action: "UPDATE_PASSWORD",
          newPassword: password,
          sessionId: sessionId,
          tenantCode: tenantCode,
          userName: username,
        },
        {
          headers: {
            tenantCode: tenantCode,
            accesstoken: bearer?.access_token,
            "x-skip-auth": true,
          },
        },
      )
      .then(response => {
        resolve(response.data);
      })
      .catch(error => {
        console.log(error);
        if (
          error?.response?.status === 401 &&
          error?.response?.data?.message.toLowerCase() === "access denied"
        ) {
          refreshBearerService.refreshAuthBearerToken();
          loginViaOTP(username);
        }
        reject(error);
      });
  });
}

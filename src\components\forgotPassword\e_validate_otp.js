import { Di<PERSON><PERSON>, ScrollView, StyleSheet, View } from "react-native";
import { ActivityIndicator, Text } from "react-native-paper";
import Input from "../common/_input";
import Button from "../common/_button";
import FlatButton from "../common/_flat_button";
import { GlobalStyles } from "../app/global-styles";
import React, { useContext, useEffect, useRef, useState } from "react";
import OTPTextInput from "react-native-otp-textinput";
import { registerContext } from "../e_authPages/e_auth_pages";
import { CountdownCircleTimer } from "react-native-countdown-circle-timer";
import { loginService } from "../e_login/model/_login_service";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { config } from "../../environment";
import { registerContextWO } from "../e_authPages/e_auth_pages_wo";
import { stackContext } from "../app/get_stack";

const { height } = Dimensions.get("window");
const width = Dimensions.get("window").width;

export default function ValidateOTP() {
  const { workModelType } = React.useContext(stackContext);
  const { setIsLoginPage } = useContext(
    workModelType === "WA" ? registerContext : registerContextWO,
  );

  const [loading, setLoading] = useState();
  const [isLoading, setisLoading] = useState(false);
  const [countdownTime, setCountDownTime] = useState(30);
  const [showResend, setShowResend] = useState(false);
  const [showTimer, setShowTimer] = useState(true);
  const [error, setError] = useState(false);
  const [errorMSG, setErrorMSG] = useState(false);
  const [disableButton, setDisableButton] = useState(true);
  const [disableResend, setDisableResend] = useState(true);
  const [key, setKey] = useState(0);
  const [OTP, setOTP] = useState();
  const [reachedMax, setReachedMax] = useState(false);
  const [isPlaying, setIsPlaying] = useState(true);
  const { height } = Dimensions.get("window");
  const [timerCount, setTimer] = useState();
  const [timerNeed, setTimerNeed] = useState(false);
  const [timeErrorMSG, setTimeErrorMSG] = useState(false);
  const [email, setEmail] = useState();

  useEffect(() => {
    AsyncStorage.getItem("email").then(res => setEmail(res));
  }, [AsyncStorage.getItem("email")]);

  let otpInput = useRef(null);
  useEffect(() => {
    let interval = setInterval(() => {
      setTimer(lastTimerCount => {
        lastTimerCount <= 1 && clearInterval(interval);
        return lastTimerCount - 1;
      });
    }, 60000);
    return () => clearInterval();
  }, []);

  useEffect(() => {
    if (timerCount === 0) {
    }
  }, timerCount);
  const resendOTP = () => {
    setShowTimer(true);
    setIsPlaying(true);
    setDisableResend(true);
    setError(false);
    loginService
      .resendOTP(email)
      .then(res => {
        setOTP();
        if (res?.data) {
          setLoading(false);
          otpInput = null;
        }
      })
      .catch(error => {
        if (error.response.status === 500) {
          setError(true);
          setErrorMSG(error.response.data.message);
          setShowResend(false);
        }
        if (error.response.status === 400) {
          setError(true);
          setErrorMSG(error.response.data.message);
        }
        if (error.response.status === 403) {
          setError(true);
          setReachedMax(true);
          setErrorMSG("You have reached the maximum number of OTP attempts");
          setDisableResend(true);
          setIsPlaying(false);
          setShowTimer(false);
        }
        setLoading(false);
      });
  };

  const verifyOTP = () => {
    setisLoading(true);
    if (OTP.length !== 0) {
      loginService
        .verifyOTP(OTP, email)
        .then(res => {
          setisLoading(false);
          if (res) {
            let sessionId = res?.sessionId
              ? res?.sessionId
              : res?.data?.sessionId;
            AsyncStorage.setItem("sessionId", sessionId);
            setIsLoginPage("UpdatePassword");
          }
        })
        .catch(error => {
          setisLoading(false);
          if (error.response.data.statusCode === 409) {
            setError(true);
            setErrorMSG(error.response.data.message);
          }
          if (error.response.status === 400) {
            setError(true);
            setErrorMSG(error.response.data.message);
            setShowResend(false);
          }
          if (error.response.status === 403) {
            setError(true);
            setTimerNeed(true);
            setErrorMSG("");
            setTimeErrorMSG(
              "Account is blocked. Please try again after " +
                timerCount +
                " minutes",
            );
            setDisableResend(true);
            setTimer(15);
            setDisableButton(true);
            setShowResend(false);
          }
        });
    }
  };

  const countDown = totalElapsedTime => {
    if (!reachedMax) {
      setKey(prevKey => prevKey + 1);
      setIsPlaying(false);
      if (timerNeed) {
        setDisableResend(true);
      } else {
        setDisableResend(false);
      }
      setShowTimer(false);
    } else {
      setIsPlaying(false);
      setShowTimer(false);
    }

    // setKey(prevKey => prevKey +1)
    // setShowTimer(false);
    // setDisable(false);
    // setIsPlaying(false)
  };

  const cancelClick = () => {
    setIsLoginPage("Login");
  };

  const handleTextChange = input => {
    setError(false);
    setErrorMSG("");
    setOTP(input);
    if (input.length === 6) {
      setDisableButton(false);
    } else {
      setDisableButton(true);
    }
  };

  return (
    <>
      <ScrollView style={styles.scrollStyle}>
        <View style={styles.textStyle}>
          <Text variant="headlineMedium" style={styles.textColorOTP}>
            Verification Code
          </Text>
        </View>
        <View style={styles.textStyle}>
          <Text variant="bodyLarge" style={styles.textColorOTP}>
            Enter OTP
          </Text>
        </View>
        <View style={styles.container}>
          <OTPTextInput
            containerStyle={styles.textInputContainer}
            textInputStyle={styles.roundedTextInput}
            inputCellLength={1}
            inputCount={6}
            ref={e => (otpInput = e)}
            tintColor={GlobalStyles.colors.ePrimary.hover}
            offTintColor={GlobalStyles.colors.ePrimary.hover}
            handleTextChange={input => handleTextChange(input)}
            returnKeyType="done"
          />
        </View>
        {showTimer ? (
          <>
            <View style={styles.contentSub}>
              <Text style={styles.textColorOTP}>Time Remaining</Text>
            </View>
            <View
              style={[
                styles.contentSubText,
                !showTimer ? styles.contentSubText2 : null,
              ]}>
              <CountdownCircleTimer
                isPlaying={isPlaying}
                duration={countdownTime}
                colors={["#fffd8d", "#A30000", "#A30000", "#F7B801"]}
                key={key}
                colorsTime={[7, 5, 2, 0]}
                size={50}
                strokeWidth={5}
                onComplete={totalElapsedTime => countDown(totalElapsedTime)}
                //      onComplete={() =>{
                //       setKey(prevKey => prevKey +1)
                //       setIsPlaying(false)
                //  }}
              >
                {({ remainingTime }) => (
                  <>
                    <Text style={styles.textColorOTP}>00:{remainingTime}</Text>
                  </>
                )}
              </CountdownCircleTimer>
            </View>
          </>
        ) : timerCount !== 0 ? (
          <View style={styles.contentFlex}>
            <Text style={styles.textColorOTP}>
              Didn’t receive OTP?{" "}
              {loading ? (
                <ActivityIndicator
                  size="small"
                  color={GlobalStyles.colors.ePrimary.base}
                />
              ) : null}
            </Text>
            <FlatButton
              textStyles={
                disableResend ? styles.disableLike : styles.textColorOTP
              }
              onPress={resendOTP}
              disable={disableResend}>
              RESEND OTP
            </FlatButton>
          </View>
        ) : null}
        {error ? (
          <>
            <View style={styles.errorContainer}>
              {timerNeed && timerCount !== 0 ? (
                <Text style={styles.errorStyle}>
                  {" "}
                  Account is blocked. Please try again after {timerCount}{" "}
                  minutes
                </Text>
              ) : (
                <Text style={styles.errorStyle}>{errorMSG}</Text>
              )}
            </View>
          </>
        ) : null}
      </ScrollView>
      <View style={styles.buttons}>
        {timerCount === 0 ? (
          <Button
            onPress={cancel}
            buttonbgColor={styles.buttonbgColor}
            textColor={styles.textColor}>
            Cancel
          </Button>
        ) : (
          <Button
            onPress={!isLoading && verifyOTP}
            buttonbgColor={
              disableButton ? styles.disableStyle : styles.buttonbgColor
            }
            textColor={
              disableButton ? styles.disableTextColor : styles.textColor
            }
            disabled={disableButton}>
            Verify{" "}
            {isLoading && (
              <ActivityIndicator
                align="center"
                size={13}
                color={GlobalStyles.colors.eWhite.base}
                style={{ paddingTop: 6 }}
              />
            )}
          </Button>
        )}
      </View>
      <FlatButton onPress={cancelClick} textStyles={styles.flatButtonOtp}>
        Cancel
      </FlatButton>
    </>
  );
}

const styles = StyleSheet.create({
  textInputContainer: {
    marginBottom: 20,
  },
  roundedTextInput: {
    borderRadius: 2,
    borderTopWidth: 2,
    borderLeftWidth: 2,
    borderRightWidth: 2,
    borderBottomWidth: 3,
    width: width / 9,
    height: height / 13,
    backgroundColor: GlobalStyles.colors.eWhite.base,
  },
  textStyle: {
    alignItems: "center",
    marginTop: "3%",
  },
  textColorOTP: {
    color: GlobalStyles.colors.eWhite.base,
    textAlign: "center",
  },
  container: {
    width: "100%",
    // paddingBottom: 10,
    paddingHorizontal: 12,
    marginTop: "4%",
    alignContent: "center",
    justifyContent: "center",
  },
  contentSubText: {
    marginVertical: 10,
    flexDirection: "row",
    justifyContent: "center",
  },
  contentSubText2: {
    marginVertical: 0,
    flexDirection: "row",
    justifyContent: "center",
  },

  flatButtonOtp: {
    color: GlobalStyles.colors.eWhite.base,
    textDecorationLine: "underline",
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
    textAlign: "center",
    marginTop: "3%",
  },
  buttons: {
    paddingHorizontal: "10%",
    alignItems: "center",
    marginTop: "5%",
  },
  buttonbgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    paddingHorizontal: "12%",
    alignItems: "center",
  },
  disableStyle: {
    backgroundColor: GlobalStyles.colors.eLight.selected,
    color: GlobalStyles.colors.eBlack.base,
    paddingHorizontal: "12%",
    alignItems: "center",
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 16,
    fontFamily: "NotoSans-Medium",
  },
  disableTextColor: {
    color: GlobalStyles.colors.eMedium.base,
    fontSize: 16,
    fontFamily: "NotoSans-Medium",
  },

  errorContainer: {
    width: "92%",
    backgroundColor: GlobalStyles.colors.eDanger.light,
    marginBottom: "2%",
    marginHorizontal: "4%",
    // overflow: "scroll",
  },
  errorStyle: {
    paddingVertical: 10,
    paddingHorizontal: "2%",
    fontSize: 12,
    color: GlobalStyles.colors.eDanger.dark,
  },
  scrollStyle: {
    height: "100%",
    paddingHorizontal: "2%",
    marginTop: "3%",
  },
  captionStyle: {
    marginTop: "5%",
    paddingHorizontal: "10%",
  },
  contentFlex: {
    textAlign: "center",
    transform: "underline",
  },
  disableLike: {
    textAlign: "center",
    color: GlobalStyles.colors.eLight.base,
  },
  contentSub: {
    justifyContent: "center",
  },
});

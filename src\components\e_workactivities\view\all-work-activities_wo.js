import {
  StyleSheet,
  View,
  Text,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import { ScrollView } from "react-native-gesture-handler";
import { GlobalStyles } from "../../app/global-styles";
import Icon from "../../icon";
import FontAwesome5Icon from "react-native-vector-icons/FontAwesome";
import { useDispatch, useSelector } from "react-redux";
import {
  setActivities,
  setCurrentActivity,
} from "../../../redux/slices/activitySlices";
import { Switch, TouchableRipple, Card } from "react-native-paper";
import React, { useEffect, useState, useContext, useMemo } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  DATE_FORMATS,
  ROUTES,
  WORK_ACTIVITY_STATUS,
} from "../../common/constants";
import _ from "lodash";
import moment from "moment/moment";
import { useIsFocused } from "@react-navigation/native";
import { useNavigation } from "@react-navigation/native";
import { servicePath } from "../../../redux/slices/servicePath";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import { workOrderContext } from "../e_workactivities";
import { AddressTextFromLatLong } from "../../e_homePage/view/_today_work-order-list";
import Button from "../../common/_button";
import { WorkOrderService } from "../model/work-order-service";
import { useTranslation } from "react-i18next";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { config } from "../../../environment";
import { WorkOrderServiceWO } from "../model/work-order-service_wo";
import { workOrderContextWO } from "../e_workactivities_wo";
import { stackContext } from "../../app/get_stack";

export default function AllWorkActivitesWO() {
  const { workModelType } = React.useContext(stackContext);
  const context = workModelType === "WA" ? drawerContext : drawerContextWO;

  const {
    setAllActivities,
    setSingleWorkOrder,
    setWorkOrder,
    setConsumerIndex,
    allWOList,
    setWOList,
    setSingleWO,
    customerNumber,
    setSingleWODetails,
    singleWODetails,
    OTPConfirmationWO,
    setOTPConfirmationWO,
    tempWorkOrder,
    setTempWorkOrder,
    setAllWOList,
    newWorkOrder,
    newWorkOrderExists,
    setNewWorkOrderExists,
  } = useContext(context);

  //const {} = useContext(workOrderContextWO);
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const { t } = useTranslation();
  const allActivities =
    useSelector(state => state.activity.allActivities) || [];

  const activities = allActivities;
  const fetchActivities = async () => {
    try {
      const activities = JSON.parse(
        await AsyncStorage.getItem(ROUTES.WORK_ACTIVITIES),
      );
      dispatch(setActivities(activities));
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    fetchActivities();
  }, []);

  //  const canShowAllOrders = _.get(route?.params || {}, "canShowAllOrders");
  const focused = useIsFocused();
  const [declinedRows, setDeclinedRows] = useState({});
  // const [newWorkOrderExists, setNewWorkOrderExists] = useState(false);
  // useEffect(() => {
  //   if (!focused) return;
  //   console.log(canShowAllOrders, "canshow");
  //   if (canShowAllOrders) setIsSwitchOn(false);
  // }, [focused, canShowAllOrders]);

  const onPressTicket = async option => {
    // if (pathName === "Home") {
    //   setWOList(true);
    //   setSingleWO(false);
    //   dispatch(servicePath("WorkActivities"));
    //   navigation.navigate("WorkActivities");
    // }
    console.log("Button pressed");
    fetchWorkOrderList();
    if (option.WorkOrderType === "Consumer Survey") {
      setSingleWODetails(option);
      //setCustomerNumber(option.ConsumerPhoneNumber);
      setWOList(false);
      setSingleWO(true);
      setOTPConfirmationWO(false);
    } else {
      setSingleWODetails(option);
      setWOList(false);
      setSingleWO(true);
      setOTPConfirmationWO(true);
    }
    // fetchWorkOrderList();
  };

  useEffect(() => {
    setTempWorkOrder(singleWODetails);
  }, [singleWODetails]);

  const pathName = useSelector(state => state?.servicePath?.servicePath);
  const getStatusText = status => {
    switch (status) {
      case "C":
      case "O":
        return "Open";
      case "I":
        return "Inprogress";
      case "CO":
        return "Complete";
      case "R":
        return "Rejected";
      case "CA":
        return "Cancelled";
      case "CL":
        return "Closed";
      default:
        return status;
    }
  };

  const acceptFn = option => {
    if (workModelType != "WA") {
      console.log("option", option);
      const updatedBy = 2;
      const status = "Y";
      WorkOrderServiceWO.acceptUpdate(option.WorkOrderId, status, updatedBy)
        .then(res => {
          if (res === 201) {
            setNewWorkOrderExists(false);
            fetchWorkOrderList();
          }
        })
        .catch(error => {
          console.log(error.response.data.message);
        });
    }
  };
  const fetchWorkOrderList = async () => {
    if (workModelType != "WA") {
      try {
        //setLoading(true);

        const res = await WorkOrderServiceWO.getAllWorkOrderList();
        res.workOrders = res?.workOrders?.filter(
          each => each["WorkOrderType"] != null,
        );
        res?.workOrders.sort((a, b) => {
          return moment(b.PlannedStartDate).diff(moment(a.PlannedStartDate));
        });
        console.log(newWorkOrder);

        setAllWOList(res);
      } catch (err) {
        console.log(err, "Error in fetching consumer index data");
      } finally {
        //setLoading(false);
      }
    }
  };
  useEffect(() => {
    console.log(newWorkOrderExists);
  }, [allWOList, newWorkOrderExists, declinedRows]);
  // useEffect(() => {}, [declinedRows]);
  // useEffect(() => {
  //   console.log("New work order exists state has changed:", newWorkOrderExists);
  // }, [newWorkOrderExists]);

  const declineFn = option => {
    const updatedBy = 2;
    const status = "N";
    if (workModelType != "WA") {
      WorkOrderServiceWO.acceptUpdate(option.WorkOrderId, status, updatedBy)
        .then(res => {
          if (res === 201) {
            setDeclinedRows(prevState => {
              const newState = { ...prevState, [option.WorkOrderId]: true };
              console.log("...", newState);
              return newState;
            });
            fetchWorkOrderList();
          }
        })
        .catch(error => {
          console.log(error.response.data.message);
        });
    }
  };
  const renderButtons = (option, WorkOrderId, isDeclined) => {
    console.log(option.FtAcceptanceStatus);
    if (
      option.FtAcceptanceStatus === null &&
      option.FtAcceptanceStatus != "Y"
    ) {
      console.log(option.FtAcceptanceStatus);
      return (
        <View style={styles.buttonContainer}>
          <Button
            buttonbgColor={styles.acceptBgColor}
            textColor={styles.textColor}
            customBtnStyle={styles.customBtnStyle}
            customTextStyle={styles.buttonText}
            onPress={() => acceptFn(option)}>
            {t("ACCEPT")}
          </Button>
          <Button
            buttonbgColor={styles.declineBgColor}
            textColor={styles.textColor}
            customBtnStyle={styles.customBtnStyle}
            customTextStyle={styles.buttonText}
            onPress={() => declineFn(option)}>
            {t("DECLINE")}
          </Button>
        </View>
      );
    }
    return null;
  };

  useEffect(() => {
    console.log(newWorkOrder, allWOList);
    if (newWorkOrder && allWOList) {
      if (allWOList.workOrders) {
        if (newWorkOrder.length != 0 && allWOList.workOrders.length > 0) {
          const exists = allWOList.workOrders.some(
            item => item.WorkOrderId === newWorkOrder.WorkOrderId,
          );
          console.log("exists", exists);
          if (exists) {
            console.log("New work order exists in the list.", exists);
            setNewWorkOrderExists(true);
          } else {
            console.log("New work order does not exist in the list.", exists);
            setNewWorkOrderExists(false);
          }
        }
      }
    }
  }, [newWorkOrder]);

  // Function to compare dates
  const isFutureOrToday = date => {
    const currentDate = new Date();
    const today = new Date(currentDate);
    today.setHours(0, 0, 0, 0);

    const planned = new Date(date);
    planned.setHours(0, 0, 0, 0);
    return planned >= today;
  };

  return (
    <View>
      {pathName !== "Home" ? (
        <>
          <Card style={styles.card}>
            <View>
              <View style={styles.wrapDirection}>
                <View>
                  <Text style={styles.titleCard}>{t("FIELD_ACTIVITES")}</Text>
                </View>
              </View>
            </View>
          </Card>
        </>
      ) : null}

      <ScrollView style={styles.container}>
        {allWOList?.workOrders?.length > 0 ? (
          <>
            <View>
              {allWOList?.workOrders?.map(option => {
                const isDeclined = declinedRows[option.WorkOrderId];
                console.log(option);
                const isNewWorkOrder =
                  option.WorkOrderId === newWorkOrder?.WorkOrderId &&
                  newWorkOrderExists;
                const isReadNewWorkOrder =
                  option.WorkOrderId === newWorkOrder?.WorkOrderId &&
                  !newWorkOrderExists;
                console.log(isNewWorkOrder, isReadNewWorkOrder);
                const plannedDate = new Date(option.PlannedStartDate);

                return (
                  <TouchableRipple
                    key={option.WorkOrderId}
                    disabled={isDeclined}
                    style={[
                      isDeclined || option.FtAcceptanceStatus === "N"
                        ? styles.declinedRow
                        : isNewWorkOrder
                        ? isReadNewWorkOrder
                          ? styles.newWorkOrderRowRemove
                          : styles.newWorkOrderRow
                        : styles.newWorkOrderRowRemove,

                      ,
                    ]}>
                    <>
                      <View>
                        <View style={styles.activityItem}>
                          <View style={styles.activityItemInner}>
                            <View style={styles.iconContainer}>
                              {option.Status === "O" ? (
                                // <FontAwesome5Icon
                                //   name=""
                                //   size={24}
                                //   color={GlobalStyles.colors.eTertiary.base}
                                // />
                                <Icon
                                  name={"FS-Open-Tasks-icon"}
                                  color={GlobalStyles.colors.eTertiary.base}
                                  size={28}
                                  onPress={() => onPressTicket(option)}
                                />
                              ) : option.Status === "CO" ? (
                                // <FontAwesome5Icon
                                //   name=""
                                //   size={24}
                                //   color={GlobalStyles.colors.eTertiary.base}
                                // />
                                <Icon
                                  name={"FS-Completed-Tasks-icon"}
                                  color={GlobalStyles.colors.eSecondary.base}
                                  size={28}
                                  onPress={() => onPressTicket(option)}
                                />
                              ) : option.Status === "CL" ? (
                                // <FontAwesome5Icon
                                //   name=""
                                //   size={24}
                                //   color={GlobalStyles.colors.eTertiary.base}
                                // />
                                <Icon
                                  name={"FS-Completed-Tasks-icon"}
                                  color={GlobalStyles.colors.ePrimary.base}
                                  size={28}
                                  onPress={() => onPressTicket(option)}
                                />
                              ) : (
                                <Icon
                                  name={"FS-Open-Tasks-icon"}
                                  color={GlobalStyles.colors.eTertiary.base}
                                  size={28}
                                  onPress={() => onPressTicket(option)}
                                />
                                // <Icon
                                //   name={iconColorMap.icon}
                                //   color={iconColorMap.color}
                                //   size={28}
                                // />
                              )}
                            </View>
                            <View style={styles.contentContainer}>
                              <View
                                style={[
                                  styles.displayFlex,
                                  { gap: 10, paddingVertical: 3 },
                                ]}>
                                <Text style={styles.headerStyleNumber}>
                                  {option.WorkOrderId}
                                </Text>
                                <Text style={styles.headerStyleNumber}>
                                  {/* {option.WorkOrderType.length > 15
                                    ? `${option.Title.substring(
                                        0,
                                        15,
                                      )}...`
                                    : option.Title} */}
                                  {option.Title}
                                </Text>
                              </View>
                              <View style={styles.subHeaderRow}>
                                <Text style={[styles.subHeaderRowMinWidth]}>
                                  {moment(option.PlannedStartDate).format(
                                    DATE_FORMATS.DATETIME,
                                  )}
                                </Text>
                                <Text style={[styles.subHeaderPriority]}>
                                  {option.Priority === "H"
                                    ? "High"
                                    : option.Priority === "L"
                                    ? "Low"
                                    : option.Priority}
                                </Text>
                              </View>
                              <View style={styles.subHeaderRowStatus}>
                                <Text style={[styles.subHeaderRowMinWidth]}>
                                  {/* {option.route} */}
                                </Text>
                                <Text style={[styles.subHeaderStatus]}>
                                  {getStatusText(option.Status)}
                                  {/* {option.Status === "C" ||
                                  option.Status === "O"
                                    ? "Open"
                                    : option.Status === "I"
                                    ? "Inprogress"
                                    : option.Status === "CO"
                                    ? "Complete"
                                    : option.Status === "R"
                                    ? "Rejected"
                                    : option.Status === "CA"
                                    ? "Cancelled"
                                    : option.Status === "CL"
                                    ? "Closed"
                                    : option.Status} */}
                                </Text>
                              </View>
                              {isFutureOrToday(plannedDate)
                                ? renderButtons(
                                    option,
                                    option.WorkOrderId,
                                    isDeclined,
                                  )
                                : null}
                            </View>
                          </View>
                          {option.FtAcceptanceStatus === "Y" ||
                          option.FtAcceptanceStatus === null ? (
                            <View style={styles.arrowIconStyle}>
                              <FontAwesome5Icon
                                name="chevron-right"
                                color={GlobalStyles.colors.eMedium.base}
                                size={12}
                                fontFamily="NotoSans-Bold"
                                onPress={() => onPressTicket(option)}
                              />
                            </View>
                          ) : null}
                        </View>
                      </View>
                      <View style={styles.lineStyle} />
                    </>
                  </TouchableRipple>
                );
              })}
            </View>
          </>
        ) : (
          <View
            style={[
              styles.noDateWrapper,
              { height: Dimensions.get("window").height },
            ]}>
            <View style={{ height: Dimensions.get("window").height - 500 }}>
              <Text
                style={{
                  fontSize: 19,
                  color: GlobalStyles.colors.ePrimary.base,
                }}>
                {t("NO_WORK_ACTIVITIES_MSG")}
              </Text>
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    borderTopEndRadius: 10,
    borderTopStartRadius: 10,
    borderBottomEndRadius: 0,
    borderBottomStartRadius: 0,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    padding: 15,
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    margin: 10,
  },
  wrapDirection: {
    flexDirection: "row",
    flexWrap: "wrap",
    alignContent: "space-between",
  },
  titleCard: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontWeight: "700",
    fontFamily: "NotoSans-Bold",
  },
  activityWrapper: {
    // paddingTop: 8,
    // paddingBottom: 8,
    // borderBottomColor: GlobalStyles.colors.eBackground.base,
    // borderBottomWidth: 1,
  },
  lineStyle: {
    borderWidth: 1,
    borderColor: GlobalStyles.colors.eBackground.base,
    //marginTop: 10,
    //marginBottom: 6,
    width: "150%",
  },
  activityItem: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 10,
    // marginTop: -5,
  },
  mainView: {
    marginVertical: 5,
  },
  activityItemInner: {
    display: "flex",
    flexDirection: "row",
    flex: 1,
    gap: 20,
  },
  arrowIconStyle: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  displayFlex: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  headerStyle: {
    fontSize: 15,
    fontFamily: "NotoSans-SemiBold",
  },
  headerStyleNumber: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eRich.base,
  },
  iconContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 10,
  },
  subHeaderRowMinWidth: {
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderPriority: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eRich.base,
    textTransform: "capitalize",
    marginTop: -6,
    textAlign: "right",
  },
  subHeaderStatus: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eRich.base,
    textTransform: "capitalize",
    marginTop: -15,
    textAlign: "right",
  },
  subHeaderRow: {
    display: "flex",
    flexDirection: "row",
    paddingVertical: 1,
    justifyContent: "space-between",
    paddingRight: 20,
    marginTop: -5,
    // textAlign: 'left'
  },
  subHeaderRowStatus: {
    display: "flex",
    flexDirection: "row",
    paddingVertical: 1,
    justifyContent: "space-between",
    paddingRight: 20,
  },
  container: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: 10,
    marginTop: -3,
    marginBottom: 140,
  },
  contentContainer: {
    flex: 1,
  },
  noDateWrapper: {
    flex: 1,
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
  },
  profilePicView: {
    height: 60,
    width: 60,
    borderRadius: 100,
    marginLeft: 0,
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    borderWidth: 10,
    borderColor: GlobalStyles.colors.eWhite.base,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: -5,
    marginBottom: 5,
    marginRight: 5,
    gap: 10,
  },
  acceptBgColor: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    paddingHorizontal: 20,
  },
  declineBgColor: {
    borderColor: GlobalStyles.colors.eTertiary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eTertiary.base,
    paddingHorizontal: 20,
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  customBtnStyle: {
    borderRadius: 15,
    paddingVertical: 1,
    paddingHorizontal: 0,
    //marginLeft: 5,
  },
  declinedRow: {
    backgroundColor: GlobalStyles.colors.eLight.base,
    color: GlobalStyles.colors.eLight.base,
  },
  newWorkOrderRow: {
    backgroundColor: GlobalStyles.colors.eLightGreen.base,
  },
  newWorkOrderRowRemove: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
  },
});

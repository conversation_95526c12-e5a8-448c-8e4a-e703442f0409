import axios from "axios";
import { config } from "../../../../environment";

export const NotificationService = {
  getAllWorkOrderList,
  getLookups,
  saveToken
};

async function getAllWorkOrderList() {
  try {
    let url = config.urls.GET_WORK_ORDERS;
    const response = await axios.get(
      `${config.urls.WORK_MANAGEMENT}/work-activity?workStatus=R&includeStatus=false&pageSize=1000`,
      {
        // params: {
        //   pageSize: 2000,
        //   getWorkActivities: true,
        //   getWorkOrderResources: true,
        //   getActivityChecklist: true,
        //   //orgHierarchyId: 1, - Discom Corporation - Testing Org Id
        //   //Division 1 - 21 - QA Test 21
        //   orgHierarchyId: 9,
        //   crewResourceId: 2,
        // },
      },
    );
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function getLookups() {
  try {
    let url = config.urls.WORK_MODEL_TYPE_URL;
    const response = await axios.get(url, {});
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}


async function saveToken(user,token) {
  try {
    let url = config.urls.SAVETOKEN_URL;
    let data = {
      user: user,//assuming logged in user is Binitha das
      token:token
     }
    const response = await axios.post(url, data);
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

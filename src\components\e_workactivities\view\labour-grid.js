import React, { useEffect, useState, useRef } from "react";
import { DataTable, Divider, Text, TextInput } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import { ScrollView } from "react-native-gesture-handler";
import { View ,Platform} from "react-native";
import { useTranslation } from "react-i18next";
import { color } from "react-native-reanimated";
import moment, { duration } from "moment";
import { WorkOrderService } from "../model/work-order-service";

const LabourTableGrid = ({
  workStatus,
  data,
  setwamIFSLbrdata,
  CrewId,
  WamWorkActivityId,
  setIFSLbrdata,
}) => {
  const { t } = useTranslation();
  const [page, setPage] = useState(0);
  const stateVal = useRef({});
  const [numberOfItemsPerPageList] = useState([2, 3, 4]);
  const [itemsPerPage, onItemsPerPageChange] = useState(
    numberOfItemsPerPageList[0],
  );
  const [currIndex, setCurrIndex] = useState(0);

  const [numberOfPeople, setNumberOfPeople] = useState("");
  const [hours, setHours] = useState("");
  const [items, setItems] = useState([]);
  const [actualData, setActualData] = useState({});

  useEffect(() => {
    if (data) {
      console.log(data, "TTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTT24");
      setItems([]);
      let objData = [];
      data.map(e => {
        if (e?.resourceClass === "CRAFT") {
          
          let obj = {
            Description: e?.resourceType,
            Qunatity: e?.quantity,
            Duration: `${e?.duration} ${
              e?.uom?.split("WD-")[1] ? e?.uom?.split("WD-")[1] : "Hrs."
            }`,
            ActualQuantity: e?.ActualQuantity,
            ActualDuration: e?.ActualDuration,
            woResourceId: e?.woResourceId,
            resourceName: e?.resourceName,
            resourceId: e?.resourceId,
          };
          objData.push(obj);
        }
      });

      setItems(objData);
    }
  }, [data]);

  useEffect(() => {
    if (items) {
      let temp = {};
      let tempQuantity = {};
      items.forEach((each, index) => {
        if (each?.ActualDuration) {
          temp[index] = {
            woResourceId: each?.woResourceId,
            duration: each?.ActualDuration,
            resourceId: each?.resourceId,
            resourceName: each?.resourceName,
          };
        } else {
          temp[index] = {
            duration: 0,
            resourceId: each?.resourceId,
            resourceName: each?.resourceName,
            woResourceId: each?.woResourceId,
          };
        }
      });
      console.log(temp, "KKKKKKK222KKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK");
      setActualData(temp);
    }
  }, [items]);

  useEffect(() => {
    //load the data here..
    //setIFSLbrdata

    if (Object.keys(actualData).length > 0) {
      setIFSLbrdata(actualData);
    }

    console.log(actualData, "PLCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC81");
  }, [actualData]);

  const actualDurationHandler = (hours, index, data) => {
    setActualData(prevData => ({
      ...prevData,
      [index]: { ...prevData[index], duration: hours },
    }));
    console.log(hours, data, index, "KKKKKKKKKKKKKkkk123456");
    setwamIFSLbrdata(prevItems => {
      if (prevItems?.length > 0) {
        const findindex = prevItems?.findIndex(
          item => item.activityId === formatWAM.activityId,
        );

        if (findindex !== -1) {
          // Update existing object
          const updatedItems = [...prevItems];
          updatedItems[index] = { ...updatedItems[index], ...formatWAM };
          return updatedItems;
        } else {
          // Add new object
          return [...prevItems, formatWAM];
        }
      } else {
        return [...prevItems];
      }
    });
  };

  const handleOnBlur = (event, item) => {
    console.log(
      item,
      item["woResourceId"],
      item["resourceName"],
      "LLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLL297",
    );

    const today = moment();
    const formattedYesterday = today.format("YYYY-MM-DD");
    let formatIFS = {
      woResourceId: item["woResourceId"],
      ActualDuration: actualData[currIndex] ? actualData[currIndex] : "0",
      resourceName: item["resourceName"] ? item["resourceName"] : "",
    };

    let formatWAM = {
      chargeType: "W1AC",
      date: formattedYesterday,
      activityId: WamWorkActivityId,
      employeeExternalId: CrewId, //user ID of IFS(CrewId)
      hours: actualData[currIndex] ? actualData[currIndex] : "0",
      regularOvertime: "W1RE",
      craft: item["resourceId"] ? item["resourceId"] : "",
    };

    setwamIFSLbrdata(prevItems => {
      const index = prevItems.findIndex(
        item => item.activityId === formatWAM.activityId,
      );

      if (index !== -1) {
        // Update existing object
        const updatedItems = [...prevItems];
        updatedItems[index] = { ...updatedItems[index], ...formatWAM };
        return updatedItems;
      } else {
        // Add new object
        return [...prevItems, formatWAM];
      }
    });
    stateVal.current[currIndex] = actualData[currIndex]; //
    //only updating in IFS db in order to avoid useState logic. But updating WAM at the time of completion.
    // WorkOrderService.updateIFSdatabase(formatIFS)
    //   .then(res => {
    //     stateVal.current[currIndex] = actualData[currIndex]; //
    //     console.log(res, "IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII 116");
    //     // WorkOrderService.updateWAM(formatWAM)
    //     //   .then(res => {
    //     //     console.log(
    //     //       actualData,
    //     //       "IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII 116",
    //     //     );
    //     //     console.log(res, "IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII 116");
    //     //   })
    //     //   .catch(err => {
    //     //     console.log(
    //     //       err,
    //     //       "IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII 116 error",
    //     //     );
    //     //   });
    //   })
    //   .catch(err => {
    //     console.log(err, "IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII 116 error");
    //   });
    console.log(item, "HGFGHJKJHGFGHJK----139");

    //   {
    //     "input": {
    //         "timeSheetDetails": {
    //             "chargeType": "W1AC",
    //             "date": "2024-12-02",
    //             "activityId": 35541301740059,
    //             "employeeExternalId": "1",//user ID of IFS(CrewId)
    //             "craft": 136188507248,//resourceId of WAm
    //             "hours": 3,
    //             "regularOvertime": "W1RE"
    //         }
    //     }
    // }
    console.log(formatWAM, "formatWAM------------------------------1523");
  };

  const from = page * itemsPerPage;
  const to = Math.min((page + 1) * itemsPerPage, items.length);

  React.useEffect(() => {
    setPage(0);
  }, [itemsPerPage]);

  console.log(actualData, "actualData1215");
  console.log(stateVal, "stateVal-------------------------189");
  return (
    <>
      {/* <View>
        <Text
          style={{
            paddingLeft: 16,
          }}>
          {t('LABOUR')}
        </Text>
      </View> */}
      <DataTable
        style={{
          paddingBottom: 10,
          marginBottom: 10,
        }}>
        <Divider />
        <ScrollView
          horizontal
          persistentScrollbar
          contentContainerStyle={{ flexDirection: "column" }}>
          <DataTable.Header>
            <DataTable.Title style={{ width: 160 }}>
              <Text style={{ textWrap: "wrap" }}>
                {t("DESCRIPTION")}
              </Text>
            </DataTable.Title>
            <DataTable.Title style={{ width: 60 }}>
              <Text style={{ width: 100, textWrap: "wrap" }}>
                {t("QUANTITY")}
              </Text>
            </DataTable.Title>
            <DataTable.Title style={{ width:Platform.OS=="ios"? 120: 100 }}>
              <Text style={{ width: 100, textWrap: "wrap" }}>
                {t("PLANNED_DURATION")}
              </Text>
            </DataTable.Title>
            <DataTable.Title
              style={{
                width: Platform.OS=="ios"?120:100,
                flex: 1,
                flexWrap: "wrap", // Allows text to wrap
                textAlign: "left", // Aligns the text properly
              }}>
              <Text style={{ width: 100, textWrap: "wrap" }}>
                {t("ACTUAL_DURATION")}
              </Text>
            </DataTable.Title>
          </DataTable.Header>
          {/* key: 1,
      Code:"Crane",
      Type:"Crane",
      Description:"Crane",
      Qunatity:1,
      Duration: 8,
      Information:this.Qunatity+" for "+this.Duration+this.UOM,
      UOM: "WD-Hour", */}
          {items &&
            Object.keys(actualData).length > 0 &&
            items.map((item, index) => {
              console.log(
                item,
                actualData[index]["duration"],
                "UUUUUUUUUUUUUUUUUUUTTTTTTTTTTTTTRRRRRRRRRRRRRRRRRRRR244",
              );
              return (
                <DataTable.Row
                  key={index}
                  style={{ flex: 1, width: "100%", height: 60 }}>
                  <DataTable.Cell style={{ width: 160 }}>
                    {item.Description}
                  </DataTable.Cell>
                  <DataTable.Cell
                    style={{
                      width: 40,
                      justifyContent: "center",
                    }}>
                    {item.Qunatity}
                  </DataTable.Cell>
                  <DataTable.Cell style={{  width:  Platform.OS=="ios"?120:100, justifyContent: "center", }}>
                    {item.Duration}
                  </DataTable.Cell>
                  <DataTable.Cell
                    style={{
                      width:  Platform.OS=="ios"?120:100,
                    }}>
                    <View
                      style={{
                        display: "flex",
                        flexDirection: "row",
                        justifyContent: "center",
                        alignItems: "center",
                      }}>
                      <TextInput
                        placeholderTextColor="gray"
                        mode="outlined"
                        keyboardType="numeric"
                        value={
                          actualData[index]["duration"]
                            ? actualData[index]["duration"]
                            : 0
                        }
                        enablesReturnKeyAutomatically
                        onChangeText={hours =>
                          actualDurationHandler(hours, index, item)
                        }
                        disabled={workStatus !== "I"}
                        //onBlur={handleOnBlur}
                        // onBlur={event => handleOnBlur(event, item)}
                        outlineColor={GlobalStyles.colors.eDark.hover}
                        activeOutlineColor={GlobalStyles.colors.ePrimary.base}
                        persistentScrollbar={true}
                        // right={<TextInput.Affix text="Hr" />}
                        style={{
                          height: 40,
                        }}
                      />
                      <Text style={{ paddingLeft: 3 }}>Hrs.</Text>
                    </View>
                  </DataTable.Cell>
                </DataTable.Row>
              );
            })}
        </ScrollView>
      </DataTable>
    </>
  );
};

export default LabourTableGrid;

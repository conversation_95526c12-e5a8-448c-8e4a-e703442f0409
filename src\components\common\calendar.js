import React, { useState, useEffect } from "react";
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  ScrollView,
  Modal,
} from "react-native";
import { Calendar } from "react-native-calendars";
import { TextInput, Button } from "react-native-paper";
import { GlobalStyles } from "../app/global-styles";
// import Icon from "../icon";
import moment from "moment";
import Icon from "react-native-vector-icons/FontAwesome";
import DateTimePickerModal from "react-native-modal-datetime-picker";

// ... (your imports)

const CalendarPicker = ({
  minDate,
  calendarDate,
  setCalendarDate,
  setSelectedDate,
  disableCalendar,
  editable,
  setErrorOn,
  setAvailableOndates,
  setCalorText,
  setTextDate,
  outlinedLable,
  index,
  setIndex,
}) => {
  const [showCalendar, setShowCalendar] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [isDatePickerVisible, setDatePickerVisible] = useState(false);
  const [isTimePickerVisible, setTimePickerVisible] = useState(false);
  const [dateAndTime, setDateAndTime] = useState();

  useEffect(() => {
    if (
      calendarDate === null ||
      calendarDate === "" ||
      calendarDate === undefined
    ) {
      setInputValue("");
    } else {
      setInputValue(calendarDate);
    }
  }, [calendarDate]);

  const showDatePicker = () => {
    setDatePickerVisible(true);
  };

  const hideDatePicker = () => {
    setDatePickerVisible(false);
  };

  const showTimePicker = () => {
    setTimePickerVisible(true);
  };

  const hideTimePicker = () => {
    setTimePickerVisible(false);
  };

  const handleCalendarIconPress = () => {
    if (editable && !disableCalendar) {
      setShowCalendar(!showCalendar);
    }
  };

  const futureDatesOnly = {
    [new Date().toISOString().split("T")[0]]: { disabled: true },
  };

  const handleInputChange = (text, cal) => {
    setIndex(index);
    if (cal) {
      setAvailableOndates(text);
      setCalorText(true);
      const selectedDateTime = moment(text, "YYYY-MM-DD").format("DD-MM-YYYY");
      setInputValue(selectedDateTime);
      setShowCalendar(false);
      setTextDate(selectedDateTime);
      setDateAndTime(selectedDateTime);
      showTimePicker();
    } else {
      setInputValue(text);
      setCalorText(false);
      let regexdate = `^(3[01]|[12][0-9]|0[1-9])-(1[0-2]|0[1-9])-[0-9]{4} [0-9]{2}:[0-9]{2}$`;
      var dateRegex = new RegExp(regexdate);
      var dateTest = dateRegex.test(text);
      let on_date = moment(text, "DD-MM-YYYY");
      setTextDate(text);

      if (dateTest === true) {
        const selectedDateTime = moment(text, "YYYY-MM-DD").format(
          "DD-MM-YYYY",
        );
        setDateAndTime(selectedDateTime);
        setAvailableOndates(moment(on_date).format("YYYY-MM-DD"));
      }
      showTimePicker();
    }
  };
  console.log(disableCalendar, editable, "KGFCGHJOIYTRDVB");
  return (
    <View>
      <TextInput
        label={outlinedLable}
        mode="outlined"
        value={inputValue}
        placeholder="DD-MM-YYYY"
        placeholderTextColor={GlobalStyles.colors.eDark.base}
        style={styles.input}
        editable={false}
        onChangeText={handleInputChange}
        keyboardType={Platform.OS === "ios" ? "default" : "numeric"}
        maxLength={19}
      />

      <TouchableOpacity
        style={styles.calendarIcon}
        onPress={handleCalendarIconPress}>
        <Icon
          name="calendar"
          size={20}
          color={GlobalStyles.colors.ePrimary.base}
          onPress={handleCalendarIconPress}
        />
      </TouchableOpacity>

      <Modal visible={showCalendar} transparent={true}>
        <TouchableOpacity
          style={styles.modalOverlay}
          disabled={true}
          onPress={() => {
            setShowCalendar(false);
          }}>
          <View style={styles.calendarContainer}>
            <Calendar
              disableAllTouchEventsForDisabledDays={disableCalendar}
              onDayPress={day => {
                handleInputChange(day.dateString, "cal");
                setShowCalendar(false);
              }}
              minDate={minDate}
              disabledDates={futureDatesOnly}
              style={styles.calendar}
            />
          </View>
        </TouchableOpacity>
      </Modal>
      <DateTimePickerModal
        isVisible={isDatePickerVisible}
        mode="date"
        onConfirm={date => {
          hideDatePicker();
          handleInputChange(moment(date).format("DD-MM-YYYY"), "cal");
        }}
        onCancel={hideDatePicker}
      />
      <DateTimePickerModal
        isVisible={isTimePickerVisible}
        mode="time"
        onConfirm={time => {
          hideTimePicker();
          const formattedTime = moment(time).format("h:mm A");
          setCalendarDate(dateAndTime + " " + formattedTime);
        }}
        onCancel={hideTimePicker}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  inputContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderRadius: 5,
    paddingHorizontal: 10,
    backgroundColor: GlobalStyles.colors.eFaint.hover,
  },
  input: {
    color: GlobalStyles.colors.eDark.base,
    backgroundColor: GlobalStyles.colors.eFaint.hover,
    fontSize: 14,
    // borderWidth: 1,
    // borderRadius: 5,
    // borderColor: GlobalStyles.colors.eRich.hover,
    // padding: 10,
  },
  calendarIcon: {
    color: GlobalStyles.colors.eDark.base,
    backgroundColor: GlobalStyles.colors.eLight.base,
    fontSize: 14,
    position: "absolute",
    top: 21,
    right: 10,
    zIndex: 1,
  },
  calendarstyle: {
    zIndex: 1,
  },
  calendarContainer: {
    backgroundColor: "white",
    width: "80%",
    maxHeight: 350,
    borderRadius: 5,
    overflow: "hidden",
  },
  calendar: {
    backgroundColor: "white",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
});

export default CalendarPicker;

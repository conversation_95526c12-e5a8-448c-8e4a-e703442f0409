import { ScrollView, StyleSheet, View } from "react-native";
import { Text } from "react-native-paper";
import { GlobalStyles } from "../app/global-styles";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";
import React, { useContext } from "react";
import { registerContext } from "../e_authPages/e_auth_pages";
import FlatButton from "../common/_flat_button";
import { config } from "../../environment";
import { registerContextWO } from "../e_authPages/e_auth_pages_wo";
import { stackContext } from "../app/get_stack";

export default function UpdatePasswordSuccess() {
  const { workModelType } = React.useContext(stackContext);
  const { setIsLoginPage } = useContext(
    workModelType === "WA" ? registerContext : registerContextWO,
  );

  const cancelClick = () => {
    setIsLoginPage("Login");
  };
  return (
    <ScrollView style={styles.scrollStyle}>
      <View style={styles.iconstyle}>
        <Icon
          name="check-circle"
          size={100}
          color="green"
          style={{
            backgroundColor: "white",
            borderRadius: 100,
          }}
        />
      </View>
      <View style={styles.textStyle}>
        <Text variant="headlineMedium" style={styles.textColorOTP}>
          Your password has been updated successfully.
        </Text>
      </View>
      <View style={styles.buttonStyle}>
        <View style={styles.leftSide}>
          <Icon
            name="chevron-left"
            size={20}
            color="white"
            onPress={cancelClick}
          />
        </View>
        <View style={styles.rightSide}>
          <FlatButton onPress={cancelClick} textStyles={styles.flatButtonOtp}>
            Back to Login
          </FlatButton>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  scrollStyle: {
    height: "100%",
    paddingHorizontal: "2%",
  },
  textStyle: {
    alignItems: "center",
    marginTop: "5%",
  },
  textColorOTP: {
    color: GlobalStyles.colors.eWhite.base,
    textAlign: "center",
  },
  iconstyle: {
    alignItems: "center",
    marginTop: "20%",
  },
  flatButtonOtp: {
    textAlign: "center",
    color: GlobalStyles.colors.eWhite.base,
    textDecorationLine: "underline",
    paddingVertical: "1%",
    paddingHorizontal: 12,
    fontSize: 16,
    fontFamily: "NotoSans-SemiBold",
  },
  buttonStyle: {
    marginTop: "20%",
    flexDirection: "row",
    alignItems: "center",
    marginLeft: "30%",
  },
  leftSide: {
    // justifyContent: "flex-start",
    paddingTop: 4,
  },
  rightSide: {
    // justifyContent: "flex-end",
  },
});

import React, { useState, useEffect, useContext } from "react";
import { StyleSheet, View, Button, TextInput } from "react-native";
import { Card, Text, Checkbox, List } from "react-native-paper";
import { GlobalStyles } from "../app/global-styles";
import { FlatList } from "react-native-gesture-handler";
import ConsumerIndexing from "./view/new-consumer-index";
import { PaginationExample } from "./view/pagination";
import ConsumnerIndexList from "./view/all-CIList";
import { CIService } from "./model/consumer-index-service";
import ReviewScreen from "./view/review-screen";
import { drawerContext } from "../app/authenticated/authenticated_layout";
import AsyncStorage from "@react-native-async-storage/async-storage";
import ConfirmationCI from "./view/confirmation";
import { config } from "../../environment";
import { drawerContextWO } from "../app/authenticated/authenticated_layout_wo";
import { stackContext } from "../app/get_stack";

export const consumerIndexContext = React.createContext();
export default function ConsumerIndexingMain() {
  const { workModelType } = React.useContext(stackContext);
  let CIList,
    setCIList,
    singleCI,
    setSingleCI,
    allCIList,
    setAllCIList,
    confirmationModal,
    reviewClose,
    setReviewClose,
    createCI,
    setCreateCI;

  if (workModelType === "WA") {
    ({
      CIList,
      setCIList,
      singleCI,
      setSingleCI,
      allCIList,
      setAllCIList,
      confirmationModal,
      reviewClose,
      setReviewClose,
      createCI,
      setCreateCI,
    } = useContext(drawerContext));
  } else {
    ({
      CIList,
      setCIList,
      singleCI,
      setSingleCI,
      allCIList,
      setAllCIList,
      confirmationModal,
      reviewClose,
      setReviewClose,
      createCI,
      setCreateCI,
    } = useContext(drawerContextWO));
  }

  const [CIDetails, setCIDetails] = useState([]);
  const [loading, setLoading] = useState(false);
  const [reviewScreen, setReviewScreen] = useState(false);
  const [tempCIData, setTempCIData] = useState({});
  const [newCIData, setNewCIData] = useState(tempCIData);
  const [filled, setfilled] = useState(false);
  const [rawData, setRawData] = useState([]);
  const [confirm, setConfirmation] = useState(false);
  const [visible, setVisible] = useState(false);
  const [revert, setRevert] = useState(false);
  const fetchCIList = async () => {
    try {
      setLoading(true);
      const res = await CIService.getAllCIList();
      setAllCIList(res);
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (reviewClose) {
      setReviewScreen(false);
      setCIList(true);
      setSingleCI(false);
      setReviewClose(false);
    }
    fetchCIList();
  }, [reviewScreen, CIList, singleCI, reviewClose]);

  return (
    <consumerIndexContext.Provider
      value={{
        allCIList,
        setAllCIList,
        CIList,
        setCIList,
        singleCI,
        setSingleCI,
        CIDetails,
        setCIDetails,
        reviewScreen,
        setReviewScreen,
        tempCIData,
        setTempCIData,
        filled,
        setfilled,
        rawData,
        setRawData,
        confirm,
        setConfirmation,
        visible,
        setVisible,
        revert,
        setRevert,
        newCIData,
        setNewCIData,
      }}>
      <View>
        {CIList && !singleCI ? (
          <>
            <ConsumnerIndexList />
          </>
        ) : (
          <></>
        )}
        {!CIList && singleCI ? (
          <>
            <ConsumerIndexing />
          </>
        ) : (
          <></>
        )}

        {reviewScreen && !CIList && !singleCI ? (
          <>
            <ReviewScreen />
          </>
        ) : (
          <></>
        )}

        {confirm ? <ConfirmationCI /> : ""}
      </View>
    </consumerIndexContext.Provider>
  );
}

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 20,
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
  },
  titleCard: {
    color: GlobalStyles.colors.ePrimary.base,
    fontFamily: "NotoSans-Bold",
    fontSize: 12,
    textAlign: "left",
  },
  leftView: {
    float: "left",
    alignSelf: "stretch",
    width: "60%",
    marginTop: 5,
  },
  rightView: {
    float: "right",
    width: "40%",
    marginTop: 5,
  },
  accountHeaderContent: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    borderRadius: 4,
    justifyContent: "flex-start",
    width: "100%",
    textAlign: "right",
    textColor: GlobalStyles.colors.eWhite.base,
  },
  content: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
  },
  lineStyle: {
    borderWidth: 0.5,
    borderColor: GlobalStyles.colors.eLight.base,
    marginTop: 10,
    marginBottom: 6,
    marginHorizontal: -20,
    paddingHorizontal: -20,
    width: "112%",
  },
  lineStyleBottom: {
    borderWidth: 0.5,
    borderColor: GlobalStyles.colors.eLight.base,
    marginLeft: -5,
    marginBottom: 2,
    marginRight: -5,
    marginTop: 10,
  },
  accountsList: {
    justifyContent: "flex-start",
    fontSize: 12,
    width: "100%",
    textAlign: "left",
    fontFamily: "NotoSans-SemiBold",
  },
  totalAmount: {
    fontSize: 12,
    textAlign: "left",
    color: GlobalStyles.colors.ePrimary.base,
    fontFamily: "NotoSans-Bold",
  },
  total: {
    flexDirection: "row",
    fontSize: 15,
    textAlign: "right",
    color: GlobalStyles.colors.eSecondary.base,
    fontFamily: "NotoSans-Bold",
    marginRight: 10,
  },
  bigTextHeader: {
    color: GlobalStyles.colors.eWhite.base,
    textAlign: "left",
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
    justifyContent: "flex-start",
    // wordBreak: "break-all",
  },
  singleHeader: {
    color: GlobalStyles.colors.eWhite.base,
    textAlign: "center",
    fontFamily: "NotoSans-SemiBold",
    fontSize: 12,
    marginTop: 10,
    marginLeft: -15,
    justifyContent: "flex-start",
  },
  headerAmount: {
    justifyContent: "flex-end",
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
    textAlign: "right",
    color: GlobalStyles.colors.eWhite.base,
    paddingVertical: 10,
    paddingRight: 15,
  },
  amountList: {
    justifyContent: "flex-end",
    fontSize: 12,
    textAlign: "right",
    marginTop: 12,
    marginRight: 10,
    fontFamily: "NotoSans-SemiBold",
  },
  checkboxDiv: {
    flex: 1,
    flexDirection: "row",
    // alignItems: "center",
    // justifyContent: "left",
    marginLeft: -18,
    marginTop: -8,
    marginBottom: -8,
  },
});

import { createSlice } from "@reduxjs/toolkit";

const defaultAlertsPreferenceReducer = createSlice({
  name: "defaultAlertsPreference",
  initialState: {
    defaultAlertsPreference: {},
  },
  reducers: {
    defaultAlertsPreference: (state, action) => {
      state.defaultAlertsPreference = action.payload;
    },
  },
});

export const defaultAlertsPreference =
  defaultAlertsPreferenceReducer.actions.defaultAlertsPreference;
export default defaultAlertsPreferenceReducer.reducer;

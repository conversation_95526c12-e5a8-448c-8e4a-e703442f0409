import React, { useState } from "react";
import { StyleSheet, View } from "react-native";
import { Card, Text, List } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import Icon from "../../icon";

export default function PaymentMethods() {
  const [expandedCredit, setExpandedCredit] = React.useState(false);
  const [expandedNet, setExpandedNet] = useState(false);
  const [expandedWallet, setExpandedWallet] = useState(false);
  const [expandedPaypal, setExpandedPaypal] = useState(false);
  const [expandedMore, setExpandedMore] = useState(false);

  const handlePress = key => {
    setExpandedMore(false);
    setExpandedNet(false);
    setExpandedWallet(false);
    setExpandedPaypal(false);
    setExpandedCredit(false);

    if (key === 0) {
      setExpandedCredit(!expandedCredit);
    } else if (key === 1) {
      setExpandedNet(!expandedNet);
    } else if (key === 2) {
      setExpandedWallet(!expandedWallet);
    } else if (key === 3) {
      setExpandedPaypal(!expandedPaypal);
    } else {
      setExpandedMore(!expandedMore);
    }
  };

  return (
    <View>
      <Card style={styles.card}>
        <Text style={styles.titleCard}>PAYMENT METHODS</Text>
        <View style={styles.lineStyle} />
        <View style={styles.paymentMethodContainer}>
          <View style={{ flex: 1 }}>
            <Icon
              name={
                expandedCredit === true ? "Card-fill-icon" : "Card-stroke-icon"
              }
              color={GlobalStyles.colors.ePrimary.base}
              size={25}
              style={styles.imgCls}
            />
          </View>
          <View style={styles.accordion}>
            <List.Accordion
              title="CREDIT/DEBIT CARDS"
              titleStyle={styles.title}
              style={styles.accordionList}
              expanded={expandedCredit}
              onPress={() => handlePress(0)}>
              <List.Item title="Credit Card" />
            </List.Accordion>
          </View>
        </View>
        <View style={styles.lineStylePayments} />
        <View style={styles.paymentMethodContainer}>
          <View style={{ flex: 1 }}>
            <Icon
              name={
                expandedNet ? "NetBanking-fill-icon" : "NetBanking-stroke-icon"
              }
              color={GlobalStyles.colors.ePrimary.base}
              size={25}
              style={styles.imgCls}
            />
          </View>
          <View style={styles.accordion}>
            <List.Accordion
              title="NET BANKING"
              titleStyle={styles.title}
              style={styles.accordionList}
              expanded={expandedNet}
              onPress={() => handlePress(1)}>
              <List.Item title="NET BANKING" />
            </List.Accordion>
          </View>
        </View>
        <View style={styles.lineStylePayments} />
        <View style={styles.paymentMethodContainer}>
          <View style={{ flex: 1 }}>
            <Icon
              name={expandedWallet ? "Wallet-fill-icon" : "Wallet-stroke-icon"}
              color={GlobalStyles.colors.ePrimary.base}
              size={25}
              style={styles.imgCls}
            />
          </View>
          <View style={styles.accordion}>
            <List.Accordion
              title="WALLET"
              titleStyle={styles.title}
              style={styles.accordionList}
              expanded={expandedWallet}
              onPress={() => handlePress(2)}>
              <List.Item title="WALLET" />
            </List.Accordion>
          </View>
        </View>
        <View style={styles.lineStylePayments} />
        <View style={styles.paymentMethodContainer}>
          <View style={{ flex: 1 }}>
            <Icon
              name={expandedPaypal ? "Paypal-fill-icon" : "Paypal-stroke-icon"}
              color={GlobalStyles.colors.ePrimary.base}
              size={25}
              style={styles.imgCls}
            />
          </View>
          <View style={styles.accordion}>
            <List.Accordion
              title="PAYPAL"
              titleStyle={styles.title}
              style={styles.accordionList}
              expanded={expandedPaypal}
              onPress={() => handlePress(3)}>
              <List.Item title="PAYPAL" />
            </List.Accordion>
          </View>
        </View>
        <View style={styles.lineStylePayments} />
        <View style={styles.paymentMethodContainer}>
          <View style={{ flex: 1 }}>
            <Icon
              name={expandedMore ? "more-fill-icon" : "more-stroke-icon"}
              color={GlobalStyles.colors.ePrimary.base}
              size={25}
              style={styles.imgCls}
            />
          </View>
          <View style={styles.accordion}>
            <List.Accordion
              title="More Payments methods"
              titleStyle={styles.title}
              style={styles.accordionList}
              expanded={expandedMore}
              onPress={() => handlePress(4)}>
              <List.Item title="More Payments Methods" />
            </List.Accordion>
          </View>
        </View>
        <View style={styles.lineStylePayments} />
        <Text style={styles.smallText}>Your money is always safe</Text>
        <Text style={styles.smallText2}>100% secure payments</Text>

        <View style={styles.content}></View>
      </Card>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 20,
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
  },
  titleCard: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
  },
  title: {
    color: GlobalStyles.colors.eRich.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
    textAlign: "left",
    backgroundColor: GlobalStyles.colors.eFaint.base,
  },
  content: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
  },
  lineStyle: {
    borderWidth: 0.5,
    fontSize: 1,
    borderColor: GlobalStyles.colors.eLight.base,
    marginTop: 5,
    marginLeft: -6,
    width: "100%",
  },
  lineStylePayments: {
    borderWidth: 1,
    borderColor: GlobalStyles.colors.eLight.base,
    marginLeft: -6,
    width: "100%",
    marginTop: -10,
  },
  smallText: {
    color: GlobalStyles.colors.eSecondary.base,
    textAlign: "center",
    marginTop: 10,
    fontSize: 10,
  },
  smallText2: {
    color: GlobalStyles.colors.eSecondary.base,
    textAlign: "center",
    fontSize: 8,
  },
  imgCls: {
    // height: 40,
    // width: 40,
    marginLeft: 0,
  },
  accordion: {
    flex: 6,
    marginLeft: -17,
  },
  accordionList: {
    backgroundColor: GlobalStyles.colors.eFaint.base,
  },
  paymentMethodContainer:{
    flexDirection: "row",
    alignItems: "center",
    gap: 6
  }
});

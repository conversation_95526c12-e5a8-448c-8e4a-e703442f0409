import React, { useEffect, useState } from "react";
import { DataTable, Divider, Text, TextInput } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import { ScrollView } from "react-native-gesture-handler";
import { View } from "react-native";
import { useTranslation } from "react-i18next";
import moment from "moment";
import { WorkOrderService } from "../model/work-order-service";

const EquipmentTableGrid = ({
  workStatus,
  data,
  setwamIFSLbrdata,
  setwamIFSEqpdata,
  setwamIFSQEqpdata,
  CrewId,
  WamWorkActivityId,
  setIFSEqpdata,
}) => {
  const { t } = useTranslation();
  const [page, setPage] = React.useState(0);
  const [numberOfItemsPerPageList] = React.useState([2, 3, 4]);
  const [itemsPerPage, onItemsPerPageChange] = React.useState(
    numberOfItemsPerPageList[0],
  );

  const [equipment, setEquipment] = React.useState("");
  const [numberOfEquipment, setNumberOfEquipment] = React.useState("");
  const [hours, setHours] = React.useState("");
  const [actualData, setActualData] = useState({});
  const [actualDataQ, setActualDataQ] = useState({});
  const [currIndex, setCurrIndex] = useState(0);
  const [items, setItems] = React.useState([]);
  React.useEffect(() => {
    if (data) {
      
      setItems([]);
      let objData = [];
      data.map(e => {
        if (e?.resourceClass === "MATERIAL") {
          let obj = {
            Description: e?.resourceType,
            Qunatity: e?.quantity,
            Duration: `${e?.duration} Hrs.`,
            ActualQuantity: e?.ActualQuantity,
            ActualDuration: e?.ActualDuration,
            woResourceId: e?.woResourceId,
            resourceName: e?.resourceName,
            resourceId: e?.resourceId,
          };
          //setItems([...items, obj]);

          objData.push(obj);
        }
      });

      setItems(objData);
    }
  }, []);

  useEffect(() => {
    if (items) {
      let temp = {};
      let tempQuantity = {};
      items.forEach((each, index) => {
        if (each?.ActualDuration) {
          temp[index] = {
            woResourceId: each?.woResourceId,
            duration: each?.ActualDuration,
            resourceId: each?.resourceId,
            resourceName: each?.resourceName,
          };
        } else {
          temp[index] = {
            duration: 0,
            resourceId: each?.resourceId,
            resourceName: each?.resourceName,
            woResourceId: each?.woResourceId,
          };
        }
      });
      setActualData(temp);
    }
  }, [items]);

  useEffect(() => {
    //load the data here..
    //setIFSLbrdata

    if (Object.keys(actualData).length > 0) {
      setIFSEqpdata(actualData);
    }

    console.log(actualData, "PLCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC81");
  }, [actualData]);

  const from = page * itemsPerPage;
  const to = Math.min((page + 1) * itemsPerPage, items.length);

  React.useEffect(() => {
    setPage(0);
  }, [itemsPerPage]);

  const actualDurationHandler = (hours, index, data, mode, quantity) => {
    console.log(
      hours,
      index,
      data,
      mode,
      quantity,
      "hours, index, data, mode, quantity------------------------------84",
    );
    setCurrIndex(index);
    let formatIFSWequipment;
    let formatWAMEquipment;
    // setActualData(prevData => ({
    //   ...prevData,
    //   [index]: hours,
    // }));
    setActualData(prevData => ({
      ...prevData,
      [index]: { ...prevData[index], duration: hours },
    }));

    // let formatWAM = {
    //   chargeDate: formattedDate,
    //   activityId: WamWorkActivityId,
    //   resourceTypeId: item?.resourceId,
    //   quantity: actualData[currIndex] ? actualData[currIndex] : "0",
    // };

    setwamIFSEqpdata(prevItems => {
      if (prevItems?.length > 0) {
        const findindex = prevItems.findIndex(
          item => item.resourceTypeId === formatWAM.resourceTypeId,
        );

        if (findindex !== -1) {
          // Update existing object
          const updatedItems = [...prevItems];
          updatedItems[index] = { ...updatedItems[index], ...formatWAM };
          return updatedItems;
        } else {
          // Add new object
          return [...prevItems, formatWAM];
        }
      }
    });

    // setwamIFSEqpdata({
    //   formatIFSWequipment,
    //   formatWAMEquipment,
    // });
  };

  const handleOnBlur = (event, item) => {
    console.log(
      item,
      item["woResourceId"],
      item["resourceName"],
      "LLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLL297",
    );
    const today = moment();
    const yesterday = today.clone().subtract(1, "days");
    const formattedDate = today.format("YYYY-MM-DD");
    let formatIFS = {
      woResourceId: item["woResourceId"],
      ActualDuration: actualData[currIndex] ? actualData[currIndex] : "0",
      resourceName: item["resourceName"] ? item["resourceName"] : "",
    };
    WorkOrderService.updateIFSdatabase(formatIFS)
      .then(res => {
        console.log(actualData, "IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII 116");
        console.log(res, "IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII E116");
      })
      .catch(err => {
        console.log(err, "IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII E116 error");
      });
    console.log(item, "HGFGHJKJHGFGHJK----139");
    // let formatWAM = {
    //   input: {
    //     resourceChargeDetails: {
    //       chargeDate: formattedDate,
    //       activityId: WamWorkActivityId,
    //       resourceTypeId: item?.resourceId,
    //       quantity: actualData[currIndex] ? actualData[currIndex] : "0",
    //     },
    //   },
    // };
    let formatWAM = {
      chargeDate: formattedDate,
      activityId: WamWorkActivityId,
      resourceTypeId: item?.resourceId,
      quantity: actualData[currIndex] ? actualData[currIndex] : "0",
    };

    setwamIFSEqpdata(prevItems => {
      const index = prevItems.findIndex(
        item => item.resourceTypeId === formatWAM.resourceTypeId,
      );

      if (index !== -1) {
        // Update existing object
        const updatedItems = [...prevItems];
        updatedItems[index] = { ...updatedItems[index], ...formatWAM };
        return updatedItems;
      } else {
        // Add new object
        return [...prevItems, formatWAM];
      }
    });

    // WorkOrderService.updateWAMEquipments(formatWAM)
    //   .then(res => {
    //     console.log(actualData, "IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII 116");
    //     console.log(res, "IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII E116");
    //   })
    //   .catch(err => {
    //     console.log(err, "IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII E116");
    //   });
    console.log(formatWAM, "formatWAM------------------------------1523");
  };

  console.log(actualData, "KKKKKKKKKKKKKkkk1234563");

  return (
    <>
      {/* <View>
        <Text
        // style={{
        //   paddingLeft: 16,
        // }}
        >
          {t("EQUIPMENT")}
        </Text>
      </View> */}
      <DataTable
        style={{
          paddingBottom: 10,
          marginBottom: 10,
        }}>
        <Divider />
        <ScrollView
          horizontal
          persistentScrollbar
          contentContainerStyle={{ flexDirection: "column" }}>
          <DataTable.Header>
            <DataTable.Title style={{ width: 160 }}>
              <Text style={{ width: 100, textWrap: "wrap" }}>
                {t("DESCRIPTION")}
              </Text>
            </DataTable.Title>
            <DataTable.Title style={{ width: 100 }}>
              <Text style={{ width: 100, textWrap: "wrap" }}>
                {t("PLANNED_QUANTITY")}
              </Text>
            </DataTable.Title>
            <DataTable.Title style={{ width: 100 }}>
              <Text style={{ width: 100, textWrap: "wrap" }}>
                {t("PLANNED_DURATION")}
              </Text>
            </DataTable.Title>
            {/* <DataTable.Title style={{ width: 100 }}>
              {t("ACTUAL_QUANTITY")}
            </DataTable.Title> */}
            <DataTable.Title style={{ width: 100 }}>
              <Text style={{ width: 100, textWrap: "wrap" }}>
                {t("ACTUAL_DURATION")}
              </Text>
            </DataTable.Title>
            {/* <DataTable.Title>
              <Text
                style={{
                  flexWrap: "wrap", // Enable text wrapping
                  textAlign: "center", // Optional: Align text in the center
                  maxWidth: 100, // Optional: Constrain width to force wrapping
                }}>
                {t("ACTUAL_DURATION")}
              </Text>
            </DataTable.Title> */}
          </DataTable.Header>
          {/* key: 1,
      Code:"Crane",
      Type:"Crane",
      Description:"Crane",
      Qunatity:1,
      Duration: 8,
      Information:this.Qunatity+" for "+this.Duration+this.UOM,
      UOM: "WD-Hour", */}

          {/* to handle multiple number of rows dynamically.. */}
          {items?.length > 0 &&
            Object.keys(actualData).length > 0 &&
            items?.map((item, index) => {
              console.log(
                item,
                actualData,
                "BBBBBBBBBBHHHHHHHHHHHHTTTTTTTTTTTTTTT 291",
              );
              return (
                <DataTable.Row
                  key={item.key}
                  style={{ flex: 1, width: "100%", height: 60 }}>
                  <DataTable.Cell style={{ width: 160 }}>
                    {item.Description}
                  </DataTable.Cell>
                  <DataTable.Cell
                    style={{ width: 100, justifyContent: "center" }}>
                    {item.Qunatity}
                  </DataTable.Cell>
                  <DataTable.Cell
                    style={{ width: 100, justifyContent: "center" }}>
                    {item.Duration}
                  </DataTable.Cell>
                  <DataTable.Cell
                    style={{ width: 100, justifyContent: "center" }}>
                    <View
                      style={{
                        display: "flex",
                        flexDirection: "row",
                        justifyContent: "center",
                        alignItems: "center",
                      }}>
                      <TextInput
                        placeholderTextColor="gray"
                        mode="outlined"
                        keyboardType="numeric"
                        value={
                          actualData[index]["duration"]
                            ? actualData[index]["duration"]
                            : 0
                        }
                        enablesReturnKeyAutomatically
                        onChangeText={hours =>
                          actualDurationHandler(hours, index, item)
                        }
                        disabled={workStatus !== "I"}
                        // onBlur={event => handleOnBlur(event, item)}
                        outlineColor={GlobalStyles.colors.eDark.hover}
                        activeOutlineColor={GlobalStyles.colors.ePrimary.base}
                        persistentScrollbar={true}
                        // right={<TextInput.Affix text="Hr" />}
                        style={{
                          height: 40,
                        }}
                      />
                      <Text style={{ paddingLeft: 3 }}>Hrs.</Text>
                    </View>
                  </DataTable.Cell>

                  {/* <DataTable.Cell style={{ width: 100 }}>
                  <View
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      justifyContent: "center",
                      alignItems: "center",
                    }}>
                    <TextInput
                      placeholderTextColor="gray"
                      mode="outlined"
                      keyboardType="numeric"
                      value={actualDataQ[index] ? actualDataQ[index] : 0}
                      enablesReturnKeyAutomatically
                      onChangeText={quantity =>
                        actualDurationHandler(
                          "",
                          index,
                          item,
                          "Quantity",
                          quantity,
                        )
                      }
                      placeholder="1"
                      outlineColor={GlobalStyles.colors.eDark.hover}
                      activeOutlineColor={GlobalStyles.colors.ePrimary.base}
                      persistentScrollbar={true}
                      // right={<TextInput.Affix text="Hr" />}
                      style={{
                        height: 40,
                      }}
                    />
                    <Text style={{ paddingLeft: 3 }}>Hrs.</Text>
                  </View>
                </DataTable.Cell> */}
                </DataTable.Row>
              );
            })}
        </ScrollView>
      </DataTable>
    </>
  );
};

export default EquipmentTableGrid;

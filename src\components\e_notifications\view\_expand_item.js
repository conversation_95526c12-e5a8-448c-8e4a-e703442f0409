import moment from "moment";
import { useEffect, useState } from "react";
import {
  StyleSheet,
  TouchableWithoutFeedback,
  View,
  Text,
  Image,
} from "react-native";
import { Card, Chip } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import Icon from "../../icon";
import { useNavigation } from "@react-navigation/native";
import TextLink from "react-native-text-link";
import { useDispatch, useSelector } from "react-redux";
import { updateData } from "../model/notification_service";
import { unreadAnnouncementInfo } from "../../../redux/slices/unreadAnnouncementNotification";
import { unreadBillInfo } from "../../../redux/slices/unreadBillNotification";
import { unreadMeterInfo } from "../../../redux/slices/unreadMeterNotification";
import RenderHtml, { defaultSystemFonts } from "react-native-render-html";
import { useWindowDimensions } from "react-native";
import { commonService } from "../../../services/common.service";
import { ScrollView } from "react-native-gesture-handler";

export default function ExpandItems({
  readData,
  key,
  item,
  origin,
  setMeterUnread,
  setAnnouncementUnread,
  setBillUnread,
  i,
}) {
  const [open, setOpen] = useState(false);
  const [moreRead, setMoreRead] = useState(true);
  const [lessRead, setLessRead] = useState(false);
  const [read, setRead] = useState(item.IsRead);
  const [hasImage, setHasImage] = useState(false);
  const [imgUrlCode, setImgUrlCode] = useState();

  const navigation = useNavigation();
  const { width } = useWindowDimensions();
  const dispatch = useDispatch();
  const systemFonts = [
    ...defaultSystemFonts,
    "NotoSans-Regular",
    "NotoSans-Bold",
  ];
  let dateTimeFormat = useSelector(
    state => state?.parameter?.parameter?.ParameterLookup?.DATE_TIME_FORMAT,
  );
  const accountId = useSelector(
    state => state?.meterDetails?.meterDetails?.accountId,
  );
  const unreadAnn = useSelector(
    State => State?.unreadAnnouncementInfo?.unreadAnnouncementInfo,
  );
  const unreadBill = useSelector(
    state => state?.unreadBillInfo?.unreadBillInfo,
  );
  const unreadMeter = useSelector(
    state => state?.unreadMeterInfo?.unreadMeterInfo,
  );
  useEffect(() => {
    setRead(item.IsRead);
  }, [item.IsRead]);

  useEffect(() => {
    if (item.ContentIconName !== null) {
      setHasImage(item.ContentIconName ? true : false);
    }
  }, [item]);

  useEffect(() => {
    if (
      hasImage !== false &&
      hasImage !== null &&
      item?.ContentIconName !== null
    ) {
      commonService.getAssestUrl(item.ContentIconName).then(res => {
        setImgUrlCode(res);
      });
    }
  }, [hasImage]);

  useEffect(() => {
    if (item?.AnnouncementDescription?.length > 90) {
      setMoreRead(false);
    } else {
      setMoreRead(true);
    }
  }, [item]);

  let originLabels = {
    announcement: "ANNOUNCEMENT",
    meter: "METER",
    bill: "BILL",
  };

  const handleChange = () => {
    setOpen(prev => (prev !== i ? i : false));
    if (readData === "UNREAD") {
      if (read == 0) setRead(1);
      if (read === 1 && open === i) {
        changeIsRead();
      }
    } else {
      if (read === 0 && open === false) {
        setRead(1);
        changeIsRead();
      }
    }
  };

  const changeIsRead = () => {
    let id = "";

    if (origin === originLabels.announcement) {
      setAnnouncementUnread(prev => prev - 1);
      id = item.AnnouncementId;
      let dataArr = JSON.parse(JSON.stringify(unreadAnn));
      let newArr = dataArr.map(i => {
        if (i.AnnouncementId === item.AnnouncementId) {
          i.IsRead = 1;
          return i;
        } else return i;
      });
      dispatch(unreadAnnouncementInfo(newArr));
    } else if (origin === originLabels.meter) {
      setMeterUnread(prev => prev - 1);
      id = item.AlertId;
      let dataArr = JSON.parse(JSON.stringify(unreadMeter));
      let newArr = dataArr.map(i => {
        if (i.AlertId === item.AlertId) {
          i.IsRead = 1;
          return i;
        } else return i;
      });
      dispatch(unreadMeterInfo(newArr));
    } else if (origin === originLabels.bill) {
      setBillUnread(prev => prev - 1);
      id = item.AlertId;
      let dataArr = JSON.parse(JSON.stringify(unreadBill));
      let newArr = dataArr.map(i => {
        if (i.AlertId === item.AlertId) {
          i.IsRead = 1;
          return i;
        } else return i;
      });
      dispatch(unreadBillInfo(newArr));
    }
    if (id) {
      updateData(origin, id, accountId).then(res => console.log("res"));
    }
  };

  return (
    <Card style={styles.cardStyles}>
      <TouchableWithoutFeedback onPress={() => handleChange()}>
        <View style={[styles.rowSpace, { padding: 10 }]}>
          <View
            style={read === 0 ? { width: width - 125 } : { width: width - 75 }}>
            <Text
              style={[
                styles.expandTitle,
                read === 0
                  ? { fontFamily: "NotoSans-Bold" }
                  : { fontFamily: "NotoSans-Regular" },
              ]}
              numberOfLines={1}>
              <Text style={{ color: GlobalStyles.colors.ePrimary.base }}>
                {moment(
                  item?.StartDate ? item.StartDate : item.InsertedDateTime,
                ).format("MMM DD,YYYY")}
              </Text>
              {origin == originLabels?.meter
                ? item?.Description && " - " + item.Description
                : origin == originLabels?.bill
                ? item?.amount && " - Bill Amount - " + item?.amount
                : origin === originLabels?.announcement
                ? item?.AnnouncementTitle && " - " + item?.AnnouncementTitle
                : item?.Description
                ? " - " + item?.Description
                : item?.EventType}
            </Text>
          </View>
          {read === 0 && (
            <View style={styles.chipBgStyle}>
              <Text style={styles.chipTextStyle}>unread</Text>
            </View>
          )}
          <View
            style={[
              { width: 25 },
              open === i
                ? {
                    transform: [{ rotateZ: "180deg" }],
                  }
                : {
                    alignItems: "flex-end",
                  },
            ]}>
            <Icon
              name="down-arrow-icon"
              color={GlobalStyles.colors.eMedium.base}
              size={15}
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
      {open === i && (
        <ScrollView>
          <View style={styles.horizontalLine} />
          <View style={styles.contentSpace}>
            {origin === originLabels?.announcement &&
            item?.AnnouncementDescription ? (
              <>
                <RenderHtml
                  baseStyle={{
                    fontSize: 12,
                    margin: 0,
                    fontFamily: "NotoSans-Regular",
                  }}
                  systemFonts={systemFonts}
                  enableExperimentalMarginCollapsing={true}
                  contentWidth={width}
                  source={{ html: item?.AnnouncementDescription }}
                  ignoredStyles={["fontFamily", "marginBottom"]}
                  defaultTextProps={{ numberOfLines: moreRead ? 0 : 2 }}
                />

                {imgUrlCode?.assetPath && hasImage && (
                  <Image
                    style={{ width: "100%", height: 200 }}
                    source={{
                      uri: imgUrlCode?.assetPath,
                    }}
                    resizeMode="contain"
                  />
                )}
                {!moreRead && (
                  <TextLink
                    textStyle={styles.textLinkStyle}
                    textLinkStyle={{
                      color: GlobalStyles.colors.ePrimary.base,
                    }}
                    pressingLinkStyle={{
                      color: GlobalStyles.colors.ePrimary.base,
                    }}
                    links={[
                      {
                        text: "Read more about this notice.",
                        onPress: () => {
                          setMoreRead(true);
                          setLessRead(true);
                        },
                      },
                    ]}>
                    Read more about this notice.
                  </TextLink>
                )}
                {lessRead && (
                  <TextLink
                    textStyle={styles.textLinkStyle}
                    textLinkStyle={{
                      color: GlobalStyles.colors.ePrimary.base,
                    }}
                    pressingLinkStyle={{
                      color: GlobalStyles.colors.ePrimary.base,
                    }}
                    links={[
                      {
                        text: "Show less",
                        onPress: () => {
                          setLessRead(false);
                          setMoreRead(false);
                        },
                      },
                    ]}>
                    Show less
                  </TextLink>
                )}
              </>
            ) : (
              <>
                <View style={styles.rowSpace}>
                  <Text style={[styles.blueColorText, { width: "50%" }]}>
                    Event Created
                  </Text>
                  <Text style={styles.valueText}>
                    {moment(item.CreatedDateTime).format(dateTimeFormat)}
                  </Text>
                </View>
                <View style={styles.rowSpace}>
                  <Text style={[styles.blueColorText, { width: "50%" }]}>
                    {origin === originLabels.meter
                      ? "Meter Badge Number"
                      : "Bill ID"}
                  </Text>
                  <Text style={styles.valueText}>
                    {origin === originLabels.meter
                      ? item?.meterBadgeNumber
                      : item?.billId}
                  </Text>
                </View>
              </>
            )}
          </View>
        </ScrollView>
      )}
    </Card>
  );
}

const styles = StyleSheet.create({
  rowSpace: {
    flexDirection: "row",
    alignItems: "center",
  },
  cardStyles: {
    marginHorizontal: 15,
    borderRadius: 11,
    backgroundColor: GlobalStyles.colors.eFaint.hover,
    boxShadow: "rgb(0 0 0 / 5%) 1px 5px 13px 0px",
    position: "relative",
    marginVertical: "2%",
  },
  textLinkStyle: {
    textAlign: "right",
    fontSize: 12,
    width: "100%",
    fontFamily: "NotoSans-SemiBold",
  },
  horizontalLine: {
    borderTopWidth: 1,
    borderColor: GlobalStyles.colors.eLight.base,
    width: "100%",
  },
  contentSpace: {
    margin: "4%",
    marginBottom: "5%",
  },
  blueColorText: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
  },
  expandTitle: {
    fontSize: 12,
    color: GlobalStyles.colors.eRich.base,
  },
  chipTextStyle: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 10,
    width: 35,
  },
  chipBgStyle: {
    backgroundColor: GlobalStyles.colors.eTertiary.base,
    padding: 5,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 10,
    marginLeft: 5,
  },
  valueText: {
    fontSize: 12,
    width: "50%",
    color: GlobalStyles.colors.eRich.base,
  },
});

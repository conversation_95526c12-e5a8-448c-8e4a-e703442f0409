import { StyleSheet, View } from "react-native";
import { GlobalStyles } from "../../app/global-styles";
import Button from "../../common/_button";
import { useContext } from "react";
import { registerContext } from "../e_auth_pages";
import { registerContextWO } from "../e_auth_pages_wo";
import { stackContext } from "../../app/get_stack";

export default function AuthButtons() {
  const { workModelType } = React.useContext(stackContext);
  let setIsLoginPage, isLoginPage;

  if (workModelType === "WA") {
    ({ setIsLoginPage, isLoginPage } = useContext(registerContext));
  } else {
    ({ setIsLoginPage, isLoginPage } = useContext(registerContextWO));
  }

  return (
    <View style={styles.container}>
      <Button
        onPress={() => setIsLoginPage("Login")}
        buttonbgColor={[
          styles.pressable,
          isLoginPage === "Login" ? styles.selected : styles.unselected,
        ]}
        textColor={[
          styles.textColor,
          isLoginPage === "Login" && styles.selectedText,
        ]}>
        Login
      </Button>
      {/* <Button
        onPress={() => setIsLoginPage("Register")}
        buttonbgColor={[
          styles.pressable,
          isLoginPage === "Register" ? styles.selected : styles.unselected,
        ]}
        textColor={[
          styles.textColor,
          isLoginPage === "Register" && styles.selectedText,
        ]}>
        Register
      </Button> */}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: 200,
    height: 46,
    borderRadius: 5,
    paddingVertical: 8,
    paddingHorizontal: 8,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    elevation: 2,
    shadowColor: "black",
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    flexDirection: "row",
  },
  textColor: {
    color: GlobalStyles.colors.eRich.base,
    fontSize: 12,
    paddingVertical: 2,
    fontFamily: "NotoSans-Medium",
  },
  selectedText: {
    color: GlobalStyles.colors.eWhite.base,
    paddingVertical: 2,
    fontSize: 13,
    marginTop: -2,
    fontFamily: "NotoSans-SemiBold",
  },
  pressable: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    marginVertical: -5,
    marginLeft: -1,
    marginRight: -3,
  },
  selected: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    borderRadius: 5,
  },
  unselected: {
    borderRadius: 0,
    borderColor: "none",
    shadowOffset: { width: 0, height: 0 },
    shadowColor: "none",
    elevation: 0,
    shadowOpacity: 0,
  },
});

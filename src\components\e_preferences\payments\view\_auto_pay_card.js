import AsyncStorage from "@react-native-async-storage/async-storage";
import moment from "moment";
import React, { useEffect, useState } from "react";
import { useContext } from "react";
import {
  StyleSheet,
  Text,
  TouchableWithoutFeedback,
  View,
  Keyboard,
} from "react-native";
import { TextInput, Card, HelperText } from "react-native-paper";
import { useSelector } from "react-redux";
import { drawerContext } from "../../../app/authenticated/authenticated_layout";
import { GlobalStyles } from "../../../app/global-styles";
import DropDown from "../../../common/_dropdown";
import Icon from "../../../icon";
import { EAutoPayServices } from "../model/auto_pay_service";
import AutoPayButton from "./_buttons";
import { config } from "../../../../environment";
import { drawerContextWO } from "../../../app/authenticated/authenticated_layout_wo";
import { stackContext } from "../../../app/get_stack";

export default function AutoPayCard({
  item,
  sourceDropdown,
  sourceList,
  cancelClick,
  deleteItem,
  saveFunction,
  checked,
}) {
  const { workModelType } = React.useContext(stackContext);
  const [open, setOpen] = useState(false);
  const [readOnly, setReadOnly] = useState(true);
  const [startDate, setStartDate] = useState();
  const [startDateErr, setStartDateErr] = useState(false);
  const [endDate, setEndDate] = useState();
  const [endDateErr, setEndDateErr] = useState(false);
  const [cardNumber, setCardNumber] = useState();
  const [cardNumberErr, setCardNumberErr] = useState(false);
  const [cardName, setCardName] = useState();
  const [cardNameErr, setCardNameErr] = useState(false);
  const [expiresOn, setExpiresOn] = useState();
  const [expiresOnErr, setExpiresOnErr] = useState(false);
  const [cvvCode, setcode] = useState();
  const [cvvCodeErr, setcvvCodeErr] = useState(false);
  const [selectedSource, setSelectedSource] = useState();
  const [sourceCodeErr, setSourceCodeErr] = useState(false);
  const [close, setClose] = useState(false);
  const [sourceCode, setSourceCode] = useState();
  const [active, setActive] = useState(true);
  const [disableButton, setDisableButton] = useState(true);
  const [isLoading, setLoading] = useState(false);
  const accountId = useSelector(
    state => state?.meterDetails?.meterDetails?.accountId,
  );
  const MAX_LIMIT_TENENT_CODE = useSelector(
    state =>
      state?.parameter?.parameter?.ParameterLookup
        ?.AUTO_PAY_MAX_WITHDRAWAL_LIMIT,
  );
  let setPopup,
    setTitle,
    setContent,
    setError,
    setButton,
    setIcon,
    setPopupCode;

  if (workModelType === "WA") {
    ({
      setPopup,
      setTitle,
      setContent,
      setError,
      setButton,
      setIcon,
      setPopupCode,
    } = useContext(drawerContext));
  } else {
    ({
      setPopup,
      setTitle,
      setContent,
      setError,
      setButton,
      setIcon,
      setPopupCode,
    } = useContext(drawerContextWO));
  }

  const cardNumberHandleChange = e => {
    if (e !== "-") {
      setCardNumber(e.replace(/[^0-9]/g, ""));
      setCardNumberErr(false);
    }
  };

  const startDateValidation = () => {
    if (startDate) {
      let dateContain = "";
      if (startDate.includes("/")) {
        dateContain = startDate.split("/");
      }
      if (startDate.includes("-")) {
        dateContain = startDate.split("-");
      }
      if (dateContain) {
        if (
          dateContain?.[0]?.length === 2 &&
          dateContain?.[1]?.length === 2 &&
          dateContain?.[2]?.length === 4
        ) {
          let start = moment(startDate, "DD-MM-YYYY").format("YYYY-MM-DD");
          let current_date = moment().format("YYYY-MM-DD");
          moment(start).isSameOrAfter(current_date)
            ? setStartDateErr(false)
            : setStartDateErr(
                "Start Date should be same or after current date.",
              );
          if (endDate) {
            endDateValidation();
          }
        } else {
          setStartDateErr('Date formate should be in "DD-MM-YYYY"!');
        }
      } else {
        setStartDateErr("Start Date is invalid!");
      }
    } else {
      setStartDateErr("Start Date is required!");
    }
  };

  const endDateValidation = () => {
    if (endDate) {
      let dateContain = "";
      if (endDate.includes("/")) {
        dateContain = endDate.split("/");
      }
      if (endDate.includes("-")) {
        dateContain = endDate.split("-");
      }
      if (dateContain) {
        if (
          dateContain?.[0]?.length === 2 &&
          dateContain?.[1]?.length === 2 &&
          dateContain?.[2]?.length === 4
        ) {
          let start = moment(startDate, "DD-MM-YYYY").format("YYYY-MM-DD");
          let end = moment(endDate, "DD-MM-YYYY").format("YYYY-MM-DD");
          moment(end).isAfter(start)
            ? setEndDateErr(false)
            : setEndDateErr("End Date should be after start date!.");
          if (expiresOn) {
            expiresOnValidation();
          }
        } else {
          setEndDateErr('Date formate should be "DD-MM-YYYY"!');
        }
      } else {
        setEndDateErr("End Date is invalid!");
      }
    } else {
      setEndDateErr("End Date is required!");
    }
  };

  const cardHolderValidation = () => {
    if (cardNumber) {
      if (cardNumber.length === 16) {
        setCardNumberErr(false);
      } else {
        setCardNumberErr(true);
      }
    } else {
      setCardNumberErr(true);
    }
  };

  const cardNameValidation = () => {
    if (cardName) {
      setCardNameErr(false);
    } else {
      setCardNameErr(true);
    }
  };

  const expiresOnValidation = () => {
    if (expiresOn) {
      let expire = "";
      if (expiresOn.includes("-")) {
        expire = expiresOn.split("-");
      } else if (expiresOn.includes("/")) {
        expire = expiresOn.split("/");
      }
      if (expire) {
        var expiration = moment(expiresOn, "MM-YYYY").format("YYYY-MM-DD");
        var current_date = moment(endDate, "DD-MM-YYYY").format("YYYY-MM-DD");
        if (Number(expire[0]) < 13 && expire[1].length === 4) {
          if (moment(expiration).isAfter(current_date)) {
            setExpiresOnErr(false);
          } else {
            setExpiresOnErr("Expire On should be after end date!");
          }
        } else {
          setExpiresOnErr("Expire On should be in 'MM-YYYY' formate");
        }
      }
    } else {
      setExpiresOnErr("Expire On is required!");
    }
  };

  const cvvValidation = () => {
    if (cvvCode && cvvCode.length === 3) {
      setcvvCodeErr(false);
    } else {
      setcvvCodeErr(true);
    }
  };

  const handleChangeSource = e => {
    setSelectedSource(e);
    sourceList.map(
      source =>
        source.description === e && setSourceCode(source.autopaySrcCode),
    );
  };

  useEffect(() => {
    if (item) {
      setReadOnly(!item.editable);
      let start = moment(item.startDate).format("DD-MM-YYYY");
      setStartDate(item.startDate ? start : "");
      let end = moment(item.endDate).format("DD-MM-YYYY");
      setEndDate(item.endDate ? end : "");
      setCardNumber(item.externalAccountId);
      setCardName(item.entityName);
      setExpiresOn(
        item.expireDate
          ? moment(item.expireDate, "MM/YYYY").format("MM-YYYY")
          : "",
      );
      setcode();
      sourceList &&
        sourceList.map(
          source =>
            source.autopaySrcCode === item.autopaySource &&
            setSelectedSource(source.description),
        );
      setSourceCode(item.autopaySource);
      checked === false
        ? setActive(false)
        : item.status === "INACTIVE"
        ? setActive(false)
        : item.status === "ACTIVE"
        ? setActive(true)
        : setActive(false);
    }
  }, [item, sourceList]);

  useEffect(() => {
    if (
      startDate &&
      startDateErr === false &&
      endDate &&
      endDateErr === false &&
      cardNumber &&
      cardNumberErr === false &&
      cardName &&
      cardNameErr === false &&
      expiresOn &&
      expiresOnErr === false &&
      cvvCode &&
      cvvCodeErr === false
    ) {
      setDisableButton(false);
    } else {
      setDisableButton(true);
    }
  }, [
    startDate,
    startDateErr,
    endDateErr,
    cardNameErr,
    cardNumberErr,
    expiresOnErr,
    cvvCodeErr,
    endDate,
    cardName,
    cardNumber,
    expiresOn,
    cvvCode,
    selectedSource,
  ]);

  const saveClick = () => {
    startDateValidation();
    endDateValidation();
    cardHolderValidation();
    cardNameValidation();
    expiresOnValidation();
    cvvValidation();
    if (
      startDate &&
      startDateErr === false &&
      endDate &&
      endDateErr === false &&
      cardNumber &&
      cardNumberErr === false &&
      cardName &&
      cardNameErr === false &&
      expiresOn &&
      expiresOnErr === false &&
      cvvCode &&
      cvvCodeErr === false
    ) {
      if (sourceCode) {
        setSourceCodeErr(false);
        setLoading(true);
        accountId &&
          EAutoPayServices.createAndSaveNewRecord(
            accountId,
            moment(startDate, "DD-MM-YYYY").format("YYYY-MM-DD"),
            moment(endDate, "DD-MM-YYYY").format("YYYY-MM-DD"),
            cardName,
            cardNumber,
            expiresOn,
            sourceCode,
            MAX_LIMIT_TENENT_CODE,
          )
            .then(response => {
              setLoading(false);
              saveFunction();
              setPopup(true);
              setTitle("Success");
              setContent("Your changes were saved successfully!");
              setError("SUCCESS");
              setButton(false);
              setIcon("Sucess-icon");
              setPopupCode();
              setOpen(false);
            })
            .catch(error => {
              setLoading(false);
              setPopup(true);
              setTitle("Failure");
              setContent(
                error?.response?.status !== 500
                  ? error.response.data.errors[0].message
                  : "Something went wrong.Please try again later.",
              );
              setError("ERROR");
              setButton(false);
              setIcon("Exclamationmark-fill-icon");
              setPopupCode();
            });
      } else {
        setSourceCodeErr(true);
      }
    }
  };

  useEffect(() => {
    if (item.editable === true) {
      setOpen(true);
    } else {
      setOpen(false);
    }
  }, [item.editable]);

  useEffect(() => {
    if (checked === false) {
      setActive(false);
    } else {
      setActive(item.status === "ACTIVE");
    }
  }, [checked, item.status]);

  // useEffect(() => {
  //   if (disableButton) {
  //     AsyncStorage.removeItem("enabled_state");
  //   } else {
  //     AsyncStorage.setItem("enabled_state", 1);
  //   }
  // }, [disableButton]);

  const openDrawer = () => {
    setOpen(prev => !prev);
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
      <View style={{ flex: 1 }}>
        <Card style={[styles.cardStyle, active && styles.cardBorder]}>
          {active && (
            <Icon
              name="Sucess-icon"
              color={GlobalStyles.colors.eSecondary.base}
              size={23}
              style={[
                styles.successIcon,
                open ? { top: "-1%" } : { top: "-6%" },
              ]}
            />
          )}
          <View style={styles.content}>
            <TouchableWithoutFeedback onPress={readOnly ? openDrawer : null}>
              <View>
                <View style={styles.rowSpace}>
                  <View
                    style={[
                      { marginTop: "3%", width: "100%" },
                      open
                        ? {
                            transform: [{ rotateZ: "180deg" }],
                          }
                        : {
                            alignItems: "flex-end",
                          },
                    ]}>
                    <Icon
                      name="down-arrow-icon"
                      color={GlobalStyles.colors.eMedium.base}
                      size={15}
                      onPress={openDrawer}
                    />
                  </View>
                </View>
                <View style={styles.rowSpace}>
                  <View style={{ width: "47%" }}>
                    <Text style={styles.textCls}>
                      Start Date
                      {!readOnly && (
                        <Text
                          style={{ color: GlobalStyles.colors.eDanger.dark }}>
                          *
                        </Text>
                      )}
                    </Text>
                    <TextInput
                      mode="outlined"
                      dense
                      outlineColor={GlobalStyles.colors.eOutline.base}
                      keyboardType="default"
                      activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
                      placeholder="DD-MM-YYYY"
                      maxLength={10}
                      disabled={readOnly}
                      style={styles.inputcls}
                      value={startDate}
                      onChangeText={e => {
                        setStartDate(e);
                        setStartDateErr(false);
                      }}
                      onBlur={startDateValidation}
                      error={startDateErr !== false}
                    />
                    <HelperText
                      type="error"
                      visible={startDateErr !== false && !readOnly}
                      padding="none">
                      {startDateErr}
                    </HelperText>
                  </View>
                  <View style={{ width: "47%" }}>
                    <Text style={styles.textCls}>
                      End Date
                      {!readOnly && (
                        <Text
                          style={{ color: GlobalStyles.colors.eDanger.dark }}>
                          *
                        </Text>
                      )}
                    </Text>
                    <TextInput
                      mode="outlined"
                      dense
                      maxLength={10}
                      outlineColor={GlobalStyles.colors.eOutline.base}
                      keyboardType="default"
                      activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
                      placeholder="DD-MM-YYYY"
                      disabled={readOnly}
                      style={styles.inputcls}
                      value={endDate}
                      onChangeText={e => {
                        setEndDate(e);
                        setEndDateErr(false);
                      }}
                      onBlur={endDateValidation}
                      error={endDateErr !== false}
                    />
                    <HelperText
                      type="error"
                      visible={endDateErr !== false && !readOnly}
                      padding="none">
                      {endDateErr}
                    </HelperText>
                  </View>
                </View>
                {readOnly && !open && (
                  <Icon
                    name="Delete-icon"
                    size={20}
                    color={GlobalStyles.colors.ePrimary.base}
                    style={{ width: "100%", textAlign: "right" }}
                    onPress={e => deleteItem(item.accountAutopayId)}
                  />
                )}
              </View>
            </TouchableWithoutFeedback>
            {open && (
              <View style={{ flex: 1 }}>
                <View style={{ marginBottom: "5%" }}>
                  <Text style={styles.textCls}>
                    Source Code
                    {!readOnly && (
                      <Text style={{ color: GlobalStyles.colors.eDanger.dark }}>
                        *
                      </Text>
                    )}
                  </Text>
                  <DropDown
                    data={sourceDropdown}
                    onChange={handleChangeSource}
                    defaultvalue={readOnly ? selectedSource : null}
                    close={close}
                    setClose={setClose}
                    customSelectButton={false}
                    disabled={readOnly}
                    title="Select Option"
                  />
                  <HelperText
                    type="error"
                    visible={sourceCodeErr && !readOnly}
                    padding="none">
                    select sourceCode
                  </HelperText>
                </View>

                <Text style={styles.textCls}>
                  Card Number
                  {!readOnly && (
                    <Text style={{ color: GlobalStyles.colors.eDanger.dark }}>
                      *
                    </Text>
                  )}
                </Text>
                <TextInput
                  mode="outlined"
                  dense
                  outlineColor={GlobalStyles.colors.eOutline.base}
                  keyboardType="numeric"
                  activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
                  placeholder="XXXX-XXXX-XXXX-XXXX"
                  disabled={readOnly}
                  maxLength={16}
                  style={styles.inputcls}
                  value={cardNumber}
                  onChangeText={cardNumberHandleChange}
                  onBlur={cardHolderValidation}
                  error={cardNumberErr}
                />
                <HelperText
                  type="error"
                  visible={cardNumberErr !== false && !readOnly}
                  padding="none">
                  card Number is invalid!
                </HelperText>

                <Text style={styles.textCls}>
                  Card Holder's Name
                  {!readOnly && (
                    <Text style={{ color: GlobalStyles.colors.eDanger.dark }}>
                      *
                    </Text>
                  )}
                </Text>
                <TextInput
                  mode="outlined"
                  dense
                  outlineColor={GlobalStyles.colors.eOutline.base}
                  keyboardType="default"
                  activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
                  disabled={readOnly}
                  style={styles.inputcls}
                  value={cardName}
                  onChangeText={e => {
                    setCardName(e);
                    setCardNameErr(false);
                  }}
                  onBlur={cardNameValidation}
                  error={cardNameErr}
                />
                <HelperText
                  type="error"
                  visible={cardNameErr && !readOnly}
                  padding="none">
                  card holder's name is invalid!
                </HelperText>

                <View style={styles.rowSpace}>
                  <View style={{ width: "47%" }}>
                    <Text style={styles.textCls}>
                      Expires On
                      {!readOnly && (
                        <Text
                          style={{ color: GlobalStyles.colors.eDanger.dark }}>
                          *
                        </Text>
                      )}
                    </Text>
                    <TextInput
                      mode="outlined"
                      dense
                      maxLength={7}
                      outlineColor={GlobalStyles.colors.eOutline.base}
                      keyboardType="default"
                      activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
                      placeholder="MM-YYYY"
                      disabled={readOnly}
                      style={styles.inputcls}
                      value={expiresOn}
                      onChangeText={e => {
                        setExpiresOn(e);
                        setExpiresOnErr(false);
                      }}
                      onBlur={expiresOnValidation}
                      error={expiresOnErr !== false}
                    />
                    <HelperText
                      type="error"
                      visible={expiresOnErr !== false && !readOnly}
                      padding="none">
                      {expiresOnErr}
                    </HelperText>
                  </View>
                  <View style={{ width: "47%" }}>
                    <Text style={styles.textCls}>
                      CSV / CVV
                      {!readOnly && (
                        <Text
                          style={{ color: GlobalStyles.colors.eDanger.dark }}>
                          *
                        </Text>
                      )}
                    </Text>
                    <TextInput
                      mode="outlined"
                      dense
                      maxLength={3}
                      outlineColor={GlobalStyles.colors.eOutline.base}
                      keyboardType="numeric"
                      activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
                      placeholder="XXX"
                      disabled={readOnly}
                      style={styles.inputcls}
                      value={cvvCode}
                      onChangeText={e => {
                        setcode(e.replace(/[^0-9]/g, ""));
                        setcvvCodeErr(false);
                      }}
                      onBlur={cvvValidation}
                      error={cvvCodeErr}
                    />
                    <HelperText
                      type="error"
                      visible={cvvCodeErr == true && !readOnly}
                      padding="none">
                      {cvvCodeErr === true && " csv/cvv is invalid!"}
                    </HelperText>
                  </View>
                </View>
                {readOnly && (
                  <Icon
                    name="Delete-icon"
                    color={GlobalStyles.colors.ePrimary.base}
                    style={{ width: "100%", textAlign: "right" }}
                    size={20}
                    onPress={e => deleteItem(item.accountAutopayId)}
                  />
                )}
              </View>
            )}
          </View>
        </Card>
        {!readOnly && (
          <AutoPayButton
            disableButton={disableButton}
            cancelClick={cancelClick}
            saveClick={saveClick}
            isLoading={isLoading}
          />
        )}
        <View style={{ margin: "2%" }} />
      </View>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  cardStyle: {
    marginHorizontal: "3%",
    borderRadius: 10,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    boxShadow: "rgb(0 0 0 / 5%) 1px 5px 13px 0px",
    position: "relative",
  },
  cardBorder: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
  },
  content: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  successIcon: {
    position: "absolute",
    right: "3%",
    borderColor: GlobalStyles.colors.eWhite.base,
  },
  rowSpace: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: "2%",
  },
  inputcls: {
    color: GlobalStyles.colors.eDark.base,
    backgroundColor: GlobalStyles.colors.eBackground.base,
    fontSize: 14,
  },
  textCls: {
    color: GlobalStyles.colors.eRich.base,
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
  },
});

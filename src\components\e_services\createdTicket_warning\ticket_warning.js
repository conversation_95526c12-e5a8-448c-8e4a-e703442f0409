import { Card, IconButton, Text } from "react-native-paper";
import FlatButton from "../../common/_flat_button";
import { GlobalStyles } from "../../app/global-styles";
import { StyleSheet, Image } from "react-native";
import React, { useContext } from "react";
import { ticketContext } from "../e_services";
import Icon from "../../icon";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import { config } from "../../../environment";
import { stackContext } from "../../app/get_stack";

export default function TicketWarning() {
  const { workModelType } = React.useContext(stackContext);
  let openDrawer, setOpenMenu, menuFlag, setmenuFlag;

  if (workModelType === "WA") {
    ({ openDrawer, setOpenMenu, menuFlag, setmenuFlag } =
      useContext(drawerContext));
  } else {
    ({ openDrawer, setOpenMenu, menuFlag, setmenuFlag } =
      useContext(drawerContextWO));
  }

  const viewDetails = () => {
    setOpenMenu(true);
    setmenuFlag(false);
  };

  return (
    <Card style={styles.cardDetails}>
      <Icon
        name="info-icon"
        size={50}
        style={styles.imgCls}
        color={GlobalStyles.colors.ePrimary.base}
      />
      <Text style={styles.alertCls}>
        Your Service Request is being processed. You can view the status of your
        request using
      </Text>
      <FlatButton onPress={viewDetails} textStyles={styles.details}>
        Past Tickets Summary
      </FlatButton>
    </Card>
  );
}

const styles = StyleSheet.create({
  iconClass: {
    width: "100%",
  },
  cardDetails: {
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    width: "100%",
    padding: "6%",
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    height: 240,
    paddingTop: "10%",
    paddingBottom: "10%",
  },
  alertCls: {
    fontSize: 14,
    color: GlobalStyles.colors.ePrimary.base,
    textAlign: "center",
    letterSpacing: 0,
    opacity: 1,
    paddingHorizontal: 30,
  },
  details: {
    color: GlobalStyles.colors.ePrimary.base,
    textDecorationLine: "underline",
    fontFamily: "NotoSans-Bold",
    alignSelf: "center",
    fontSize: 14,
    paddingHorizontal: 30,
    paddingTop: "3%",
  },
  imgCls: {
    alignSelf: "center",
    marginBottom: "7%",
  },
});

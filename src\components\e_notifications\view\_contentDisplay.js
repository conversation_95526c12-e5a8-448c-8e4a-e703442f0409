import moment from "moment";
import { useEffect, useRef } from "react";
import { useState } from "react";
import {
  ActivityIndicator,
  Dimensions,
  ScrollView,
  StyleSheet,
  View,
  Text,
} from "react-native";
import { useSelector } from "react-redux";
import { GlobalStyles } from "../../app/global-styles";
import ExpandItems from "./_expand_item";

export default function ContentDisplay({
  // finalData,
  readData,
  setMeterUnread,
  setBillUnread,
  setAnnouncementUnread,
}) {
  const expandRef = useRef();
  const pathName = useSelector(state => state?.servicePath?.servicePath);
  const meterData = useSelector(
    state => state?.unreadMeterInfo?.unreadMeterInfo,
  );
  const billData = useSelector(state => state?.unreadBillInfo?.unreadBillInfo);
  const annData = useSelector(
    state => state?.unreadAnnouncementInfo?.unreadAnnouncementInfo,
  );
  const { height } = Dimensions.get("window");
  const [displayData, setDisplayData] = useState();
  const [origin, setOrigin] = useState();
  const [data, setData] = useState();

  useEffect(() => {
    if (pathName === "Notifications") {
      setOrigin("METER");
    } else {
      setOrigin(pathName);
    }
  }, [pathName]);

  useEffect(() => {
    if (meterData && billData && annData) {
      if (pathName === "Notifications" || pathName === "METER") {
        let mData = JSON.parse(JSON.stringify(meterData));
        setDisplayData(mData);
        setOrigin("METER");
      } else if (pathName === "BILL") {
        let bData = JSON.parse(JSON.stringify(billData));
        setDisplayData(bData);
        setOrigin(pathName);
      } else if (pathName === "ANNOUNCEMENT") {
        let aData = JSON.parse(JSON.stringify(annData));
        setDisplayData(aData);
        setOrigin(pathName);
      }
    }
  }, [pathName, meterData, annData, billData]);

  useEffect(() => {
    let newData = [];
    if (displayData) {
      displayData.sort(
        (a, b) =>
          new Date(b.StartDate ? b.StartDate : b.InsertedDateTime) -
          new Date(a.StartDate ? a.StartDate : a.InsertedDateTime),
      );
      if (readData === "READ") {
        newData = displayData.filter(i => i.IsRead === 1);
      } else if (readData === "UNREAD") {
        newData = displayData.filter(i => i.IsRead == 0);
      } else {
        newData = displayData;
      }

      setData(newData);
    }
  }, [readData, displayData]);
  useEffect(() => {
    if (data) {
      let tempData = data.filter(i => i.IsRead === 0);
      if (origin === "METER") {
        setMeterUnread(tempData.length);
      } else if (origin === "BILL") {
        setBillUnread(tempData.length);
      } else if (origin === "ANNOUNCEMENT") {
        setAnnouncementUnread(tempData.length);
      }
    }
  }, [data]);

  return (
    <ScrollView style={{ height: height - 335 }}>
      {data ? (
        data.length > 0 ? (
          data.map((data, key) => {
            return (
              <View ref={expandRef} key={key}>
                <ExpandItems
                  readData={readData}
                  key={key}
                  item={data}
                  origin={origin}
                  setMeterUnread={setMeterUnread}
                  setAnnouncementUnread={setAnnouncementUnread}
                  setBillUnread={setBillUnread}
                  i={data?.AnnouncementId ? data.AnnouncementId : data?.AlertId}
                />
              </View>
            );
          })
        ) : (
          <View style={[styles.noContentText, { marginTop: height / 4 }]}>
            <Text style={{ fontFamily: "NotoSans-SemiBold" }}>
              No Content Found
            </Text>
          </View>
        )
      ) : (
        <ActivityIndicator color={GlobalStyles.colors.ePrimary.base} />
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  rowSpace: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: "3%",
  },
  noContentText: {
    alignItems: "center",
  },
});

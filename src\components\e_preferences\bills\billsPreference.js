import { useEffect, useState } from "react";
import { ActivityIndicator, ScrollView, View } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { defaultAlertsPreference } from "../../../redux/slices/defaultAlertsPreference";
import { defaultBillRouting } from "../../../redux/slices/defaultBillRouting";
import { alertsPreference } from "../../../redux/slices/handleChangeAlertsPreference";
import { billRouting } from "../../../redux/slices/handleChangeBillRouting";
import { GlobalStyles } from "../../app/global-styles";
import ModalPopup from "../_modal_popup";
import { billPreferenceService } from "./model/billPreferenceService";
import BillAlertsNotifications from "./view/_bill_alerts_notifications";
import BillRouting from "./view/_bill_routing";
import SaveButton from "./view/_save_button";

export default function Bills() {
  let accountId = useSelector(
    store => store?.meterDetails?.meterDetails?.accountId,
  );
  const [billRoutingLoading, setBillRoutingLoading] = useState(true);
  const [billAlertsError, setBillAlertsError] = useState();
  const dispatch = useDispatch();
  useEffect(() => {
    if (accountId) {
      dispatch(defaultAlertsPreference());
      dispatch(alertsPreference());
      setBillRoutingLoading(true);
      billPreferenceService
        .getBillNotifyPreference(accountId)
        .then(response => {
          if (response?.data?.getBillNotifyPreference?.notificationTypes) {
            let data =
              response?.data?.getBillNotifyPreference?.notificationTypes.filter(
                i => i.applicableFor !== "PREPAID",
              );
            dispatch(defaultAlertsPreference(data));
            dispatch(alertsPreference(data));
            setBillAlertsError();
            setBillRoutingLoading(false);
          }
        })
        .catch(err => {
          setBillAlertsError(err);
          dispatch(defaultAlertsPreference());
          dispatch(alertsPreference());
          setBillRoutingLoading(false);
        });
      billPreferenceService
        .getBEbill(accountId)
        .then(response => {
          if (response?.data?.getBEbill) {
            dispatch(defaultBillRouting(response?.data?.getBEbill));
            dispatch(billRouting(response?.data?.getBEbill));
            setBillRoutingLoading(false);
          }
        })
        .catch(err => {
          setBillRoutingLoading(false);
          dispatch(defaultBillRouting());
          dispatch(billRouting());
        });
    }
  }, [accountId]);

  return (
    <View style={{ marginBottom: "8%" }}>
      <BillRouting />
      <View style={{ margin: "2%" }} />
      {billAlertsError ? null : (
        <BillAlertsNotifications billRoutingLoading={billRoutingLoading} />
      )}
      <SaveButton />
    </View>
  );
}

import { Text, Card, List } from "react-native-paper";
import { GlobalStyles } from "../../../app/global-styles";
import {
  StyleSheet,
  View,
  Dimensions,
  ScrollView,
  ActivityIndicator,
  Image,
} from "react-native";
import { useSelector } from "react-redux";
import TipsItem from "./_tips_item";
import { commonService } from "../../../../services/common.service";
import { useState, useEffect } from "react";
import { selfhelpFAQService } from "../../modal/_faqs-service";
import moment from "moment";

export default function Tips() {
  const { height } = Dimensions.get("window");
  let noTipsimage = useSelector(
    store => store?.parameter?.parameter?.ParameterLookup?.E_NO_TIPS_IMAGE,
  );
  const [NoTipsImage, setNoTipsImage] = useState("");
  const [noTips, setNoTips] = useState(false);
  const [TipsData, setTipsData] = useState();
  const [loading, setLoading] = useState(true);

  let customerClass = useSelector(
    store => store?.meterDetails?.meterDetails?.customerClassCd,
  );

  let languageCode = useSelector(
    state =>
      state?.parameter?.parameter?.ParameterLookup?.TENANT_PRIMARY_LANGUAGE,
  );

  useEffect(() => {
    setLoading(true);
    let customerClassFinal = "";
    if (customerClass && languageCode) {
      if (customerClass.includes("-")) {
        customerClassFinal = customerClass.split("-")?.[1];
      } else {
        customerClassFinal = customerClass;
      }
      selfhelpFAQService
        .Tips(customerClassFinal, languageCode)
        .then(res => {
          if (res?.data?.getTips?.tipsDetails) {
            let data = res.data.getTips.tipsDetails;
            if (data.length > 0) {
              setTipsData(data);
              setLoading(false);
              setNoTips(false);
            } else {
              setNoTips(true);
              setLoading(false);
            }
          } else {
            setNoTips(true);
            setLoading(false);
          }
        })
        .catch(err => {
          setNoTips(true);
          setLoading(false);
        });
    }
  }, [customerClass, languageCode]);

  useEffect(() => {
    if (noTipsimage && noTips === true) {
      commonService
        .getAssestUrl(noTipsimage)
        .then(response => {
          setNoTipsImage(response);
        })
        .catch(error => {
          setNoTipsImage("");
        });
    }
  }, [noTipsimage, noTips]);

  return (
    <View style={styles.aroundMargin}>
      <Card
        style={[
          styles.card,
          Platform.OS === "ios"
            ? { height: height - 365 }
            : { height: height - 310 },
        ]}>
        {loading ? (
          <ActivityIndicator
            size="large"
            color={GlobalStyles.colors.ePrimary.base}
          />
        ) : noTips ? (
          <>
            <Text style={styles.titleStyle}>ENERGY SAVING TIP(S)</Text>
            <View style={styles.lineStyle} />
            <View style={styles.content}>
              {NoTipsImage && (
                <Image
                  style={{ width: "100%", height: 200, marginBottom: 25 }}
                  source={{
                    uri: NoTipsImage?.assetPath,
                  }}
                  resizeMode="contain"
                />
              )}
              <Text style={styles.noTipsStyle}>No Tips Available</Text>
            </View>
          </>
        ) : (
          <ScrollView>
            <Text style={styles.titleStyle}>ENERGY SAVING TIP(S)</Text>
            <View style={styles.lineStyle} />
            {TipsData && TipsData.map(item => <TipsItem item={item} />)}
          </ScrollView>
        )}
      </Card>
    </View>
  );
}

const styles = StyleSheet.create({
  aroundMargin: {
    marginLeft: "5%",
    marginRight: "5%",
    height: "50%",
  },
  card: {
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    width: "100%",
    padding: "1%",
    borderColor: "white",
    display: "flex",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
  },
  lineStyle: {
    borderBottomWidth: 1,
    borderBottomColor: GlobalStyles.colors.eOutline.base,
    marginHorizontal: 10,
  },
  noTipsStyle: {
    fontSize: 12,
    color: GlobalStyles.colors.ePrimary.base,
    textAlign: "center",
  },
  titleStyle: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    textAlign: "left",
    paddingVertical: -5,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    marginHorizontal: "5%",
    marginVertical: "3%",
  },
  content: {
    paddingVertical: 25,
    paddingHorizontal: 25,
  },
});

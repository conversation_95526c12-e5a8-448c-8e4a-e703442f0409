import React, { useState, useEffect, useContext } from "react";
import { Card, Text } from "react-native-paper";
import {
  StyleSheet,
  View,
  Dimensions,
  Linking,
  ActivityIndicator,
  FlatList, // Import FlatList
} from "react-native";
import { GlobalStyles } from "../../app/global-styles";
import Map from "./_map";
import TodayWorkOrderList from "./_today_work-order-list";
import { homeContext } from "../e_home";
import { homeContextWO } from "../e_home_wo";
import { config } from "../../../environment";
import Workactivities from "../../e_workactivities/e_workactivities";
import { useTranslation } from "react-i18next";
import TodayWorkOrderListWO from "./_today_work-order-list_wo";
import { stackContext } from "../../app/get_stack";

export default function TodaysTasks() {
  const { workModelType } = React.useContext(stackContext);
  const { t } = useTranslation();
  let todayList, markers;

  if (workModelType === "WA") {
    ({ todayList, markers } = useContext(homeContext));
  } else {
    ({ todayList, markers } = useContext(homeContextWO));
  }

  console.log("TodaysTasks is re-rendering");

  return (
    <>
      <Card style={styles.card}>
        <View style={{ height: Dimensions.get("window").height / 3 }}>
          <View>
            <View style={styles.wrapDirection}>
              <View>
                <Text style={styles.titleCard}>{t("STOPPAGES")}</Text>
              </View>
              <View style={styles.lineStyle} />
            </View>
          </View>
          <View style={styles.listclass}>
            {/* <Workactivities /> */}
            {workModelType == "WA" ? (
              <TodayWorkOrderList />
            ) : (
              <TodayWorkOrderListWO />
            )}
          </View>
        </View>
      </Card>
    </>
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    width: "100%",
    padding: 15,
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    backgroundColor: GlobalStyles.colors.eWhite.base,
  },
  listclass: {
    marginHorizontal: -25,
  },
  titleCard: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontWeight: "700",
    fontFamily: "NotoSans-Bold",
  },
  cardHeight: {
    alignSelf: "center",
    marginTop: "8%",
    fontSize: 12,
  },
  loadingUsage: {
    display: "flex",
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    height: 200,
  },
  wrapDirection: {
    flexDirection: "row",
    flexWrap: "wrap",
    alignContent: "space-between",
  },
  rightContent: {
    width: "35%",
    flexDirection: "row",
    flexWrap: "wrap",
    alignContent: "space-between",
  },
  weekStyle: {
    width: 9,
    borderRadius: 25,
    height: 9,
    marginRight: "5%",
    marginTop: 5,
  },
  primaryColorStyle: {
    backgroundColor: GlobalStyles.colors.ePrimary.base,
  },
  secondaryColorStyle: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
  },
  fontText: {
    fontSize: 9,
    fontFamily: "NotoSans-Regular",
  },
  widthStyle80: {
    width: "80%",
  },
  lineStyle: {
    borderWidth: 0.5,
    borderColor: GlobalStyles.colors.eLight.selected,
    marginTop: 10,
    //marginBottom: 6,
    marginHorizontal: -15,
    paddingHorizontal: -20,
    width: "109%",
  },
});

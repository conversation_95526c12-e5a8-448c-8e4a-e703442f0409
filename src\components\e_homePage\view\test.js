import * as React from "react";
import { View, StyleSheet, Text, TouchableHighlight } from "react-native";
// import Constants from 'expo-constants';
import { Card } from "react-native-paper";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  btnNormal: {
    borderColor: "blue",
    borderWidth: 1,
    borderRadius: 10,
    height: 30,
    width: 100,
  },
  btnPress: {
    borderColor: "blue",
    borderWidth: 1,
    height: 30,
    width: 100,
  },
  btn: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    display: "flex",
  },
});
export default function Test() {
  const [isPress, setIsPress] = React.useState(false);

  const touchProps = {
    activeOpacity: 1,
    underlayColor: "blue",
    style: isPress ? styles.btnPress : styles.btnNormal,
    onHideUnderlay: () => setIsPress(false),
    onShowUnderlay: () => setIsPress(true),
    onPress: () => console.log("hello"),
  };

  return (
    <View style={styles.container}>
      <TouchableHighlight {...touchProps}>
        <Text style={styles.btn}>Click here</Text>
      </TouchableHighlight>
    </View>
  );
}

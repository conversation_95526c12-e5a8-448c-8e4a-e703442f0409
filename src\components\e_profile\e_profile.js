import React, { useEffect, useRef } from "react";
import { useState } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  Pressable,
  KeyboardAvoidingView,
} from "react-native";
import ProfileDetails from "./view/_profile-details";
import ChangePassword from "./view/_change-password";
import ChangePasswordModal from "./view/_change-password-modal";
import ProfileModal from "./view/_profile-modal";
import { GlobalStyles } from "../app/global-styles";
import { Card } from "react-native-paper";
import ConfirmProfileModal from "./view/_confirm_profile-modal";

export const profileContext = React.createContext();

export default function Profile({ navigation }) {
  const [totalValue, setTotalValue] = useState(0);
  const [currency, setCurrency] = useState(0);
  const [showChangepasswordPopup, setShowCPasswordPopup] = useState(false);
  const [closePopup, setClosePopup] = useState(true);
  const [showProfilePopup, setProfilePopup] = useState(false);
  const [showProfileConfirmPopup, setProfileConfirmPopup] = useState(false);
  const profileRef = useRef();
  const [mobileNumber, setMobileNumber] = useState("");

  React.useEffect(() => {
    // Logic to close the mobile number input box
    const unsubscribe = navigation.addListener("focus", () => {
      profileRef.current.closeTextInput();
      profileRef.current.setMobilePhoneNumber();
    });
    // Return the function to unsubscribe from the event so it gets removed on unmount
    return unsubscribe;
  }, [navigation]);

  return (
    <>
      <profileContext.Provider
        value={{
          setShowCPasswordPopup,
          showChangepasswordPopup,
          showProfilePopup,
          setProfilePopup,
          mobileNumber,
          setMobileNumber,
          showProfileConfirmPopup,
          setProfileConfirmPopup,
        }}>
        <ScrollView style={styles.pressable}>
          <View style={styles.spaceAroundCard}>
            <ProfileDetails
              totalValue={totalValue}
              setTotalValue={setTotalValue}
              currency={currency}
              setCurrency={setCurrency}
              ref={profileRef}
            />
          </View>
          {/* <KeyboardAvoidingView behavior="padding"> */}
            <View style={styles.spaceAroundCardChangePWD}>
              <ChangePassword />
            </View>
            {/* <View style={{ height: 40 }} /> */}
          {/* </KeyboardAvoidingView> */}
        </ScrollView>
        {showChangepasswordPopup ? (
          <View>
            <Pressable
              style={styles.accountDrawerOpacity}
              onPress={() => setShowCPasswordPopup(false)}></Pressable>
            <Card style={styles.changePWDStyle}>
              <ChangePasswordModal
                showChangepasswordPopup={showChangepasswordPopup}
                hideModel={closePopup}
              />
            </Card>
          </View>
        ) : null}

        {showProfileConfirmPopup ? (
          <View>
            <Pressable
              style={styles.accountDrawerOpacity}
              onPress={() => setShowCPasswordPopup(false)}></Pressable>
            <Card style={styles.changePWDStyle}>
              <ConfirmProfileModal
                showProfileConfirmPopup={showProfileConfirmPopup}
                hideModel={closePopup}
              />
            </Card>
          </View>
        ) : null}

        {showProfilePopup ? (
          <View>
            <Pressable
              style={styles.accountDrawerOpacity}
              onPress={() => setProfilePopup(false)}></Pressable>
            <Card style={styles.accountStyle}>
              <ProfileModal
                showProfilePopup={showProfilePopup}
                hideModel={closePopup}
              />
            </Card>
          </View>
        ) : null}
      </profileContext.Provider>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
    alignItems: "center",
    justifyContent: "center",
  },
  pressable: {
    flex: 1,
    marginTop: 20,
  },
  spaceAroundCard: {
    marginBottom: 10,
    width: "100%",
  },
  spaceAroundCardChangePWD: {
    width: "100%",
    marginTop: 20,
  },
  spaceAroundCardSubmit: {
    width: "100%",
  },
  accountDrawerOpacity: {
    height: "100%",
    width: "100%",
    backgroundColor: "black",
    opacity: 0.5,
  },
  accountStyle: {
    position: "absolute",
    height: 450,
    width: "100%",
    bottom: 0,
    borderTopEndRadius: 25,
    borderTopLeftRadius: 25,
    backgroundColor: GlobalStyles.colors.eFaint.base,
  },
  changePWDStyle: {
    position: "absolute",
    height: 380,
    width: "100%",
    bottom: 0,
    borderTopEndRadius: 25,
    borderTopLeftRadius: 25,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    paddingTop: 20,
  },
});

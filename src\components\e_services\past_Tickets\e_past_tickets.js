import { View, StyleSheet, Pressable } from "react-native";
import { GlobalStyles } from "../../app/global-styles";
import { Text, IconButton } from "react-native-paper";
import * as React from "react";
import PastTicketDrawer from "./view/_past_ticket_drawer";
import { useContext } from "react";
import { ticketContext } from "../e_services";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import Icon from "../../icon";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { config } from "../../../environment";
import { stackContext } from "../../app/get_stack";

export default function PastTickets({ open }) {
  const { workModelType } = React.useContext(stackContext);
  const { setOpen } = useContext(ticketContext);
  let openDrawer,
    setOpenMenu,
    menuFlag,
    setmenuFlag,
    submitLoader,
    setSubmitLoader;

  if (workModelType === "WA") {
    ({
      openDrawer,
      setOpenMenu,
      menuFlag,
      setmenuFlag,
      submitLoader,
      setSubmitLoader,
    } = useContext(drawerContext));
  } else {
    ({
      openDrawer,
      setOpenMenu,
      menuFlag,
      setmenuFlag,
      submitLoader,
      setSubmitLoader,
    } = useContext(drawerContextWO));
  }

  const drawerOpen = () => {
    setOpenMenu(true);
    setmenuFlag(false);
  };
  return (
    <ticketContext.Provider value={{ setOpen }}>
      {open ? (
        <View style={styles.drawerMenu}>
          <View style={styles.drawerMenuContent}>
            <PastTicketDrawer />
          </View>
          <Pressable style={styles.openDrawerIcon} onPress={drawerOpen}>
            <View style={styles.iconContainer}>
              <Icon
                name="Hamburger-icon"
                color={GlobalStyles.colors.eWhite.base}
                style={{
                  transform: [{ rotateZ: "270deg" }],
                }}
                size={20}
                disabled={submitLoader}
                onPress={drawerOpen}
              />
            </View>
          </Pressable>
        </View>
      ) : (
        <View
          style={[
            styles.container,
            {
              transform: [{ rotateZ: "270deg" }],
            },
          ]}>
          <View style={styles.textContainer}>
            {/* <Text style={styles.textStyle}>View Past Tickets</Text> */}
          </View>
          <View style={styles.iconContainer}>
            <Icon
              name="Hamburger-icon"
              color={GlobalStyles.colors.eWhite.base}
              style={styles.menuIcon}
              size={10}
              disabled={submitLoader}
              onPress={drawerOpen}
            />
          </View>
        </View>
      )}
    </ticketContext.Provider>
  );
}

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    left: "-20.2%",
    top: "60%",
  },
  textContainer: {
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    borderBottomLeftRadius: 10,
    borderBottomRightRadius: 10,
    width: 180,
    height: 2,
    paddingHorizontal: 10,
    paddingTop: 5,
  },
  textStyle: {
    textAlign: "center",
    color: GlobalStyles.colors.eWhite.base,
    marginTop: -2,
  },
  menuIcon: {
    marginLeft: 13,
    marginTop: 2,
  },
  menuIconOpen: {
    height: 30,
    width: 10,
  },
  iconContainer: {
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    borderTopWidth: 0,
    height: 17,
    width: 38,
    alignSelf: "center",
  },
  drawerMenu: {
    position: "absolute",
    height: "100%",
    width: "100%",
    zIndex: 100,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    flexDirection: "row",
  },
  openDrawerIcon: {
    alignSelf: "center",
  },
  drawerMenuContent: {
    height: "100%",
    width: "90%",
    // zIndex: 100
  },
});

import axios from "axios";
import { config } from "../../../environment";

export const CIService = {
  getAllCIList,
  updateCIList,
  createCI,
};

async function getAllCIList() {
  try {
    let gettURL = config.urls.GET_CONSUMER_LIST;
    console.log(gettURL);
    const response = await axios.get(
      gettURL,
      // Add headers if needed
      // {
      //   headers: {
      //     'Authorization': 'Bearer YOUR_ACCESS_TOKEN',
      //     'Content-Type': 'application/json',
      //   },
      // }
    );
    console.log("Response Data:.....", response.data);
    return response.data;
  } catch (error) {
    // Handle any errors
    console.log("An error occurred....:", error);
    throw error;
  }
}

async function updateCIList(CIdata) {
  const consumerNumber = CIdata.ConsumerNumber;
  const WorkorderId = CIdata.WorkorderId;
  let tempCIData = CIdata;
  console.log("CIdataCIdata", CIdata);
  let key = "ConsumerIndexingHistoryId";
  let key2 = "WorkorderId";
  let updateURL = config.urls.UPDATE_CONSUMER_LIST;
  console.log(updateURL);
  delete tempCIData[key];
  delete tempCIData[key2];
  console.log("making API call...update....:", updateURL, tempCIData, {
    params: {
      consumerNumber: parseInt(consumerNumber),
    },
  });
  try {
    const response = await axios.put(
      updateURL,
      tempCIData,
      {
        params: {
          consumerNumber: parseInt(consumerNumber),
          WorkorderId: WorkorderId,
        },
      },
      // Add your request data here if needed
    );
    console.log(response);
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function createCI(CIdata) {
  // return false;
  let createURL = config.urls.UPDATE_CONSUMER_LIST;
  console.log(CIdata);
  console.log(createURL);
  try {
    const response = await axios.post(createURL, CIdata);
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

export const ACITIVTY_STATUS = {
  DONE: "done",
  TODO: "todo",
};

export const CONSUMER_INDEXING_DROPDOWN_VALUES = {
  ORGANIZATION: [
    {
      label: "Select Organaization",
      value: "",
      id: 1,
    },
    {
      label: "APDCL 1-A , Guahati",
      value: "APDCL 1-A , Guahati",
      id: 2,
    },
    {
      label: "APDCL 2-B , Guahati",
      value: "APDCL 2-B , Guahati",
      id: 3,
    },
    {
      label: "APDCL 3-C , Guahati",
      value: "APDCL 3-C , Guahati",
      id: 4,
    },
    {
      label: "APDCL 4-D , Guahati",
      value: "APDCL 4-D , Guahati",
      id: 5,
    },
    {
      label: "APDCL 5-E , Dibrugarh",
      value: "APDCL 5-E , Dibrugarh",
      id: 6,
    },
    {
      label: "APDCL 6-F , <PERSON><PERSON><PERSON>arh",
      value: "APDCL 6-F , <PERSON><PERSON><PERSON><PERSON><PERSON>",
      id: 7,
    },
    {
      label: "APDCL 7-G , <PERSON><PERSON><PERSON><PERSON><PERSON>",
      value: "APDCL 7-G , Dibrugarh",
      id: 8,
    },
    {
      label: "APDCL 8-H , Dibrugarh",
      value: "APDCL 8-H , Dibrugarh",
      id: 9,
    },
  ],
  DT: [
    {
      label: "Select Item",
      value: "",
    },
    {
      label: "TA0045",
      value: "TA0045",
    },
    {
      label: "TA8765",
      value: "TA8765",
    },
    {
      label: "TA0870",
      value: "TA0870",
    },
    {
      label: "TA0988",
      value: "TA0988",
    },
  ],
  METER_MAKE: [
    {
      label: "Select Item",
      value: "",
    },
    {
      label: "Landis+Gyr E470",
      value: "Landis+Gyr E470",
    },
    {
      label: "Liberty 116",
      value: "Liberty 116",
    },
    {
      label: "Liberty 370",
      value: "Liberty 370",
    },
  ],
  EXISTING_METER_TYPE: [
    {
      label: "Select Type",
      value: "",
    },
    {
      label: "Single Phase",
      value: "Single Phase",
    },
    {
      label: "Three Phase",
      value: "Three Phase",
    },
  ],
  EXISTING_METER_SEAL_STATUS: [
    {
      label: "Select Status",
      value: "",
    },
    {
      label: "Available",
      value: "Available",
    },
    {
      label: "Not Available",
      value: "Not Available",
    },
  ],
  EXISTING_METER_BOX_STATUS: [
    {
      label: "Select Status",
      value: "",
    },
    // {
    //   label: "Available",
    //   value: "Available",
    // },
    // {
    //   label: "Not Available",
    //   value: "Not Available",
    // },
    {
      label: "Good",
      value: "Good",
    },
    {
      label: "Broken",
      value: "Broken",
    },
    {
      label: "Intact",
      value: "Intact",
    },
  ],
  METER_LOCATION: [
    {
      label: "Select Location",
      value: "",
    },
    {
      label: "Inside",
      value: "Inside",
    },
    {
      label: "Outside",
      value: "Outside",
    },
  ],
  WORK_ORDER_TYPES: [
    {
      label: "Select Item",
      value: "",
    },
    {
      label: "Consumer Indexing",
      value: "Consumer Indexing",
    },
    {
      label: "Meter Installation",
      value: "Meter Installation",
    },
  ],
  POLE_TYPE: [
    {
      label: "Select Item",
      value: "",
    },
    {
      label: "HT",
      value: "HT",
    },
    {
      label: "LT",
      value: "LT",
    },
  ],
  JOINTS_AVAILABLE_IN_CABLE: [
    {
      label: "Select Item",
      value: "",
    },
    {
      label: "Yes",
      value: "Yes",
    },
    {
      label: "No",
      value: "No",
    },
  ],
  CONSUMER_STATUS: [
    {
      label: "Select Status",
      value: "",
    },
    {
      label: "Active",
      value: "Active",
    },
    {
      label: "Stopped",
      value: "Stopped",
    },
  ],
};

export const DATE_FORMATS = {
  DATETIME: "DD-MM-YYYY HH:MM:SS A",
};

export const CHECKLIST = {
  SMART_METER_INSTALLATION: [
    {
      id: 1,
      checklistId: "ID10",
      done: false,
      title:
        "Verify power spply to the meter is switched off, Disconnected Existing Meter",
    },
    {
      id: 2,
      checklistId: "ID20",
      done: false,
      title: "Existing Meter Number",
      controlType: "text",
      controlFieldName: "existingMeterNo",
      existingMeterNo: "",
      submitType: "Button",
    },
    {
      id: 3,
      checklistId: "ID30",
      done: false,
      title: "New Meter Number",
      controlType: "text",
      controlFieldName: "newMeterNo",
      newMeterNo: "",
      submitType: "Button",
    },
    {
      id: 4,
      checklistId: "ID30",
      done: false,
      title: "Configure Communication, Test Functionality",
    },
    { id: 5, checklistId: "ID40", done: false, title: "Seal and Secure" },
    { id: 6, checklistId: "ID50", done: false, title: "Document Installation" },
    {
      id: 7,
      checklistId: "ID60",
      done: false,
      title: "Work Order Completion Date",
      controlType: "text",
      controlFieldName: "taskCompletionDateTime",
      taskCompletionDateTime: "",
      submitType: "Button",
    },
  ],
  CONSUMER_INDEXING: [
    {
      id: 1,
      checklistId: "ID10",
      done: false,
      title: "Verify power spply to the meter is working fine ",
    },
    {
      id: 2,
      checklistId: "ID20",
      done: false,
      title: "Existing Meter Number",
      controlType: "text",
      controlFieldName: "existingMeterNo",
      existingMeterNo: "",
      submitType: "Button",
    },
    {
      id: 3,
      checklistId: "ID30",
      done: false,
      title: "Consumer Indexing Data ",
      controlType: "",
      controlFieldName: "captureConsumerIndexing",
      submitType: "Button",
    },
    {
      id: 4,
      checklistId: "ID30",
      done: false,
      title: "Document Installation",
    },
    { id: 5, checklistId: "ID40", done: false, title: "Seal and Secure" },
    { id: 6, checklistId: "ID50", done: false, title: "Document Installation" },
    {
      id: 7,
      checklistId: "ID60",
      done: false,
      title: "Work Order Completion Date",
      controlType: "text",
      controlFieldName: "taskCompletionDateTime",
      taskCompletionDateTime: "",
      submitType: "Button",
    },
  ],
};

export const SIDEBAR_MENU_OPTIONS = [
  {
    id: 1,
    name: "Consumer_Indexing",
    icon: "cog",
  },
  {
    id: 2,
    name: "Work_Activities",
    icon: "folder",
  },

  {
    id: 3,
    name: "Log out",
    icon: "power-off",
  },
];
export const ROUTES = {
  WORK_ACTIVITIES: "Work_Activities",
  OPERATION_DETAILS: "Operation_Details",
  CONSUMER_INDEXING: "Consumer_Indexing",
  WORK_ORDER: "Work_Order",
  WORK_ORDER_LIST: "Work_Order_List",
  ASSETS: "Assets",
};

export const SNACKBAR_TIMEOUT_DURATION = 4000;

export const MESSAGES = {
  SUCCESSFUL_CONSUMER_INDEXING:
    "Consumer Indexing information has been successfully saved!",
};

export const URLS = {
  WORK_ORDER_API_URL:
    "https://impresa-fieldwork-demo.abjayon.com/dummyData/manageWorkOrders.json",
};

export const WORK_ACTIVITY_STATUS = {
  CANCELLED: "Cancelled",
  CLOSED: "Closed",
  OVERDUE: "Overdue",
  OPEN: "Open",
  INPROGRESS: "In Progress",
};

import Axios from "axios";
import moment from "moment";
import { useEffect, useState } from "react";
import { View, ActivityIndicator, Text } from "react-native";
import { Card, TextInput } from "react-native-paper";
import { ScrollView, StyleSheet } from "react-native";
import { useSelector } from "react-redux";
import { config } from "../../../../environment";
import { GlobalStyles } from "../../../app/global-styles";

export default function AccountBalance() {
  const [balance, setBalance] = useState(0);
  const [minBalance, setMinBalance] = useState(0);
  const currencySymbol = useSelector(
    store => store?.meterDetails?.meterDetails?.curSymbol,
  );
  let [prepaidEngineUser, setPrepaidEngineUser] = useState(false);
  let prepaidEngineFlag = useSelector(
    store => store?.meterDetails?.meterDetails,
  );
  let customerClass = useSelector(
    state => state?.meterDetails?.meterDetails?.customerClassDesc,
  );
  // const minimumBalanceThreshold = useSelector(
  //   (state) =>
  //     state?.meterDetails?.meterDetails?.getSaInfo?.prepaidSaDetail
  //       ?.minBalanceThld
  // );
  const minimumBalance = useSelector(
    state =>
      state?.parameter?.parameter?.ParameterLookup?.PREPAID_MINIMUM_BALANCE,
  );

  useEffect(() => {
    const body = {
      indexName: "tenant-threshold-configuration",
      isSortRequired: false,
      query: {
        match: {
          customerClass: customerClass,
        },
      },
    };
    Axios.post(config.urls.PREPAID_ELASTC_SEARCH_DATA, body)
      .then(res => {
        const resArray = res.data.data;
        let thresoldDataByDate = resArray.filter(s =>
          dateBetween(s._source.startDate, s._source.endDate),
        );
        setBalance(
          thresoldDataByDate.length == 1
            ? thresoldDataByDate[0]._source.thresholdValue
            : "0",
        );
      })
      .catch(err => {
        console.log(err);
      });
  }, []);

  function dateBetween(startDate, endDate) {
    if (endDate) {
      let from = moment(startDate).unix();
      let to = moment(endDate).unix();
      let check = moment().unix();
      let ans = check >= from && check <= to;
      return ans;
    } else {
      //endDate is optional
      let from = moment(startDate).unix();
      let check = moment().unix();
      let ans = check >= from;
      return ans;
    }
  }

  useEffect(() => {
    if (minimumBalance) {
      setMinBalance(minimumBalance);
    }
  }, [minimumBalance]);

  return (
    <Card style={styles.cardStyle}>
      <ScrollView style={styles.scrollStyle}>
        <Text style={styles.blueTextTitle}>Account Balance</Text>
        <>
          <View style={styles.content}>
            <View style={{ width: "60%" }}>
              <Text style={styles.blueCheckBoxLabel}>Balance Threshold</Text>
              <Text style={styles.valueText}>
                Minimum balance below which you will get an alert to top-up.
              </Text>
            </View>
            <View style={{ width: "30%" }}>
              <Text style={styles.balanceStyle}>
                {currencySymbol + " " + balance}
              </Text>
            </View>
          </View>
          <View style={styles.content}>
            <View style={{ width: "60%" }}>
              <Text style={styles.blueCheckBoxLabel}>
                Minimum Balance to Maintain
              </Text>
              <Text style={styles.valueText}>
                Minimum balance you must always maintain as per terms.
              </Text>
            </View>
            <View style={{ width: "30%" }}>
              <Text style={styles.balanceStyle}>
                {currencySymbol + " " + minBalance}
              </Text>
            </View>
          </View>
        </>
      </ScrollView>
    </Card>
  );
}

const styles = StyleSheet.create({
  valueText: {
    fontSize: 12,
    fontFamily: "NotoSans-Regular",
    color: GlobalStyles.colors.eRich.hover,
  },
  cardStyle: {
    marginHorizontal: "3%",
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    paddingVertical: 15,
  },
  scrollStyle: {
    paddingHorizontal: 20,
    paddingVertical: 5,
    paddingBottom: 10,
    overflow: "scroll",
    zIndex: 100,
  },
  blueTextTitle: {
    color: GlobalStyles.colors.ePrimary.base,
    fontFamily: "NotoSans-SemiBold",
    fontSize: 14,
  },
  blueCheckBoxLabel: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontFamily: "NotoSans-regular",
  },
  content: {
    flexDirection: "row",
    alignItems: "flex-start",
    paddingTop: 15,
    justifyContent: "space-between",
  },
  balanceStyle: {
    fontSize: 14,
    fontFamily: "NotoSans-Regular",
  },
});

import React, { useEffect, useRef, useState, useContext } from "react";
import {
  StyleSheet,
  View,
  SafeAreaView,
  Dimensions,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { GlobalStyles } from "../../app/global-styles";
import {
  Checkbox,
  Text,
  Portal,
  Dialog,
  Snackbar,
  TextInput,
  TouchableRipple,
  Card,
} from "react-native-paper";
import { useDispatch, useSelector } from "react-redux";
import FontAwesome5Icon from "react-native-vector-icons/FontAwesome";
import moment from "moment";
import _ from "lodash";
import { ScrollView } from "react-native-gesture-handler";
import {
  setActivities,
  setCurrentActivity,
} from "../../../redux/slices/activitySlices";
import OTPTextInput from "react-native-otp-textinput";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  DATE_FORMATS,
  ROUTES,
  WORK_ACTIVITY_STATUS,
} from "../../common/constants";
import DatePicker from "react-native-date-picker";
import { useIsFocused } from "@react-navigation/native";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import Icon from "../../icon";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import CalendarPicker from "../../common/calendar";
import Button from "../../common/_button";
import ConfirmationModal from "./otp-confirm";
import { workOrderContext } from "../e_workactivities";
//import { WorkOrderServiceWO } from "../model/work-order-service";
import Input from "../../common/_input";
import { introspectionFromSchema } from "graphql";
import MaterialTableGrid from "./material-table-grid";
import LabourTableGrid from "./labour-grid";
import EquipmentTableGrid from "./equipment-table-grid";
import DirectChargesGrid from "./direct-charges-grid";
import ActivityGrid from "./activity-grid";
// import CameraComponent from "../../common/camera";
import { useTranslation } from 'react-i18next';
import { WorkOrderServiceWO } from "../model/work-order-service_wo";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";

export default function WorkOrderListWO() {
  const {
    setAllActivities,
    setSingleWorkOrder,
    workOrder,
    confirmationModal,
    setConfirmationModal,
    setOTPModal,
    OTPModal,
    WOList,
    setWOList,
    singleWO,
    setSingleWO,
    customerNumber,
    OTPVerification,
    updateWAObj,
    setUpdateWAObj,
    setconfirmNote,
    setConfirmModalType,
    setOTPError,
    setCustomerNumber,
    singleWODetails,
    OTPConfirmationWO,
    setOTPConfirmationWO,
    tempWorkOrder,
    newWorkOrderExists,
    setNewWorkOrderExists,
  } = useContext(drawerContextWO);
  const selectedActivity = useSelector(
    state => state.activity.selectedActivity,
  );

  const focused = useIsFocused();

  const isCurrentChecklistCompleted = useSelector(
    state => state.activity.isCurrentChecklistCompleted,
  );
  const pathName = useSelector(state => state?.servicePath?.servicePath);

  const handleBack = () => {
    //navigation.goBack();
    if (newWorkOrderExists) {
      setNewWorkOrderExists(!newWorkOrderExists);
    }
    setWOList(true);
    setSingleWO(false);
    setConfirmationModal(false);
    setOTPModal(false);
  };

  const today = new Date();
  const OffAvailableDates = today.toISOString().split("T")[0];
  const [actualStartDate, setActualStartDate] = useState();
  const [actualEndDate, setActualEndDate] = useState();
  const [changeindex, setIndex] = useState();
  const [selectedDate, setSelectedDate] = useState(null);
  const [showCalendar, setShowCalendar] = useState(false);
  const [editable, setEditable] = useState(true);
  const [errorOnoff, setErrorOnOff] = useState(false);
  const [errorOn, setErrorOn] = useState(false);
  const [availableOndates, setAvailableOndates] = useState(null);
  const [calOrText, setCalorText] = useState(true);
  const [textDateOff, setTextDateOff] = useState(null);
  const [disableCancle, setDisableCancle] = useState(false);
  const [disableSubmit, setDisableSubmit] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [isActivityLoading, setActivityLoading] = useState(false);
  const [wAList, setAllWAList] = useState([]);
  const [wAListCopy, setAllWAListCopy] = useState([]);
  const [dupWAListCopy, setDupAllWAListCopy] = useState([]);
  const [activitiesList, setActivitiesList] = useState(false);
  const [expandedSections, setExpandedSections] = useState([]);
  const [info, setInfo] = useState([]);
  const [infoAsset, setInfoAsset] = useState([]);
  const [spID, setSpID] = useState("");
  const [addressLoad, setAddressLoad] = useState(false);
  const [addressLoadAsset, setAddressLoadAsset] = useState(false);
  const [WADetails, setWADetails] = useState([]);
  const [plannedDuration, setPlannedDuration] = useState();
  const [actualDuration, setActualDuration] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
  });
  const [comments, setComment] = useState();
  const [, forceUpdate] = React.useState();
  const [updatedWAListCopy, setupdatedWAListCopy] = useState([]);
  const [customerNumberError, setCustomerNumberError] = useState(false);
  const [customerNumberText, setCustomerNumberText] = useState("");
  const [consumnerData, setConsumnerData] = useState("");
  const [consumnerDataLoad, setConsumnerDataLoad] = useState(false);
  const { t } = useTranslation();

  const handleToggle = (ind, item) => {
    const newExpandedSections = [...expandedSections];
    const isAlreadyExpanded = newExpandedSections.includes(ind);
    setIndex(ind);
    if (!isAlreadyExpanded) {
      newExpandedSections.forEach(expandedIndex => {
        if (expandedIndex !== ind) {
          newExpandedSections.splice(
            newExpandedSections.indexOf(expandedIndex),
            1,
          );
        }
      });
    } else {
      newExpandedSections.splice(newExpandedSections.indexOf(ind), 1);
    }
    setComment(item.Comments);

    if (singleWODetails.WorkOrderType === "Consumer Survey") {
      getAddress(singleWODetails.MeterNumber);
      getConsumerDetails(singleWODetails.ConsumerIndexingId);
    } else if (singleWODetails.WorkOrderType === "Meter Inspection") {
      getMeterInspectionAddress(singleWODetails.AssetId);
      getConsumerDetails(singleWODetails.ConsumerIndexingId);
    } else if (singleWODetails.WorkOrderType === "Meter Replacement") {
      getAssetAddress(singleWODetails.AssetId);
      getConsumerDetails(singleWODetails.ConsumerIndexingId);
    } else if (singleWODetails.WorkOrderType === "Meter Installation") {
      //getAssetInstallationAddress(singleWODetails.AssetId);
      getAssetAddress(singleWODetails.AssetId);
      getConsumerDetails(singleWODetails.ConsumerIndexingId);
    } else {
      //getAssetAddress(singleWODetails.AssetId);
    }
    console.log("singleWODetailssingleWODetails", singleWODetails);

    if (!isAlreadyExpanded) {
      newExpandedSections.push(ind);
    }
    setExpandedSections(newExpandedSections, item);

    // const formattedDate = moment(
    //   wAListCopy?.workActivities[ind]?.ActualStartDate,
    // ).format("DD-MM-YYYY");

    if (
      wAListCopy?.workActivities[ind]?.ActualStartDate !== null ||
      wAListCopy?.workActivities[ind]?.ActualEndDate !== null
    ) {
      setActualStartDate(
        moment(wAListCopy?.workActivities[ind]?.ActualStartDate).format(
          "DD-MM-YYYY h:mm A",
        ),
      );
      setActualEndDate(
        moment(wAListCopy?.workActivities[ind]?.ActualEndDate).format(
          "DD-MM-YYYY h:mm A",
        ),
      );
      forceUpdate({});
    } else {
      const currentStartDateTime = moment().format("DD-MM-YYYY h:mm A");
      //const currentEndDateTime = moment().format("DD-MM-YYYY h:mm A");
      console.log(
        "........... at starting",
        currentStartDateTime,
        wAListCopy?.workActivities[ind],
      );
      setActualStartDate(currentStartDateTime);
      // setActualEndDate(
      //   moment(wAListCopy?.workActivities[ind]?.ActualEndDate).format(
      //     "DD-MM-YYYY h:mm A",
      //   ),
      // );
      forceUpdate({});
    }
  };
  const getAssetInstallationAddress = async assetId => {
    try {
      setLoading(true);

      const workorderid = wAListCopy.WorkOrderId;
      const res = await WorkOrderServiceWO.getMeterInsAddress(assetId);

      setCustomerNumber(res?.Mobile);
      const resInfo = JSON.stringify(res);
      setInfoAsset(JSON.parse(resInfo));
      setAddressLoadAsset(true);

      setSpID(res?.spId);
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
      setLoading(false);
    }
  };
  const getMeterInspectionAddress = async assetID => {
    try {
      setLoading(true);

      const workorderid = wAListCopy.WorkOrderId;
      const res = await WorkOrderServiceWO.getMeterInspectionAddress(assetID);

      setCustomerNumber(res?.Mobile);
      const resInfo = JSON.stringify(res);
      setInfoAsset(JSON.parse(resInfo));
      setAddressLoadAsset(true);
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
      setLoading(false);
    }
  };
  const getConsumerDetails = async ConsumerIndexingId => {
    try {
      setLoading(true);
      console.log("ConsumerIndexingId.....", ConsumerIndexingId);
      const res = await WorkOrderServiceWO.getConsumerDetails(ConsumerIndexingId);
      console.log("........", res);
      setConsumnerData(res);
      setConsumnerDataLoad(true);
      // setCustomerNumber(res?.Mobile);
      // const resInfo = JSON.stringify(res);
      // setInfoAsset(JSON.parse(resInfo));
      // setAddressLoadAsset(true);
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
      setLoading(false);
    }
  };
  const getAssetAddress = async assetID => {
    try {
      setLoading(true);

      const workorderid = wAListCopy.WorkOrderId;
      const res = await WorkOrderServiceWO.getAssetAddress(assetID, workorderid);

      setCustomerNumber(res?.Mobile);
      const resInfo = JSON.stringify(res);
      setInfoAsset(JSON.parse(resInfo));
      setAddressLoadAsset(true);
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
      setLoading(false);
    }
  };
  const getAddress = async meterNo => {
    try {
      setLoading(true);

      const workorderid = wAListCopy.WorkOrderId;
      const res = await WorkOrderServiceWO.getAddress(meterNo, workorderid);

      setCustomerNumber(res?.Mobile);
      console.log("res", res);
      const resInfo = JSON.stringify(res);
      setInfo(JSON.parse(resInfo));
      setAddressLoad(true);
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
      setLoading(false);
    }
  };

  const cancelClick = () => {};
  const markCheckListItemDone = (index, checklistItem, isChecked) => {
    const updatedWADetails = { ...wAListCopy };
    const updatedChecklist = updatedWADetails.workActivities[
      index
    ].WorkOrderActivityChecklist.map(obj =>
      obj.activityChecklistId === checklistItem.activityChecklistId
        ? { ...obj, answer: isChecked ? "yes" : "no" }
        : obj,
    );
    updatedWADetails.workActivities[index].WorkOrderActivityChecklist =
      updatedChecklist;

    setAllWAListCopy(updatedWADetails);
  };
  useEffect(() => {
    fetchWorkActivityList();
  }, []);

  useEffect(() => {
    let req = { ...wAListCopy };
    setDupAllWAListCopy(prev => req);
  }, [wAListCopy]);

  const submitClick = () => {
    requestOTP();
  };
  const submitActivityClick = (workActivity, ind) => {
    console.log(
      "workActivity.WorkOrderActivityChecklist",
      workActivity.WorkOrderActivityChecklist,
    );
    const allYesAnswers = workActivity.WorkOrderActivityChecklist.every(
      checklist =>
        checklist.answer !== "" ||
        checklist.answer !== undefined ||
        checklist.answer !== "no" ||
        checklist.answer !== null,
    );
    const workStatus = allYesAnswers ? "CO" : "I";

    const newArray = {
      WorkOrderId: workActivity.WorkOrderId,
      WorkOrderType: wAListCopy.WorkOrderType,
      workActivities: [
        {
          WorkActivityId: workActivity.WorkActivityId,
          WorkStatus: workStatus,
          Comments: workActivity.Comments,
          ActualStartDate: moment(
            workActivity.ActualStartDate,
            "DD-MM-YYYY HH:mm A",
          ).format("YYYY-MM-DD HH:mm:ss"),
          ActualEndDate: moment(
            workActivity.ActualEndDate,
            "DD-MM-YYYY HH:mm A",
          ).format("YYYY-MM-DD HH:mm:ss"),
          WorkOrderId: workActivity.WorkOrderId,
          WorkOrderActivityChecklist:
            workActivity.WorkOrderActivityChecklist.map(checklist => ({
              activityChecklistId: checklist.activityChecklistId,
              answer:
                checklist.dataType === "number"
                  ? parseInt(checklist.answer)
                  : checklist.dataType === "string" ||
                    checklist.dataType === "boolean"
                  ? checklist.answer !== null
                    ? checklist.answer
                    : "no"
                  : checklist.dataType === "asset_url"
                  ? "/C:/Users/<USER>/Desktop/Apples.png"
                  : info[checklist.label],
              WorkActivityId: workActivity.WorkActivityId,
              WorkOrderId: workActivity.WorkOrderId,
              label: checklist.label,
              checklistType: checklist.checklistType,
              isCustom: checklist.isCustom,
            })),
        },
      ],
    };

    console.log(JSON.stringify(newArray, null, 2));

    //return false;
    if (newArray) {
      workActitityAPIcall(newArray);
      //return false;
      //console.log("OTPVerification...", OTPVerification);
      // setUpdateWAObj(newArray);
      // requestOTP();
      // if (OTPVerification) {
      //   //console.log("newArray..otp...", newArray);
      // }
    }
  };
  const workActitityAPIcall = async newArray => {
    try {
      setActivityLoading(true);
      const res = await WorkOrderServiceWO.workActitityAPIcall(newArray);

      if (res.status === 201) {
        setconfirmNote(t('WORK_ACTIVITY_UPDATE_MSG'));
        setConfirmModalType("workOrder");
        setConfirmationModal(true);
        setOTPModal(false);
      }
      if (res.status === 400) {
        console.log(res.message);
        setOTPError(res.message);
      }
    } catch (err) {
      console.log(err, "Error in fetching in request otp");
    } finally {
      setActivityLoading(false);
    }
  };

  const requestOTP = async () => {
    try {
      setLoading(true);
      //customerNumber = 0;

      // if (customerNumber !== undefined) {
      const res = await WorkOrderServiceWO.requestOTP(customerNumber);

      if (res.isOk) {
        setconfirmNote(t('WORK_ORDER_UPDATE_MSG'));
        setConfirmModalType("workOrder");
        setConfirmationModal(false);
        setOTPModal(true);
        let newArray = {
          WorkOrderId: singleWODetails?.WorkOrderId,
          updatedBy: 2,
          Comments: "",
        };
        setUpdateWAObj(newArray);
      }
      if (res.status === 400) {
        console.log(res.message);
        setOTPError(res.message);
      }
      // } else {
      //   setCustomerNumberError(true);
      //   setCustomerNumberText("Customer Number Required");
      // }
    } catch (err) {
      console.log(err, "Error in fetching in request otp");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (changeindex != null) {
      if (actualStartDate || actualEndDate) {
        if (wAListCopy) {
          if (wAListCopy?.workActivities[changeindex]) {
            let updatedWAListCopy = { ...wAListCopy };
            updatedWAListCopy.workActivities = [
              {
                ...wAListCopy.workActivities[changeindex],
                ActualStartDate: moment(
                  actualStartDate,
                  "DD-MM-YYYY HH:mm A",
                ).format("YYYY-MM-DD HH:mm:ss"),
                ActualEndDate: moment(
                  actualEndDate,
                  "DD-MM-YYYY h:mm A",
                ).format("YYYY-MM-DD HH:mm:ss"),
              },
              ...wAListCopy.workActivities.slice(1),
            ];
            setAllWAListCopy(prevWorkOrderObj => {
              const updatedWorkOrderObj = { ...prevWorkOrderObj };
              const workActivity =
                updatedWorkOrderObj?.workActivities[changeindex];

              if (workActivity) {
                workActivity.ActualStartDate = actualStartDate; // replace with your startdate
                workActivity.ActualEndDate = actualEndDate; // replace with your enddate
              }

              return updatedWorkOrderObj;
            });

            //calculatePlannedDuration();
          }
        }
      }
    }
  }, [changeindex, actualStartDate, actualEndDate]);
  useEffect(() => {}, [wAListCopy]);
  useEffect(() => {
    calculatePlannedDuration();
  }, [wAListCopy]);
  const calculatePlannedDuration = () => {
    if (actualStartDate && actualEndDate) {
      // Assuming the date format is DD-MM-YYYY HH:mm A
      const startDateParts = actualStartDate.split(/[\s:-]/);
      const endDateParts = actualEndDate.split(/[\s:-]/);

      // Month is zero-based, so subtract 1 from the month value
      const startDate = new Date(
        startDateParts[2],
        startDateParts[1] - 1,
        startDateParts[0],
        startDateParts[3],
        startDateParts[4],
      );

      const endDate = new Date(
        endDateParts[2],
        endDateParts[1] - 1,
        endDateParts[0],
        endDateParts[3],
        endDateParts[4],
      );

      const timeDifference = endDate - startDate;
      const daysDifference = Math.floor(timeDifference / (1000 * 3600 * 24));
      const hoursDifference = Math.floor(
        (timeDifference % (1000 * 3600 * 24)) / (1000 * 3600),
      );
      setActualDuration({ days: daysDifference, hours: hoursDifference });
    } else {
      console.log("Invalid start or end date");
    }
    if (changeindex === 0) {
      if (wAListCopy) {
        if (
          wAListCopy?.workActivities?.[changeindex].PlannedStartDate &&
          wAListCopy?.workActivities?.[changeindex].PlannedEndDate
        ) {
          const plannedStartDate = moment(
            wAListCopy?.workActivities[changeindex].PlannedStartDate,
          );
          const plannedEndDate = moment(
            wAListCopy?.workActivities[changeindex].PlannedEndDate,
          );
          // const startDate = new Date(
          //   wAListCopy?.workActivities[changeindex].PlannedStartDate,
          // );
          // const endDate = new Date(
          //   wAListCopy?.workActivities[changeindex].PlannedEndDate,
          // );
          // const timeDifference = endDate - startDate;
          // const daysDifference = timeDifference / (1000 * 3600 * 24);
          // console.log(`Duration: ${daysDifference} days`);
          const diff = moment(plannedEndDate).diff(plannedStartDate, "hours");

          setPlannedDuration(Number(diff) ? diff.toFixed(2) : "");
        }
        if (
          wAListCopy?.workActivities?.[changeindex].ActualStartDate &&
          wAListCopy?.workActivities?.[changeindex].ActualEndDate
        ) {
          const startDate = new Date(
            wAListCopy?.workActivities[changeindex].ActualStartDate,
          );
          const endDate = new Date(
            wAListCopy?.workActivities[changeindex].ActualEndDate,
          );
          const timeDifference = endDate - startDate;
          const daysDifference = timeDifference / (1000 * 3600 * 24);
          console.log(`Duration: ${daysDifference} days`);
          //setActualDuration(daysDifference);
        }
      }
    } else {
      if (wAListCopy) {
        if (
          wAListCopy?.workActivities?.[changeindex]?.PlannedStartDate &&
          wAListCopy?.workActivities?.[changeindex]?.PlannedEndDate
        ) {
          const startDate = new Date(
            wAListCopy?.workActivities[changeindex].PlannedStartDate,
          );
          const endDate = new Date(
            wAListCopy?.workActivities[changeindex].PlannedEndDate,
          );
          const timeDifference = endDate - startDate;
          const daysDifference = timeDifference / (1000 * 3600 * 24);
          //console.log(`Duration: ${daysDifference} days`);

          setPlannedDuration(daysDifference);
        }
        if (
          wAListCopy?.workActivities?.[changeindex]?.ActualStartDate &&
          wAListCopy?.workActivities?.[changeindex]?.ActualEndDate
        ) {
          const startDate = new Date(
            wAListCopy?.workActivities[changeindex].ActualStartDate,
          );
          const endDate = new Date(
            wAListCopy?.workActivities[changeindex].ActualEndDate,
          );
          const timeDifference = endDate - startDate;
          const daysDifference = timeDifference / (1000 * 3600 * 24);
          //setActualDuration(daysDifference);
        }
      }
    }
  };
  useEffect(() => {}, [wAListCopy, changeindex]);

  const fetchWorkActivityList = async () => {
    try {
      setLoading(true);
      const res = await WorkOrderServiceWO.getAllWorkActivities(
        singleWODetails?.WorkOrderId,
      );

      setAllWAList(res);
      setAllWAListCopy(res.workOrders[0]);
      setActivitiesList(true);
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {}, [wAList, wAListCopy, info]);

  const commentHandler = ind => comments => {
    const updatedWAListCopy = { ...wAListCopy };
    const updatedWorkActivities = [...updatedWAListCopy.workActivities];
    updatedWorkActivities[ind].Comments = comments;
    updatedWAListCopy.workActivities = updatedWorkActivities;
    setAllWAListCopy(updatedWAListCopy);
  };

  const handlerChecklist = (index, ind, enteredVal, checklistItem) => {
    const updatedWADetails = { ...wAListCopy };
    const updatedChecklist = updatedWADetails.workActivities[
      ind
    ].WorkOrderActivityChecklist.map(obj =>
      obj.activityChecklistId === checklistItem.activityChecklistId
        ? { ...obj, answer: enteredVal }
        : obj,
    );
    updatedWADetails.workActivities[ind].WorkOrderActivityChecklist =
      updatedChecklist;

    if (info) {
      const updatedInfo = { ...info };
      updatedInfo[checklistItem.label] = enteredVal;

      setInfo(updatedInfo);
    }

    if (infoAsset) {
      const updatedInfo = { ...infoAsset };
      updatedInfo[checklistItem.label] = enteredVal;

      setInfoAsset(updatedInfo);
    }
    // updatedWADetails.workActivities[ind].WorkOrderActivityChecklist =
    //   updatedChecklist;
    // }
    //setAllWAListCopy(updatedWADetails);
  };
  useEffect(() => {}, [info, infoAsset, addressLoadAsset]);
  return (
    <View>
      <Card style={styles.card}>
        <TouchableOpacity
          style={[styles.backButtonWrapper, styles.paddingRight]}
          onPress={handleBack}>
          <FontAwesome
            name="chevron-left"
            color={GlobalStyles.colors.eWhite.base}
            style={{ fontFamily: "NotoSans-Thin", marginTop: 2 }}
            size={22}
          />
          <Text
            style={{
              color: GlobalStyles.colors.eWhite.base,
              fontSize: 12,
              fontWeight: "700",
              fontFamily: "NotoSans-Bold",
            }}>
            {singleWODetails?.WorkOrderId} {t('WORK_ACTIVITY_DETAILS')}
          </Text>
        </TouchableOpacity>
      </Card>
      <ScrollView>
        {activitiesList ? (
          <>
            {wAListCopy?.workActivities?.map((item, ind) => (
              <View key={ind}>
                <TouchableOpacity onPress={() => handleToggle(ind, item)}>
                  <View style={styles.container}>
                    <View style={styles.activityItem}>
                      <View style={styles.activityItemInner}>
                        <View style={styles.iconContainer}>
                          {item?.WorkStatus === "pending" ? (
                            // <FontAwesome5Icon
                            //   name=""
                            //   size={24}
                            //   color={GlobalStyles.colors.eTertiary.base}
                            // />
                            <Icon
                              name={"FS-Open-Tasks-icon"}
                              color={GlobalStyles.colors.eTertiary.base}
                              size={28}
                            />
                          ) : item?.WorkStatus === "CO" ? (
                            <Icon
                              name={"FS-Completed-Tasks-icon"}
                              color={GlobalStyles.colors.eSecondary.base}
                              size={28}
                            />
                          ) : item?.WorkStatus === "I" ? (
                            <Icon
                              name={"FS-Open-Tasks-icon"}
                              color={GlobalStyles.colors.eTertiary.base}
                              size={28}
                            />
                          ) : (
                            <Icon
                              name={"FS-Open-Tasks-icon"}
                              color={GlobalStyles.colors.eTertiary.base}
                              size={28}
                            />
                          )}
                        </View>
                        <View style={styles.contentContainer}>
                          <View
                            style={[
                              styles.displayFlex,
                              { gap: 10, paddingVertical: 3 },
                            ]}>
                            {/* <Text style={styles.headerStyleNumber}>
                          {singleWODetails?.WorkOrderCode}
                        </Text> */}
                            <Text style={styles.headerStyleNumber}>
                              {item?.Title}
                            </Text>
                          </View>
                          <View style={styles.subHeaderRow}>
                            <Text style={[styles.subHeaderRowMinWidth]}>
                              {moment(item?.PlannedStartDate).format(
                                DATE_FORMATS.DATETIME,
                              )}
                            </Text>
                            {/* <Text style={[styles.subHeaderPriority]}>
                          {singleWODetails?.Priority === "H"
                            ? "High"
                            : singleWODetails?.Priority}
                        </Text> */}
                            <Text style={[styles.subHeaderPriority]}>
                              {item?.WorkStatus === "CO"
                                ? "Completed"
                                : item?.WorkStatus === "I"
                                ? "Inprogress"
                                : item?.WorkStatus}
                            </Text>
                          </View>
                          <View style={styles.subHeaderRowStatus}>
                            {/* <Text style={[styles.subHeaderRowMinWidth]}>
                          {/* {singleWODetails?.route} 
                        </Text> */}
                          </View>
                        </View>
                      </View>
                      <View style={styles.arrowIconStyle}>
                        <FontAwesome5Icon
                          name={
                            expandedSections.includes(ind)
                              ? "chevron-down"
                              : "chevron-right"
                          }
                          color={GlobalStyles.colors.eMedium.base}
                          size={12}
                          fontFamily="NotoSans-Bold"
                        />
                      </View>
                    </View>
                  </View>
                  <View style={styles.lineStyle} />
                </TouchableOpacity>
                {expandedSections.includes(ind) && (
                  <>
                    <View>
                      <View style={styles.container}>
                        <View style={[styles.rowFlexContainer]}>
                          <View style={styles.labelWidth}>
                            <Text style={[styles.labelStyle]}>
                              {t('PLANNED_START_DATE')}
                            </Text>
                          </View>
                          <View style={styles.flexWrap}>
                            <Text style={[styles.valueStyle]}>
                              {moment(
                                wAListCopy?.workActivities[ind]
                                  .PlannedStartDate,
                              ).format(DATE_FORMATS.DATETIME)}
                            </Text>
                          </View>
                        </View>
                        <View style={[styles.rowFlexContainer]}>
                          <View style={styles.labelWidth}>
                            <Text style={[styles.labelStyle]}>
                              {t('PLANNED_END_DATE')}
                            </Text>
                          </View>
                          <View style={styles.flexWrap}>
                            <Text style={[styles.valueStyle]}>
                              {moment(
                                wAListCopy?.workActivities[ind].PlannedEndDate,
                              ).format(DATE_FORMATS.DATETIME)}
                            </Text>
                          </View>
                        </View>
                        <View style={[styles.rowFlexContainerDate]}>
                          <CalendarPicker
                            minDate={OffAvailableDates}
                            setCalendarDate={setActualStartDate}
                            // selectedDate={selectedDate}
                            setSelectedDate={setActualStartDate}
                            showCalendar={showCalendar}
                            setShowCalendar={setShowCalendar}
                            calendarDate={actualStartDate}
                            // requiredCardHeight={requiredHeight}
                            editable={editable}
                            setErrorOn={setErrorOn}
                            setAvailableOndates={setAvailableOndates}
                            setCalorText={setCalorText}
                            setTextDate={setTextDateOff}
                            outlinedLable={t('ACTUAL_START_DATE')}
                            index={ind}
                            setIndex={setIndex}
                          />
                        </View>

                        <View
                          style={[
                            styles.rowFlexContainerDate,
                            styles.marginVertical2,
                          ]}>
                          <CalendarPicker
                            minDate={OffAvailableDates}
                            setCalendarDate={setActualEndDate}
                            selectedDate={selectedDate}
                            setSelectedDate={setActualEndDate}
                            showCalendar={showCalendar}
                            setShowCalendar={setShowCalendar}
                            calendarDate={actualEndDate}
                            // requiredCardHeight={requiredHeight}
                            editable={editable}
                            setErrorOn={setErrorOn}
                            setAvailableOndates={setAvailableOndates}
                            setCalorText={setCalorText}
                            setTextDate={setTextDateOff}
                            outlinedLable={t('ACTUAL_END_DATE')}
                            index={ind}
                            setIndex={setIndex}
                          />
                        </View>
                        <View style={[styles.rowFlexContainer]}>
                          <View style={styles.labelWidth}>
                            <Text style={[styles.labelStyle]}>
                              {t('PLANNED_DURATION')}
                            </Text>
                          </View>
                          <View style={styles.flexWrap}>
                            <Text style={[styles.valueStyle]}>
                              {" "}
                              {plannedDuration}
                            </Text>
                          </View>
                        </View>
                        <View style={[styles.rowFlexContainer]}>
                          <View style={styles.labelWidth}>
                            <Text style={[styles.labelStyle]}>
                              {t('ACTUAL_DURATION')}
                            </Text>
                          </View>
                          <View style={styles.flexWrap}>
                            <Text style={[styles.valueStyle]}>
                              {" "}
                              {actualDuration.days
                                ? actualDuration.days + " days"
                                : null}{" "}
                              {actualDuration.hours
                                ? actualDuration.hours + " hrs"
                                : null}
                              {actualDuration.minutes
                                ? actualDuration.minutes + " minutes"
                                : null}
                            </Text>
                          </View>
                        </View>
                      </View>
                    </View>
                    <View style={styles.containerDiv}>
                      {addressLoad && info && consumnerData && (
                        <View
                          style={[
                            styles.rowFlexContainer,
                            {
                              paddingVertical: 10,
                              paddingLeft: 20,
                            },
                          ]}>
                          <Text style={styles.labelHeader}>{t('INFORMATION')}</Text>
                        </View>
                      )}
                      <View style={styles.lineStyleInfo} />
                      {addressLoad && info && consumnerData ? (
                        <>
                          <View style={[styles.rowFlexContainer]}>
                            <View style={styles.labelWidth}>
                              <Text style={[styles.labelStyle]}>
                                {t('CONSUMER_NAME')}
                              </Text>
                            </View>
                            <View style={styles.flexWrap}>
                              <Text style={[styles.valueStyle]}>
                                {info?.ConsumerName}
                              </Text>
                            </View>
                          </View>
                          {info?.Address1 ? (
                            <>
                              <View style={[styles.rowFlexContainer]}>
                                <View style={styles.labelWidth}>
                                  <Text style={[styles.labelStyle]}>
                                    {t('ADDRESS1')}
                                  </Text>
                                </View>
                                <View style={styles.flexWrap}>
                                  <Text style={[styles.valueStyle]}>
                                    {info?.Address1}
                                  </Text>
                                </View>
                              </View>
                            </>
                          ) : null}
                          {info?.Address2 ? (
                            <>
                              <View style={[styles.rowFlexContainer]}>
                                <View style={styles.labelWidth}>
                                  <Text style={[styles.labelStyle]}>
                                    {t('ADDRESS2')}
                                  </Text>
                                </View>
                                <View style={styles.flexWrap}>
                                  <Text style={[styles.valueStyle]}>
                                    {info?.Address2}
                                  </Text>
                                </View>
                              </View>
                            </>
                          ) : null}
                          {info?.Address3 ? (
                            <>
                              <View style={[styles.rowFlexContainer]}>
                                <View style={styles.labelWidth}>
                                  <Text style={[styles.labelStyle]}>
                                    {t('ADDRESS3')}
                                  </Text>
                                </View>
                                <View style={styles.flexWrap}>
                                  <Text style={[styles.valueStyle]}>
                                    {info?.Address3}
                                  </Text>
                                </View>
                              </View>
                            </>
                          ) : null}
                          {info?.Address4 ? (
                            <>
                              <View style={[styles.rowFlexContainer]}>
                                <View style={styles.labelWidth}>
                                  <Text style={[styles.labelStyle]}>
                                  {t('ADDRESS4')}
                                  </Text>
                                </View>
                                <View style={styles.flexWrap}>
                                  <Text style={[styles.valueStyle]}>
                                    {info?.Address4}
                                  </Text>
                                </View>
                              </View>
                            </>
                          ) : null}
                          {consumnerDataLoad ? (
                            <>
                              <View
                                style={[
                                  styles.rowFlexContainer,
                                  styles.marginVertical2,
                                ]}>
                                <View style={styles.labelWidth}>
                                  <Text style={[styles.labelStyle]}>{t('PHONE')}</Text>
                                </View>
                                <View style={styles.flexWrap}>
                                  <Text style={[styles.valueStyle]}>
                                    {/* {info?.Mobile} */}
                                    {consumnerData?.Mobile}
                                  </Text>
                                </View>
                              </View>
                              <View style={[styles.rowFlexContainer]}>
                                <View style={styles.labelWidth}>
                                  <Text style={[styles.labelStyle]}>
                                    {t('FULL_ADDRESS')}
                                  </Text>
                                </View>
                                <View style={styles.flexWrap}>
                                  <Text style={[styles.valueStyle]}>
                                    {/* {info?.FullAddress} */}
                                    {consumnerData?.FullAddress}
                                  </Text>
                                </View>
                              </View>
                            </>
                          ) : null}

                          <View
                            style={[
                              styles.rowFlexContainer,
                              styles.marginVertical2,
                            ]}>
                            <View style={styles.labelWidth}>
                              <Text style={[styles.labelStyle]}>{t('LOCATION')}</Text>
                            </View>
                            <View style={styles.flexWrap}>
                              <Text style={[styles.valueStyle]}>
                                <FontAwesome
                                  name="map-marker"
                                  size={20}
                                  color={GlobalStyles.colors.ePrimary.base}
                                />{" "}
                                {info?.LatitudeLongitude}
                              </Text>
                            </View>
                          </View>
                        </>
                      ) : null}
                      {addressLoadAsset ? (
                        <>
                          <View style={[styles.rowFlexContainer]}>
                            <View style={styles.labelWidth}>
                              <Text style={[styles.labelStyle]}>
                                {t('DEVICE_TYPE')}
                              </Text>
                            </View>
                            <View style={styles.flexWrap}>
                              <Text style={[styles.valueStyle]}>
                                {infoAsset?.deviceType}
                              </Text>
                            </View>
                          </View>
                          <View style={[styles.rowFlexContainer]}>
                            <View style={styles.labelWidth}>
                              <Text style={[styles.labelStyle]}>
                                {t('SERIAL_NUMBER')}
                              </Text>
                            </View>
                            <View style={styles.flexWrap}>
                              <Text style={[styles.valueStyle]}>
                                {infoAsset?.serialNumber}
                              </Text>
                            </View>
                          </View>
                          <View style={[styles.rowFlexContainer]}>
                            <View style={styles.labelWidth}>
                              <Text style={[styles.labelStyle]}>
                                {t('MANUFACTURER')}
                              </Text>
                            </View>
                            <View style={styles.flexWrap}>
                              <Text style={[styles.valueStyle]}>
                                {infoAsset?.manufacturer}
                              </Text>
                            </View>
                          </View>
                          <View style={[styles.rowFlexContainer]}>
                            <View style={styles.labelWidth}>
                              <Text style={[styles.labelStyle]}>{t('MODEL')}</Text>
                            </View>
                            <View style={styles.flexWrap}>
                              <Text style={[styles.valueStyle]}>
                                {infoAsset?.model}
                              </Text>
                            </View>
                          </View>
                          <View style={[styles.rowFlexContainer]}>
                            <View style={styles.labelWidth}>
                              <Text style={[styles.labelStyle]}>
                                {t('DEVICE_STATUS')}
                              </Text>
                            </View>
                            <View style={styles.flexWrap}>
                              <Text style={[styles.valueStyle]}>
                                {infoAsset?.deviceStatus}
                              </Text>
                            </View>
                          </View>
                          <View
                            style={[
                              styles.rowFlexContainer,
                              styles.marginVertical2,
                            ]}>
                            <View style={styles.labelWidth}>
                              <Text style={[styles.labelStyle]}>{t('PHONE')}</Text>
                            </View>
                            <View style={styles.flexWrap}>
                              <Text style={[styles.valueStyle]}>
                                {infoAsset?.mobileNo}
                              </Text>
                            </View>
                          </View>
                          {infoAsset?.address1 !== null ? (
                            <>
                              <View style={[styles.rowFlexContainer]}>
                                <View style={styles.labelWidth}>
                                  <Text style={[styles.labelStyle]}>
                                    {t('ADDRESS1')}
                                  </Text>
                                </View>
                                <View style={styles.flexWrap}>
                                  <Text style={[styles.valueStyle]}>
                                    {infoAsset?.address1}
                                  </Text>
                                </View>
                              </View>
                            </>
                          ) : null}
                          {infoAsset?.address2 !== null ? (
                            <>
                              <View style={[styles.rowFlexContainer]}>
                                <View style={styles.labelWidth}>
                                  <Text style={[styles.labelStyle]}>
                                    {t('ADDRESS2')}
                                  </Text>
                                </View>
                                <View style={styles.flexWrap}>
                                  <Text style={[styles.valueStyle]}>
                                    {infoAsset?.address2}
                                  </Text>
                                </View>
                              </View>
                            </>
                          ) : null}
                          {infoAsset?.address3 !== null ? (
                            <>
                              <View style={[styles.rowFlexContainer]}>
                                <View style={styles.labelWidth}>
                                  <Text style={[styles.labelStyle]}>
                                    {t('ADDRESS3')}
                                  </Text>
                                </View>
                                <View style={styles.flexWrap}>
                                  <Text style={[styles.valueStyle]}>
                                    {infoAsset?.address3}
                                  </Text>
                                </View>
                              </View>
                            </>
                          ) : null}
                          {infoAsset?.address4 !== null ? (
                            <>
                              <View style={[styles.rowFlexContainer]}>
                                <View style={styles.labelWidth}>
                                  <Text style={[styles.labelStyle]}>
                                    {t('ADDRESS4')}
                                  </Text>
                                </View>
                                <View style={styles.flexWrap}>
                                  <Text style={[styles.valueStyle]}>
                                    {infoAsset?.address4}
                                  </Text>
                                </View>
                              </View>
                            </>
                          ) : null}
                          {infoAsset?.city !== null ? (
                            <>
                              <View style={[styles.rowFlexContainer]}>
                                <View style={styles.labelWidth}>
                                  <Text style={[styles.labelStyle]}>{t('CITY')}</Text>
                                </View>
                                <View style={styles.flexWrap}>
                                  <Text style={[styles.valueStyle]}>
                                    {infoAsset?.city}
                                  </Text>
                                </View>
                              </View>
                            </>
                          ) : null}

                          <>
                            <View style={[styles.rowFlexContainer]}>
                              <View style={styles.labelWidth}>
                                <Text style={[styles.labelStyle]}>{t('COUNTRY')}</Text>
                              </View>
                              <View style={styles.flexWrap}>
                                <Text style={[styles.valueStyle]}>
                                  {infoAsset?.county}
                                </Text>
                              </View>
                            </View>
                          </>

                          <View
                            style={[
                              styles.rowFlexContainer,
                              styles.marginVertical2,
                            ]}>
                            <View style={styles.labelWidth}>
                              <Text style={[styles.labelStyle]}>{t('LOCATION')}</Text>
                            </View>
                            <View style={styles.flexWrap}>
                              <Text style={[styles.valueStyle]}>
                                <FontAwesome
                                  name="map-marker"
                                  size={20}
                                  color={GlobalStyles.colors.ePrimary.base}
                                />{" "}
                                {infoAsset?.geocodeLatitude},{" "}
                                {infoAsset?.geocodeLongitude}
                              </Text>
                            </View>
                          </View>
                          {/* </View> */}
                        </>
                      ) : null}
                    </View>
                    <View style={styles.containerChecklist}>
                      <View style={{ paddingVertical: 10, paddingLeft: 20 }}>
                        <Text style={styles.labelHeader}>{t('CHECKLIST')}</Text>
                      </View>
                      <View style={styles.lineStyleInfoCheckList} />
                      <View style={{ paddingLeft: 20 }}>
                        {wAListCopy?.workActivities[
                          ind
                        ]?.WorkOrderActivityChecklist?.map(
                          (checklistItem, index) => (
                            <>
                              {/* {checklistItem?.checklistType ===
                              "standard-consumer-indexing" ? ( */}
                              {/* <> */}
                              {checklistItem?.dataType === "number" ||
                              checklistItem?.dataType === "string" ? (
                                <>
                                  <View style={styles.labelWidth}>
                                    <Text style={[styles.labelStyle]}>
                                      {checklistItem?.description}
                                    </Text>
                                    <View style={styles.inputContainer}>
                                      <Input
                                        onUpdateValue={enteredVal =>
                                          handlerChecklist(
                                            index,
                                            ind,
                                            enteredVal,
                                            checklistItem,
                                          )
                                        }
                                        value={
                                          checklistItem?.dataType === "number"
                                            ? addressLoad
                                              ? `${info[checklistItem?.label]}`
                                              : `${
                                                  infoAsset[
                                                    checklistItem?.label
                                                  ]
                                                }`
                                            : addressLoad
                                            ? info[checklistItem?.label]
                                            : infoAsset[
                                                checklistItem?.label
                                              ] === null ||
                                              infoAsset[
                                                checklistItem?.label
                                              ] === undefined
                                            ? ""
                                            : `${
                                                infoAsset[checklistItem?.label]
                                              }`
                                        }
                                      />
                                    </View>
                                  </View>
                                </>
                              ) : null}
                              {checklistItem?.dataType === "boolean" ? (
                                <View key={index}>
                                  <View style={styles.checkListItemContainer}>
                                    <Checkbox.Item
                                      mode="android"
                                      label={checklistItem.description}
                                      position="leading"
                                      status={
                                        checklistItem.answer === "yes"
                                          ? "checked"
                                          : "unchecked"
                                      }
                                      onPress={() =>
                                        markCheckListItemDone(
                                          ind,
                                          checklistItem,
                                          checklistItem.answer === "yes"
                                            ? false
                                            : true,
                                        )
                                      }
                                      labelStyle={styles.bigTextHeader}
                                      color={
                                        GlobalStyles.colors.eSecondary.base
                                      }
                                      uncheckedColor={
                                        GlobalStyles.colors.eRich.hover
                                      }></Checkbox.Item>
                                  </View>
                                </View>
                              ) : null}
                              {checklistItem?.dataType === "asset_url" ? (
                                <View key={index}>
                                  <View style={styles.imgContainer}>
                                    <View style={styles.imgSideBySideView}>
                                      <Text style={[styles.labelStyle]}>
                                        {checklistItem?.label}
                                      </Text>
                                    </View>
                                    <View style={styles.imgSideBySideView2}>
                                      <FontAwesome5Icon
                                        name="camera"
                                        size={22}
                                        color={
                                          GlobalStyles.colors.ePrimary.base
                                        }
                                      />
                                      {/* <CameraComponent /> */}
                                    </View>
                                  </View>
                                </View>
                              ) : null}
                              {checklistItem?.dataType === "location" ? (
                                <View key={index}>
                                  <View style={styles.imgContainer}>
                                    <View style={styles.imgSideBySideView}>
                                      <Text style={[styles.labelStyle]}>
                                        {checklistItem?.label}
                                      </Text>
                                    </View>
                                    <View style={styles.imgSideBySideView2}>
                                      <FontAwesome5Icon
                                        name="camera"
                                        size={22}
                                        color={
                                          GlobalStyles.colors.ePrimary.base
                                        }
                                      />
                                      {/* <CameraComponent /> */}
                                    </View>
                                  </View>
                                </View>
                              ) : null}
                              {/* {checklistItem?.dataType === "boolean" ? (
                                    <>
                                      <View style={styles.labelWidthMeter}>
                                        <Checkbox.Item
                                          label={checklistItem?.description}
                                          position="leading"
                                          mode="android"
                                          status={
                                            checklistItem?.answer === "yes"
                                              ? "checked"
                                              : "unchecked"
                                          }
                                          onPress={() => {
                                            markCheckListItemDone(
                                              index,
                                              checklistItem,
                                              checklistItem?.answer,
                                            );
                                          }}
                                          labelStyle={styles.bigTextHeader}
                                          color={
                                            GlobalStyles.colors.eSecondary.base
                                          }
                                          uncheckedColor={
                                            GlobalStyles.colors.eRich.hover
                                          }
                                        />
                                      </View>
                                    </>
                                  ) : null} */}
                              {/* </> */}
                              {/* ) : null} */}
                            </>
                          ),
                        )}
                        <View>
                          <Text style={styles.text}>
                            {t('COMMENTS')}<Text style={styles.textStar}>*</Text>:
                          </Text>
                          <TextInput
                            multiline
                            numberOfLines={4}
                            placeholderTextColor="#5C5E60"
                            mode="outlined"
                            maxLength={2000}
                            value={wAListCopy?.workActivities[ind].Comments}
                            enablesReturnKeyAutomatically
                            onChangeText={commentHandler(ind)}
                            placeholder={t('ENTER_COMMENTS_REASON')}
                            style={styles.commentsText}
                            outlineColor={GlobalStyles.colors.eDark.hover}
                            activeOutlineColor={
                              GlobalStyles.colors.ePrimary.base
                            }
                            persistentScrollbar={true}
                          />
                          <View
                            style={{
                              flex: 1,
                              flexDirection: "row",
                              marginBottom: "2%",
                            }}>
                            <Text style={{ textAlign: "right", flex: 1 }}>
                              {/* {comment ? comment.length : "0"}/2000 */}
                            </Text>
                          </View>
                        </View>
                      </View>
                      {singleWODetails?.WorkOrderType ==
                        "Transformer Replacement" &&
                        wAListCopy?.workActivities[ind]?.Title.includes(
                          "Debrief",
                        ) && (
                          <>
                            <ActivityGrid />
                            <LabourTableGrid labourType={"Technician"} />
                            <EquipmentTableGrid />
                            <MaterialTableGrid />
                            <DirectChargesGrid />
                          </>
                        )}
                      {singleWODetails?.WorkOrderType ==
                        "Transformer Inspection" &&
                        wAListCopy?.workActivities[ind]?.Title.includes(
                          "Debrief",
                        ) && (
                          <>
                            <LabourTableGrid labourType={"Mechanic"} />
                            <DirectChargesGrid />
                          </>
                        )}

                      <View style={styles.btnContainer}>
                        <Button
                          onPress={cancelClick}
                          buttonbgColor={[
                            styles.cancelBg,
                            disableCancle && styles.disabledCancleStyle,
                          ]}
                          textColor={[
                            disableCancle
                              ? styles.disableColor
                              : styles.cancelText,
                          ]}
                          disabled={disableCancle}>
                          {t('CANCEL')}
                        </Button>
                        <Button
                          buttonbgColor={[
                            styles.buttonBgColor,
                            disableSubmit && styles.disabledStyle,
                          ]}
                          textColor={[
                            disableSubmit
                              ? styles.disableColor
                              : styles.textColor,
                          ]}
                          onPress={() =>
                            submitActivityClick(
                              wAListCopy?.workActivities[ind],
                              ind,
                            )
                          }
                          disabled={disableSubmit}>
                          {t('SAVE')}
                          {isActivityLoading && (
                            <ActivityIndicator
                              align="center"
                              size={13}
                              color={GlobalStyles.colors.eWhite.base}
                            />
                          )}
                        </Button>
                      </View>
                    </View>
                    {/* <View style={styles.containerSubmit}> */}

                    {/* </View> */}
                  </>
                )}
                <View style={styles.lineStyle} />
              </View>
            ))}
          </>
        ) : null}
        {customerNumberError ? (
          <>
            <View style={[styles.rowFlexContainerError]}>
              <Text style={[styles.labelStyleError]}>{customerNumberText}</Text>
            </View>
          </>
        ) : null}
        {OTPConfirmationWO ? (
          <View style={styles.containerSubmit}>
            <View style={styles.btnContainer}>
              <Button
                onPress={cancelClick}
                buttonbgColor={[
                  styles.cancelBg,
                  disableCancle && styles.disabledCancleStyle,
                ]}
                textColor={[
                  disableCancle ? styles.disableColor : styles.cancelText,
                ]}
                disabled={disableCancle}>
                {t('CANCEL')}
              </Button>
              <Button
                buttonbgColor={[
                  styles.buttonBgColor,
                  disableSubmit && styles.disabledStyle,
                ]}
                textColor={[
                  disableSubmit ? styles.disableColor : styles.textColor,
                ]}
                onPress={submitClick}
                disabled={disableSubmit}>
                {t('COMPLETE')}
                {isLoading && (
                  <ActivityIndicator
                    align="center"
                    size={13}
                    color={GlobalStyles.colors.eWhite.base}
                  />
                )}
              </Button>
            </View>
          </View>
        ) : (
          <View style={styles.containerSubmit}>
            <View style={styles.btnContainer}></View>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    borderTopEndRadius: 10,
    borderTopStartRadius: 10,
    borderBottomEndRadius: 0,
    borderBottomStartRadius: 0,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    padding: 15,
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    margin: 10,
  },
  imgContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 0,
  },
  imgSideBySideView: {
    flex: 1,
    padding: 0,
  },
  imgSideBySideView2: {
    flex: 1,
    marginBottom: 10,
  },
  wrapDirection: {
    flexDirection: "row",
    flexWrap: "wrap",
    alignContent: "space-between",
  },
  bigTextHeader: {
    color: GlobalStyles.colors.eRich.base,
    textAlign: "left",
    fontSize: 14,
    justifyContent: "flex-start",
    // wordBreak: "break-all",
  },
  titleCard: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontWeight: "700",
    fontFamily: "NotoSans-Bold",
  },
  backButtonWrapper: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 3,
  },
  paddingRight: {
    paddingRight: 10,
  },
  scrollViewContent: {
    flexGrow: 1,
    padding: 20, // Adjust the padding as per your design
  },
  checkListDescription: {
    fontSize: 14,
    color: GlobalStyles.colors.ePrimary.base,
  },
  checkListDoneBtnStyle: {
    marginVertical: 10,
    marginTop: 20,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  checkListTextInput: {
    // minWidth: 150,
    flex: 1,
    height: 30,
    justifyContent: "center",
    marginRight: 10,
  },
  checkListIdLabel: {
    fontFamily: "NotoSans-SemiBold",
    fontSize: 13,
  },
  checkListStatus: {
    fontSize: 13,
    fontFamily: "NotoSans-SemiBold",
  },
  checkListItemContainer: {
    marginLeft: -25,
    marginVertical: -5,
  },
  camClass: {
    marginLeft: 25,
  },
  container: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: 10,
    borderRadius: 5,
  },
  containerDiv: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: 10,
    marginTop: 0,
    borderRadius: 5,
  },
  containerChecklist: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: 10,
    // marginTop: 15,
    borderRadius: 5,
  },
  containerNoChecklist: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    alignItems: "center",
    marginVertical: 15,
    borderRadius: 5,
  },
  containerSubmit: {
    marginHorizontal: 10,
    marginBottom: 150,
    marginTop: 15,
  },
  containerStyle: {
    backgroundColor: "white",
    padding: 70,
    marginHorizontal: 20,
  },
  displayFlex: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 20,
  },
  ePrimary: {
    color: GlobalStyles.colors.ePrimary.base,
  },
  flexRowCenter: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  flexWrap: {
    flex: 1,
  },
  workActivityDetailContainer: {
    paddingHorizontal: 8,
    paddingVertical: 10,
  },
  labelHeader: {
    fontFamily: "NotoSans-Bold",
    fontSize: 14,
    color: GlobalStyles.colors.ePrimary.base,
  },
  nolabelHeader: {
    fontFamily: "NotoSans-Bold",
    fontSize: 14,
    color: GlobalStyles.colors.ePrimary.base,
  },
  labelStyle: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eRich.base,
  },
  labelStyleError: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eDanger.dark,
  },
  inputContainer: {
    marginRight: 10,
    marginVertical: -20,
  },
  valueStyle: {
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  labelWidth: {
    flex: 1,
  },
  labelWidthMeter: {
    marginLeft: -25,
  },
  labelWidthDuration: {
    flex: 1,
  },
  labelWidthTime: {
    width: 150,
    marginHorizontal: 20,
    marginTop: 15,
    marginBottom: 10,
  },
  opacityDimmed: {
    opacity: 0.5,
  },
  otpTextInputStyle: {
    borderBottomWidth: 2,
    borderBottomColor: GlobalStyles.colors.ePrimary.base,
    color: GlobalStyles.colors.ePrimary.base,
  },
  overdueStyle: {
    color: GlobalStyles.colors.eTertiary.base,
  },
  pastDueStyle: {
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderWidth: 1,
    borderColor: "#d5d5d5",
    backgroundColor: "#fef9e8",
    display: "flex",
    flexDirection: "row",
    gap: 10,
    alignItems: "center",
  },

  rowFlexContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 5,
  },
  rowFlexContainerError: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  rowFlexContainerDate: {
    marginHorizontal: 20,
    marginTop: 10,
  },
  snackbarWrapper: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
  textInputWrapper: {
    paddingVertical: 3,
    flex: 1,
    // width: Dimensions.get("screen").width - 100,
  },
  workOrderCompletionTextStyle: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 13,
  },
  activityItem: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 10,
    // marginTop: -5,
  },
  activityItemInner: {
    display: "flex",
    flexDirection: "row",
    flex: 1,
    gap: 20,
  },
  iconContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 10,
  },
  contentContainer: {
    flex: 1,
  },
  displayFlex: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  headerStyleNumber: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderRow: {
    display: "flex",
    flexDirection: "row",
    paddingVertical: 1,
    justifyContent: "space-between",
    paddingRight: 20,
    marginTop: -5,
    // textAlign: 'left'
  },
  subHeaderRowMinWidth: {
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderPriority: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eRich.base,
    textTransform: "capitalize",
    marginTop: -5,
    textAlign: "right",
  },
  subHeaderRowStatus: {
    display: "flex",
    flexDirection: "row",
    paddingVertical: 1,
    justifyContent: "space-between",
    paddingRight: 20,
    marginBottom: 10,
  },
  subHeaderRowMinWidth: {
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderRowMinWidth: {
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderStatus: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eRich.base,
    textTransform: "capitalize",
    marginTop: -15,
    textAlign: "right",
  },
  arrowIconStyle: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  lineStyle: {
    // borderWidth: 0.3,
    borderColor: GlobalStyles.colors.eMedium.hover,
    marginTop: 1,
    width: "90%",
  },
  lineStyleInfo: {
    borderWidth: 0.3,
    borderColor: GlobalStyles.colors.eSeparationLine.base,
    marginTop: 0,
    width: "100%",
  },
  lineStyleInfoCheckList: {
    borderWidth: 0.3,
    borderColor: GlobalStyles.colors.eSeparationLine.base,
    marginBottom: 5,
    width: "100%",
  },
  btnContainer: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 0,
    marginBottom: 15,
  },
  disabledCancleStyle: {
    // opacity: 0.5,
    backgroundColor: GlobalStyles.colors.ePage.base,
    borderColor: GlobalStyles.colors.eLight.selected,
    borderWidth: 1,
  },
  disableColor: {
    color: GlobalStyles.colors.eLight.selected,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  cancelText: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  bgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    marginLeft: 10,
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginLeft: 10,
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.ePrimary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 10,
  },
  disabledStyle: {
    opacity: 0.5,
    backgroundColor: GlobalStyles.colors.eBackground.selected,
  },
});

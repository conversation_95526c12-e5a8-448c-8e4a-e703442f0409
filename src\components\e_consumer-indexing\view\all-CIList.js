import {
  StyleSheet,
  View,
  Text,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import { ScrollView } from "react-native-gesture-handler";
import { GlobalStyles } from "../../app/global-styles";
import Icon from "../../icon";
import FontAwesome5Icon from "react-native-vector-icons/FontAwesome";
import { useDispatch, useSelector } from "react-redux";
import {
  setActivities,
  setCurrentActivity,
} from "../../../redux/slices/activitySlices";
import { Switch, TouchableRipple, Card } from "react-native-paper";
import React, { useEffect, useState, useContext } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  DATE_FORMATS,
  ROUTES,
  WORK_ACTIVITY_STATUS,
} from "../../common/constants";
import _ from "lodash";
import moment from "moment/moment";
import { useIsFocused } from "@react-navigation/native";
import { useNavigation } from "@react-navigation/native";
import { servicePath } from "../../../redux/slices/servicePath";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import { consumerIndexContext } from "../e_consumer-index";
import { useTranslation } from "react-i18next";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { config } from "../../../environment";
import { stackContext } from "../../app/get_stack";

export default function ConsumnerIndexList() {
  const { workModelType } = React.useContext(stackContext);
  const { t } = useTranslation();
  const { setCIList, setSingleCI, setCIDetails, setTempCIData, setNewCIData } =
    useContext(consumerIndexContext);
  let allCIList, createCI, setCreateCI;

  if (workModelType === "WA") {
    ({ allCIList, createCI, setCreateCI } = useContext(drawerContext));
  } else {
    ({ allCIList, createCI, setCreateCI } = useContext(drawerContextWO));
  }

  const onPressTicket = async option => {
    const temp = JSON.stringify(option);
    console.log("temp.....", temp);
    setTempCIData(JSON.parse(temp));
    setSingleCI(true);
    setCIList(false);
    setCreateCI(false);
  };

  const isScrollViewContentLess =
    Dimensions.get("window").height - allCIList.length * 100 > 130;

  const handlePlusIconPress = () => {
    setCIDetails([]);
    setTempCIData([]);
    setCIList(false);
    setSingleCI(true);
    setCreateCI(true);
  };
  return (
    <>
      <View style={styles.containerView}>
        <Card style={styles.card}>
          <View>
            <View style={styles.wrapDirection}>
              <View>
                <Text style={styles.titleCard}>{t("CHAT_HISTORY")}</Text>
              </View>
            </View>
          </View>
        </Card>
        <ScrollView
          style={styles.container}
          contentContainerStyle={styles.scrollViewContent}>
          {allCIList.length > 0 ? (
            <>
              <View>
                {allCIList.map(option => {
                  return (
                    <TouchableRipple
                      onPress={() => onPressTicket(option)}
                      key={option.id}>
                      <>
                        <View>
                          <View style={styles.activityItem}>
                            <View style={styles.activityItemInner}>
                              <View style={styles.iconContainer}>
                                {option.status === "Overdue" ? (
                                  <FontAwesome5Icon
                                    name={"exclamation-triangle"}
                                    color={GlobalStyles.colors.eTertiary.base}
                                    size={28}
                                  />
                                ) : (
                                  <FontAwesome5Icon
                                    name={"exclamation-triangle"}
                                    color={GlobalStyles.colors.eTertiary.base}
                                    size={28}
                                  />
                                )}
                              </View>
                              <View style={styles.contentContainer}>
                                <View
                                  style={[
                                    styles.displayFlex,
                                    { gap: 10, paddingVertical: 3 },
                                  ]}>
                                  <Text style={styles.headerStyleNumber}>
                                    {option.ConsumerNumber}
                                  </Text>
                                  <Text style={styles.headerStyleNumber}>
                                    {option.ConsumerName}
                                  </Text>
                                </View>
                                <View style={styles.subHeaderRow}>
                                  <Text style={[styles.subHeaderRowMinWidth]}>
                                    {moment(option.createdOn).format(
                                      DATE_FORMATS.DATETIME,
                                    )}
                                  </Text>
                                  {/* <Text style={[styles.subHeaderPriority]}>
                                    {option.priority}
                                  </Text> */}
                                </View>
                                <View style={styles.subHeaderRowStatus}>
                                  <Text style={[styles.subHeaderStatus]}>
                                    {option.ConsumerStatus}
                                  </Text>
                                </View>
                              </View>
                            </View>
                            <View style={styles.arrowIconStyle}>
                              <FontAwesome5Icon
                                name="chevron-right"
                                color={GlobalStyles.colors.eMedium.base}
                                size={12}
                                fontFamily="NotoSans-Bold"
                              />
                            </View>
                          </View>
                        </View>
                        <View style={styles.lineStyle} />
                      </>
                    </TouchableRipple>
                  );
                })}
              </View>
            </>
          ) : (
            <View style={[styles.noDateWrapper, { height: 380 }]}>
              <View style={{ height: 200 }}>
                <Text
                  style={{
                    fontSize: 19,
                    color: GlobalStyles.colors.ePrimary.base,
                  }}>
                  {t("NO_CHAT_HISTORY_MSG")}
                </Text>
              </View>
            </View>
          )}
        </ScrollView>
        <View
          style={{
            width: "100%",
            height: 50,
            bottom: 20,
            position: "absolute",
          }}>
          <TouchableOpacity
            onPress={handlePlusIconPress}
            style={[
              styles.plusIconContainer,
              // { bottom: isScrollViewContentLess ? -350 : 150 },
            ]}>
            <View style={styles.circle}>
              <FontAwesome5Icon name="plus" size={20} color="white" />
            </View>
          </TouchableOpacity>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  scrollViewContent: {
    flexGrow: 1,
  },
  containerView: {
    position: "relative",
    height: "100%",
  },
  plusIconContainer: {
    position: "absolute",
    bottom: 0,
    right: 20,
    alignItems: "center",
  },
  circle: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: GlobalStyles.colors.ePrimary.base, // Customize the background color of the circle
    justifyContent: "center",
    alignItems: "center",
  },
  card: {
    borderTopEndRadius: 10,
    borderTopStartRadius: 10,
    borderBottomEndRadius: 0,
    borderBottomStartRadius: 0,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    padding: 15,
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    margin: 10,
  },
  wrapDirection: {
    flexDirection: "row",
    flexWrap: "wrap",
    alignContent: "space-between",
  },
  titleCard: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontWeight: "700",
    fontFamily: "NotoSans-Bold",
  },
  activityWrapper: {
    // paddingTop: 8,
    // paddingBottom: 8,
    // borderBottomColor: GlobalStyles.colors.eBackground.base,
    // borderBottomWidth: 1,
  },
  lineStyle: {
    borderWidth: 1,
    borderColor: GlobalStyles.colors.eBackground.base,
    marginTop: 0,
    marginBottom: 6,
    width: "150%",
  },
  activityItem: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 10,
    // marginTop: -5,
  },
  mainView: {
    marginVertical: 5,
  },
  activityItemInner: {
    display: "flex",
    flexDirection: "row",
    flex: 1,
    gap: 20,
  },
  arrowIconStyle: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  displayFlex: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  headerStyle: {
    fontSize: 15,
    fontFamily: "NotoSans-SemiBold",
  },
  headerStyleNumber: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eRich.base,
  },
  iconContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 10,
  },
  subHeaderRowMinWidth: {
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderPriority: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eRich.base,
    textTransform: "capitalize",
    marginTop: 10,
    textAlign: "right",
  },
  subHeaderStatus: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eRich.base,
    textTransform: "capitalize",
    marginTop: -15,
    textAlign: "right",
  },
  subHeaderRow: {
    display: "flex",
    flexDirection: "row",
    paddingVertical: 1,
    justifyContent: "space-between",
    paddingRight: 20,
    marginTop: -5,
    // textAlign: 'left'
  },
  subHeaderRowStatus: {
    display: "flex",
    flexDirection: "row",
    paddingVertical: 1,
    justifyContent: "space-between",
    paddingRight: 20,
    paddingVertical: 10,
  },
  container: {
    //flex: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: 10,
    marginTop: -10,
    marginBottom: 10,
  },
  contentContainer: {
    flex: 1,
  },
  noDateWrapper: {
    flex: 1,
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
  },
  profilePicView: {
    height: 60,
    width: 60,
    borderRadius: 100,
    marginLeft: 0,
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    borderWidth: 10,
    borderColor: GlobalStyles.colors.eWhite.base,
  },
});

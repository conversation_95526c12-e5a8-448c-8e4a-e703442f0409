import {
  StyleSheet,
  View,
  Text,
  Dimensions,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { ScrollView } from "react-native-gesture-handler";
import { GlobalStyles } from "../../app/global-styles";
import Icon from "../../icon";
import FontAwesome5Icon from "react-native-vector-icons/FontAwesome";
import { useDispatch, useSelector } from "react-redux";
import {
  setActivities,
  setCurrentActivity,
} from "../../../redux/slices/activitySlices";
import { Switch, TouchableRipple, Card } from "react-native-paper";
import React, { useEffect, useState, useContext } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  DATE_FORMATS,
  ROUTES,
  WORK_ACTIVITY_STATUS,
} from "../../common/constants";
import _ from "lodash";
import moment from "moment/moment";
import { useIsFocused } from "@react-navigation/native";
import { useNavigation } from "@react-navigation/native";
import { servicePath } from "../../../redux/slices/servicePath";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import { consumerIndexContext } from "../e_consumer-index";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import Button from "../../common/_button";
import { CIService } from "../model/consumer-index-service";
import { useTranslation } from "react-i18next";
import { config } from "../../../environment";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { stackContext } from "../../app/get_stack";

export default function ReviewScreen() {
  const { workModelType } = React.useContext(stackContext);
  const {
    setCIList,
    singleCI,
    setSingleCI,
    tempCIData,
    setTempCIData,
    setVisible,
    setConfirmation,
    newCIData,
    setNewCIData,
  } = useContext(consumerIndexContext);
  let setAllActivities,
    setSingleWorkOrder,
    workOrder,
    confirmationModal,
    setConfirmationModal,
    setOTPModal,
    OTPModal,
    confirmNote,
    setconfirmNote,
    updateCI,
    setupdateCI,
    createCI;

  if (workModelType === "WA") {
    ({
      setAllActivities,
      setSingleWorkOrder,
      workOrder,
      confirmationModal,
      setConfirmationModal,
      setOTPModal,
      OTPModal,
      confirmNote,
      setconfirmNote,
      updateCI,
      setupdateCI,
      createCI,
    } = useContext(drawerContext));
  } else {
    ({
      setAllActivities,
      setSingleWorkOrder,
      workOrder,
      confirmationModal,
      setConfirmationModal,
      setOTPModal,
      OTPModal,
      confirmNote,
      setconfirmNote,
      updateCI,
      setupdateCI,
      createCI,
    } = useContext(drawerContextWO));
  }

  const dispatch = useDispatch();
  const { t } = useTranslation();
  const [isSwitchOn, setIsSwitchOn] = React.useState(true);
  const [loader, setLoader] = React.useState(false);
  const navigation = useNavigation();
  const allActivities =
    useSelector(state => state.activity.allActivities) || [];

  useEffect(() => {}, [tempCIData, newCIData]);

  const [disableCancle, setDisableCancle] = useState(false);
  const [disableSubmit, setDisableSubmit] = useState(false);
  const [isLoading, setisLoading] = useState(false);

  const handleBack = () => {
    //navigation.goBack();
    setCIList(false);
    setSingleCI(true);
    //setConfirmationModal(false);
    //setOTPModal(false);
  };
  const cancleHandler = () => {
    setVisible(true);
    setConfirmation(true);
  };
  const submitClick = e => {
    setLoader(true);
    if (!createCI) {
      CIService.updateCIList(newCIData)
        .then(response => {
          if (response.Inserted) {
            setLoader(false);
            setConfirmationModal(true);
            setconfirmNote(t("CONSUMER_INDEXING_SAVE_MSG"));
            setupdateCI(true);
          }
        })
        .catch(error => {
          console.log(error);
        });
    } else {
      let tempData = {
        ProjectId: "1",
        FullAddress: "",
        ServiceConnectionNumber: 0,
      };
      tempData = { ...tempData, ...newCIData };
      // Check if FullAddress is present in newDataCI and is not an empty string
      if (newCIData.FullAddress !== undefined && newCIData.FullAddress !== "") {
        tempData.FullAddress = newCIData.FullAddress;
      }
      if (
        newCIData.ServiceConnectionNumber !== undefined &&
        newCIData.ServiceConnectionNumber !== ""
      ) {
        tempData.ServiceConnectionNumber = newCIData.ServiceConnectionNumber;
      }

      // Now tempData will be updated with FullAddress if it was present in newDataCI

      for (const key in newCIData) {
        if (newCIData.hasOwnProperty(key) && tempData.hasOwnProperty(key)) {
          tempData[key] = newCIData[key];
        }
      }

      CIService.createCI(tempData)
        .then(response => {
          if (response.Inserted) {
            setLoader(false);
            setConfirmationModal(true);
            setconfirmNote(t("CONSUMER_INDEXING_SAVE_MSG"));
            setupdateCI(true);
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
  };
  return (
    <>
      <View style={styles.containerView}>
        <Card style={styles.card}>
          <TouchableOpacity
            style={[styles.backButtonWrapper, styles.paddingRight]}
            onPress={handleBack}>
            <FontAwesome
              name="chevron-left"
              color={GlobalStyles.colors.eWhite.base}
              style={{ fontFamily: "NotoSans-Thin", marginTop: 2 }}
              size={18}
            />
            <Text
              style={{
                color: GlobalStyles.colors.eWhite.base,
                fontSize: 12,
                fontWeight: "700",
                fontFamily: "NotoSans-Bold",
              }}>
              {t("SUMMARY")}
            </Text>
          </TouchableOpacity>
        </Card>
        <View style={styles.container}>
          <View>
            <ScrollView
              persistentScrollbar={true}
              contentContainerStyle={{ flexGrow: 1 }}
              style={{ height: 500 }}>
              <View style={styles.headerWrapper}>
                <Text style={styles.sectionHeaderText}>
                  {t("CONSUMER_INFORMATION")}
                </Text>
              </View>
              <View style={styles.lineStyleBottom}></View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("ORGANIZATION")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.OrgHierarchyName}
                  </Text>
                </View>
              </View>
              {!createCI ? (
                <View
                  style={{
                    flexDirection: "row",
                    paddingLeft: 10,
                    paddingVertical: 3,
                  }}>
                  <View style={{ flex: 1 }}>
                    <Text
                      style={[
                        styles.subtext,
                        { justifyContent: "flex-start" },
                      ]}>
                      {t("WORK_ORDER_NUMBER")}
                    </Text>
                  </View>
                  <View style={{ flex: 1 }}>
                    <Text
                      style={[
                        styles.subtextvalue,
                        { justifyContent: "flex-end" },
                      ]}>
                      {newCIData.WorkorderId}
                    </Text>
                  </View>
                </View>
              ) : (
                <></>
              )}

              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("CONSUMER_NUMBER")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.ConsumerNumber}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("CONSUMER_NAME")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.ConsumerName}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("EMAIL_ID")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.EmailId}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("FULL_ADDRESS")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.FullAddress}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("CONSUMER_STATUS")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.ConsumerStatus}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("SERVICE_CONNECTION_NUMBER")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.ServiceConnectionNumber}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("MOBILE_NUMBER")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.Mobile}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("SANCTION_LOAD")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.SanctionLoad}
                  </Text>
                </View>
              </View>
              <View style={styles.lineStyleBottom}></View>
              <View style={styles.headerWrapper}>
                <Text style={styles.sectionHeaderText}>
                  {t("UTILITY_DIVISION_DETAILS")}
                </Text>
              </View>
              <View style={styles.lineStyleBottom}></View>

              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("DISCOM_UTILITY_CODE")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.DivisionCode}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("ZONE_NAME")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.ZoneName}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("DIVISION_CODE")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.DivisionCode}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("SUB_DIVISION_NAME")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.SubDivisionName}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("DISCOM_UTILITY_NAME")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.DiscomUtilityName}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("WARD_CODE")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.WardCode}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("DIVISION_NAME")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.DivisionName}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("ZONE_CODE")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.ZoneCode}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("WARD_NAME")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.WardName}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("SUB_DIVISION_CODE")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.SubDivisionCode}
                  </Text>
                </View>
              </View>
              <View style={styles.lineStyleBottom}></View>

              <View style={styles.headerWrapper}>
                <Text style={styles.sectionHeaderText}>
                  {t("NETWORK_DETAILS")}
                </Text>
              </View>
              <View style={styles.lineStyleBottom}></View>

              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("FEEDER_CODE")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.FeederCode}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("DTR_NAME")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.DTRName}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("POLE_NO")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.poleNo}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("FEEDER_NAME")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.FeederName}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("DT_RATING")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.DTRating}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("No_METER_ON_POLE")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.NoOfMetersOnPole}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("DTR_CODE")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.DTRCode}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("DT_CT_RATIO")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.CTRatioOfDT}
                  </Text>
                </View>
              </View>
              <View style={styles.lineStyleBottom}></View>
              <View style={styles.headerWrapper}>
                <Text style={styles.sectionHeaderText}>
                  {t("EXISTING_METER_INFORMATION")}
                </Text>
              </View>
              <View style={styles.lineStyleBottom}></View>

              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("METER_NUMBER")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.ExistingMeterNo}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("METER_TYPE")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.ExistingMeterType}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("METER_READING_KWH")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.MeterReadingKWH}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {" "}
                    {t("METER_MF")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.MeterMF}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {" "}
                    {t("METER_READING_KVAH")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.MeterReadingKVAH}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("METER_MAKE")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.MeterMake}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("METER_BOX_STATUS")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.ExistingMeterBoxStatus}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {" "}
                    {t("METER_BOX_SEAL_STATUS")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.ExisitingMeterBoxSealStatus}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("METER_LOCATION")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.MeterLocation}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("ENERGY_METER_NAME_PLATE_DETAILS")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.EnerygyMeterNamePlateDetails}
                  </Text>
                </View>
              </View>

              <View style={styles.lineStyleBottom}></View>
              <View style={styles.headerWrapper}>
                <Text style={styles.sectionHeaderText}>
                  {t("OTHER_INFORMATION")}
                </Text>
              </View>
              <View style={styles.lineStyleBottom}></View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("REMARKS")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.Remarks}
                  </Text>
                </View>
              </View>
              <View
                style={{
                  flexDirection: "row",
                  paddingLeft: 10,
                  paddingVertical: 3,
                }}>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[styles.subtext, { justifyContent: "flex-start" }]}>
                    {t("LAT_LONG")}
                  </Text>
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.subtextvalue,
                      { justifyContent: "flex-end" },
                    ]}>
                    {newCIData.LatitudeLongitude}
                  </Text>
                </View>
              </View>
            </ScrollView>
          </View>
        </View>

        <View style={styles.btnContainer}>
          <Button
            onPress={cancleHandler}
            buttonbgColor={[
              styles.cancelBg,
              disableCancle && styles.disabledCancleStyle,
            ]}
            textColor={[
              disableCancle ? styles.disableColor : styles.cancelText,
            ]}
            disabled={disableCancle}>
            {t("CANCEL")}
          </Button>
          <Button
            buttonbgColor={[
              styles.buttonBgColor,
              disableSubmit && styles.disabledStyle,
            ]}
            textColor={[disableSubmit ? styles.disableColor : styles.textColor]}
            onPress={submitClick}
            disabled={disableSubmit}>
            {t("REVIEW_SUBMIT")}
            {loader && (
              <ActivityIndicator
                align="center"
                size={13}
                color={GlobalStyles.colors.eWhite.base}
              />
            )}
          </Button>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  scrollViewContent: {
    flexGrow: 1,
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    //marginLeft: 10,
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.ePrimary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginHorizontal: 20,
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.ePrimary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginHorizontal: 20,
  },
  lineStyleBottom: {
    borderWidth: 0.5,
    borderColor: GlobalStyles.colors.eSeparationLine.base,
    marginVertical: 5,
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  cancelText: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  btnContainer: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 20,
    marginBottom: 0,
  },
  headerWrapper: {
    paddingBottom: 0,
    paddingLeft: 10,
  },
  sectionHeaderText: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eDark.selected,
  },
  subtext: {
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eDark.selected,
  },
  subtextvalue: {
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.ePrimary.base,
  },
  containerView: {
    // flex: 1,
  },
  plusIconContainer: {
    position: "absolute",
    //bottom: 50,
    right: 20,
    alignItems: "center",
  },
  circle: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: GlobalStyles.colors.ePrimary.base, // Customize the background color of the circle
    justifyContent: "center",
    alignItems: "center",
  },
  card: {
    borderTopEndRadius: 10,
    borderTopStartRadius: 10,
    borderBottomEndRadius: 0,
    borderBottomStartRadius: 0,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    padding: 15,
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    margin: 10,
  },
  backButtonWrapper: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 3,
  },
  wrapDirection: {
    flexDirection: "row",
    flexWrap: "wrap",
    alignContent: "space-between",
  },
  titleCard: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontWeight: "700",
    fontFamily: "NotoSans-Bold",
  },
  activityWrapper: {
    // paddingTop: 8,
    // paddingBottom: 8,
    // borderBottomColor: GlobalStyles.colors.eBackground.base,
    // borderBottomWidth: 1,
  },
  lineStyle: {
    borderWidth: 1,
    borderColor: GlobalStyles.colors.eBackground.base,
    marginTop: 10,
    marginBottom: 6,
    width: "150%",
  },
  activityItem: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 10,
    // marginTop: -5,
  },
  mainView: {
    marginVertical: 5,
  },
  activityItemInner: {
    display: "flex",
    flexDirection: "row",
    flex: 1,
    gap: 20,
  },
  arrowIconStyle: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  displayFlex: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  headerStyle: {
    fontSize: 15,
    fontFamily: "NotoSans-SemiBold",
  },
  headerStyleNumber: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eRich.base,
  },
  iconContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 10,
  },
  subHeaderRowMinWidth: {
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderPriority: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eRich.base,
    textTransform: "capitalize",
    marginTop: -10,
    textAlign: "right",
  },
  subHeaderStatus: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eRich.base,
    textTransform: "capitalize",
    marginTop: -15,
    textAlign: "right",
  },
  subHeaderRow: {
    display: "flex",
    flexDirection: "row",
    paddingVertical: 1,
    justifyContent: "space-between",
    paddingRight: 20,
    marginTop: -5,
    // textAlign: 'left'
  },
  subHeaderRowStatus: {
    display: "flex",
    flexDirection: "row",
    paddingVertical: 1,
    justifyContent: "space-between",
    paddingRight: 20,
  },
  container: {
    //flex: 1,
    backgroundColor: GlobalStyles.colors.ePage.base,
    marginHorizontal: 10,
    marginTop: -10,
    marginBottom: 10,
    borderWidth: 0.5,
    borderColor: GlobalStyles.colors.eOutline.base,
  },
  contentContainer: {
    flex: 1,
  },
  noDateWrapper: {
    flex: 1,
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
  },
  profilePicView: {
    height: 60,
    width: 60,
    borderRadius: 100,
    marginLeft: 0,
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    borderWidth: 10,
    borderColor: GlobalStyles.colors.eWhite.base,
  },
});

import React from "react";
import { StyleSheet, View, Dimensions, Image } from "react-native";
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "react-native-maps";
import MapViewDirections from "react-native-maps-directions";
import { GlobalStyles } from "../../app/global-styles";
import { Text } from "react-native-paper";
const { width, height } = Dimensions.get("window");

const ASPECT_RATIO = width / height;
const LATITUDE = 13.7967;
const LONGITUDE = 100.5553;
const LATITUDE_DELTA = 0.009222;
const LONGITUDE_DELTA = LATITUDE_DELTA * ASPECT_RATIO;

const GOOGLE_MAPS_APIKEY = "AIzaSyCEki8XOhlK7BkZXxyIBxgruZS5c9PMcwI"; // Add your Google Maps API Key here

const locations = [
  // {
  //   latitude: 13.7975,
  //   longitude: 100.556,
  //   title: "Work Order 373",
  //   type: "Created",
  //   markerName: "A",
  // },
  {
    latitude: 13.7995,
    longitude: 100.5562,
    title: "Work Order 373",
    type: "Overdue",
    markerName: "A",
  },

  {
    latitude: 13.8036,
    longitude: 100.5539,
    title: "Work Order 374",
    type: "Created",
    markerName: "B",
  },
  // {
  //   latitude: 13.7998,
  //   longitude: 100.5616,
  //   title: "Work Order WO000236",
  // },
  // {
  //   latitude: 13.7723,
  //   longitude: 100.5488,
  //   title: "Work Order WO000311",
  //   type: "Created",
  // },
];

const mapStyle = [
  {
    featureType: "all",
    elementType: "labels",
    stylers: [
      {
        visibility: "off",
      },
    ],
  },
  {
    featureType: "poi",
    elementType: "labels",
    stylers: [
      {
        visibility: "off",
      },
    ],
  },
  {
    featureType: "poi",
    elementType: "geometry",
    stylers: [
      {
        visibility: "off",
      },
    ],
  },
];
const MapWO = () => {
  const segmentColors = [
    "green",
    "darkslategray",
    "orangered",
    "blue",
    "skyblue",
  ];
  return (
    <View style={styles.container}>
      <MapView
        style={styles.map}
        customMapStyle={mapStyle}
        initialRegion={{
          latitude: LATITUDE,
          longitude: LONGITUDE,
          latitudeDelta: LATITUDE_DELTA,
          longitudeDelta: LONGITUDE_DELTA,
        }}>
        {locations.map((location, index) => (
          <Marker
            key={index}
            coordinate={{
              latitude: location.latitude,
              longitude: location.longitude,
            }}
            title={location.title}>
            <Image
              source={
                location.type === "Completed"
                  ? require("../../../../assets/dashboard-icons/map/completed.png")
                  : location.type === "Created"
                  ? require("../../../../assets/dashboard-icons/map/open.png")
                  : require("../../../../assets/dashboard-icons/map/overdue.png")
              }
              style={{ width: 18, height: 25 }}
            />
            <View style={styles.markerView}>
              <Text style={styles.markerText}>{location.markerName}</Text>
            </View>
            <Image
              source={require("../../../../assets/dashboard-icons/map/marker.png")}
              style={{
                width: 10,
                height: 10,
                position: "absolute",
                top: 5,
                left: 4,
              }}
            />
          </Marker>
        ))}

        {/* <Polyline
          coordinates={locations.map(loc => ({
            latitude: loc.latitude,
            longitude: loc.longitude,
          }))}
          strokeColor="blue"
          strokeWidth={5}
        /> */}

        {locations.length > 1 &&
          locations.slice(0, -1).map((location, index) => (
            <MapViewDirections
              key={index}
              origin={location}
              destination={locations[index + 1]}
              apikey={GOOGLE_MAPS_APIKEY}
              strokeWidth={4}
              strokeColor={segmentColors[index]}
              // lineDashPattern={[10, 10]}
              // optimizeWaypoints={true}
            />
          ))}
      </MapView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderBottomEndRadius: 15, // Add border radius
    borderBottomStartRadius: 15, // Add border radius
    overflow: "hidden",
    marginHorizontal: -15,
    marginBottom: -15,
  },
  map: {
    height: 350,
    borderRadius: 15,
  },
  title: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontWeight: "700",
  },
  zoomControl: {
    position: "absolute",
    top: 16,
    right: 16,
  },
  markerText: {
    fontWeight: "bold",
    textDecorationLine: "underline",
    fontSize: 20,
  },
  markerView: {
    padding: 10,
    borderRadius: 30,
    backgroundColor: "white",
    borderStyle: "solid",
  },
});

export default MapWO;

import AsyncStorage from "@react-native-async-storage/async-storage";
import axios from "axios";
import { config } from "../../../environment";
import moment from "moment";

async function getAccountDetails(account, username) {
  let tenantCode = config?.constants?.BASE_TENANT_CODE;

  let APIquery =
    `{getAccountDetails(input:{accountId:"` +
    account +
    `",tenantCode:"` +
    tenantCode +
    `"})}`;
  let headers = {
    username: username,
  };
  return new Promise((resolve, reject) => {
    axios({
      url: config.urls.AUTH_SERVICE,
      method: "post",
      data: {
        query: APIquery,
      },
      headers: headers,
    })
      .then(function (response) {
        resolve(response.data.data.getAccountDetails);
      })
      .catch(function (error) {
        // if(!error.response){
        //   history.push(routePaths.RETRY)
        // }
        // else if(error.response && error.response.status === 500 || error.response.status=== 404 ){
        //   history.push(routePaths.RETRY)
        // }
        reject(error);
      });
  });
}

async function prepayment(amount, accountId, saId, bearer, email) {
  let APIquery =
    `query{prePaymentDetailsRazorPay(input:{RequestInput:{accountId:"` +
    accountId +
    `",PaymentType:"RAZORPAY_PAYMENT",amount:` +
    amount * 100 +
    `}})}`;

  let headers = {
    accountId: accountId,
    saId: saId,
    tenantCode: config.constants.BASE_TENANT_CODE,
    username: email,
    accessToken: JSON.parse(bearer)?.acessToken,
  };
  return new Promise((resolve, reject) => {
    axios({
      url: config.urls.USAGE_SERVICE_BASE_URL,
      method: "post",
      data: {
        query: APIquery,
      },
      headers: headers,
    })
      .then(function (response) {
        resolve(response);
      })
      .catch(function (error) {
        reject(error);
      });
  });
}

async function postPaymentProcessing(
  orderData,
  razorPayData,
  accountData,
  prepaidEngineUser,
  email,
) {
  let accountInfo = JSON.stringify(accountData).replace(
    /"([^("")"]+)":/g,
    "$1:",
  );
  let paymentDateTime = moment().format();
  let headers = {
    username: email,
    tenantCode: config.constants.BASE_TENANT_CODE,
  };
  let APIquery =
    `query{postPaymentDetailsRazorPay(input:{
      RequestInput:{  
      prepaidEngineUser:` +
    prepaidEngineUser +
    `,    
      Accounts:` +
    accountInfo +
    `,  
      paymentDateTime:"` +
    paymentDateTime +
    `" ,   
      orderId:"` +
    orderData?.id +
    `",      
      razorpay_payment_id:"` +
    razorPayData?.razorpay_payment_id +
    `",      
      razorpay_signature:"` +
    razorPayData?.razorpay_signature +
    `",      
      PaymentType:"RAZORPAY_PAYMENT"      
          }      
      })}`;
  return new Promise((resolve, reject) => {
    axios({
      url: config.urls.USAGE_SERVICE_BASE_URL,
      method: "post",
      data: {
        query: APIquery,
      },
      headers: headers,
    })
      .then(function (response) {
        resolve(response);
      })
      .catch(function (error) {
        reject(error);
      });
  });
}

export const paymentService = {
  getAccountDetails,
  prepayment,
  postPaymentProcessing,
};

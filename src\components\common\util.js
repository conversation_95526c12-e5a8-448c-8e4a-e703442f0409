export const getInitialConsumerIndexingValues = () => {
  return {
    generalInfo: {
      organization: "",
      dt: "",
      text: "",
      poleNo: "",
      feederMeters: "",
      workOrderType: "",
      workOrderNumber: "",
    },
    consumerInfo: {
      consumerId: "",
      name: "",
      permanentAddress: "",
      mobileNo: "",
    },
    existingMeterInfo: {
      meterMake: "",
      existingMeterNo: "",
      existingMeterReading: "",
      existingMeterType: "",
      existingMeterSealStatus: "",
      poleType: "",
      jointAvailableInServiceCable: "",
    },
    gisInfo: {
      address: "MG Road Guahati",
      surveyDate: new Date(),
    },
    rfCommunication: {
      rfGprsCommunication: "",
      ciMiPersonName: "",
      ciMIPersonNo: "",
    },
  };
};

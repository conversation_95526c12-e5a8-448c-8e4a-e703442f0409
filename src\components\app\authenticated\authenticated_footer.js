import React, { useEffect, useState, useContext } from "react";
import { GlobalStyles } from "../global-styles";
import {
  Platform,
  Pressable,
  StyleSheet,
  Text,
  View,
  Image,
} from "react-native";
import { useNavigation } from "@react-navigation/native";
import { useDispatch } from "react-redux";
import { servicePath } from "../../../redux/slices/servicePath";
import { BackHandler } from "react-native";
import Icon from "../../icon";
import { drawerContext } from "./authenticated_layout";
import { useTranslation } from "react-i18next";
import { drawerContextWO } from "./authenticated_layout_wo";
import { config } from "../../../environment";

export default function AuthenticatedFooter() {
  console.log(useContext(drawerContext), "drawerContext------------33");
  const {
    submitLoader,
    setConfirmationModal,
    setOTPModal,
    setCIList,
    setSingleCI,
    setSingleWO,
    setWOList,
    selectedItem,
    setSelectedItem,
  } = useContext(drawerContext) || {};

  const navigation = useNavigation();
  const dispatch = useDispatch();
  const { t } = useTranslation();

  let footerMenu = [
    {
      icon: "FS-NA-Home-Main",
      unselectedicon: "FS-NA-Home-Main",
      name: t("HOME"),
      pathName: "Home",
      src: require("../../../../assets/dashboard-icons/fs-home-selected.png"),
    },
    {
      icon: "FS-NA-WorkActivities-main",
      unselectedicon: "FS-NA-WorkActivities-main",
      name: t("FIELD_ACTIVITES"),
      pathName: "WorkActivities",
      src: require("../../../../assets/dashboard-icons/fs-work-selected.png"),
    },
    {
      icon: "FS-Consumer-Indexing-icon",
      unselectedicon: "FS-Consumer-Indexing-icon",
      name: t("COMMUNICATION"),
      pathName: "ConsumerIndexingMain",
      src: require("../../../../assets/dashboard-icons/FW-Consumer-Indexing-icon.png"),
    },
    {
      icon: "FS-Assets-icon-default",
      unselectedicon: "FS-Assets-icon-default",
      name: t("ASSETS"),
      pathName: "Assets",
      src: require("../../../../assets/dashboard-icons/FS-Assets-icon-selected.png"),
    },
  ];

  //let [selectedItem, setSelectedItem] = useState("Home");

  const onPressItem = route => {
    if (route === "WorkActivities") {
      setWOList(true);
      setSingleWO(false);
      // setAllActivities(true);
      // setSingleWorkOrder(false);
      setConfirmationModal(false);
      setOTPModal(false);
    }
    if (route === "ConsumerIndexingMain") {
      setSingleCI(false);
      setCIList(true);
      // setConfirmationModal(false);
      // setOTPModal(false);
    }
    if (route === "Assets") {
      // setSingleCI(false);
      // setCIList(true);
      // setConfirmationModal(false);
      // setOTPModal(false);
    }
    setSelectedItem(route);
    dispatch(servicePath(route));
    navigation.navigate(route);
  };

  function handleBackButtonClick() {
    navigation.goBack();

    dispatch(servicePath(selectedItem));
    return true;
  }

  useEffect(() => {
    BackHandler.addEventListener("hardwareBackPress", handleBackButtonClick);
    return () => {
      BackHandler.removeEventListener(
        "hardwareBackPress",
        handleBackButtonClick,
      );
    };
  }, []);

  return (
    <View style={styles.container}>
      {footerMenu.map((item, k) => {
        return (
          <Pressable
            android_ripple={{ color: GlobalStyles.colors.ePrimary.hover }}
            style={styles.pressableItem}
            key={k}
            onPress={() => onPressItem(item.pathName)}
            disabled={submitLoader}>
            {selectedItem === item.pathName ? (
              <Image
                style={
                  selectedItem === "Home"
                    ? styles.clientLogoHome
                    : selectedItem === "ConsumerIndexingMain"
                    ? styles.clientLogoCI
                    : selectedItem === "Assets"
                    ? styles.clientLogoCI
                    : styles.clientLogoWork
                }
                source={item.src}
              />
            ) : (
              <Icon
                name={item.icon}
                size={selectedItem === "Home" ? 34 : 32}
                color={GlobalStyles.colors.ePrimary.base}
              />
            )}
            <Text
              style={[
                styles.itemText,
                selectedItem === item.pathName && styles.boldItem,
              ]}>
              {item.name}
            </Text>
          </Pressable>
        );
      })}
    </View>
  );
}

const styles = StyleSheet.create({
  containerSVG: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  container: {
    height: Platform.OS === "ios" ? 80 : 70,
    width: "100%",
    backgroundColor: GlobalStyles.colors.eBorderColor.base,
    alignItems: "center",
    justifyContent: "center",
    flexDirection: "row",
    borderTopColor: GlobalStyles.colors.ePastelColor2.hover,
    borderTopWidth: 1,
    paddingBottom: Platform.OS === "ios" ? 10 : 0,
  },
  boldItem: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.ePrimary.selected,
  },
  iconStyle: {
    margin: -7,
  },
  itemText: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.ePrimary.base,
  },
  pressableItem: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 20,
    height: "100%",
    paddingBottom: 18,
    borderLeftColor: GlobalStyles.colors.ePastelColor2.base,
    borderLeftWidth: 1,
  },
  badgeStyle: {
    position: "absolute",
    top: 10,
    left: 50,
  },
  clientLogoHome: {
    height: 28,
    width: 28,
  },
  clientLogoCI: {
    height: 34,
    width: 34,
  },
  clientLogoWork: {
    height: 26,
    width: 34,
  },
});

import * as React from "react";
import { DataTable, Divider, Text, TextInput } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import { ScrollView } from "react-native-gesture-handler";
import { View } from "react-native";
import { useTranslation } from "react-i18next";

const MaterialTableGrid = data => {
  const { t } = useTranslation();
  const [page, setPage] = React.useState(0);
  const [numberOfItemsPerPageList] = React.useState([2, 3, 4]);
  const [itemsPerPage, onItemsPerPageChange] = React.useState(
    numberOfItemsPerPageList[0],
  );

  const [quantity, setQuantity] = React.useState("");

  const [items, setItems] = React.useState([]);
  React.useEffect(() => {
    if (data) {
      setItems([]);
      let arr = [];
      data["data"].map(e => {
        if (e?.resourceClass === "STOREROOM") {
          let obj = {
            StockItemCode: e?.stockItem,
            Description: e?.resourceType,
            Quantity: `${e?.quantity} ${
              e?.uom?.split("WD-")[1] ? e?.uom?.split("WD-")[1] : "Hrs."
            }`,
            StoreRoom: e?.resourceName,
          };
          arr.push(obj);
        }
      });
      setItems(arr);
    }
  }, [data]);

  const from = page * itemsPerPage;
  const to = Math.min((page + 1) * itemsPerPage, items.length);

  React.useEffect(() => {
    setPage(0);
  }, [itemsPerPage]);

  return (
    <>
      {/* <View>
        <Text
          style={{
            paddingLeft: 16,
          }}>
          {t('MATERIAL')}
        </Text>
      </View> */}
      <DataTable
        style={{
          paddingBottom: 10,
          marginBottom: 10,
        }}>
        <Divider />
        <ScrollView
          horizontal
          persistentScrollbar
          contentContainerStyle={{ flexDirection: "column" }}>
          <DataTable.Header>
            <DataTable.Title style={{ width: 100 }}>
              <Text style={{ width: 100, textWrap: "wrap" }}>{t("CODE")}</Text>
            </DataTable.Title>
            <DataTable.Title style={{ width: 150 }}>
              <Text style={{ width: 100, textWrap: "wrap" }}>
                {t("DESCRIPTION")}
              </Text>
            </DataTable.Title>
            <DataTable.Title style={{ width: 80 }}>
              <Text style={{ width: 100, textWrap: "wrap" }}>
                {t("QUANTITY")}
              </Text>
            </DataTable.Title>
            <DataTable.Title style={{ width: 250 }}>
              <Text style={{ width: 100, textWrap: "wrap" }}>
                {t("STORE_ROOM")}
              </Text>
            </DataTable.Title>
            {/* <DataTable.Title style={{ width: 100 }}>Returned</DataTable.Title> */}
          </DataTable.Header>
          {/* key: 1,
      itemCode: "261685808499",
      stockItem: 47,
      UOM: "WD-Each",
       Issued: "12",
      itemDesciption: "Bolt, Thru, NE70",
      */}
          {items.map(item => (
            <DataTable.Row
              key={item.key}
              style={{ flex: 1, width: "100%", height: 50 }}>
              <DataTable.Cell style={{ width: 100 }}>
                {item.StockItemCode}
              </DataTable.Cell>
              {/* <DataTable.Cell style={{ width: 100 }}>
                {item.Issued}
              </DataTable.Cell> */}
              {/* <DataTable.Cell
                style={{
                  width: 100,
                }}>
                <TextInput
                  placeholderTextColor="gray"
                  mode="outlined"
                  value={quantity}
                  enablesReturnKeyAutomatically
                  onChangeText={quantity => setQuantity(quantity)}
                  placeholder="0"
                  outlineColor={GlobalStyles.colors.eDark.hover}
                  activeOutlineColor={GlobalStyles.colors.ePrimary.base}
                  persistentScrollbar={true}
                  style={{
                    height: 25,
                  }}
                />
              </DataTable.Cell> */}
              <DataTable.Cell style={{ width: 150 }}>
                {item.Description}
              </DataTable.Cell>
              <DataTable.Cell style={{ width: 80 }}>
                {item.Quantity}
              </DataTable.Cell>
              <DataTable.Cell style={{ width: 250 }}>
                {item.StoreRoom}
              </DataTable.Cell>
              {/* <DataTable.Cell
                style={{
                  width: 100,
                }}>
                <TextInput
                  placeholderTextColor="gray"
                  mode="outlined"
                  value={quantity}
                  enablesReturnKeyAutomatically
                  onChangeText={quantity => setQuantity(quantity)}
                  placeholder="0"
                  outlineColor={GlobalStyles.colors.eDark.hover}
                  activeOutlineColor={GlobalStyles.colors.ePrimary.base}
                  persistentScrollbar={true}
                  style={{
                    height: 25,
                  }}
                />
              </DataTable.Cell> */}
            </DataTable.Row>
          ))}
        </ScrollView>
      </DataTable>
    </>
  );
};

export default MaterialTableGrid;

import React, {
  useState,
  useEffect,
  useContext,
  useImperativeHandle,
} from "react";
import { StyleSheet, View, ActivityIndicator } from "react-native";
import { Card, Text, TextInput } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import { useSelector } from "react-redux";
import FlatButton from "../../common/_flat_button";
import { profileContext } from "../e_profile";
import { UpdateMobileDetails } from "../model/_profile-service";
import Icon from "../../icon";
import { isEmpty, compact } from "lodash";

const ProfileDetails = ({ setTotalValue }, ref) => {
  const [editMobile, setEditMobile] = useState(false);
  const [contactId, setContactId] = useState(null);
  const [loading, setLoading] = useState(false);

  const accountData = useSelector(state => state.meterDetails?.meterDetails);
  const countryDialingCode = useSelector(
    state => state?.parameter?.parameter?.ParameterLookup?.COUNTRY_DIALING_CODE,
  );
  const { showProfilePopup, setProfilePopup, mobileNumber, setMobileNumber } =
    useContext(profileContext);

  let accountDetails = useSelector(
    store => store?.accountDetails?.accountDetails,
  );
  const { accountSummary = {} } = accountDetails;
  const {
    personAcccountDetail: {},
    personDetailList = {},
  } = accountSummary || {};
  const personData = personDetailList["C1-Person"] || {};
  const { personContactDetail = [] } = personData;

  const name = personData?.personName?.entityName;

  const setMobilePhoneNumber = () => {
    let number = null;
    personContactDetail.map(item => {
      if (item.personContactType === "CELLPHONE") {
        number = item.contactDetailValue.replace(/\D/g, "");
        setContactId(item.contactDetail);
      }
    });
    setMobileNumber(number);
  };

  const getEmail = () => {
    if (isEmpty(personContactDetail)) return "";

    const emails = compact(
      personContactDetail.map(
        item =>
          item.personContactType === "PRIMARYEMAIL" && item.contactDetailValue,
      ),
    );
    if (!isEmpty(emails)) return emails[0];
    return "";
  };

  const onClickVerifyNumber = async () => {
    setLoading(true);
    try {
      const res = await UpdateMobileDetails.UpdateMobileService(
        mobileNumber,
        contactId,
        accountId,
      );
      if (res.data) {
        setProfilePopup(true);
        setLoading(false);
      }
    } catch (err) {
      console.log(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (showProfilePopup === false) {
      setEditMobile(false);
    }
  }, [showProfilePopup]);

  useEffect(() => {
    setMobilePhoneNumber();
  }, [personContactDetail]);

  const mobileChangeHandle = number => {
    let newNumber = number.replace(/\D/g, "");
    setMobileNumber(newNumber);
  };

  const email = personData?.emailAddress || getEmail();
  const mailingAddress =
    accountDetails?.accountSummary?.personAcccountDetail?.address ||
    compact(
      accountDetails?.accountSummary?.personAcccountDetail?.personAccountList.map(
        item => {
          if (item.accountId === accountData.accountId) {
            return item?.mailingAddress;
          }
        },
      ),
    )[0];
  const accountId = accountData?.accountId;

  //use imperative handle to provide the ref access
  // so that the mobile number input box should be closed
  // when the component loses focus
  useImperativeHandle(ref, () => {
    return {
      closeTextInput: () => {
        setEditMobile(false);
      },
      setMobilePhoneNumber,
    };
  });

  return (
    <View style={{ flex: 1 }}>
      <Card style={styles.card}>
        <Text style={styles.titleCard}>MANAGE PROFILE</Text>
        <View style={styles.accountHeaderContent1}>
          <View style={{ width: "35%" }}>
            <Text style={styles.singleHeader}>Name:</Text>
          </View>
          <View style={{ width: "65%" }}>
            <Text style={styles.headerAmount}>
              {name && name.replace(",", " ")}
            </Text>
          </View>
        </View>

        <View style={styles.accountHeaderContent}>
          <View style={{ width: "35%" }}>
            <Text style={styles.singleHeader}>Email ID:</Text>
          </View>
          <View style={{ width: "65%" }}>
            <Text style={styles.headerAmount}>{email}</Text>
          </View>
        </View>
        <View style={styles.accountHeaderContent}>
          <View style={{ width: "35%",marginTop:"2.5%" }}>
            <Text style={styles.mobileNumber}>Mobile:</Text>
          </View>
          <View style={{ width: "65%" }}>
            {editMobile ? (
              <View style={{ flexDirection: "row", width: "100%" }}>
                <View style={{ width: "60%" }}>
                  <TextInput
                    value={mobileNumber || ""}
                    placeholder={mobileNumber}
                    maxLength={10}
                    returnKeyType="done"
                    type="number"
                    keyboardType="numeric"
                    onChangeText={mobileChangeHandle}
                    style={styles.inputCls}
                    dense
                    activeUnderlineColor={GlobalStyles.colors.ePrimary.base}
                  />
                </View>
                {loading ? (
                  <View style={styles.loader}>
                    <ActivityIndicator
                      size="small"
                      color={GlobalStyles.colors.eWhite.base}
                    />
                  </View>
                ) : (
                  <View style={{ width: "40%" }}>
                    <FlatButton
                      textStyles={styles.verifyButtonStyle}
                      onPress={onClickVerifyNumber}>
                      Verify
                    </FlatButton>
                  </View>
                )}
              </View>
            ) : (
              <TextInput
                placeholder={mobileNumber}
                placeholderTextColor={GlobalStyles.colors.eWhite.base}
                type="number"
                keyboardType="numeric"
                value={
                  countryDialingCode
                    ? countryDialingCode + mobileNumber
                    : "+91" + mobileNumber
                }
                style={styles.inputClsEnable}
                theme={{ colors: { text: "white" } }}
                editable={false}
                textColor="white"
                underlineColor="transparent"
                dense
                right={
                  <TextInput.Icon
                    icon={() => (
                      <Icon
                        name="Edit-icon"
                        size={15}
                        color={GlobalStyles.colors.eWhite.base}
                      />
                    )}
                    onPress={() => setEditMobile(true)}
                    color={GlobalStyles.colors.eWhite.base}
                    size={15}
                  />
                }
              />
            )}
          </View>
        </View>
        <View style={styles.accountHeaderMail}>
          <View style={{ width: "35%" }}>
            <Text style={styles.singleHeaderMail}>Mailing Address:</Text>
          </View>
          <View style={{ width: "65%" }}>
            <Text style={styles.headerAmountMail}>
              {mailingAddress.replaceAll(",", ", ")}
            </Text>
          </View>
        </View>
      </Card>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 17,
    paddingHorizontal: 17,
    paddingVertical: 17,
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    textColor: GlobalStyles.colors.eWhite.base,
    borderWidth: 1,
    borderColor: GlobalStyles.colors.eWhite.base,
  },
  titleCard: {
    fontSize: 12,
    color: GlobalStyles.colors.eWhite.base,
    fontFamily: "NotoSans-Bold",
  },
  leftView: {
    float: "left",
    alignSelf: "stretch",
    width: "60%",
    marginTop: 5,
  },
  rightView: {
    float: "right",
    width: "40%",
    marginTop: 5,
  },
  accountHeaderContent1: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    justifyContent: "flex-start",
    width: "100%",
    textAlign: "left",
    textColor: GlobalStyles.colors.eWhite.base,
    // height: 40,
    marginTop: 5,
  },
  accountHeaderContent: {
    flexDirection: "row",
    width: "100%",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    justifyContent: "space-between",
    width: "100%",
    textColor: GlobalStyles.colors.eWhite.base,
    // height: 40,
    // marginTop: 5
  },
  accountHeaderMail: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    justifyContent: "flex-start",
    width: "100%",
    textAlign: "left",
    textColor: GlobalStyles.colors.eWhite.base,
    marginTop: 5,
  },
  content: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
  },
  lineStyle: {
    borderWidth: 0.5,
    borderColor: GlobalStyles.colors.eLight.base,
    marginTop: 5,
    marginBottom: 3,
    marginLeft: -6,
    width: "113%",
  },
  lineStyleBottom: {
    borderWidth: 0.5,
    borderColor: GlobalStyles.colors.eLight.base,
    marginLeft: -3,
    marginBottom: 2,
    marginRight: -3,
  },
  accountsList: {
    justifyContent: "flex-start",
    fontSize: 15,
    width: "100%",
    textAlign: "left",
    fontFamily: "NotoSans-Bold",
    wordBreak: "break-word",
  },
  totalAmount: {
    fontSize: 15,
    textAlign: "left",
    color: GlobalStyles.colors.ePrimary.base,
    fontFamily: "NotoSans-Bold",
  },
  total: {
    flexDirection: "row",
    fontSize: 15,
    textAlign: "right",
    color: GlobalStyles.colors.eSecondary.base,
    fontFamily: "NotoSans-Bold",
    marginRight: 20,
  },
  bigTextHeader: {
    color: GlobalStyles.colors.eWhite.base,
    textAlign: "left",
    fontSize: 15,
    justifyContent: "flex-start",
    // wordBreak: "break-all",
  },
  singleHeader: {
    color: GlobalStyles.colors.eWhite.base,
    textAlign: "left",
    fontSize: 12,
    marginTop: 5,
    justifyContent: "flex-start",
    fontFamily: "NotoSans-SemiBold",
  },
  singleHeaderMail: {
    color: GlobalStyles.colors.eWhite.base,
    textAlign: "left",
    fontSize: 12,
    marginTop: 0,
    marginBottom: 5,
    justifyContent: "flex-start",
    fontFamily: "NotoSans-SemiBold",
  },
  headerAmountMail: {
    justifyContent: "flex-end",
    flexWrap: "wrap",
    fontSize: 12,
    textAlign: "left",
    color: GlobalStyles.colors.eWhite.base,
  },
  headerAmount: {
    justifyContent: "flex-end",
    flexWrap: "wrap",
    fontSize: 12,
    textAlign: "left",
    color: GlobalStyles.colors.eWhite.base,
    paddingVertical: 5,
  },
  mobileNumberContainer: {
    display: "flex",
    justifyContent: "flex-start",
    flexDirection: "row",
    paddingVertical: 2,
    alignItems: "center",
  },
  mobileNumber: {
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eWhite.base,
    fontWeight: 600,
    width: 100,
  },
  headerAmount1: {
    fontSize: 12,
    color: GlobalStyles.colors.eWhite.base,
    width: 120,
    flexShrink: 1,
  },
  inputCls: {
    height: 30,
    marginLeft: 0,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    marginTop: -2,
    backgroundColor: GlobalStyles.colors.eBackground.base,
  },
  inputClsEnable: {
    width: 180,
    fontSize: 12,
    marginLeft: -12,
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    color: GlobalStyles.colors.eWhite.base,
  },
  verifyButtonStyle: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    textDecorationLine: "underline",
    paddingLeft: 10,
    fontFamily: "NotoSans-SemiBold",
  },
  loader: {
    fontSize: 15,
    paddingLeft: -5,
    marginTop: 5,
    marginLeft: 20,
  },
  imgCls: {
    marginLeft: 40,
    marginTop: 10,
  },
});

export default React.forwardRef(ProfileDetails);

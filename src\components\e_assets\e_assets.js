import React, { useState, useEffect, useContext, useRef } from "react";
import { StyleSheet, TouchableOpacity, View } from "react-native";
import {
  Card,
  Text,
  List,
  Searchbar,
  Provider as PaperProvider,
} from "react-native-paper";
import { scrollTo } from "react-native-reanimated";
import { drawerContext } from "../app/authenticated/authenticated_layout";
import { CIService } from "../e_consumer-indexing/model/consumer-index-service";
import { GlobalStyles } from "../app/global-styles";
import { ScrollView } from "react-native-gesture-handler";

import { useTranslation } from "react-i18next";
import axios from "axios";
import Icon from "../icon";
import moment from "moment";
import { AssetService } from "./model/asset.service";
import { Button } from "react-native";
import { config } from "../../environment";
import { drawerContextWO } from "../app/authenticated/authenticated_layout_wo";
import { stackContext } from "../app/get_stack";

export const consumerIndexContext = React.createContext();
export default function AssetsPage({ route }) {
  const { workModelType } = React.useContext(stackContext);
  let CIList,
    setCIList,
    singleCI,
    setSingleCI,
    allCIList,
    setAllCIList,
    confirmationModal,
    reviewClose,
    setReviewClose,
    createCI,
    setCreateCI;

  if (workModelType === "WA") {
    ({
      CIList,
      setCIList,
      singleCI,
      setSingleCI,
      allCIList,
      setAllCIList,
      confirmationModal,
      reviewClose,
      setReviewClose,
      createCI,
      setCreateCI,
    } = useContext(drawerContext));
  } else {
    ({
      CIList,
      setCIList,
      singleCI,
      setSingleCI,
      allCIList,
      setAllCIList,
      confirmationModal,
      reviewClose,
      setReviewClose,
      createCI,
      setCreateCI,
    } = useContext(drawerContextWO));
  }

  let listViewRef;
  const scrollViewRef = useRef(null);
  const [assets, setAssets] = useState([]);
  const [expandedSections, setExpandedSections] = useState([]);
  const [index, setIndex] = useState();
  const [expanded, setExpanded] = useState({});
  const [height, setHeight] = useState(0);
  const [searchQuery, setSearchQuery] = React.useState("");
  const [searchResult, setSearchResult] = useState([]);
  const [dataToShow, setDataToShow] = useState([]);

  const handleLayout = event => {
    const { height } = event.nativeEvent.layout;
    console.log(height, "JHUHJIUHJKIUHGBNL");
    setHeight(height);
  };

  const scrollToRecord = index => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({ y: index * 70, animated: true });
    }
  };

  // Toggle the expanded state for each item
  const handlePress = itemIndex => {
    setExpanded(prevState => ({
      ...prevState,
      [itemIndex]: !prevState[itemIndex],
    }));
  };

  console.log(expanded, "JHGHJIUYGHJJHGHJK");
  const { t } = useTranslation();

  const fetchAssetDetails = () => {
    AssetService.getAssetLists()
      .then(resp => {
        console.log(resp, "YUIUYTRERTYUIOIUYTRE");
        setAssets(resp.assetsDetails);
      })
      .catch(err => {
        console.log(err, "YUIUYTRERTYUIOIUYTRE");
      });
  };
  const scrollViewRefs = useRef(null);
  const scrollToFirstItem = () => {
    if (scrollViewRefs.current) {
      scrollViewRefs.current.scrollTo({ x: 0, y: 1000, animated: true });
    }
  };

  useEffect(() => {
    const { assetId } = route?.params || {};
    if (assetId && assets) {
      let assetToShow = assets.findIndex(e => e?.assetId == assetId);
      console.log(expanded, assetToShow, "JHGHJIUYGHJJHGHJK");
      handlePress(assetToShow);
      scrollToRecord(assetToShow);
    }
  }, [assets, route.params]);
  useEffect(() => {
    fetchAssetDetails();
  }, []);

  const handleToggle = (ind, item) => {
    console.log(ind, item);
    setIndex(ind);
  };

  function filterData(query, data) {
    const lowerCaseQuery = query.toLowerCase();

    return data.filter(item => {
      return (
        (item.assetId && item.assetId.toLowerCase().includes(lowerCaseQuery)) ||
        (item.deviceType &&
          item.deviceType.toLowerCase().includes(lowerCaseQuery)) ||
        (item.address1 &&
          item.address1.toLowerCase().includes(lowerCaseQuery)) ||
        (item.address2 &&
          item.address2.toLowerCase().includes(lowerCaseQuery)) ||
        (item.address3 &&
          item.address3.toLowerCase().includes(lowerCaseQuery)) ||
        (item.address4 && item.address4.toLowerCase().includes(lowerCaseQuery))
      );
    });
  }

  const onChangeSearch = query => {
    setSearchQuery(query);
    let selectedData = filterData(query, assets);
    console.log(selectedData, "selectedData-------------------129");
    setSearchResult(selectedData);
  };

  useEffect(() => {
    if (searchQuery) {
      if (searchResult?.length > 0) {
        setDataToShow(searchResult);
      } else if (searchResult?.length == 0) {
        setDataToShow([]);
      }
    } else {
      setDataToShow(assets);
    }
  }, [searchQuery, searchResult, assets]);

  return (
    <View style={{ marginBottom: 80 }}>
      <Card style={styles.card}>
        <TouchableOpacity
          style={[styles.backButtonWrapper, styles.paddingRight]}>
          <Text
            style={{
              color: GlobalStyles.colors.eWhite.base,
              fontSize: 12,
              fontWeight: "700",
              fontFamily: "NotoSans-Bold",
            }}>
            {t("ASSETS")}
          </Text>
        </TouchableOpacity>
      </Card>
      <View style={{ marginLeft: 10, marginRight: 10, marginBottom: 10 }}>
        <Searchbar
          placeholder={t("SEARCH_ASSETS")}
          onChangeText={onChangeSearch}
          value={searchQuery}
          style={styles.searchbar}
        />
      </View>
      <ScrollView ref={scrollViewRef} style={styles.container}>
        <PaperProvider>
          <ScrollView>
            {dataToShow?.length > 0 &&
              dataToShow.map((item, ind) => {
                let jsonData = item?.jsonData;
                let parsedData;
                if (jsonData) {
                  parsedData = JSON.parse(jsonData);
                }
                console.log(parsedData, "POIUYHJOGVBJK");
                return (
                  <List.Accordion
                    key={ind}
                    title={
                      <View
                        key={ind}
                        ref={ref => {
                          listViewRef = ref;
                        }}
                        onLayout={handleLayout}>
                        <TouchableOpacity
                          onPress={() => handleToggle(ind, item)}>
                          <View>
                            <View>
                              <View>
                                <View>
                                  <View>
                                    <View style={styles.statusGrid}>
                                      <Text>
                                        {item?.assetId ? item?.assetId : ""}{" "}
                                        &nbsp; -&nbsp; &nbsp;
                                      </Text>
                                      <Text style={styles.statusStyling}>
                                        {item?.deviceStatus}
                                      </Text>
                                    </View>
                                  </View>
                                  <View style={styles.subHeaderRow}>
                                    <Text style={[styles.subHeaderRowMinWidth]}>
                                      {item?.deviceType ? item?.deviceType : ""}
                                    </Text>
                                  </View>
                                  <View
                                    style={styles.subHeaderRowStatus}></View>
                                </View>
                              </View>
                            </View>
                          </View>
                          <View style={styles.lineStyle} />
                        </TouchableOpacity>
                      </View>
                    }
                    style={styles.lineStyleCard}
                    expanded={expanded[ind]}
                    onPress={() => handlePress(ind)}>
                    <View style={styles.containerExpanded}>
                      <View>
                        <Text
                          style={{
                            marginTop: 10,
                            fontWeight: "bold",
                            fontSize: 15,
                          }}>
                          {t("ASSET_DETAILS")}
                        </Text>
                      </View>
                      {/* <View style={[styles.rowFlexContainer]}>
                      <View style={styles.labelWidth}>
                        <Text style={[styles.labelStyle]}>
                          {t("CREATED_ON")}
                        </Text>
                      </View>
                      <View style={styles.flexWrap}>
                        <Text style={[styles.valueStyle]}>
                          {moment(
                            item?.CreatedOn,
                            "YYYY-MM-DD HH:mm:ss",
                          ).format("DD-MM-YYYY h:mm A")}
                        </Text>
                      </View>
                    </View> */}
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("ASSET_ID")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.assetId}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("ASSET_TYPE")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.deviceType}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("SERIAL_NUMBER")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.serialNumber}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("MANUFACTURER")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.manufacturer}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>{t("MODEL")}</Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>{item?.model}</Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("SPECIFICATION")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.specification}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("CONFIGURATION")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.configuration}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("ASSET_STATUS")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.deviceStatus}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("SERVICE_POINT_ID")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>{item?.spId}</Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("CONTACT_ID")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.contactId}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("DESCRIPTION")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.contactId ? parsedData?.contactId : ""}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("ASSET_OWNERSHIP")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.assetOwnership
                              ? parsedData?.assetOwnership
                              : ""}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("GIS_ASSET_CLASS")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.gisAssetClass
                              ? parsedData?.gisAssetClass
                              : ""}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("ASSET_NUMBER")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.assetNumber
                              ? parsedData?.assetNumber
                              : ""}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("BADGE_NUMBER")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.badgeNumber
                              ? parsedData?.badgeNumber
                              : ""}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("PALLET_NUMBER")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.palletNumber
                              ? parsedData?.palletNumber
                              : ""}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("EXTERNAL_ID")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.externalId
                              ? parsedData?.externalId
                              : ""}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("SCADA_ID")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.scadaId ? parsedData?.scadaId : ""}
                          </Text>
                        </View>
                      </View>
                      <View>
                        <Text
                          style={{
                            marginTop: 10,
                            fontWeight: "bold",
                            fontSize: 15,
                          }}>
                          {t("ASSET_LOCATION")}
                        </Text>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("COUNTRY")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.country}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("POSTAL_CODE")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.postal}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("ADDRESS_ONE")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.address1}
                            {/* {`${item?.address1} ${
                            item?.address2 ? ", " + item?.address2 : ""
                          } ${item?.address3 ? ", " + item?.address3 : ""} ${
                            item?.address4 ? ", " + item?.address4 : ""
                          }`} */}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("ADDRESS_TWO")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.address2}
                            {/* {`${item?.address1} ${
                            item?.address2 ? ", " + item?.address2 : ""
                          } ${item?.address3 ? ", " + item?.address3 : ""} ${
                            item?.address4 ? ", " + item?.address4 : ""
                          }`} */}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("ADDRESS_THREE")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.address3}
                            {/* {`${item?.address1} ${
                            item?.address2 ? ", " + item?.address2 : ""
                          } ${item?.address3 ? ", " + item?.address3 : ""} ${
                            item?.address4 ? ", " + item?.address4 : ""
                          }`} */}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("ADDRESS_FOUR")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.address4}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("BUILDING")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.building}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>{t("ROOM")}</Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.room}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("POSITION")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.position}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("SERVICE_AREA")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.serviceArea}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("SERVICE_AREA_DESCRIPTION")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.serviceAreaDescription}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("LocationDetail")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.siteLocationDetail}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>{t("CITY")}</Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>{item?.city}</Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("NUMBER_ONE")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.number1}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("NUMBER_TWO")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.number2}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("HOUSE_TYPE")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.houseType}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("IN_CITY_LIMIT")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.inCityLimit}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>{t("STATE")}</Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>{item?.state}</Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("MOBILE_NUMBER")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.mobileNo}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>{t("EMAIL")}</Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>{item?.email}</Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("DISCONNECT_LOCATION")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.disconnectLocation}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("SITE_LOCATION")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.siteLocation}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("SITE_LOCATION_DETAIL")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.siteLocationDetail}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("INSTALL_EVENT_ID")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.installEventId}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("ASSET_CONFIGURATION_ID")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.deviceConfigurationId}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("INSTALL_DATETIME")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.installDateTime
                              ? moment(
                                  item?.installDateTime,
                                  "YYYY-MM-DD HH:mm:ss",
                                ).format("DD-MM-YYYY h:mm A")
                              : ""}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("REMOVAL_DATETIME")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.removalDateTime}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("INSTALLATION_STATUS")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {item?.installationStatus}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("LATITUDE")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.geocodeLatitude}
                          </Text>
                        </View>
                      </View>
                      <View style={[styles.rowFlexContainer]}>
                        <View style={styles.labelWidth}>
                          <Text style={[styles.labelStyle]}>
                            {t("LONGITUDE")}
                          </Text>
                        </View>
                        <View style={styles.flexWrap}>
                          <Text style={[styles.valueStyle]}>
                            {parsedData?.geocodeLongitude}
                          </Text>
                        </View>
                      </View>
                    </View>
                  </List.Accordion>
                );
              })}
            {dataToShow?.length == 0 && searchQuery?.length > 0 && (
              <Text
                style={{
                  textAlign: "center",
                  paddingTop: "50%",
                  paddingBottom: "100%",
                }}>
                {t("NO_ASSETS_FOUND")}
              </Text>
            )}
          </ScrollView>
        </PaperProvider>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  searchbar: {
    borderRadius: 10,
    borderWidth: 0.5,
    borderColor: GlobalStyles.colors.ePrimary.base, // Add a custom border
    backgroundColor: "white",
    elevation: 3, // Adds a shadow effect (Android)
    shadowColor: "#000", // Shadow color (iOS)
    shadowOpacity: 0.2, // Shadow opacity (iOS)
    shadowOffset: { width: 0, height: 1 }, // Shadow offset (iOS)
    shadowRadius: 1, // Shadow radius (iOS)
  },
  card: {
    borderTopEndRadius: 10,
    borderTopStartRadius: 10,
    borderBottomEndRadius: 0,
    borderBottomStartRadius: 0,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    padding: 15,
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    margin: 10,
  },
  cardItems: {
    borderTopEndRadius: 10,
    borderTopStartRadius: 10,
    borderBottomEndRadius: 0,
    borderBottomStartRadius: 0,
    padding: 15,
    borderColor: "white",
    backgroundColor: "transparent",
  },
  imgContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 0,
  },
  imgSideBySideView: {
    flex: 1,
    padding: 0,
  },
  imgSideBySideView2: {
    flex: 1,
    marginBottom: 10,
  },
  wrapDirection: {
    flexDirection: "row",
    flexWrap: "wrap",
    alignContent: "space-between",
  },
  bigTextHeader: {
    color: GlobalStyles.colors.eRich.base,
    textAlign: "left",
    fontSize: 14,
    justifyContent: "flex-start",
    // wordBreak: "break-all",
  },
  titleCard: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontWeight: "700",
    fontFamily: "NotoSans-Bold",
  },
  backButtonWrapper: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 3,
  },
  paddingRight: {
    paddingRight: 10,
  },
  scrollViewContent: {
    flexGrow: 1,
    padding: 20, // Adjust the padding as per your design
  },
  checkListDescription: {
    fontSize: 14,
    color: GlobalStyles.colors.ePrimary.base,
  },
  checkListDoneBtnStyle: {
    marginVertical: 10,
    marginTop: 20,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  checkListTextInput: {
    // minWidth: 150,
    flex: 1,
    height: 30,
    justifyContent: "center",
    marginRight: 10,
  },
  checkListIdLabel: {
    fontFamily: "NotoSans-SemiBold",
    fontSize: 13,
  },
  checkListStatus: {
    fontSize: 13,
    fontFamily: "NotoSans-SemiBold",
  },
  checkListItemContainer: {
    marginLeft: -25,
    marginVertical: -5,
  },
  camClass: {
    marginLeft: 25,
  },
  container: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: 10,
    borderRadius: 5,
  },
  containerExpanded: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: 10,
    borderRadius: 5,
    marginBottom: 10,
  },
  containerItem: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    borderRadius: 5,
    width: "100%",
  },
  containerEquip: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    // marginHorizontal: 10,
    // borderRadius: 5,
  },
  containerDiv: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: 10,
    marginTop: 0,
    borderRadius: 5,
  },
  containerChecklist: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: 10,
    // marginTop: 15,
    borderRadius: 5,
  },
  containerNoChecklist: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    alignItems: "center",
    marginVertical: 15,
    borderRadius: 5,
  },
  containerSubmit: {
    marginHorizontal: 10,
    marginBottom: 150,
    marginTop: 15,
  },
  containerStyle: {
    backgroundColor: "white",
    padding: 70,
    marginHorizontal: 20,
  },
  ePrimary: {
    color: GlobalStyles.colors.ePrimary.base,
  },
  flexRowCenter: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  flexWrap: {
    flex: 1,
  },
  workActivityDetailContainer: {
    paddingHorizontal: 8,
    paddingVertical: 10,
  },
  labelHeader: {
    fontFamily: "NotoSans-Bold",
    fontSize: 14,
    color: GlobalStyles.colors.ePrimary.base,
  },
  nolabelHeader: {
    fontFamily: "NotoSans-Bold",
    fontSize: 14,
    color: GlobalStyles.colors.ePrimary.base,
  },
  labelStyle: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eRich.base,
  },
  labelStyleError: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eDanger.dark,
  },
  inputContainer: {
    marginRight: 10,
    marginVertical: -20,
  },
  valueStyle: {
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  labelWidth: {
    flex: 1,
  },
  labelWidthMeter: {
    marginLeft: -25,
  },
  labelWidthDuration: {
    flex: 1,
  },
  labelWidthTime: {
    width: 150,
    marginHorizontal: 20,
    marginTop: 15,
    marginBottom: 10,
  },
  opacityDimmed: {
    opacity: 0.5,
  },
  otpTextInputStyle: {
    borderBottomWidth: 2,
    borderBottomColor: GlobalStyles.colors.ePrimary.base,
    color: GlobalStyles.colors.ePrimary.base,
  },
  overdueStyle: {
    color: GlobalStyles.colors.eTertiary.base,
  },
  pastDueStyle: {
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderWidth: 1,
    borderColor: "#d5d5d5",
    backgroundColor: "#fef9e8",
    display: "flex",
    flexDirection: "row",
    gap: 10,
    alignItems: "center",
  },

  rowFlexContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 5,
    paddingVertical: 10,
  },
  rowFlexContainerError: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  rowFlexContainerDate: {
    marginHorizontal: 20,
    marginTop: 10,
  },
  snackbarWrapper: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
  textInputWrapper: {
    paddingVertical: 3,
    flex: 1,
    // width: Dimensions.get("screen").width - 100,
  },
  workOrderCompletionTextStyle: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 13,
  },
  activityItem: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    // paddingHorizontal: 10,
    // marginTop: -5,
  },
  activityItemInner: {
    display: "flex",
    flexDirection: "row",
    flex: 1,
  },
  iconContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 10,
  },
  contentContainer: {
    flex: 1,
  },
  displayFlex: {
    display: "flex",
    flexDirection: "row",
    backgroundColor: "pink",
  },
  headerStyleNumber: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eRich.base,
  },
  statusStyling: {
    paddingLeft: 5,
    paddingRight: 5,
    fontWeight: "bold",
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    color: GlobalStyles.colors.eWhite.base,
    borderRadius: 10,
  },
  statusGrid: {
    flexDirection: "row",
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderRow: {
    display: "flex",
    flexDirection: "row",
    paddingVertical: 1,
    justifyContent: "space-between",
    paddingRight: 20,
    marginTop: -5,
    paddingTop: 10,
    // textAlign: 'left'
  },
  subHeaderRowMinWidth: {
    fontSize: 15,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderPriority: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eRich.base,
    textTransform: "capitalize",
    marginTop: -5,
    textAlign: "right",
  },
  subHeaderRowStatus: {
    display: "flex",
    flexDirection: "row",
    paddingVertical: 1,
    justifyContent: "space-between",
    paddingRight: 20,
    marginBottom: 10,
  },
  subHeaderRowMinWidth: {
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderRowMinWidth: {
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderStatus: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eRich.base,
    textTransform: "capitalize",
    marginTop: -15,
    textAlign: "right",
  },
  arrowIconStyle: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  arrowStatusText: {
    flex: 0,
    alignItems: "center", // Centers items horizontally
    justifyContent: "center",
  },
  lineStyle: {
    // borderWidth: 0.3,
    borderColor: GlobalStyles.colors.eMedium.hover,
    marginTop: 1,
    width: "90%",
  },
  lineStyleCard: {
    borderBottomWidth: 1,
    borderColor: GlobalStyles.colors.eMedium.hover,
  },
  lineStyleInfo: {
    borderWidth: 0.3,
    borderColor: GlobalStyles.colors.eSeparationLine.base,
    marginTop: 0,
    width: "100%",
  },
  lineStyleInfoCheckList: {
    borderWidth: 0.3,
    borderColor: GlobalStyles.colors.eSeparationLine.base,
    marginBottom: 5,
    width: "100%",
  },
  btnContainer: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 0,
    marginBottom: 15,
  },
  disabledCancleStyle: {
    // opacity: 0.5,
    backgroundColor: GlobalStyles.colors.ePage.base,
    borderColor: GlobalStyles.colors.eLight.selected,
    borderWidth: 1,
  },
  disableColor: {
    color: GlobalStyles.colors.eLight.selected,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  cancelText: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  onHoldText: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  bgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    marginLeft: 10,
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginLeft: 10,
  },
  cancelBgonHold: {
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    color: GlobalStyles.colors.eWhite.base,
    borderColor: GlobalStyles.colors.ePrimary.base,
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.ePrimary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 10,
  },
  disabledStyle: {
    opacity: 0.5,
    backgroundColor: GlobalStyles.colors.eBackground.selected,
  },
});

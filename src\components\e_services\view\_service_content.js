import Complaints from "../complaints/e_complaints";
import ReportOutage from "../reportOutage/report";
import TurnONOFF from "../turnOnOff/_turn-on_off";
import RelocateMain from "../relocate/relocate_main";
import { useSelector } from "react-redux";

export default function ServiceContent({ requiredHeight, requiredCardHeight }) {
  const pathName = useSelector(state => state?.servicePath?.servicePath);

  return (
    <>
      {pathName === "Complaint_account" ? (
        <Complaints
          name="My Account"
          requiredHeight={requiredHeight}
          requiredCardHeight={requiredCardHeight}
        />
      ) : pathName === "Complaint_General" ? (
        <Complaints
          name="General"
          requiredHeight={requiredHeight}
          requiredCardHeight={requiredCardHeight}
        />
      ) : pathName === "Complaint_billing" ? (
        <Complaints
          name="Billing"
          requiredHeight={requiredHeight}
          requiredCardHeight={requiredCardHeight}
        />
      ) : pathName === "PaymentsComplaint" ? (
        <Complaints
          name="Payments"
          requiredHeight={requiredHeight}
          requiredCardHeight={requiredCardHeight}
        />
      ) : pathName === "Relocate" ? (
        <RelocateMain
          requiredHeight={requiredHeight}
          requiredCardHeight={requiredCardHeight}
        />
      ) : pathName === "TurnOnOff" ? (
        <TurnONOFF
          requiredHeight={requiredHeight}
          requiredCardHeight={requiredCardHeight}
        />
      ) : (
        <ReportOutage
          requiredHeight={requiredHeight}
          requiredCardHeight={requiredCardHeight}
        />
      )}
    </>
  );
}

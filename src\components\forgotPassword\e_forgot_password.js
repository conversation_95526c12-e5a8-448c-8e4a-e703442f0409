import {
  ScrollView,
  StyleSheet,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import Input from "../common/_input";
import { GlobalStyles } from "../app/global-styles";
import React, { useContext, useEffect, useState } from "react";
import { registerContext } from "../e_authPages/e_auth_pages";
import Button from "../common/_button";
import FlatButton from "../common/_flat_button";
import { ActivityIndicator, Text } from "react-native-paper";
import { loginService } from "../e_login/model/_login_service";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { config } from "../../environment";
import { registerContextWO } from "../e_authPages/e_auth_pages_wo";
import { stackContext } from "../app/get_stack";

export default function ForgotPassword() {
  const { workModelType } = React.useContext(stackContext);
  const context = workModelType === "WA" ? registerContext : registerContextWO;

  const { setIsLoginPage } = useContext(context);

  const { t } = useTranslation();
  const [enteredEmail, setEnteredEmail] = useState("");
  const [emailRequire, setEmailRequire] = useState(false);
  const [emailError, setEmailError] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [disable, setdisable] = useState(false);
  const [loginError, setLoginError] = useState("");
  const [loginErrorMessage, setLoginErrorMessage] = useState("");
  const [lockDuration, setLockDuration] = useState(null);
  const lockDurationDefault = useSelector(
    state => state?.parameter?.parameter?.ParameterLookup?.LOCK_DURATION,
  );

  useEffect(() => {
    setLockDuration(lockDurationDefault);
  }, [lockDurationDefault]);

  const emailBlur = () => {
    if (enteredEmail) {
      setEmailRequire(false);
      setEmailError(false);
      var emailRegExp = new RegExp(
        "^([a-z0-9\\+_\\-]+)(\\.[a-z0-9\\+_\\-]+)*@([a-z0-9\\-]+\\.)+[a-z]{2,6}$",
      );
      var emailTest = emailRegExp.test(enteredEmail);

      if (emailTest) {
        setEmailError(false);
      } else {
        setEmailError(true);
      }
    } else {
      setEmailRequire(true);
      setEmailError(false);
    }
  };
  const getEmailErrorMsg = () => {
    if (emailRequire) {
      return t("EMAIL_REQ_MSG");
    } else if (emailError) {
      return t("EMAIL_VALIDATION_MSG");
    } else return;
  };

  const cancelClick = () => {
    setIsLoginPage("Login");
  };

  const loginSubmit = () => {
    emailBlur();
    if (!emailError && !emailRequire && enteredEmail) {
      setLoading(true);
      loginService
        .reset(enteredEmail)
        .then(res => {
          setLoading(false);
          AsyncStorage.setItem("email", enteredEmail);
          setIsLoginPage("FORGOT_ValidateOtp");
        })
        .catch(error => {
          console.log("err-forot", error);
          setLoading(false);

          if (error.response && error.response.data.statusCode === 403) {
            setLoginError(true);
            setLoading(false);
            setdisable(false);
            setLoginErrorMessage(error?.response?.data?.message);
            let text = t("ACCOUNT_BLOCKED_MSG");
            if (error.response.data.message === "User Blocked") {
              setLockDuration(lockDurationDefault);
              setLoginErrorMessage(text.replace("***", lockDurationDefault));
            } else {
              let min = error.response.data.message;
              if (isNaN(min)) {
                setLockDuration(lockDurationDefault);
                setLoginErrorMessage(text.replace("***", lockDurationDefault));
              } else {
                setLockDuration(min);
                setLoginErrorMessage(text.replace("***", min));
              }
            }
          } else if (error.response) {
            if (error.response.data.statusCode === 500) {
              setLoginError(true);
              setLoginErrorMessage(t("SYSTEM_DOWN_MSG"));
              setLoading(false);
              setdisable(false);
            } else {
              setLoginError(true);

              setLoginErrorMessage(
                error?.response?.data?.message
                  ? error?.response?.data?.message
                  : t("SYSTEM_DOWN_MSG"),
              );
              setLoading(false);
              setdisable(false);
            }
          }
        });
    }
  };

  const updateInputValueHandler = (inputType, enteredValue) => {
    setLoginError(false);
    setLoginErrorMessage("");
    setEnteredEmail(enteredValue);
  };

  return (
    <TouchableWithoutFeedback>
      <>
        <ScrollView style={styles.scrollStyle}>
          <View style={styles.textStyle}>
            <Text variant="headlineMedium" style={styles.textColorOTP}>
              {t("SEND_OTP")}
            </Text>
          </View>
          <View style={styles.container}>
            <Input
              onUpdateValue={enteredVal =>
                updateInputValueHandler("email", enteredVal)
              }
              // value={enteredEmail}
              keyboardType="email-address"
              onBlur={emailBlur}
              isInvalid={emailRequire || emailError}
              errorMsg={getEmailErrorMsg}
              secure={false}
              placeholder={t("ENTER_EMAIL")}
              name="account-circle"
            />
          </View>
          {loginError ? (
            <ScrollView style={styles.errorContainer}>
              <Text style={styles.errorStyle}>{loginErrorMessage}</Text>
            </ScrollView>
          ) : (
            <View style={{ padding: "4%" }} />
          )}
        </ScrollView>
        <View style={styles.buttons}>
          <Button
            onPress={!isLoading && loginSubmit}
            buttonbgColor={styles.buttonbgColor}
            textColor={styles.textColor}
            disabled={disable}>
            {t("SEND_OTP")}{" "}
            {isLoading && (
              <ActivityIndicator
                align="center"
                size={13}
                color={GlobalStyles.colors.eWhite.base}
                style={{ paddingTop: 6 }}
              />
            )}
          </Button>
          <FlatButton onPress={cancelClick} textStyles={styles.flatButtonOtp}>
            {t("CANCEL")}
          </FlatButton>
        </View>
        <View style={styles.captionStyle}>
          <Text style={styles.textColorOTP} variant="bodyMedium">
            {t("OTP_MSG")}
          </Text>
        </View>
      </>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  textStyle: {
    alignItems: "center",
  },
  textColorOTP: {
    color: GlobalStyles.colors.eWhite.base,
    textAlign: "center",
  },
  container: {
    width: "100%",
    // paddingBottom: 10,
    paddingHorizontal: 12,
    marginTop: "4%",
    alignContent: "center",
    justifyContent: "center",
  },

  flatButtonOtp: {
    color: GlobalStyles.colors.eWhite.base,
    textDecorationLine: "underline",
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
    textAlign: "center",
    marginTop: "3%",
  },
  buttons: {
    paddingHorizontal: "10%",
  },
  buttonbgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    paddingVertical: "3%",
    paddingHorizontal: "3%",
    width: "100%",
    alignItems: "center",
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 16,
    fontFamily: "NotoSans-Medium",
  },
  errorContainer: {
    width: "92%",
    backgroundColor: GlobalStyles.colors.eDanger.light,
    maxHeight: 48,
    marginBottom: "2%",
    marginHorizontal: "4%",
    // overflow: "scroll",
  },
  errorStyle: {
    paddingVertical: 10,
    paddingHorizontal: "2%",
    fontSize: 12,
    color: GlobalStyles.colors.eDanger.dark,
  },
  scrollStyle: {
    height: "100%",
    paddingHorizontal: "2%",
    marginTop: "5%",
  },
  captionStyle: {
    marginTop: "5%",
    paddingHorizontal: "10%",
  },
});

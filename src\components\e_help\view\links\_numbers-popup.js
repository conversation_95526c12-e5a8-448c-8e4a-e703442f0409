import React, { useState, useEffect } from "react";
import { View, StyleSheet, ScrollView } from "react-native";
import { Modal, Text, Card, IconButton } from "react-native-paper";
import { GlobalStyles } from "../../../app/global-styles";
import FlatButton from "../../../common/_flat_button";
import { Linking } from "react-native";
import { useDispatch, useSelector } from "react-redux";

export default function NumbersPopup({ hideModel }) {
  const [description, setDescription] = useState();
  const [multipleNo, setMultipleNo] = useState([]);
  const [multipleLocation, setMultipleLocation] = useState([]);

  let phnNumber = useSelector(
    store =>
      store?.parameter?.parameter?.ParameterLookup?.SUPPORT_CALL_PHONE_NUMBER,
  );
  let phnLocation = useSelector(
    store =>
      store?.parameter?.parameter?.ParameterLookup?.SUPPORT_CALL_PHONE_LOCATION,
  );

  useEffect(() => {
    if (phnLocation && multipleLocation.length === 0) {
      setMultipleLocation(phnLocation.split(","));
    }

    const multiple = phnNumber?.includes(",");
    if (multiple === true && multipleNo.length === 0) {
      setMultipleNo(phnNumber.split(","));
    }
  }, [phnNumber, phnLocation]);
  return (
    <Card style={styles.container}>
      <View style={styles.headerContainer}>
        <IconButton
          icon="close"
          color={GlobalStyles.colors.eRich.base}
          onPress={hideModel}
          style={styles.closeIcon}
          size={20}
        />
      </View>
      <ScrollView nestedScrollEnabled={true}>
        {multipleNo &&
          multipleNo.map((item, k) => {
            return (
              <View key={k} style={styles.contentPadding}>
                <Text
                  style={styles.content}
                  onPress={() => Linking.openURL(`tel:${item}`)}>
                  {item}
                </Text>
                <Text
                  style={styles.content}
                  onPress={() => Linking.openURL(`tel:${item}`)}>
                  {multipleLocation[k] ? multipleLocation[k] : ""}
                </Text>
              </View>
            );
          })}
      </ScrollView>
    </Card>
  );
}

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    width: "90%",
    backgroundColor: GlobalStyles.colors.eFaint.base,
    top: 0,
    left: 15,
    height: 150,
  },
  contentPadding: {
    paddingBottom: 10,
  },
  headerContainer: {
    alignItems: "flex-end",
  },
  linkWhite: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 16,
    textDecorationLine: "underline",
    paddingVertical: "3%",
    paddingHorizontal: "3%",
    textAlign: "center",
  },
  content: {
    textAlign: "center",
    color: GlobalStyles.colors.ePrimary.base,
  },
});

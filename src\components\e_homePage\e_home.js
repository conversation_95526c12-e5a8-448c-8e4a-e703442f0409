import React, { useState, useEffect } from "react";
import BalanceSummary from "./view/_balanceSummary";
import ServiceRequest from "./view/_service_request";
import TitleCard from "./view/_titlecard";
import TaskCards from "./view/_taskcards";
import JobLocations from "./view/_job-locations";
import TodaysTasks from "./view/_todays-tasks";
import { StyleSheet, Text, View, ScrollView } from "react-native";
import { GlobalStyles } from "../app/global-styles";
import { useSelector } from "react-redux";
import { SERVICE_TYPE_IDENTIFIER } from "../account_switcher/constants";
import { DashboardService } from "./model/service";
import moment from "moment";
import { DashboardServiceWO } from "./model/service_wo";
import { config } from "../../environment";
import JobLocationsWO from "./view/_job-locations_wo";
import TaskCardsWO from "./view/_taskcards_wo";
import { stackContext } from "../app/get_stack";

export const homeContext = React.createContext();
export default function Home() {
  const { workModelType } = React.useContext(stackContext);
  const currentServiceTypeCd = useSelector(
    store => store?.meterDetails?.meterDetails?.getSaInfo?.serviceType,
  );
  const [todayList, setTodayList] = useState([]);
  const [taskCount, setTaskCount] = useState([]);
  const [mainData, setData] = useState([]);
  const [markers, setMarkers] = useState([]);
  const [markersCT, setMarkersCT] = useState([]);
  const [markersOT, setMarkersOT] = useState([]);
  const [markersODT, setMarkersODT] = useState([]);

  useEffect(() => {
    getTaskCount();
  }, []);
  useEffect(() => {
    getTodaysList();
  }, []);
  const getTaskCount = async () => {
    try {
      if (workModelType == "WA") {
        const res = await DashboardService.getTaskCount();
      } else {
        const res = await DashboardServiceWO.getTaskCount();
      }
      const mockRes = [
        {
          labels: "Created",
          name: "Created",
          count: 1,
          src: [
            {
              WorkOrderId: 1,
              WorkOrderCode: "W001",
              Title: "Consumer Indexing",
              WorkOrderType: "ConsumerIndexing",
              Priority: "H",
              Description: "",
              CrewId: 1,
              LatitudeLongitude: "17.38405, 78.45636",
              TeamName: "Smart Meter Team",
            },
          ],
        },
        {
          labels: "OverDue",
          name: "OverDue",
          count: 1,
          src: [
            {
              WorkOrderId: 6,
              WorkOrderCode: "W006",
              Title: "Consumer Indexing",
              WorkOrderType: "Consumer Indexing",
              Priority: "H",
              Description: "",
              CrewId: 1,
              LatitudeLongitude: "17.361431, 78.474533",
              TeamName: "Smart Meter Team",
            },
          ],
        },
        {
          labels: "Completed",
          name: "Completed",
          count: 0,
          src: [
            {
              WorkOrderId: 5,
              WorkOrderCode: "W005",
              Title: "Consumer Indexing",
              WorkOrderType: "Consumer Indexing",
              Priority: "H",
              Description: "",
              CrewId: 1,
              LatitudeLongitude: "17.382330, 78.401604",
              TeamName: "Smart Meter Team",
            },
          ],
        },
      ];
      setTaskCount(mockRes);
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
      // setLoading(false);
    }
  };

  const getMarkers = async res => {
    try {
      res.forEach(item => {});
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
    }
  };

  const getTodaysList = async () => {
    try {
      // setLoading(true);
      let res;
      if (workModelType == "WA") {
        res = await DashboardService.getTodayList();
      } else {
        res = await DashboardServiceWO.getTodayList();
      }
      res.workOrder?.sort((a, b) => {
        // Sort by WorkActivityId in descending order
        return b.WorkOrderId - a.WorkOrderId;
      });
      setTodayList(res.workActivities);
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
      // setLoading(false);
    }
  };
  useEffect(() => {}, [markers, todayList]);

  useEffect(() => {
    if (taskCount) {
      const newMarkers = [];
      taskCount.forEach((item, index) => {
        item.src.forEach((workOrder, subIndex) => {
          const { LatitudeLongitude } = workOrder;

          const coordinatesMatch = LatitudeLongitude.match(
            /([0-9]+\.[0-9]+), ([0-9]+\.[0-9]+)/,
          );

          if (coordinatesMatch) {
            const latitude = parseFloat(coordinatesMatch[1]);
            const longitude = parseFloat(coordinatesMatch[2]);

            let color = "";
            if (item.name === "Created") {
              color = "#F77D0F";
            } else if (item.name === "Completed") {
              color = "#12805C";
            } else if (item.name === "OverDue") {
              color = "#C9252D";
            }
            newMarkers.push({
              id: `${index + 1}`,
              title: workOrder.WorkOrderId
                ? workOrder.WorkOrderId?.toString()
                : "",
              coordinate: { latitude, longitude },
              color: color, // Replace with the color you want
              type: item.name,
            });
          }
        });
      });
      setData(newMarkers);
      setMarkers(todayList);
      //setMarkers(newMarkers);
    }
  }, [taskCount, todayList]);

  useEffect(() => {
    if (markers.length > 0) {
      const ctMarkers = mainData.filter(option => option.type === "Created");
      const odtMarkers = mainData.filter(option => option.type === "OverDue");
      const otMarkers = mainData.filter(option => option.type === "Completed");

      setMarkersCT(ctMarkers);
      setMarkersODT(odtMarkers);
      setMarkersOT(otMarkers);
    }
  }, [markers]);

  return (
    <homeContext.Provider
      value={{
        markers,
        setMarkers,
        mainData,
        setData,
        taskCount,
        markersCT,
        setMarkersCT,
        markersOT,
        setMarkersOT,
        markersODT,
        setMarkersODT,
        todayList,
      }}>
      <ScrollView style={styles.container}>
        <View style={[styles.spaceAroundCard, styles.paddingWeekly]}>
          <TitleCard />
          {workModelType == "WA" ? <TaskCards /> : <TaskCardsWO />}
        </View>
        <View style={[styles.spaceAroundCardMap, styles.paddingMap]}>
          {workModelType == "WA" ? <JobLocations /> : <JobLocationsWO />}
        </View>
        <View style={[styles.spaceAroundCardTask, styles.paddingMap]}>
          <TodaysTasks />
        </View>
      </ScrollView>
    </homeContext.Provider>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: GlobalStyles.colors.ePage.base,
  },
  spaceAroundCard: {
    width: "100%",
    marginTop: 15,
  },
  spaceAroundCardMap: {
    width: "100%",
    marginTop: 0,
  },
  spaceAroundCardTask: {
    width: "100%",
    marginTop: 10,
    marginBottom: 10,
    marginHorizontal: 0,
  },
  paddingWeekly: {
    marginTop: 12,
    paddingHorizontal: 15,
  },
  paddingSpace: {
    paddingHorizontal: 15,
  },
  paddingMap: {
    marginVertical: 0,
    paddingHorizontal: 15,
  },
});

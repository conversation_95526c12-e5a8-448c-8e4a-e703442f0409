import React, { useState, useEffect, useContext } from "react";
import { DataTable, Divider, Text, TextInput } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import { ScrollView } from "react-native-gesture-handler";
import { StyleSheet, View, Platform } from "react-native";
import { useTranslation } from "react-i18next";
import FontAwesomeEye from "react-native-vector-icons/Ionicons"; // this worked for ios, apk file.
import Button from "../../common/_button";
import { useDispatch } from "react-redux";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import { servicePath } from "../../../redux/slices/servicePath";
import { useNavigation } from "@react-navigation/native";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { config } from "../../../environment";
import { stackContext } from "../../app/get_stack";
const AssetsGrid = ({ assets, assetDataFrid }) => {
  const { workModelType } = React.useContext(stackContext);
  const { setSelectedItem } = useContext(
    workModelType === "WA" ? drawerContext : drawerContextWO,
  );

  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const [assetsList, setAssetsList] = useState([]);
  const [currData, setCurrData] = useState([]);

  const handleAssetView = assetId => {
    console.log(assetId, "YYYYYYYYYYYYYYYHNHHHHHHHHHHH");
    if (assetId) {
      const ROUTE = "Assets";
      setSelectedItem(ROUTE);
      dispatch(servicePath(ROUTE));
      navigation.navigate(ROUTE, { assetId: assetId });
    }
  };

  useEffect(() => {
    if (assets?.length > 0) {
      setAssetsList(assets);
    }
  }, [assets]);

  console.log(
    assets,
    assetsList,
    "assetLists--------------------------------------22",
  );

  return (
    <>
      <DataTable
        style={{
          paddingBottom: 10,
          marginBottom: 10,
        }}>
        <Divider />
        <ScrollView
          horizontal
          persistentScrollbar
          contentContainerStyle={{ flexDirection: "column" }}>
          <DataTable.Header>
            <DataTable.Title style={{ width: 100 }}>
              <Text style={{ width: 100, textWrap: "wrap" }}>
                {t("ASSET_ID")}
              </Text>
            </DataTable.Title>
            <DataTable.Title style={{ width: 100 }}>
              <Text style={{ width: 100, textWrap: "wrap" }}>
                {t("SERIAL_NUMBER")}
              </Text>
            </DataTable.Title>
            <DataTable.Title style={{ width: 150 }}>
              <Text style={{ width: 100, textWrap: "wrap" }}>
                {t("ASSET_LOCATION")}
              </Text>
            </DataTable.Title>
            <DataTable.Title style={{ width: 50 }}>
              <Text style={{ textWrap: "wrap" }}>{t("ACTIONS")}</Text>
            </DataTable.Title>
          </DataTable.Header>

          {assetsList?.length > 0 &&
            assetsList.map(item => (
              <DataTable.Row
                key={item.index}
                style={{
                  flex: 1,
                  height: 50,
                  marginBottom: 15,
                }}>
                {/* {item.activityType} */}
                <DataTable.Cell
                  style={{
                    maxwidth: 200,
                    width: 150,
                  }}>
                  <View style={{ alignItems: "center" }}>
                    <Text>{item?.assetId}</Text>
                  </View>
                </DataTable.Cell>
                <DataTable.Cell
                  style={{
                    maxwidth: 200,
                    width: 150,
                  }}>
                  <View style={{ alignItems: "center" }}>
                    <Text>{item?.serialNumber}</Text>
                  </View>
                </DataTable.Cell>
                <DataTable.Cell
                  style={{
                    maxwidth: 200,
                    width: 200,
                  }}>
                  <View style={{ alignItems: "center" }}>
                    <Text>{item?.siteLocationDetail}</Text>
                  </View>
                </DataTable.Cell>
                <DataTable.Cell
                  style={{
                    width: 100,
                  }}>
                  <View style={{ alignItems: "center" }}>
                    <FontAwesomeEye
                      name="eye"
                      color={GlobalStyles.colors.ePrimary.base}
                      size={22}
                      onPress={() => {
                        handleAssetView(item.assetId);
                      }}
                    />
                  </View>
                </DataTable.Cell>
              </DataTable.Row>
            ))}
        </ScrollView>
      </DataTable>
    </>
  );
};

export default AssetsGrid;

/**
 * @format
 */
// import '@formatjs/intl-relativetimeformat/polyfill';
// import '@formatjs/intl-relativetimeformat/locale-data/en';
import { AppRegistry } from "react-native";
import App from "./App";
import { name as appName } from "./app.json";

// if(__DEV__) {
//     import('./reactotronconfig').then(() => console.log('Reactotron Configured'))
//   }
  

AppRegistry.registerComponent(appName, () => App);

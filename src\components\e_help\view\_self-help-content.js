import { useEffect, useState, useContext } from "react";
import FAQ from "../view/faq/_faq";
import UserGuides from "../view/userguide/_userguide";
import Links from "../view/links/_links";
import Tips from "../view/tips/_tips";
import { useSelector } from "react-redux";

export default function SelfHelpContent({
  labels,
  FAQData,
  UserguideData,
  LinksData,
  CallUSDescription,
  ImageURL,
  LinkDescription
}) {
  const pathName = useSelector(state => state?.servicePath?.servicePath);

  return (
    <>
      {pathName === "FAQ" ? (
        <FAQ
          FAQData={FAQData}
          CallUSDescription={CallUSDescription}
        />
      ) : pathName === "UserGuides" ? (
        <UserGuides
          UserguideData={UserguideData}
          CallUSDescription={CallUSDescription}
        />
      ) : pathName === "Links" ? (
        <Links
          LinksData={LinksData}
          LinkDescription={LinkDescription}
          CallUSDescription={CallUSDescription}
          ImageURL={ImageURL}
        />
      ) : pathName === "Tips" ? (
        <Tips />
      ) : (
        <FAQ
          FAQData={FAQData}
          CallUSDescription={CallUSDescription}
        />
      )}
    </>
  );
}

import axios from "axios";
import { config } from "../../../environment";

export const ticketService = {
  getAllTicket,
  getUserServiceTickets,
  getUserServiceTicketSubmit,
};

async function getAllTicket(dateFrom, dateTo, accountId) {
  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.COMMUNICATION_SERVICE_BASE_URL,
        {
          query:
            `query {
            getTicketList(
              input: {
                action : "LIST",
        
                dateFrom: "` +
            dateFrom +
            `" ,
                dateTo: "` +
            dateTo +
            `" 
              }
            ) {
              caseId,
              caseStatus,             
              ticketType,
              ticketTypeDesc,
              lastUpdDttm,
              ticketCategory,
              createdDate,
              issueDesc,
              ticketAttachments
            }
          }`,
        },
        {
          headers: {
            accountId: accountId,
          },
        },
      )
      .then(function (response) {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
}

async function getUserServiceTickets(
  dateFrom,
  dateTo,
  ticketType,
  accountId,
  saId,
) {
  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.COMMUNICATION_SERVICE_BASE_URL,
        {
          query:
            `query {
              getUserServiceTickets(
              input: {
                dateFrom: "` +
            dateFrom +
            `" ,
                dateTo: "` +
            dateTo +
            `" ,
                ticketTypeCd: "` +
            ticketType +
            `"
              }) {
                  serviceTicketList {
                  ticketId,
                  ticketStatusCd,
                  lastUpdatedDate,
                  createdDate,
                  ticketTypeCd,
                  issueDescription,
                  ticketTypeDescr,
                  csrTicketRespList
                  userSubmittedValues
                }
              }
          }`,
        },
        {
          headers: {
            accountId: accountId,
            saId: saId,
          },
        },
      )
      .then(function (response) {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
}

async function getUserServiceTicketSubmit(
  dateFrom,
  dateTo,
  ticketType,
  accountId,
  saId,
) {
  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.COMMUNICATION_SERVICE_BASE_URL,
        {
          query:
            `query{
              createTurnOffServiceTicket(input: {
                  ticketTypeCd: "` +
            ticketType +
            `",
                  startDate:"` +
            dateFrom +
            `",
                  endDate:"` +
            dateTo +
            `",}){
                      ticketId
                  }
              }`,
        },
        {
          headers: {
            accountId: accountId,
            saId: saId,
          },
        },
      )
      .then(function (response) {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
}

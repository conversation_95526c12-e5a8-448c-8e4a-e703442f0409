fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## iOS

### ios test

```sh
[bundle exec] fastlane ios test
```

test

### ios connect_app_store

```sh
[bundle exec] fastlane ios connect_app_store
```

Connect to App store using app store connect API

### ios build_ipa

```sh
[bundle exec] fastlane ios build_ipa
```

build ipa for appstore

### ios commit_bump_and_push_to_git

```sh
[bundle exec] fastlane ios commit_bump_and_push_to_git
```



### ios beta

```sh
[bundle exec] fastlane ios beta
```

Push a new beta build to AppStore TestFlight

### ios sync_certificates

```sh
[bundle exec] fastlane ios sync_certificates
```

Sync certificates

### ios upload_ipa_to_testflight

```sh
[bundle exec] fastlane ios upload_ipa_to_testflight
```



### ios beta_demo

```sh
[bundle exec] fastlane ios beta_demo
```

create a demo enviroment build and upload it to test flight

### ios beta_dev

```sh
[bundle exec] fastlane ios beta_dev
```

create a dev env build and upload it to test flight

### ios beta_qa

```sh
[bundle exec] fastlane ios beta_qa
```

create a QA env build and upload it to test flight

### ios beta_ipa

```sh
[bundle exec] fastlane ios beta_ipa
```

create a QA env build and upload it to test flight

### ios fetch_version_number

```sh
[bundle exec] fastlane ios fetch_version_number
```



### ios update_version_number_appstore

```sh
[bundle exec] fastlane ios update_version_number_appstore
```

update version number

### ios beta_build

```sh
[bundle exec] fastlane ios beta_build
```



----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).

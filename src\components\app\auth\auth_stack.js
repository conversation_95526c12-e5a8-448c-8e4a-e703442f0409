import { createNativeStackNavigator } from "@react-navigation/native-stack";
import AuthPages from "../../e_authPages/e_auth_pages";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { getBearerToken } from "../../../services/bearer.service";
import React, { useEffect } from "react";
import getParametersWidgets from "../authenticated/model/parameter-service";
import { parameter } from "../../../redux/slices/parameterWidgets";
import { useDispatch } from "react-redux";
import { config } from "../../../environment";
import AuthPagesWO from "../../e_authPages/e_auth_pages_wo";
import { stackContext } from "../get_stack";

export default function AuthStack() {
  const { workModelType } = React.useContext(stackContext);
  const Stack = createNativeStackNavigator();
  const dispatch = useDispatch();

  const initAuthBearerToken = async () => {
    try {
      await getBearerToken();
      const parameterWidgets = await getParametersWidgets();
      dispatch(parameter(parameterWidgets));
    } catch (err) {
      console.log("Error initializing bearer token");
    }
  };
  console.log("local", AsyncStorage.getItem("authbearer"));
  useEffect(() => {
    initAuthBearerToken();
  }, []);

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}>
      {workModelType == "WA" ? (
        <Stack.Screen name="Login" component={AuthPages} />
      ) : (
        <Stack.Screen name="Login" component={AuthPagesWO} />
      )}
    </Stack.Navigator>
  );
}

import { createSlice } from "@reduxjs/toolkit";

const unreadNotificationReducer = createSlice({
    name: "unreadMeterInfo",
    initialState: {
        unreadMeterInfo:[],
    },
    reducers: {
        unreadMeterInfo: (state, action) => {
            state.unreadMeterInfo = action.payload
        },
    }
})

export const unreadMeterInfo = unreadNotificationReducer.actions.unreadMeterInfo;
export default unreadNotificationReducer.reducer;
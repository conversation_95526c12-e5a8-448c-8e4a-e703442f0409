import { useEffect, useState } from "react";
import { ActivityIndicator } from "react-native";
import { Dimensions, ScrollView, View, StyleSheet } from "react-native";
import { Card, RadioButton, Text } from "react-native-paper";
import { useDispatch, useSelector } from "react-redux";
import { billRouting } from "../../../../redux/slices/handleChangeBillRouting";
import { GlobalStyles } from "../../../app/global-styles";

export default function BillRouting() {
  const [checked, setChecked] = useState("first");
  const BEData = useSelector((state) => state?.billRouting?.billRouting);
  const meterDetails = useSelector((state) => state.meterDetails?.meterDetails);
  const profileDetails = useSelector(
    (store) =>
      store?.accountDetails?.accountDetails?.accountSummary?.personDetailList?.[
        "C1-Person"
      ]?.personContactDetail
  );
  const [email, setEmail] = useState();
  const dispatch = useDispatch();
  useEffect(() => {
    if (profileDetails) {
      profileDetails.map(
        (item) =>
          item.personContactType === "PRIMARYEMAIL" &&
          setEmail(item?.contactDetailValue)
      );
    }
  }, [profileDetails]);

  useEffect(() => {
    if (BEData) {
      if (
        BEData?.billRoutingInfo?.billRouteType ===
        BEData?.availableBillRoutes?.billRouteTypeInfo?.[0]?.billRouteType
      ) {
        setChecked("second");
      } else {
        setChecked("first");
      }
    }
  }, [BEData]);

  const billRouteHandleChange = (check) => {
    setChecked(check);
    let stringData = JSON.stringify(BEData);
    let data = JSON.parse(stringData);
    if (check === "second") {
      data.billRoutingInfo.billRouteType =
        data?.availableBillRoutes?.billRouteTypeInfo?.[0]?.billRouteType;
      dispatch(billRouting(data));
    } else {
      data.billRoutingInfo.billRouteType =
        data?.availableBillRoutes?.billRouteTypeInfo?.[1]?.billRouteType;
      dispatch(billRouting(data));
    }
  };

  return (
    <Card style={styles.cardStyle}>
      <ScrollView style={styles.scrollStyle}>
        <Text style={styles.blueTextTitle}>Bill Routing</Text>
        {BEData ? (
          <>
            <View style={styles.content}>
              <View>
                <RadioButton.Android
                  color={GlobalStyles.colors.ePrimary.base}
                  value="first"
                  status={checked === "first" ? "checked" : "unchecked"}
                  onPress={() => billRouteHandleChange("first")}
                />
              </View>
              <View style={{ flex: 1 }}>
                <Text style={styles.blueCheckBoxLabel}>Postal Bill</Text>
                <Text style={{ fontSize: 12 }}>
                  {meterDetails?.mailingAddress
                    ? meterDetails?.mailingAddress
                    : meterDetails?.accountPersonDetail
                        ?.accountPremiseDetailList?.address1 +
                      ", " +
                      meterDetails?.accountPersonDetail
                        ?.accountPremiseDetailList?.address2}
                </Text>
              </View>
            </View>
            <View style={styles.content}>
              <View>
                <RadioButton.Android
                  color={GlobalStyles.colors.ePrimary.base}
                  value="eBill"
                  status={checked === "second" ? "checked" : "unchecked"}
                  onPress={() => billRouteHandleChange("second")}
                />
              </View>
              <View style={{ flex: 1 }}>
                <Text style={styles.blueCheckBoxLabel}>eBill</Text>
                <Text style={{ fontSize: 12 }}>To be emailed on {email}</Text>
              </View>
            </View>
          </>
        ) : (
          <ActivityIndicator
            size="large"
            color={GlobalStyles.colors.ePrimary.base}
          />
        )}
      </ScrollView>
    </Card>
  );
}

const styles = StyleSheet.create({
  cardStyle: {
    marginHorizontal: "3%",
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    paddingVertical: 15,
  },
  scrollStyle: {
    paddingHorizontal: 20,
    paddingVertical: 5,
    paddingBottom: 10,
    overflow: "scroll",
    zIndex: 100,
  },
  blueTextTitle: {
    color: GlobalStyles.colors.ePrimary.base,
    fontFamily: "NotoSans-SemiBold",
    fontSize: 14,
  },
  blueCheckBoxLabel: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
  },
  content: { flexDirection: "row", alignItems: "flex-start", paddingTop: 15 },
});

import React, { useRef } from "react";
import { Text, Card } from "react-native-paper";
import { GlobalStyles } from "../../../app/global-styles";
import {
  StyleSheet,
  View,
  Dimensions,
  ScrollView,
  ActivityIndicator,
  Platform,
} from "react-native";
import SupportCard from "../links/_support-card";
import ExpandFaq from "./_expand-faq";

export default function FAQ({ FAQData, CallUSDescription }) {
  const { height } = Dimensions.get("window");

  return (
      <View style={styles.aroundMargin}>
        <Card
          style={[
            styles.card,
            Platform.OS === "ios"
              ? { height: height - 405 }
              : { height: height - 350 },
          ]}>
          {FAQData=== "NODATA" ?
           (
            <Text>No Content Found</Text>
          ):
          FAQData.length === 0 ? (
            <ActivityIndicator
              size="large"
              color={GlobalStyles.colors.ePrimary.base}
            />
          ) : (
            <ScrollView nestedScrollEnabled={true} style={styles.scrollStyle}>
              {FAQData && (
                FAQData.length > 0 && (
                  FAQData.map((item, key) => {
                    return (
                      <View
                        i={item.contentId}
                        key={item.contentId}
                        style={
                          FAQData.length === key + 1 && { marginBottom: 20 }
                        }>
                        <ExpandFaq item={item} />
                      </View>
                    );
                  })
                )) }
             
            </ScrollView>
          )}
        </Card>
        <SupportCard  CallUSDescription={CallUSDescription} />
      </View>
  );
}

const styles = StyleSheet.create({
  aroundMargin: {
    marginLeft: "5%",
    marginRight: "5%",
  },
  card: {
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    width: "100%",
    padding: "1%",
    borderColor: "white",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
  },
  view: {
    backgroundColor: GlobalStyles.colors.eBackground.base,
    // boxShadow: "rgb(0 0 0 / 15%) 1px 2px 1px -1px",
    marginVertical: 7,
    borderRadius: 5,
  },
  item: {
    paddingHorizontal: "5%",
    color: GlobalStyles.colors.eDark.base,
    paddingVertical: "3%",
  },
  lineStyle: {
    borderWidth: 0.5,
    borderColor: GlobalStyles.colors.eBackground3.base,
    marginBottom: 5,
    width: "100%",
  },
  titleStyle: {
    color: GlobalStyles.colors.eRich.base,
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    textAlign: "left",
    backgroundColor: GlobalStyles.colors.eBackground.base,
  },
  drawerStyle: {
    borderRadius: 10,
  },
  textLinkStyle: {
    textAlign: "left",
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    paddingLeft: 9,
    paddingBottom: 10,
    textDecorationLine: "underline",
    width: "100%",
  },
  scrollStyle: {
    padding: 10,
  },
});

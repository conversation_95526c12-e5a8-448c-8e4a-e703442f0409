import { useEffect, useState } from "react";
import { StyleSheet, TouchableWithoutFeedback, View, Text } from "react-native";
import { Card } from "react-native-paper";
import { GlobalStyles } from "../../../../components/app/global-styles";
import Icon from "../../../icon";
import RenderHtml, { defaultSystemFonts } from "react-native-render-html";
import { useWindowDimensions } from "react-native";

export default function ExpandFaq({ item, i }) {
  const [open, setOpen] = useState(false);
  const [read, setRead] = useState(item.answer);
  const { width } = useWindowDimensions();
  const systemFonts = [
    ...defaultSystemFonts,
    "Montserrat-Regular",
    "Montserrat-Bold",
  ];

  useEffect(() => {
    setRead(item.IsRead);
  }, [item.IsRead]);

  const handleChange = () => {
    setOpen(prev => (prev !== i ? i : false)); //need to test
    if (read === 0 && open === false) {
      setTimeout(setRead(1), 3000);
    }
  };

  return (
    <Card style={styles.cardStyles}>
      <TouchableWithoutFeedback onPress={() => handleChange()}>
        <View style={[styles.rowSpace, { padding: "4%" }]}>
          <View style={read === 0 ? { width: "62%" } : { width: "90%" }}>
            <Text style={styles.expandTitle} numberOfLines={1}>
              {item.question}
            </Text>
          </View>
          <View
            style={[
              { width: "10%" },
              open === i
                ? {
                    transform: [{ rotateZ: "180deg" }],
                  }
                : {
                    alignItems: "flex-end",
                  },
            ]}>
            <Icon
              name="down-arrow-icon"
              color={GlobalStyles.colors.eMedium.base}
              size={15}
            />
          </View>
        </View>
      </TouchableWithoutFeedback>
      {open === i && (
        <>
          <View style={styles.horizontalLine} />
          <View style={styles.contentSpace}>
            {item.answer ? (
              <>
                <RenderHtml
                  baseStyle={{
                    fontSize: 12,
                    margin: 0,
                    fontFamily: "NotoSans-Medium",
                    color: GlobalStyles.colors.eRich.base,
                  }}
                  systemFonts={systemFonts}
                  enableExperimentalMarginCollapsing={true}
                  contentWidth={width}
                  source={{ html: item.answer }}
                  ignoredStyles={["fontFamily", "marginBottom"]}
                />
              </>
            ) : (
              <>
                <View style={styles.rowSpace}>
                  <Text style={{ fontSize: 12, width: "100%" }}>
                    {item.answer}
                  </Text>
                </View>
              </>
            )}
          </View>
        </>
      )}
    </Card>
  );
}

const styles = StyleSheet.create({
  rowSpace: {
    flexDirection: "row",
    alignItems: "center",
  },
  cardStyles: {
    marginHorizontal: "3%",
    borderRadius: 5,
    backgroundColor: GlobalStyles.colors.eFaint.hover,
    boxShadow: "rgb(0 0 0 / 5%) 1px 5px 13px 0px",
    position: "relative",
    marginVertical: "2%",
  },
  textLinkStyle: {
    textAlign: "left",
    fontSize: 10,
    width: "100%",
    fontFamily: "NotoSans-Medium",
    textDecorationLine: "underline",
  },
  horizontalLine: {
    borderTopWidth: 1,
    borderColor: GlobalStyles.colors.ePastelColor2.hover,
    width: "100%",
  },
  contentSpace: {
    padding: "4%",
  },
  blueColorText: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
  },
  expandTitle: {
    fontFamily: "NotoSans-Bold",
    fontSize: 12,
    color: GlobalStyles.colors.eRich.base,
  },
  chipTextStyle: {
    color: GlobalStyles.colors.eWhite.base,
    padding: 0,
    margin: 0,
  },
  chipBgStyle: {
    backgroundColor: GlobalStyles.colors.eTertiary.base,
    padding: 0,
    margin: 0,
  },
});

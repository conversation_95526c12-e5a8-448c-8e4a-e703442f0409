import React, { useState, useEffect, useContext } from "react";
import { StyleSheet, View, Button, TextInput } from "react-native";
import { Card, Text, Checkbox, List } from "react-native-paper";
import { GlobalStyles } from "../app/global-styles";
import { FlatList } from "react-native-gesture-handler";
import { useSelector } from "react-redux";
import { PaymentContext } from "../e_payments";
import WorkActivityList from "./view/work-list-main";
import { drawerContext } from "../app/authenticated/authenticated_layout";
import { WorkOrderService } from "./model/work-order-service";
import AllWorkActivites from "./view/all-work-activities";
import WorkOrderList from "./view/work-order";
import moment from "moment";
import ConsumerIndex from "../e_consumer-indexing/view/new-consumer-index";
import { config } from "../../environment";
import WorkOrderListWO from "./view/work-order_wo";
import { drawerContextWO } from "../app/authenticated/authenticated_layout_wo";
import AllWorkActivitesWO from "./view/all-work-activities_wo";
import { stackContext } from "../app/get_stack";

export const workOrderContext = React.createContext();
export default function Workactivities() {
  const { workModelType } = React.useContext(stackContext);
  const context = workModelType === "WA" ? drawerContext : drawerContextWO;

  const {
    confirmationModal,
    WOList,
    setWOList,
    singleWO,
    setSingleWO,
    allWOList,
    setAllWOList,
    triggerUpdate,
  } = useContext(context);

  const [workOrderDetails, setWorkOrderDetails] = useState([]);
  const [loading, setLoading] = useState(false);
  const [confirm, setConfirmation] = useState(false);
  const [visible, setVisible] = useState(false);
  const [revert, setRevert] = useState(false);
  // const [singleWODetails, setSingleWODetails] = useState({});
  //const [OTPConfirmationWO, setOTPConfirmationWO] = useState(false);
  const pathName = useSelector(state => state?.servicePath?.servicePath);

  const fetchWorkOrderList = async () => {
    try {
      setLoading(true);
      const res = await WorkOrderService.getAllWorkOrderList();
      res.workActivities = res?.workActivities?.filter(
        each => each["WamRefNum"] != undefined,
      );
      res?.workActivities.sort((a, b) => {
        return moment(b.WorkActivityId).diff(moment(a.WorkActivityId));
      });
      setAllWOList(res);
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchWorkOrderList();
  }, []);

  return (
    <workOrderContext.Provider value={{}}>
      <View>
        {WOList && pathName === "Home" ? (
          <>
            {workModelType === "WA" ? (
              <AllWorkActivites />
            ) : (
              <AllWorkActivitesWO />
            )}
          </>
        ) : (
          <>
            {WOList ? (
              workModelType === "WA" ? (
                <AllWorkActivites />
              ) : (
                <AllWorkActivitesWO />
              )
            ) : singleWO ? (
              workModelType === "WA" ? (
                <WorkOrderList />
              ) : (
                <WorkOrderListWO />
              )
            ) : (
              <ConsumerIndex />
            )}
          </>
        )}

        {/* <WorkActivityList /> */}
      </View>
    </workOrderContext.Provider>
  );
}

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 20,
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
  },
  titleCard: {
    color: GlobalStyles.colors.ePrimary.base,
    fontFamily: "NotoSans-Bold",
    fontSize: 12,
    textAlign: "left",
  },
  leftView: {
    float: "left",
    alignSelf: "stretch",
    width: "60%",
    marginTop: 5,
  },
  rightView: {
    float: "right",
    width: "40%",
    marginTop: 5,
  },
  accountHeaderContent: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    borderRadius: 4,
    justifyContent: "flex-start",
    width: "100%",
    textAlign: "right",
    textColor: GlobalStyles.colors.eWhite.base,
  },
  content: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
  },
  lineStyle: {
    borderWidth: 0.5,
    borderColor: GlobalStyles.colors.eLight.base,
    marginTop: 10,
    marginBottom: 6,
    marginHorizontal: -20,
    paddingHorizontal: -20,
    width: "112%",
  },
  lineStyleBottom: {
    borderWidth: 0.5,
    borderColor: GlobalStyles.colors.eLight.base,
    marginLeft: -5,
    marginBottom: 2,
    marginRight: -5,
    marginTop: 10,
  },
  accountsList: {
    justifyContent: "flex-start",
    fontSize: 12,
    width: "100%",
    textAlign: "left",
    fontFamily: "NotoSans-SemiBold",
  },
  totalAmount: {
    fontSize: 12,
    textAlign: "left",
    color: GlobalStyles.colors.ePrimary.base,
    fontFamily: "NotoSans-Bold",
  },
  total: {
    flexDirection: "row",
    fontSize: 15,
    textAlign: "right",
    color: GlobalStyles.colors.eSecondary.base,
    fontFamily: "NotoSans-Bold",
    marginRight: 10,
  },
  bigTextHeader: {
    color: GlobalStyles.colors.eWhite.base,
    textAlign: "left",
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
    justifyContent: "flex-start",
    // wordBreak: "break-all",
  },
  singleHeader: {
    color: GlobalStyles.colors.eWhite.base,
    textAlign: "center",
    fontFamily: "NotoSans-SemiBold",
    fontSize: 12,
    marginTop: 10,
    marginLeft: -15,
    justifyContent: "flex-start",
  },
  headerAmount: {
    justifyContent: "flex-end",
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
    textAlign: "right",
    color: GlobalStyles.colors.eWhite.base,
    paddingVertical: 10,
    paddingRight: 15,
  },
  amountList: {
    justifyContent: "flex-end",
    fontSize: 12,
    textAlign: "right",
    marginTop: 12,
    marginRight: 10,
    fontFamily: "NotoSans-SemiBold",
  },
  checkboxDiv: {
    flex: 1,
    flexDirection: "row",
    // alignItems: "center",
    // justifyContent: "left",
    marginLeft: -18,
    marginTop: -8,
    marginBottom: -8,
  },
});

import axios from "axios";
import { config } from "../../../environment";

export const ChangePasswordService = {
  changePassword
};

async function changePassword(usrname, oldPwd, newPwd,accountId) {

    const url = config.urls.UPDATE_PASSWORD;
    const tenantCode = config.constants.BASE_TENANT_CODE;
      return new Promise((resolve, reject) => {
        axios({
          url: url,      
          method: "post",
          headers: { "Content-Type": "application/json",'accountId': accountId },
          data: {
            "username":usrname,
            "oldPassword": oldPwd,
            "newPassword": newPwd,
            "tenantCode": tenantCode
          },
        }).then(function(response) {  
            resolve(response);
          })
          .catch(function(error) { 
            reject(error);
          });
      });
}

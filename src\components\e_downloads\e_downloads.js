import React, { useContext, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>iew, StyleSheet, Text } from "react-native";
import { <PERSON><PERSON>, <PERSON> } from "react-native-paper";
import { useSelector } from "react-redux";
import { drawerContext } from "../app/authenticated/authenticated_layout";
import { GlobalStyles } from "../app/global-styles";
import getaddressDownloadData from "./model/download_service";
import DownloadContent from "./view/_download_content";
import { ACCOUNT_STATEMENT, ZERO_BALANCE, certificates } from "./constants";
import { convertBase64ToPDF } from "../common/_base64toPDF";
import { PermissionsAndroid } from "react-native";
import { config } from "../../environment";
import { drawerContextWO } from "../app/authenticated/authenticated_layout_wo";
import { stackContext } from "../app/get_stack";

export default function Downloads() {
  const { workModelType } = React.useContext(stackContext);
  const [fromDate, setFromDate] = useState();
  const [endDate, setEndDate] = useState();
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState("");

  let setPopup,
    setTitle,
    setContent,
    setError,
    setButton,
    setIcon,
    setPopupCode;

  if (workModelType === "WA") {
    ({
      setPopup,
      setTitle,
      setContent,
      setError,
      setButton,
      setIcon,
      setPopupCode,
    } = useContext(drawerContext));
  } else {
    ({
      setPopup,
      setTitle,
      setContent,
      setError,
      setButton,
      setIcon,
      setPopupCode,
    } = useContext(drawerContextWO));
  }

  const accountId = useSelector(
    state => state?.meterDetails?.meterDetails?.accountId,
  );
  const languageCode = useSelector(
    state =>
      state?.parameter?.parameter?.ParameterLookup?.TENANT_PRIMARY_LANGUAGE,
  );

  const personDetails = useSelector(
    state =>
      state?.accountDetails?.accountDetails?.accountSummary?.personDetailList?.[
        "C1-Person"
      ]?.personContactDetail,
  );

  useEffect(() => {
    if (personDetails) {
      personDetails.map(item => {
        item.personContactType === "PRIMARYEMAIL" &&
          setEmail(item.contactDetailValue);
      });
    }
  }, [personDetails]);

  let fileType = "";
  const downloadClick = type => {
    setLoading(type);
    let startDate = "";
    let finalDate = "";
    if (type === ACCOUNT_STATEMENT) {
      (startDate = fromDate), (finalDate = endDate);
    }
    fileType = type.toLowerCase();
    languageCode &&
      getaddressDownloadData(
        accountId,
        type,
        "",
        "",
        email,
        startDate,
        finalDate,
        languageCode,
      ).then(res => {
        if (type === ZERO_BALANCE && res.status === 204) {
          //Replace with Alert
          setPopup(true);
          setTitle("Warning");
          setContent("Please pay all pending due.");
          setError("WARNING");
          setButton(false);
          setIcon("Exclamationmark-fill-icon");
          setPopupCode();
        } else if (type === ACCOUNT_STATEMENT && res.status === 204) {
          setPopup(true);
          setTitle("Warning");
          setContent("There are no transactions in selected date range.");
          setError("WARNING");
          setButton(false);
          setIcon("Exclamationmark-fill-icon");
          setPopupCode();
        }
        if (res.status === 200) {
          convertBase64ToPDF(res?.data?.data?.getBase64Document, fileType);
        }
        setLoading("");
      });
  };

  return (
    <Card style={styles.cardStyles}>
      <Text style={styles.textColor}>DOWNLOADS</Text>
      <ScrollView>
        {certificates.map(cert => {
          return (
            <DownloadContent
              title={cert.title}
              content={cert.content}
              code={cert.code}
              downloadClick={downloadClick}
              fromDate={fromDate}
              setFromDate={setFromDate}
              endDate={endDate}
              setEndDate={setEndDate}
              loading={loading}
              key={cert.code}
            />
          );
        })}
      </ScrollView>
    </Card>
  );
}

const styles = StyleSheet.create({
  cardStyles: {
    margin: "4%",
    borderRadius: 10,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    boxShadow: "rgb(0 0 0 / 5%) 1px 5px 13px 0px",
    position: "relative",
    padding: "4%",
    height: "92%",
  },
  textColor: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
  },
});

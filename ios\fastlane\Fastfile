
#bundle identifier
MY_APP_ID = "com.abjayon.impresafieldworkdemo"
MY_PROFILE = "match AppStore com.abjayon.impresafieldworkdemo"

# find MY_TEAM in the membership details page
#https://developer.apple.com/account
MY_TEAM = "55LFX39W35"

# find it APP_STORE_CONNECT_KEY_ID here
# https://appstoreconnect.apple.com/access/integrations/api
APP_STORE_CONNECT_KEY_ID = "CUP426SP4G";

# find it APP_STORE_CONNECT_ISSUER_ID here
# https://appstoreconnect.apple.com/access/integrations/api
APP_STORE_CONNECT_ISSUER_ID = "6c6481b7-fb53-4cae-a431-f4309d0b71c9"


default_platform(:ios)

platform :ios do
  before_all do
    #find FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD here
    #https://appleid.apple.com/account/manage
    ENV["FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD"] = "sgqb-rdvx-ajmn-csam"
    ENV["FASTLANE_USER"]= "<EMAIL>"
    # this might be different for your machine and different for the office laptop
    ENV["password"]="test"
    ENV["LOGIN_KEYCHAIN_PASSWORD"]= "123456"
    unlock_keychain(
      path: "login.keychain",
      password: ENV['LOGIN_KEYCHAIN_PASSWORD']
    )
    # ensure_git_branch(branch: "jenkins-fastlane-set-up")
    #ensure_git_status_clean
    #sh "git branch --set-upstream-to=origin/jenkins-fastlane-set-up jenkins-fastlane-set-up "
    # git_pull
  end

  desc "test"
  lane :test do
    #we can pass variable from outside using export
    # for example export name="john"
    UI.message("testing env  #{ENV['name']}");
    # UI.message "version xcode #" #{get_version_number_from_xcodeproj(xcodeproj: 'icxnativeui.xcodeproj, target: "icxnativeui")}"
    # UI.message "version plist " #{get_version_number_from_plist(xcodeproj: 'icxnativeui.xcodeproj, target: "icxnativeui")}"
    # UI.message "build xcode " #{get_build_number_from_xcodeproj(xcodeproj: 'icxnativeui.xcodeproj, target: "icxnativeui")}"
    # UI.message "build plist " #{get_build_number_from_plist(xcodeproj: 'icxnativeui.xcodeproj, target: "icxnativeui")}"
    
    sh "pwd"
  end

  desc "Connect to App store using app store connect API"
  lane :connect_app_store do
    app_store_connect_api_key(
      key_id: APP_STORE_CONNECT_KEY_ID ,
      issuer_id: APP_STORE_CONNECT_ISSUER_ID,
      key_filepath: "AuthKey_CUP426SP4G.p8", #files should be stored on CI
      is_key_content_base64: false,
    )
  end

  desc "build ipa for appstore"
  lane :build_ipa do |options|
    gym(
      workspace: "icxnativeui.xcworkspace",
      scheme: options[:scheme],
      include_bitcode: false,
      output_directory:"./build/",
      output_name: "fieldwork.ipa",
      include_symbols: false,
      export_method: "app-store",
      export_options: {
        method: "appstore",
        provisioningProfiles: {
          MY_APP_ID => MY_PROFILE
        },
        uploadBitcode: false,
        uploadSymbols: false,
        compileBitcode: false,
      }
    )
  end

  lane :commit_bump_and_push_to_git do
    commit_version_bump(message: 'bump build')
    push_to_git_remote
  end

  desc "Push a new beta build to AppStore TestFlight"
  lane :beta do |options|
    clear_derived_data
    connect_app_store
    api_key = lane_context[SharedValues::APP_STORE_CONNECT_API_KEY]
    disable_automatic_code_signing(path: "icxnativeui.xcodeproj",
      team_id: MY_TEAM
    )
    sync_certificates
    # update_version_number_appstore
    # increment_build_number
    # increment_version_number_in_xcodeproj(
    #   bump_type: 'patch', # Automatically increment patch version number,
    #   xcodeproj: 'icxnativeui.xcodeproj',
    #   target: options[:scheme]
    # )
    increment_build_number_in_plist(xcodeproj: 'icxnativeui.xcodeproj', target: options[:scheme])
    increment_build_number_in_xcodeproj(xcodeproj: 'icxnativeui.xcodeproj', target: options[:scheme])
    build_ipa(scheme: options[:scheme])
    upload_ipa_to_testflight
    commit_version_bump(
      message: "Version Bump",
      xcodeproj: "icxnativeui.xcodeproj"
    )
    push_to_git_remote
  end

  desc "Sync certificates"
  lane :sync_certificates do
    #read-only disables match from overriding the existing certificates.
    match(readonly: true, type: "appstore", app_identifier: MY_APP_ID )
  end

  lane :upload_ipa_to_testflight do
    ENV["FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD"] = "pbpi-vdzb-qqut-uyrh"
    ENV["FASTLANE_USER"]= "<EMAIL>"
    upload_to_testflight(skip_waiting_for_build_processing: true, ipa: "./build/fieldwork.ipa")
    # upload_to_testflight
  end

  desc "create a demo enviroment build and upload it to test flight"
  lane :beta_demo do
    beta(scheme: "icxnativeui-stage")
  end

  desc "create a dev env build and upload it to test flight"
  lane :beta_dev do
    beta(scheme: "icxnativeui-dev")
  end

  desc "create a QA env build and upload it to test flight"
  lane :beta_qa do
    beta(scheme: "icxnativeui-qa")
  end

  desc "create a QA env build and upload it to test flight"
  lane :beta_ipa do
    beta(scheme: "icxnativeui")
  end

  lane :fetch_version_number do
    version = get_version_number(
      xcodeproj: "icxnativeui.xcodeproj",
      target: "icxnativeui"
    )
    puts "version : #{version}"
  end

  desc "update version number"
  lane :update_version_number_appstore do |options|
    # folllowing line set error
    app_store_version = get_app_store_version_number(bundle_id: MY_APP_ID)
    plist_version = get_version_number_from_plist(xcodeproj: 'icxnativeui.xcodeproj', target: options[:scheme])
    if Gem::Version.new(plist_version.to_f) == Gem::Version.new(app_store_version.to_f)
        UI.message "bumping minor"
        increment_version_number_in_plist(xcodeproj: 'icxnativeui.xcodeproj', bump_type: 'minor', target: options[:scheme])
    else
        UI.message "bumping patch"
        increment_version_number_in_plist(xcodeproj: 'icxnativeui.xcodeproj', bump_type: 'patch', target: options[:scheme])
    end
  end

  lane :beta_build do |options|
    UI.message "scheme received #{options[:scheme]}"
    
    scheme = options[:scheme]

    if scheme.nil?
      UI.message "No scheme received as input. considering the default scheme: icxnativeui"
      scheme = "icxnativeui"
    end
    clear_derived_data
    connect_app_store
    disable_automatic_code_signing(path: "icxnativeui.xcodeproj",
      team_id: MY_TEAM
    )
    increment_build_number(xcodeproj: "icxnativeui.xcodeproj", skip_info_plist: true)
    get_certificates( # Create or get certificate, and install it
      output_path: "./builds"
    )
    get_provisioning_profile( # Create or get provisioning profile
      output_path: "./builds",
      filename: "provisioning.mobileprovision"
    )
    update_project_provisioning( # Set the project provisioning profile (related in Xcode to the General > Signing Release section)
      xcodeproj: "icxnativeui.xcodeproj", # Place here your project name
      target_filter: "icxnativeui", # Here too
      profile: "./builds/provisioning.mobileprovision",
      build_configuration: "Release"
    )
    update_project_team( # Set the right team on your project
      teamid: CredentialsManager::AppfileConfig.try_fetch_value(:team_id)
    )
    build_app(
      workspace: "icxnativeui.xcworkspace",
      scheme: scheme,
    )
  end
end

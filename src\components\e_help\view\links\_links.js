import React from "react";
import { Text, Card } from "react-native-paper";
import { GlobalStyles } from "../../../app/global-styles";
import {
  StyleSheet,
  View,
  Dimensions,
  ScrollView,
  ActivityIndicator,
  Platform,
} from "react-native";
import RenderHtml from "react-native-render-html";
import FlatButton from "../../../common/_flat_button";
import { Linking, Image } from "react-native";
import SupportCard from "./_support-card";
import { config } from "../../../../environment";

export default function Links({
  LinksData = [],
  LinkDescription,
  CallUSDescription,
}) {
  const { height } = Dimensions.get("window");
  return (
    <View style={styles.aroundMargin}>
      <Card
        style={[
          styles.card,
          Platform.OS === "ios"
              ? { height: height - 560 }
              : { height: height - 505 },
        ]}>
        {LinksData === "NODATA" ? (
          <Text>No Content Found</Text>
        ) : LinksData.length > 0 ? (
          <ScrollView nestedScrollEnabled={true} style={styles.scrollStyle}>
            <Text style={styles.titleStyle}>{LinkDescription}</Text>
            <View style={styles.lineStyle} />
            <View style={styles.content}>
              <View style={styles.leftView}>
                {LinksData.map((item, index) => {
                  if (item.contentGroup === "LINK") {
                    return (
                      <FlatButton
                        key={`${item.contentId}#${index}`}
                        textStyles={styles.linkWhite}
                        onPress={() => Linking.openURL(item.answer)}>
                        {item.question}
                      </FlatButton>
                    );
                  }
                })}
              </View>
              <View style={styles.rightView}>
                <View style={styles.image}>
                  {/* <Image source={ImageURL} style={styles.attachIcon} /> */}
                  <RenderHtml
                    source={{
                      html:
                        "<img src='" +
                        config.constants.BASE_URL +
                        "/assets/e_self_help_links_image.png' alt=' width='180' height='100' />",
                    }}
                  />
                </View>
              </View>
            </View>
          </ScrollView>
        ) : (
          <ActivityIndicator
            size="large"
            color={GlobalStyles.colors.ePrimary.base}
          />
        )}
      </Card>
      <SupportCard CallUSDescription={CallUSDescription} />
    </View>
  );
}

const styles = StyleSheet.create({
  aroundMargin: {
    marginLeft: "5%",
    marginRight: "5%",
  },
  card: {
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    width: "100%",
    padding: "1%",
    borderColor: "white",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
  },
  headerCard: {
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    width: "100%",
    marginTop: 10,
  },
  card2: {
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    width: "100%",
    height: 460,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    marginTop: 0,
  },
  view: {
    backgroundColor: GlobalStyles.colors.eBackground.base,
    boxShadow: "rgb(0 0 0 / 15%) 1px 2px 1px -1px",
    borderRadius: 10,
    marginHorizontal: 0,
    marginVertical: 5,
  },
  item: {
    paddingHorizontal: "5%",
    color: GlobalStyles.colors.eDark.base,
  },
  lineStyle: {
    borderWidth: 0.5,
    borderColor: GlobalStyles.colors.eLight.base,
    maringinBottom: 5,
    width: "110%",
  },
  titleStyleCallUS: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 14,
    fontFamily: "NotoSans-Bold",
    textAlign: "left",
    backgroundColor: GlobalStyles.colors.eFaint.base,
    marginVertical: 10,
    marginLeft: 20,
  },
  titleStyle: {
    color: GlobalStyles.colors.eRich.base,
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    textAlign: "left",
    paddingVertical: -5,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    marginHorizontal: "5%",
    marginVertical: "3%",
  },
  cardTitle: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    textAlign: "center",
    paddingVertical: -5,
    marginBottom: "5%",
    marginTop: "5%",
  },
  cardTitle1: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 10,
    fontFamily: "NotoSans-Bold",
    textAlign: "center",
    paddingVertical: -5,
    marginBottom: 5,
    marginTop: 10,
  },
  html: {
    paddingHorizontal: 20,
  },
  leftView: {
    float: "left",
    marginVertical: "3%",
    alignSelf: "stretch",
    width: "40%",
  },
  rightViewShowNumber: {
    float: "right",
  },
  leftViewShowNumber: {
    float: "left",
    marginTop: 11,
    marginLeft: 10,
    marginRight: 3,
  },
  rightView: {
    float: "right",
    width: "60%",
  },
  leftViewSupport: {
    float: "left",
    marginTop: "6%",
    alignSelf: "stretch",
    width: "40%",
  },
  rightViewSupport: {
    float: "right",
    width: "60%",
  },
  content: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
    paddingHorizontal: "6%",
    paddingVertical: "1%",
  },
  contentShowNumber: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
  },
  linkWhite: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    textDecorationLine: "underline",
    paddingVertical: "3%",
    paddingHorizontal: "3%",
    fontFamily: "NotoSans-SemiBold",
  },
  showNumbers: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 14,
    // paddingVertical: "2%",
    // paddingHorizontal: "2%",
    textAlign: "center",
    marginTop: 7,
    marginLeft: 3,
  },
  shownumberImg: {
    paddingVertical: "2%",
    paddingHorizontal: "2%",
    textAlign: "left",
    marginTop: 6,
    marginBottom: 7,
    marginRight: 5,
    marginLeft: 5,
  },
  rightViewShowIcon: {
    marginVertical: 10,
    marginLeft: 7,
    marginRight: 0,
    paddingTop: 3,
  },
  scrollStyleCAllUS: {
    backgroundColor: GlobalStyles.colors.eBackground3.base,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  attachIcon: {
    width: 145,
    height: 90,
  },
  image: {
    marginTop: 10,
  },
});

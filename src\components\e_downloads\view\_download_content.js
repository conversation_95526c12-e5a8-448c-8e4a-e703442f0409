import moment from "moment";
import { useEffect, useState } from "react";
import { Text, StyleSheet, View, ActivityIndicator } from "react-native";
import { Card, FAB, HelperText, TextInput } from "react-native-paper";
import { useSelector } from "react-redux";
import { GlobalStyles } from "../../app/global-styles";
import Icon from "../../icon";

export default function DownloadContent({
  title,
  content,
  code,
  downloadClick,
  fromDate,
  setFromDate,
  endDate,
  setEndDate,
  loading,
}) {
  const [startDateErr, setStartDateErr] = useState(false);
  const [endDateErr, setEndDateErr] = useState(false);
  const [disableIcon, setDisableIcon] = useState(false);
  const accountStartDate = useSelector(
    state => state?.meterDetails?.meterDetails?.setUpDate,
  );

  const itemClick = i => {
    if (i === "ACCOUNT_STATEMENT") {
      setDisableIcon(true);
      startDateValidation();
      endDateValidation();
      if (
        fromDate &&
        endDate &&
        startDateErr === false &&
        endDateErr === false
      ) {
        setDisableIcon(false);
        downloadClick(i);
      }
    } else {
      downloadClick(i);
    }
  };

  function isValidDate(dateString) {
    const dateObj = moment(dateString, "DD-MM-YYYY");
    return dateObj._isValid;
  }

  const startDateValidation = () => {
    if (fromDate) {
      let dateContain = "";
      if (fromDate.includes("/")) {
        dateContain = fromDate.split("/");
      }
      if (fromDate.includes("-")) {
        dateContain = fromDate.split("-");
      }
      if (dateContain) {
        if (
          dateContain?.[0]?.length === 2 &&
          dateContain?.[1]?.length === 2 &&
          dateContain?.[2]?.length === 4
        ) {
          if (isValidDate(fromDate)) {
            let start = moment(fromDate, "DD-MM-YYYY").format("YYYY-MM-DD");
            let current_date = moment().format("YYYY-MM-DD");
            let accountStart = moment(accountStartDate, "YYYY-MM-DD").format(
              "DD-MM-YYYY",
            );
            moment(start).isSameOrBefore(current_date) &&
            moment(start).isSameOrAfter(accountStartDate)
              ? setStartDateErr(false)
              : setStartDateErr(
                  "From Date should be in between account start Date (" +
                    accountStart +
                    ") and current date!",
                );
          } else {
            setStartDateErr("From Date is invalid!");
          }
        } else {
          setStartDateErr('Date formate should be "DD-MM-YYYY"!');
        }
      } else {
        setStartDateErr("From Date is invalid!");
      }
    } else {
      setStartDateErr("From Date is required!");
    }
  };
  const endDateValidation = () => {
    if (endDate) {
      let dateContain = "";
      if (endDate.includes("/")) {
        dateContain = endDate.split("/");
      }
      if (endDate.includes("-")) {
        dateContain = endDate.split("-");
      }
      if (dateContain) {
        if (
          dateContain?.[0]?.length === 2 &&
          dateContain?.[1]?.length === 2 &&
          dateContain?.[2]?.length === 4
        ) {
          if (isValidDate(endDate)) {
            let start = moment(fromDate, "DD-MM-YYYY").format("YYYY-MM-DD");
            let end = moment(endDate, "DD-MM-YYYY").format("YYYY-MM-DD");
            let current_date = moment().format("YYYY-MM-DD");
            moment(end).isAfter(start) &&
            moment(end).isSameOrBefore(current_date)
              ? setEndDateErr(false)
              : setEndDateErr(
                  "To Date should be in between from date and current date!",
                );
          } else {
            setEndDateErr("To Date is invalid!");
          }
        } else {
          setEndDateErr('Date formate should be "DD-MM-YYYY"!');
        }
      } else {
        setEndDateErr("To Date is invalid!");
      }
    } else {
      setEndDateErr("To Date is required!");
    }
  };

  useEffect(() => {
    if (code === "ACCOUNT_STATEMENT") {
      if (fromDate && endDate) {
        startDateValidation();
        endDateValidation();
        if (startDateErr !== false || endDateErr !== false) {
          setDisableIcon(true);
        } else {
          setDisableIcon(false);
        }
      } else {
        setDisableIcon(true);
      }
    }
  }, [fromDate, endDate, startDateErr, endDateErr]);
  return (
    <Card style={styles.cardStyles}>
      <View style={styles.paddingViewTitle}>
        <View style={styles.rowSpace}>
          <Text style={styles.textStyleTitle}>{title}</Text>
          <FAB
            disabled={disableIcon || loading === code}
            icon={() =>
              loading === code ? (
                <ActivityIndicator
                  size={15}
                  color={GlobalStyles.colors.eWhite.base}
                />
              ) : (
                <Icon
                  name="Download-icon"
                  size={15}
                  color={GlobalStyles.colors.eWhite.base}
                />
              )
            }
            style={[
              styles.fabSize,
              disableIcon ? styles.disableBg : styles.enableBg,
            ]}
            onPress={loading !== code ? () => itemClick(code) : null}
            animated={false}
          />
        </View>
      </View>
      <View style={styles.horizontalLine} />
      <View style={styles.paddingView}>
        <Text style={styles.textStyle}>{content}</Text>
        {code === "ACCOUNT_STATEMENT" && (
          <View style={styles.stmtPadding}>
            <Text style={styles.stmtText}>
              From Date
              <Text style={{ color: GlobalStyles.colors.eDanger.dark }}>*</Text>
              :
            </Text>
            <TextInput
              mode="outlined"
              dense
              outlineColor={GlobalStyles.colors.eOutline.base}
              keyboardType="default"
              activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
              placeholder="DD-MM-YYYY"
              maxLength={10}
              style={styles.inputcls}
              value={fromDate}
              onChangeText={e => {
                setFromDate(e);
              }}
              onBlur={startDateValidation}
              error={startDateErr !== false}
            />
            <HelperText
              type="error"
              visible={startDateErr !== false}
              padding="none">
              {startDateErr}
            </HelperText>
            <Text style={styles.stmtText}>
              To Date
              <Text style={{ color: GlobalStyles.colors.eDanger.dark }}>*</Text>
              :
            </Text>
            <TextInput
              mode="outlined"
              dense
              outlineColor={GlobalStyles.colors.eOutline.base}
              keyboardType="default"
              activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
              placeholder="DD-MM-YYYY"
              maxLength={10}
              style={styles.inputcls}
              value={endDate}
              onChangeText={e => {
                setEndDate(e);
              }}
              onBlur={endDateValidation}
              error={endDateErr !== false}
            />
            <HelperText
              type="error"
              visible={endDateErr !== false}
              padding="none">
              {endDateErr}
            </HelperText>
          </View>
        )}
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  cardStyles: {
    marginVertical: "3%",
    borderRadius: 10,
    backgroundColor: GlobalStyles.colors.eBackground.base,
    boxShadow: "rgb(0 0 0 / 5%) 1px 5px 13px 0px",
    position: "relative",
  },
  horizontalLine: {
    borderTopWidth: 1,
    borderColor: GlobalStyles.colors.eLight.base,
    width: "100%",
  },
  paddingView: {
    padding: "4%",
  },
  textStyle: {
    color: GlobalStyles.colors.ePrimary.base,
    fontFamily: "NotoSans-Medium",
    fontSize: 10,
  },
  rowSpace: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  fabSize: {
    width: 30,
    height: 30,
    justifyContent: "center",
    alignItems: "center",
  },
  enableBg: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
  },
  disableBg: {
    backgroundColor: GlobalStyles.colors.eLight.base,
  },
  paddingViewTitle: {
    paddingHorizontal: "4%",
    paddingVertical: "3%",
  },
  textStyleTitle: {
    fontFamily: "NotoSans-Bold",
    fontSize: 12,
    color: GlobalStyles.colors.eRich.base,
  },
  stmtText: {
    fontFamily: "NotoSans-SemiBold",
    fontSize: 12,
    color: GlobalStyles.colors.eRich.base,
  },
  stmtPadding: {
    paddingVertical: "2%",
  },
  inputcls: {
    color: GlobalStyles.colors.eDark.base,
    backgroundColor: GlobalStyles.colors.eBackground.base,
    fontSize: 14,
  },
});

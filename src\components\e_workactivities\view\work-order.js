import React, { useEffect, useState, useContext } from "react";
import {
  StyleSheet,
  View,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
  Pressable,
} from "react-native";
import { GlobalStyles } from "../../app/global-styles";
import { Text, Card, Snackbar, Portal } from "react-native-paper";
import { useDispatch, useSelector } from "react-redux";
import FontAwesome5Icon from "react-native-vector-icons/FontAwesome";
import moment from "moment";
import { useNavigation } from "@react-navigation/native";
import _ from "lodash";
import { ScrollView } from "react-native-gesture-handler";
import { useIsFocused } from "@react-navigation/native";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import Icon from "../../icon";
import Icons from "react-native-vector-icons/FontAwesome";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import CalendarPicker from "../../common/calendar";
import Button from "../../common/_button";
import { WorkOrderService } from "../model/work-order-service";
import MaterialTableGrid from "./material-table-grid";
import LabourTableGrid from "./labour-grid";
import EquipmentTableGrid from "./equipment-table-grid";
import DirectChargesGrid from "./direct-charges-grid";
import ActivityGrid from "./activity-grid";
import FontAwesomeEye from "react-native-vector-icons/AntDesign";
// import CameraComponent from "../../common/camera";
import { useTranslation } from "react-i18next";
import ServiceHostoryTableGrid from "./servicehistory-table-grid";
import { servicePath } from "../../../redux/slices/servicePath";
import AssetsGrid from "./assets-grid";
import AsyncStorage from "@react-native-async-storage/async-storage";
import CalendarPickerWO from "../../common/calendar_wo";
import { config } from "../../../environment";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { stackContext } from "../../app/get_stack";

export default function WorkOrderList() {
  const { workModelType } = React.useContext(stackContext);
  const context = workModelType === "WA" ? drawerContext : drawerContextWO;

  const {
    setAllActivities,
    setSingleWorkOrder,
    workOrder,
    confirmationModal,
    setConfirmationModal,
    setOTPModal,
    OTPModal,
    WOList,
    setWOList,
    singleWO,
    setSingleWO,
    customerNumber,
    OTPVerification,
    setSingleWODetails,
    updateWAObj,
    setUpdateWAObj,
    setconfirmNote,
    setAllWOList,
    setConfirmModalType,
    setOTPError,
    setCustomerNumber,
    singleWODetails,
    OTPConfirmationWO,
    setOTPConfirmationWO,
    tempWorkOrder,
    setTriggerUpdate,
    triggerUpdate,
    newWorkOrderExists,
    allWOList,
    setNewWorkOrderExists,
    setSelectedItem,
    assetDataFrid,
  } = useContext(context);

  const selectedActivity = useSelector(
    state => state.activity.selectedActivity,
  );
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const focused = useIsFocused();

  const isCurrentChecklistCompleted = useSelector(
    state => state.activity.isCurrentChecklistCompleted,
  );
  const pathName = useSelector(state => state?.servicePath?.servicePath);

  const fetchWorkOrderList = async () => {
    try {
      //setLoading(true);
      let res = await WorkOrderService.getAllWorkOrderList();
      res.workActivities = res?.workActivities?.filter(
        each => each["WamRefNum"] != undefined,
      );
      res.workActivities?.sort((a, b) => {
        // Sort by WorkActivityId in descending order
        return b.WorkActivityId - a.WorkActivityId;
      });

      setAllWOList(res);
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
      //setLoading(false);
    }
  };

  const handleBack = async () => {
    //navigation.goBack();
    // if (newWorkOrderExists) {
    //   setNewWorkOrderExists(!newWorkOrderExists);
    // }
    setWOList(true);
    setSingleWO(false);
    setConfirmationModal(false);
    setOTPModal(false);

    let res = await WorkOrderService.getAllWorkOrderList();
    res.workActivities = res?.workActivities?.filter(
      each => each["WamRefNum"] != undefined,
    );
    res.workActivities?.sort((a, b) => {
      // Sort by WorkActivityId in descending order
      return b.WorkActivityId - a.WorkActivityId;
    });

    setAllWOList(res);
  };

  const today = new Date();
  const OffAvailableDates = today.toISOString().split("T")[0];
  const [actualStartDate, setActualStartDate] = useState();
  const [actualEndDate, setActualEndDate] = useState();
  const [changeindex, setIndex] = useState();
  const [selectedDate, setSelectedDate] = useState(null);
  const [showCalendar, setShowCalendar] = useState(false);
  const [editable, setEditable] = useState(true);
  const [errorOnoff, setErrorOnOff] = useState(false);
  const [errorOn, setErrorOn] = useState(false);
  const [availableOndates, setAvailableOndates] = useState(null);
  const [calOrText, setCalorText] = useState(true);
  const [textDateOff, setTextDateOff] = useState(null);
  const [disableCancle, setDisableCancle] = useState(false);
  const [disableonHold, setDisableonHold] = useState(false);
  const [disableComplete, setDisableComplete] = useState(false);
  const [disableCompleteTime, setDisableCompleteTime] = useState(false);
  const [disableStart, setDisableStart] = useState(false);
  const [disableAllChecks, setDisableChecks] = useState(false);
  const [disableTextInput, setDisableTextInput] = useState(false);
  const [status, setstatus] = useState();
  const [disableSubmit, setDisableSubmit] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [submitLoading, setsubmitLoading] = useState(false);
  const [isActivityLoading, setActivityLoading] = useState(false);
  const [wAList, setAllWAList] = useState([]);
  const [wAListCopy, setAllWAListCopy] = useState([]);
  const [dupWAListCopy, setDupAllWAListCopy] = useState([]);
  const [activitiesList, setActivitiesList] = useState(false);
  const [expandedSections, setExpandedSections] = useState([]);
  const [equipToggle, setEquipToggle] = useState(false);
  const [assetToggle, setAssetToggle] = useState(false);
  const [labourToggle, setLabourToggle] = useState(false);
  const [serviceToggle, setServiceToggle] = useState(false);
  const [storeToggle, setstoreToggle] = useState(false);
  const [expenseToggle, setExpenseToggle] = useState(false);
  const [otherToggle, setOtherToggle] = useState(false);

  const [equipShow, setEquipShow] = useState(false);
  const [labourShow, setLabourShow] = useState(false);
  const [serviceShow, setServiceShow] = useState();
  const [storeShow, setstoreShow] = useState(false);
  const [otherShow, setOtherShow] = useState(false);

  const [info, setInfo] = useState([]);
  const [infoAsset, setInfoAsset] = useState([]);
  const [spID, setSpID] = useState("");
  const [addressLoad, setAddressLoad] = useState(true);
  const [addressLoadAsset, setAddressLoadAsset] = useState(false);
  const [WADetails, setWADetails] = useState([]);
  const [finalShData, setFinalShData] = useState([]);
  const [plannedDuration, setPlannedDuration] = useState();
  const [actualDuration, setActualDuration] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
  });
  const [comments, setComment] = useState();
  const [, forceUpdate] = React.useState();
  const [updatedWAListCopy, setupdatedWAListCopy] = useState([]);
  const [customerNumberError, setCustomerNumberError] = useState(false);
  const [customerNumberText, setCustomerNumberText] = useState("");
  const [consumnerData, setConsumnerData] = useState("");
  const [consumnerDataLoad, setConsumnerDataLoad] = useState(false);
  const [acitvityId, setAcitvityId] = useState();
  const [finalApiData, setFinalApiData] = useState();
  const { t } = useTranslation();
  const [serviceArray, setServiceArray] = useState();
  const [allowCheckbox, setAllowCheckbox] = useState(false);
  const [workStatus, setWorkStatus] = useState();
  const [wamIFSLbrdata, setwamIFSLbrdata] = useState([]); // wam format to save data into IFS DB
  const [wamIFSEqdata, setwamIFSEqpdata] = useState([]); // wam format to save data into IFS DB

  const [iFSLbrdata, setIFSLbrdata] = useState([]); //format to save data into IFS DB
  const [iFSEqdata, setIFSEqpdata] = useState([]); //format to save data into IFS DB

  const [wamIFSQEqdata, setwamIFSQEqpdata] = useState({});
  const [showTimeErr, setshowTimeErr] = useState(false);
  const [showTimeErrMsg, setshowTimeErrMsg] = useState();
  const [expenseItems, setExpenseItems] = useState([
    { description: "", amount: 0 },
  ]);
  const [currentAsset, setCurrentAsset] = useState([]);
  const [bearer, setBearer] = useState();
  const [items, setItems] = React.useState([]);
  const [checkboxData, setCheckboxData] = useState({});
  const [safecheckboxData, setSafeCheckboxData] = useState({});
  const [simpleTextData, setSimpleTextData] = useState({});
  const [progressLoad, setProgressLoad] = useState(null);
  const todayDate = moment();
  const yesterday = todayDate.clone().subtract(1, "days");
  const formattedDate = yesterday.format("YYYY-MM-DD");
  const [visibleSnack, setVisibleSnack] = React.useState(false);
  const [snackMsg, setSnackMsg] = useState("");

  useEffect(async () => {
    const storedLanguage = await AsyncStorage.getItem("bearer");
    let bearerJson = JSON.parse(storedLanguage);
    setBearer(bearerJson);
  }, []);

  useEffect(() => {
    if (singleWODetails?.WorkStatus == "I") {
      setWorkStatus("I");
      setDisableStart(true);
      setDisableonHold(false);

      setDisableComplete(false);
      setAllowCheckbox(true);
    } else if (singleWODetails?.WorkStatus == "OH") {
      setWorkStatus("OH");
      setDisableonHold(true);
      setDisableStart(false);
      setAllowCheckbox(false);

      setDisableComplete(false);
    } else if (
      singleWODetails?.WorkStatus == "CO" ||
      singleWODetails?.WorkStatus == "CL"
    ) {
      setWorkStatus("CO");
      setDisableonHold(true);
      setDisableStart(true);
      setAllowCheckbox(false);

      setDisableComplete(true);
    }
    // const format = "DD-MM-YYYY hh:mm A";

    // // Parse the dates using the format
    // if (actualEndDate && actualStartDate) {
    //   const momentDate1 = moment(actualStartDate, format);
    //   const momentDate2 = moment(actualEndDate, format);
    //   let dateValid = momentDate1.isBefore(momentDate2);
    //   console.log(
    //     actualStartDate,
    //     actualEndDate,
    //     dateValid,
    //     "LLLLLLLLLLLLLLLLLLLLLLLLLLLLLL",
    //   );
    //   if (dateValid) setDisableComplete(false);
    //   else setDisableComplete(true);
    // }
  }, [singleWODetails, wAListCopy]); //removing actualStartDate, actualEndDate

  const equipToggleHandler = () => {
    setEquipToggle(!equipToggle);
  };
  const labourToggleHandler = () => {
    setLabourToggle(!labourToggle);
  };
  const assetToggleHandler = () => {
    setAssetToggle(!assetToggle);
  };
  const serviceToggleHandler = () => {
    setServiceToggle(!serviceToggle);
  };

  const storeToggleHandler = () => {
    setstoreToggle(!storeToggle);
  };

  const expenseToggleHandler = () => {
    setExpenseToggle(!expenseToggle);
  };

  const otherToggleHandler = () => {
    setOtherToggle(!otherToggle);
  };

  const handleToggle = (ind, item) => {
    const newExpandedSections = [...expandedSections];
    const isAlreadyExpanded = newExpandedSections.includes(ind);
    setIndex(ind);
    if (!isAlreadyExpanded) {
      newExpandedSections.forEach(expandedIndex => {
        if (expandedIndex !== ind) {
          newExpandedSections.splice(
            newExpandedSections.indexOf(expandedIndex),
            1,
          );
        }
      });
    } else {
      newExpandedSections.splice(newExpandedSections.indexOf(ind), 1);
    }
    setComment(item.Comments);

    if (singleWODetails.WorkOrderType === "Consumer Survey") {
      getAddress(singleWODetails.MeterNumber);
      getConsumerDetails(singleWODetails.ConsumerIndexingId);
    } else if (singleWODetails.WorkOrderType === "Meter Inspection") {
      getMeterInspectionAddress(singleWODetails.AssetId);
      getConsumerDetails(singleWODetails.ConsumerIndexingId);
    } else if (singleWODetails.WorkOrderType === "Meter Replacement") {
      getAssetAddress(singleWODetails.AssetId);
      getConsumerDetails(singleWODetails.ConsumerIndexingId);
    } else if (singleWODetails.WorkOrderType === "Meter Installation") {
      //getAssetInstallationAddress(singleWODetails.AssetId);
      getAssetAddress(singleWODetails.AssetId);
      getConsumerDetails(singleWODetails.ConsumerIndexingId);
    } else {
      //getAssetAddress(singleWODetails.AssetId);
    }
    console.log("singleWODetailssingleWODetails", singleWODetails);

    if (!isAlreadyExpanded) {
      newExpandedSections.push(ind);
    }
    setExpandedSections(newExpandedSections, item);

    // const formattedDate = moment(
    //   wAListCopy?.workActivities[ind]?.ActualStartDate,
    // ).format("DD-MM-YYYY");
    if (
      wAListCopy?.workActivities?.find(
        e => e?.WorkActivityId == singleWODetails.WorkActivityId,
      )?.ActualStartDate !== null ||
      wAListCopy?.workActivities?.find(
        e => e?.WorkActivityId == singleWODetails.WorkActivityId,
      )?.ActualEndDate !== null
    ) {
      setActualStartDate(
        moment(
          wAListCopy?.workActivities?.find(
            e => e?.WorkActivityId == singleWODetails.WorkActivityId,
          )?.ActualStartDate,
          "YYYY-MM-DD HH:mm:ss",
        ).format("DD-MM-YYYY h:mm A"),
      );
      if (wAListCopy?.workActivities[ind]?.ActualEndDate) {
        setActualEndDate(
          moment(wAListCopy?.workActivities[ind]?.ActualEndDate).format(
            "DD-MM-YYYY h:mm A",
          ),
        );
      } else {
        setActualEndDate(wAListCopy?.workActivities[ind]?.ActualStartDat);
      }
      forceUpdate({});
    } else {
      const currentStartDateTime = moment().format("DD-MM-YYYY h:mm A");
      //const currentEndDateTime = moment().format("DD-MM-YYYY h:mm A");

      setActualStartDate(currentStartDateTime);
      setActualEndDate(currentStartDateTime);
      forceUpdate({});
    }
  };
  const getAssetInstallationAddress = async assetId => {
    try {
      setLoading(true);

      const workorderid = wAListCopy.WorkOrderId;
      const res = await WorkOrderService.getMeterInsAddress(assetId);

      setCustomerNumber(res?.Mobile);
      const resInfo = JSON.stringify(res);
      setInfoAsset(JSON.parse(resInfo));
      setAddressLoadAsset(true);

      setSpID(res?.spId);
    } catch (err) {
    } finally {
      setLoading(false);
    }
  };
  const getMeterInspectionAddress = async assetID => {
    try {
      setLoading(true);

      const workorderid = wAListCopy.WorkOrderId;
      const res = await WorkOrderService.getMeterInspectionAddress(assetID);

      setCustomerNumber(res?.Mobile);
      const resInfo = JSON.stringify(res);
      setInfoAsset(JSON.parse(resInfo));
      setAddressLoadAsset(true);
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
      setLoading(false);
    }
  };
  const getConsumerDetails = async ConsumerIndexingId => {
    try {
      setLoading(true);
      console.log("ConsumerIndexingId.....", ConsumerIndexingId);
      const res = await WorkOrderService.getConsumerDetails(ConsumerIndexingId);

      setConsumnerData(res);
      setConsumnerDataLoad(true);
      // setCustomerNumber(res?.Mobile);
      // const resInfo = JSON.stringify(res);
      // setInfoAsset(JSON.parse(resInfo));
      // setAddressLoadAsset(true);
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
      setLoading(false);
    }
  };
  const getAssetAddress = async assetID => {
    try {
      setLoading(true);

      const workorderid = wAListCopy.WorkOrderId;
      const res = await WorkOrderService.getAssetAddress(assetID, workorderid);

      setCustomerNumber(res?.Mobile);
      const resInfo = JSON.stringify(res);
      setInfoAsset(JSON.parse(resInfo));
      setAddressLoadAsset(true);
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
      setLoading(false);
    }
  };
  const getAddress = async meterNo => {
    try {
      setLoading(true);

      const workorderid = wAListCopy.WorkOrderId;
      const res = await WorkOrderService.getAddress(meterNo, workorderid);

      setCustomerNumber(res?.Mobile);
      console.log("res", res);
      const resInfo = JSON.stringify(res);
      setInfo(JSON.parse(resInfo));
      setAddressLoad(true);
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
      setLoading(false);
    }
  };
  const fetchWorkActivityList = async () => {
    console.log("EEEEEEEEEEWWWWWWQQQQQQQQQQQQQQQQQ-564");

    try {
      setLoading(true);

      //api fr
      const res = await WorkOrderService.getAllWorkActivities(
        singleWODetails?.WorkOrderId, // "455",
      );

      setAllWAList(res);
      console.log(res, "YYYYYYYYYYYYYYYYYYYYYYYYYYY");
      setAllWAListCopy(res.workOrders[0]);
      setActivitiesList(true);
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
      setLoading(false);
    }
  };

  // useEffect(() => {
  //   console.log(
  //     "UUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUUU89098",
  //     wAListCopy,
  //     singleWODetails?.WorkActivityId,
  //   );
  //   let updatedObj = wAListCopy?.workActivities?.find(
  //     e => e?.WorkActivityId == singleWODetails?.WorkActivityId,
  //   );
  //   console.log(updatedObj, "updatedObj-----------383");
  //   setSingleWO(updatedObj);
  // }, [wAListCopy]);

  const priorityRenderer = statusNumber => {
    return statusNumber == "1"
      ? "Low"
      : statusNumber == "3"
      ? "Normal"
      : statusNumber == "5"
      ? "High"
      : statusNumber == "7"
      ? "Urgent"
      : "Emergency";
  };

  const cancelClick = () => {};
  const onStatusClick = async (singleWODetailsData, status) => {
    let activityId = singleWODetailsData?.WorkActivityId;
    let data = {};
    if (status == "I") {
      setProgressLoad("I");
      setSnackMsg(t("PROGRESS_ERROR"));
      data = {
        WorkActivityId: activityId,
        WorkStatus: status,
        ActualStartDate: new Date(),
      };
    } else if (status == "OH") {
      setProgressLoad("OH");
      setSnackMsg(t("ON_HOLD_ERROR"));
      data = {
        WorkActivityId: activityId,
        WorkStatus: status,
      };
    } else if (status == "CO") {
      data = {
        WorkActivityId: activityId,
        WorkStatus: status,
        ActualEndDate: new Date(),
      };
    }
    WorkOrderService.UpdateWAStatus(data).then(async resp => {
      const res = await WorkOrderService.getAllWorkActivities(
        singleWODetails?.WorkOrderId, // "455",
      );

      // const res = await WorkOrderService.getAllWorkActivities(
      //   singleWODetails?.WorkOrderId, // "455",
      // );

      // setAllWAList(res);
      // setAllWAListCopy(res.workOrders[0]);
      // setActivitiesList(true);

      let updatedObj = res.workOrders[0]?.workActivities?.find(
        e => e?.WorkActivityId == activityId,
      );

      console.log(updatedObj, "Updated Status successfully");
      setSingleWODetails(updatedObj);
      setProgressLoad(null);
      setVisibleSnack(true);
    });

    let logsPayload = {
      WorkActivityId: activityId,
      OldStatus: singleWODetailsData?.WorkStatus,
      NewStatus: status,
      SouceSystem: "IFSMOBILE",
      UpdatedBy: bearer?.userName,
      UpdatedJson: {
        status: status,
      },
    };

    WorkOrderService.UpdateIFSdbLogs(logsPayload)
      .then(resp => {
        console.log(resp, resp.data, "response - UpdateIFSdbLogs");
      })
      .catch(err => {
        console.log(err, "error occured");
      });
  };
  const markCheckListItemDone = (index, checklistItem, isChecked) => {
    const updatedWADetails = { ...wAListCopy };
    const updatedChecklist = updatedWADetails.workActivities[
      index
    ].WorkOrderActivityChecklist.map(obj =>
      obj.activityChecklistId === checklistItem.activityChecklistId
        ? { ...obj, answer: isChecked ? "yes" : "no" }
        : obj,
    );
    updatedWADetails.workActivities[index].WorkOrderActivityChecklist =
      updatedChecklist;
    console.log("YYYYYYYYYYYYYYYYYYYYYYYYYYY");
    setAllWAListCopy(updatedWADetails);
  };
  useEffect(() => {
    fetchWorkActivityList();
    // const intervalId = setInterval(() => {
    //   fetchWorkActivityList();
    // }, 15000);

    // // Cleanup interval on unmount
    // return () => clearInterval(intervalId);
  }, []);
  useEffect(() => {
    let req = { ...wAListCopy };
    setDupAllWAListCopy(prev => req);
  }, [wAListCopy]);

  const submitClick = () => {
    //requestOTP();
    setsubmitLoading(true);
    if (expenseItems.length > 1) {
      // If length is more than 1, then there will be 1 record added.
      const allExceptLast = expenseItems.slice(0, -1);
      console.log(allExceptLast, "IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII-1210");
      allExceptLast?.map((eachItem, index) => {
        let formatIFS = {
          WorkOrderId: singleWODetails?.WorkOrderId,
          WorkActivityId: singleWODetails?.WorkActivityId,
          resourceType: eachItem.description,
          ActualDuration: eachItem?.amount,
        };
        WorkOrderService.addExpenseDetails(formatIFS)
          .then(res => {
            console.log(res);
          })
          .catch(err => {
            console.log(err);
          });
        let formatWAM = {
          input: {
            resourceChargeDetails: {
              chargeDate: formattedDate,
              activityId: singleWODetails?.WamWorkActivityId,
              resourceTypeId: "291656239099", //as of now hardcoded.
              quantity: eachItem?.amount,
              comments: eachItem.description,
            },
          },
        };
        formatWAM = JSON.stringify(formatWAM);
        WorkOrderService.updateWAMEquipments(formatWAM)
          .then(res => {
            let logsPayload = {
              WorkActivityId: singleWODetails?.WorkActivityId,
              OldStatus: singleWODetails?.WamStatusCode,
              NewStatus: singleWODetails?.WamStatusCode,
              SouceSystem: "IFSMOBILE",
              UpdatedBy: bearer?.userName,
              //   updatedJson:""
              IsWAMTransaction: "true",
              UpdatedJson: formatWAM,
            };
            WorkOrderService.UpdateIFSdbLogs(logsPayload)
              .then(resp => {
                console.log(resp, resp.data, "response - UpdateIFSdbLogs");
              })
              .catch(err => {
                console.log(err, "error occured");
              });
          })
          .catch(err => {
            console.log(err);
          });
      });
    }

    if (Object.keys(iFSLbrdata)?.length) {
      let arrayedValues = Object.values(iFSLbrdata); //converting to array
      const today = moment();
      const formattedToday = today.format("YYYY-MM-DD");
      arrayedValues.map(items => {
        console.log(items, "TGHJIUGVB2345");
        let formatIFS = {
          woResourceId: items["woResourceId"],
          ActualDuration: items["duration"] ? items["duration"] : "0",
          resourceName: items["resourceName"] ? items["resourceName"] : "",
        };
        WorkOrderService.updateIFSdatabase(formatIFS)
          .then(res => {
            let formatWAM = {
              chargeType: "W1AC",
              date: formattedToday,
              activityId: singleWODetails?.WamWorkActivityId,
              employeeExternalId: singleWODetails?.CrewId, //user ID of IFS(CrewId)
              hours: items["duration"] ? items["duration"] : "0",
              regularOvertime: "W1RE",
              craft: items["resourceId"] ? items["resourceId"] : "",
            };
            let formatWAMInp = {
              input: {
                timeSheetDetails: {
                  ...formatWAM,
                },
              },
            };

            WorkOrderService.updateWAM(formatWAMInp)
              .then(res => {
                console.log(res, "wam response");
                let logsPayload = {
                  WorkActivityId: singleWODetails?.WorkActivityId,
                  OldStatus: singleWODetails?.WamStatusCode,
                  NewStatus: singleWODetails?.WamStatusCode,
                  SouceSystem: "IFSMOBILE",
                  UpdatedBy: bearer?.userName,
                  //   updatedJson:""
                  IsWAMTransaction: "true",
                  UpdatedJson: formatWAMInp,
                };
                WorkOrderService.UpdateIFSdbLogs(logsPayload)
                  .then(resp => {
                    console.log(resp, resp.data, "response - UpdateIFSdbLogs");
                  })
                  .catch(err => {
                    console.log(err, "error occured");
                  });
              })
              .catch(err => {
                console.error(err);
              });
          })
          .catch(err => {
            console.error(err);
          });
        console.log(formatIFS, "TGHJIUGVB2345");
      });
    }

    if (Object.keys(iFSEqdata)?.length) {
      let arrayedValues = Object.values(iFSEqdata); //converting to array
      const today = moment();
      const formattedToday = today.format("YYYY-MM-DD");
      arrayedValues.map(items => {
        console.log(items, "TGHJIUGVB2345111111");
        let formatIFS = {
          woResourceId: items["woResourceId"],
          ActualDuration: items["duration"] ? items["duration"] : "0",
          resourceName: items["resourceName"] ? items["resourceName"] : "",
        };
        WorkOrderService.updateIFSdatabase(formatIFS)
          .then(res => {
            console.log(res);
            // let formatWAM = {
            //   chargeType: "W1AC",
            //   date: formattedToday,
            //   activityId: singleWODetails?.WamWorkActivityId,
            //   employeeExternalId: singleWODetails?.CrewId, //user ID of IFS(CrewId)
            //   hours: items["duration"] ? items["duration"] : "0",
            //   regularOvertime: "W1RE",
            //   craft: items["resourceId"] ? items["resourceId"] : "",
            // };
            let formatWAM = {
              chargeDate: formattedToday,
              activityId: singleWODetails?.WamWorkActivityId,
              resourceTypeId: items["resourceId"],
              quantity: items["duration"] ? items["duration"] : "0",
            };
            let formatWAMInp = {
              input: {
                resourceChargeDetails: {
                  ...formatWAM,
                },
              },
            };

            WorkOrderService.updateWAMEquipments(formatWAMInp)
              .then(res => {
                console.log(res);
                let logsPayload = {
                  WorkActivityId: singleWODetails?.WorkActivityId,
                  OldStatus: singleWODetails?.WamStatusCode,
                  NewStatus: singleWODetails?.WamStatusCode,
                  SouceSystem: "IFSMOBILE",
                  UpdatedBy: bearer?.userName,
                  //   updatedJson:""
                  IsWAMTransaction: "true",
                  UpdatedJson: formatWAMInp,
                };
                WorkOrderService.UpdateIFSdbLogs(logsPayload)
                  .then(resp => {
                    console.log(resp, resp.data, "response - UpdateIFSdbLogs");
                  })
                  .catch(err => {
                    console.log(err, "error occured");
                  });
              })
              .catch(err => {
                console.log(err, "wam response-updateWAMEquipments");
              });
          })
          .catch(err => {
            console.error(err);
          });
        console.log(formatIFS, "TGHJIUGVB2345");
      });
    }

    if (wamIFSLbrdata?.length > 0) {
      wamIFSLbrdata?.forEach(each => {
        let formatWAM = {
          input: {
            timeSheetDetails: {
              ...each,
            },
          },
        };
        WorkOrderService.updateWAM(formatWAM)
          .then(res => {
            console.log(res);
            let logsPayload = {
              WorkActivityId: singleWODetails?.WorkActivityId,
              OldStatus: singleWODetails?.WamStatusCode,
              NewStatus: singleWODetails?.WamStatusCode,
              SouceSystem: "IFSMOBILE",
              UpdatedBy: bearer?.userName,
              //   updatedJson:""
              IsWAMTransaction: "true",
              UpdatedJson: formatWAM,
            };
            WorkOrderService.UpdateIFSdbLogs(logsPayload)
              .then(resp => {
                console.log(resp);
              })
              .catch(err => {
                console.log(err);
              });
          })
          .catch(err => {
            console.log(err);
          });
      });
    }
    if (wamIFSEqdata?.length > 0) {
      wamIFSEqdata?.forEach(each => {
        let formatWAM = {
          input: {
            resourceChargeDetails: {
              ...each,
            },
          },
        };
        console.log(formatWAM, "formatWAM");
        WorkOrderService.updateWAMEquipments(formatWAM)
          .then(res => {
            let logsPayload = {
              WorkActivityId: singleWODetails?.WorkActivityId,
              OldStatus: singleWODetails?.WamStatusCode,
              NewStatus: singleWODetails?.WamStatusCode,
              SouceSystem: "IFSMOBILE",
              UpdatedBy: bearer?.userName,
              //   updatedJson:""
              IsWAMTransaction: "true",
              UpdatedJson: formatWAM,
            };
            WorkOrderService.UpdateIFSdbLogs(logsPayload)
              .then(resp => {
                console.log(resp, resp.data, "response - UpdateIFSdbLogs");
              })
              .catch(err => {
                console.log(err, "error occured");
              });
          })
          .catch(err => {});
      });
    }

    WorkOrderService.UpdateServiceHistory(
      singleWODetails?.WorkActivityId,
      JSON.stringify(serviceArray),
    )
      .then(resp => {
        WorkOrderService.UpdateWAStatus({
          WorkActivityId: singleWODetails?.WorkActivityId,
          WorkStatus: "CO",
          ActualEndDate: new Date(),
          WamStatusCode: "COMPLETE",
        }).then(res1 => {
          WorkOrderService.UpdateWrokActivity(finalApiData).then(response => {
            if (response?.status == 201) {
              setconfirmNote(t("WORK_ACTIVITY_UPDATE_MSG"));
              setConfirmModalType("workOrder");
              setConfirmationModal(true);
              setOTPModal(false);
              WorkOrderService.getAllWorkActivities(
                singleWODetails?.WorkOrderId, // "455",
              ).then(responseUpdate => {
                let updatedObj =
                  responseUpdate.workOrders[0]?.workActivities?.find(
                    e => e?.WorkActivityId == singleWODetails?.WorkActivityId,
                  );

                console.log(updatedObj, "Updated Status successfully");
                setSingleWODetails(updatedObj);
                // fetchWorkOrderList();
                setsubmitLoading(false);
              });
              let logsPayload = {
                WorkActivityId: singleWODetails?.WorkActivityId,
                OldStatus: singleWODetails?.WamStatusCode,
                NewStatus: "CO",
                SouceSystem: "IFSMOBILE",
                UpdatedBy: bearer?.userName,
                //   updatedJson:""
                IsWAMTransaction: "true",
                UpdatedJson: finalApiData,
              };
              WorkOrderService.UpdateIFSdbLogs(logsPayload)
                .then(resp => {
                  console.log(resp, resp.data, "response - UpdateIFSdbLogs");
                })
                .catch(err => {
                  console.log(err, "error occured");
                });

                let logsPayloadIFS = {
                  WorkActivityId: singleWODetails?.WorkActivityId,
                  OldStatus: singleWODetails?.WorkStatus,
                  NewStatus: "CO",
                  SouceSystem: "IFSMOBILE",
                  UpdatedBy: bearer?.userName,
                };
                WorkOrderService.UpdateIFSdbLogs(logsPayloadIFS)
                  .then(resp => {
                    console.log(resp, resp.data, "response - UpdateIFSdbLogs");
                  })
                  .catch(err => {
                    console.log(err, "error occured");
                  });
            } else if (response.status === 400) {
              setsubmitLoading(false);
              //setOTPError(res.message);
              let logsPayload = {
                WorkActivityId: singleWODetails?.WorkActivityId,
                OldStatus: singleWODetails?.WamStatusCode,
                NewStatus: singleWODetails?.WamStatusCode,
                SouceSystem: "IFSMOBILE",
                UpdatedBy: bearer?.userName,
                //   updatedJson:""
                IsWAMTransaction: "true",
                UpdatedJson: finalApiData,
              };
              WorkOrderService.UpdateIFSdbLogs(logsPayload)
                .then(resp => {
                  console.log(resp, resp.data, "response - UpdateIFSdbLogs");
                })
                .catch(err => {
                  console.log(err, "error occured");
                });
            }
          });
        });
        // submitActivityClick(singleWODetails?.WorkActivityId, 1);
        fetchWorkActivityList();
      })
      .catch(error => {
        setsubmitLoading(false);
      });

    // let logsPayload = {
    //   WorkActivityId: singleWODetails?.WorkActivityId,
    //   OldStatus: singleWODetails?.WorkStatus,
    //   NewStatus: "CO",
    //   SouceSystem: "IFSMOBILE",
    //   UpdatedBy: bearer?.userName,
    //   //   updatedJson:""
    // };
    // WorkOrderService.UpdateIFSdbLogs(logsPayload)
    //   .then(resp => {
    //     console.log(resp, resp.data, "response - UpdateIFSdbLogs");
    //   })
    //   .catch(err => {
    //     console.log(err, "error occured");
    //   });
  };
  const submitActivityClick = (workActivity, ind) => {
    console.log(
      "workActivity.WorkOrderActivityChecklist",
      workActivity.WorkOrderActivityChecklist,
    );
    const allYesAnswers = workActivity.WorkOrderActivityChecklist.every(
      checklist =>
        checklist.answer !== "" ||
        checklist.answer !== undefined ||
        checklist.answer !== "no" ||
        checklist.answer !== null,
    );
    const workStatus = allYesAnswers ? "CO" : "I";

    const newArray = {
      WorkOrderId: workActivity.WorkOrderId,
      WorkOrderType: wAListCopy.WorkOrderType,
      workActivities: [
        {
          WorkActivityId: workActivity.WorkActivityId,
          WorkStatus: "CO",
          Comments: workActivity.Comments,
          ActualStartDate: moment(
            workActivity.ActualStartDate,
            "DD-MM-YYYY HH:mm A",
          ).format("YYYY-MM-DD HH:mm:ss"),
          ActualEndDate: moment(
            workActivity.ActualEndDate,
            "DD-MM-YYYY HH:mm A",
          ).format("YYYY-MM-DD HH:mm:ss"),
          WorkOrderId: workActivity.WorkOrderId,
          WorkOrderActivityChecklist:
            workActivity.WorkOrderActivityChecklist.map(checklist => ({
              activityChecklistId: checklist.activityChecklistId,
              answer:
                checklist.dataType === "number"
                  ? parseInt(checklist.answer)
                  : checklist.dataType === "string" ||
                    checklist.dataType === "boolean"
                  ? checklist.answer !== null
                    ? checklist.answer
                    : "no"
                  : checklist.dataType === "asset_url"
                  ? "/C:/Users/<USER>/Desktop/Apples.png"
                  : info[checklist.label],
              WorkActivityId: workActivity.WorkActivityId,
              WorkOrderId: workActivity.WorkOrderId,
              label: checklist.label,
              checklistType: checklist.checklistType,
              isCustom: checklist.isCustom,
            })),
        },
      ],
    };

    console.log(JSON.stringify(newArray, null, 2));

    //return false;
    if (newArray) {
      workActitityAPIcall(newArray);
      //return false;
      //console.log("OTPVerification...", OTPVerification);
      // setUpdateWAObj(newArray);
      // requestOTP();
      // if (OTPVerification) {
      //   //console.log("newArray..otp...", newArray);
      // }
    }
  };
  const workActitityAPIcall = async newArray => {
    try {
      setActivityLoading(true);
      const res = await WorkOrderService.workActitityAPIcall(newArray);

      if (res.status === 201) {
        setconfirmNote(t("WORK_ACTIVITY_UPDATE_MSG"));
        setConfirmModalType("workOrder");
        setConfirmationModal(true);
        setOTPModal(false);
      }
      if (res.status === 400) {
        console.log(res.message);
        setOTPError(res.message);
      }
    } catch (err) {
      console.log(err, "Error in fetching in request otp");
    } finally {
      setActivityLoading(false);
    }
  };

  const handleAssetView = assetId => {
    if (assetId) {
      const ROUTE = "Assets";
      setSelectedItem(ROUTE);
      dispatch(servicePath(ROUTE));
      navigation.navigate(ROUTE, { assetId: assetId });
    }
  };
  const onDismissSnackBar = () => setVisibleSnack(false);

  const requestOTP = async () => {
    try {
      setLoading(true);
      //customerNumber = 0;

      // if (customerNumber !== undefined) {
      const res = await WorkOrderService.requestOTP(customerNumber);

      if (res.isOk) {
        setconfirmNote(t("WORK_ORDER_UPDATE_MSG"));
        setConfirmModalType("workOrder");
        setConfirmationModal(false);
        setOTPModal(true);
        let newArray = {
          WorkOrderId: singleWODetails?.WorkOrderId,
          updatedBy: 2,
          Comments: "",
        };
        setUpdateWAObj(newArray);
      }
      if (res.status === 400) {
        console.log(res.message);
        setOTPError(res.message);
      }
      // } else {
      //   setCustomerNumberError(true);
      //   setCustomerNumberText("Customer Number Required");
      // }
    } catch (err) {
      console.log(err, "Error in fetching in request otp");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (changeindex != null) {
      if (actualStartDate || actualEndDate) {
        if (wAListCopy) {
          if (wAListCopy?.workActivities[changeindex]) {
            let updatedWAListCopy = { ...wAListCopy };
            updatedWAListCopy.workActivities = [
              {
                ...wAListCopy.workActivities[changeindex],
                ActualStartDate: moment(
                  actualStartDate,
                  "DD-MM-YYYY HH:mm A",
                ).format("YYYY-MM-DD HH:mm:ss"),
                ActualEndDate: moment(
                  actualEndDate,
                  "DD-MM-YYYY h:mm A",
                ).format("YYYY-MM-DD HH:mm:ss"),
              },
              ...wAListCopy.workActivities.slice(1),
            ];
            console.log("YYYYYYYYYYYYYYYYYYYYYYYYYYY");
            setAllWAListCopy(prevWorkOrderObj => {
              const updatedWorkOrderObj = { ...prevWorkOrderObj };
              const workActivity =
                updatedWorkOrderObj?.workActivities[changeindex];

              if (workActivity) {
                workActivity.ActualStartDate = actualStartDate; // replace with your startdate
                workActivity.ActualEndDate = actualEndDate; // replace with your enddate
              }
              debugger;
              return updatedWorkOrderObj;
            });

            //calculatePlannedDuration();
          }
        }
      }
    }
  }, [changeindex, actualStartDate, actualEndDate]);
  useEffect(() => {}, [wAListCopy]);
  useEffect(() => {
    calculatePlannedDuration();
  }, [wAListCopy]);
  const calculatePlannedDuration = () => {
    if (actualStartDate && actualEndDate) {
      // Assuming the date format is DD-MM-YYYY HH:mm A
      const startDateParts = actualStartDate.split(/[\s:-]/);
      const endDateParts = actualEndDate.split(/[\s:-]/);

      // Month is zero-based, so subtract 1 from the month value
      const startDate = new Date(
        startDateParts[2],
        startDateParts[1] - 1,
        startDateParts[0],
        startDateParts[3],
        startDateParts[4],
      );

      const endDate = new Date(
        endDateParts[2],
        endDateParts[1] - 1,
        endDateParts[0],
        endDateParts[3],
        endDateParts[4],
      );

      const timeDifference = endDate - startDate;
      const daysDifference = Math.floor(timeDifference / (1000 * 3600 * 24));
      const hoursDifference = Math.floor(
        (timeDifference % (1000 * 3600 * 24)) / (1000 * 3600),
      );
      setActualDuration({ days: daysDifference, hours: hoursDifference });
    } else {
      console.log("Invalid start or end date");
    }
    if (changeindex === 0) {
      if (
        wAListCopy?.workActivities?.[changeindex].PlannedStartDate &&
        wAListCopy?.workActivities?.[changeindex].PlannedEndDate
      ) {
        if (true) {
          const plannedStartDate = moment(
            wAListCopy?.workActivities[changeindex].PlannedStartDate,
          );
          const plannedEndDate = moment(
            wAListCopy?.workActivities[changeindex].PlannedEndDate,
          );
          // const startDate = new Date(
          //   wAListCopy?.workActivities[changeindex].PlannedStartDate,
          // );
          // const endDate = new Date(
          //   wAListCopy?.workActivities[changeindex].PlannedEndDate,
          // );
          // const timeDifference = endDate - startDate;
          // const daysDifference = timeDifference / (1000 * 3600 * 24);
          // console.log(`Duration: ${daysDifference} days`);
          const diff = moment(plannedEndDate).diff(plannedStartDate, "hours");

          setPlannedDuration(Number(diff) ? diff.toFixed(2) : "");
        }
        if (
          wAListCopy?.workActivities?.[changeindex].ActualStartDate &&
          wAListCopy?.workActivities?.[changeindex].ActualEndDate
        ) {
          const startDate = new Date(
            wAListCopy?.workActivities[changeindex].ActualStartDate,
          );
          const endDate = new Date(
            wAListCopy?.workActivities[changeindex].ActualEndDate,
          );
          const timeDifference = endDate - startDate;
          const daysDifference = timeDifference / (1000 * 3600 * 24);
          console.log(`Duration: ${daysDifference} days`);
          //setActualDuration(daysDifference);
        }
      }
    } else {
      if (wAListCopy) {
        if (
          wAListCopy?.workActivities?.[changeindex]?.PlannedStartDate &&
          wAListCopy?.workActivities?.[changeindex]?.PlannedEndDate
        ) {
          const startDate = new Date(
            wAListCopy?.workActivities[changeindex].PlannedStartDate,
          );
          const endDate = new Date(
            wAListCopy?.workActivities[changeindex].PlannedEndDate,
          );
          const timeDifference = endDate - startDate;
          const daysDifference = timeDifference / (1000 * 3600 * 24);
          //console.log(`Duration: ${daysDifference} days`);

          setPlannedDuration(daysDifference);
        }
        if (
          wAListCopy?.workActivities?.[changeindex]?.ActualStartDate &&
          wAListCopy?.workActivities?.[changeindex]?.ActualEndDate
        ) {
          const startDate = new Date(
            wAListCopy?.workActivities[changeindex].ActualStartDate,
          );
          const endDate = new Date(
            wAListCopy?.workActivities[changeindex].ActualEndDate,
          );
          const timeDifference = endDate - startDate;
          const daysDifference = timeDifference / (1000 * 3600 * 24);
          //setActualDuration(daysDifference);
        }
      }
    }
  };
  useEffect(() => {}, [wAListCopy, changeindex]);

  useEffect(() => {}, [wAList, wAListCopy, info]);

  const commentHandler = ind => comments => {
    const updatedWAListCopy = { ...wAListCopy };
    const updatedWorkActivities = [...updatedWAListCopy.workActivities];
    updatedWorkActivities[ind].Comments = comments;
    updatedWAListCopy.workActivities = updatedWorkActivities;
    setAllWAListCopy(updatedWAListCopy);
  };

  const handlerChecklist = (index, ind, enteredVal, checklistItem) => {
    const updatedWADetails = { ...wAListCopy };
    const updatedChecklist = updatedWADetails.workActivities[
      ind
    ].WorkOrderActivityChecklist.map(obj =>
      obj.activityChecklistId === checklistItem.activityChecklistId
        ? { ...obj, answer: enteredVal }
        : obj,
    );
    updatedWADetails.workActivities[ind].WorkOrderActivityChecklist =
      updatedChecklist;

    if (info) {
      const updatedInfo = { ...info };
      updatedInfo[checklistItem.label] = enteredVal;

      setInfo(updatedInfo);
    }

    if (infoAsset) {
      const updatedInfo = { ...infoAsset };
      updatedInfo[checklistItem.label] = enteredVal;

      setInfoAsset(updatedInfo);
    }
    // updatedWADetails.workActivities[ind].WorkOrderActivityChecklist =
    //   updatedChecklist;
    // }
    //setAllWAListCopy(updatedWADetails);
  };

  useEffect(() => {
    if (wAListCopy?.workActivities?.[wAListCopy.workActivities?.length - 1]) {
      setAcitvityId(
        wAListCopy?.workActivities?.[wAListCopy.workActivities?.length - 1][
          "WamWorkActivityId"
        ],
      );
    }
  }, [wAListCopy?.workActivities?.[wAListCopy.workActivities?.length - 1]]);

  useEffect(() => {}, [info, infoAsset, addressLoadAsset]);

  useEffect(() => {
    let newshArray = [];
    const completionDateTime = moment().format("YYYY-MM-DD-HH.mm.ss");
    setServiceArray(finalShData);
    if (finalShData?.length > 0) {
      finalShData?.forEach((each, index) => {
        let aaa = {
          serviceHistoryType: each?.serviceHistoryType,
          serviceHistoryCompletion: each?.questionnaireDetails,
          effectiveDateTime: completionDateTime,
        };
        newshArray.push(aaa);
      });
    }
    let apiDataFinal = {
      input: {
        activityId: acitvityId,
        completionDateTime,
        completion: {
          assetLocationAssets: {
            assetLocationAssetList: {
              nodeId: singleWODetails?.AssetLocationId,
              assetId: singleWODetails.AssetId,
              serviceHistories: {
                serviceHistoryList: [...newshArray],
              },
            },
          },
        },
      },
    };
    setFinalApiData(apiDataFinal);
  }, [finalShData]);

  useEffect(() => {
    if (wAListCopy?.workActivities) {
      let acLength = singleWODetails.WorkActivityId;
      let isOtherThere = wAListCopy.workActivities
        ?.find(e => e?.WorkActivityId == acLength)
        .WorkOrderResources?.find(e => e?.resourceClass == "OTHER");
      let isStoreThere = wAListCopy.workActivities
        ?.find(e => e?.WorkActivityId == acLength)
        .WorkOrderResources?.find(e => e?.resourceClass == "STOREROOM");
      let isEquipThere = wAListCopy.workActivities
        ?.find(e => e?.WorkActivityId == acLength)
        .WorkOrderResources?.find(e => e?.resourceClass == "MATERIAL");
      let isLabourThere = wAListCopy.workActivities
        ?.find(e => e?.WorkActivityId == acLength)
        .WorkOrderResources?.find(e => e?.resourceClass == "CRAFT");

      setOtherShow(isOtherThere);
      setstoreShow(isStoreThere);
      setEquipShow(isEquipThere);
      setLabourShow(isLabourThere);
    }
  }, [wAListCopy.workActivities]);

  useEffect(() => {
    let serviceHisArrayIsThere = wAListCopy?.workActivities?.find(
      e => e?.WorkActivityId == singleWODetails.WorkActivityId,
    ).WorkOrderActivityChecklist?.[0]?.serviceHistory;

    if (serviceHisArrayIsThere) {
      setServiceShow(serviceHisArrayIsThere);
    } else {
      setServiceShow();
    }
  }, [singleWODetails, wAListCopy]);

  useEffect(() => {
    // if (expenseItems.length > 1) {
    //   // If length is more than 1, then there will be 1 record added.
    //   const allExceptLast = expenseItems.slice(0, -1);
    //   console.log(allExceptLast, "IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII-1210");
    //   allExceptLast?.map((eachItem, index) => {
    //     let formatWAM = {
    //       input: {
    //         resourceChargeDetails: {
    //           chargeDate: formattedDate,
    //           activityId: singleWODetails?.WamWorkActivityId,
    //           resourceTypeId: "291656239099", //as of now hardcoded.
    //           quantity: eachItem?.amount,
    //           comments: eachItem.description,
    //         },
    //       },
    //     };
    //     WorkOrderService.updateWAMEquipments(formatWAM)
    //       .then(res => {
    //         console.log(
    //           actualData,
    //           "Expenses-- IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII 116",
    //         );
    //         console.log(res, "Expenses--IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII E116");
    //       })
    //       .catch(err => {
    //         console.log(err, "Expenses--IIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIIII E116");
    //       });
    //   });
    // }
  }, [expenseItems]);

  useEffect(() => {
    //rules for disabling.
    //done-0.If Actual end time, actual star time is not provided, and actual start time is greater than actual end time.
    //done- 1. if the Activity is not in InProgress starte.
    //done-2.If startDate time is less than end datetime.
    //done-3. if the Actual duration entered in Labour,Equipment grid less than tha entered Actual time, Actual end time.
    //4.If service history is not filled.
    //done-5.If Activity is already completed.

    if (
      actualStartDate &&
      actualStartDate != "Invalid date" &&
      actualEndDate &&
      actualEndDate != "Invalid date"
    ) {
      let lbrTime = 0;
      let equiptime = 0;
      let initialValue = 0;
      if (Object.keys(iFSLbrdata)?.length > 0) {
        let arrayedLbrValues = Object.values(iFSLbrdata);
        lbrTime = arrayedLbrValues.reduce(
          (sum, item) => sum + parseFloat(item?.duration, 10),
          initialValue,
        );
      }
      if (Object.keys(iFSEqdata)?.length > 0) {
        let arrayedEqValues = Object.values(iFSEqdata);
        equiptime = arrayedEqValues.reduce(
          (sum, item) => sum + parseFloat(item?.duration, 10),
          initialValue,
        );
      }

      let total = (lbrTime ? lbrTime : 0) + (equiptime ? equiptime : 0);
      const format = "DD-MM-YYYY h:mm A";
      const startMoment = moment(actualStartDate, format);
      const endMoment = moment(actualEndDate, format);
      const updatedStartTime = startMoment.add(total, "hours");

      if (
        endMoment &&
        startMoment &&
        (singleWODetails?.WorkStatus != "CO" ||
          singleWODetails?.WorkStatus != "CL")
        // endMoment.isAfter(updatedStartTime)
      ) {
        if (total > 0) {
          if (endMoment.isAfter(updatedStartTime)) {
            //close the error
            setshowTimeErr(false);
            setshowTimeErrMsg();
            setDisableCompleteTime(false);
          } else {
            //open the error.
            // error: Namrata's message
            setshowTimeErr(true);
            setshowTimeErrMsg(t("TIME_ERROR"));
            setDisableCompleteTime(true);
          }
        } else {
          if (startMoment.isAfter(endMoment)) {
            //show error
            setshowTimeErr(true);
            setshowTimeErrMsg(
              "The actual start duration entered is greater than the actual end duration",
            );
            setDisableCompleteTime(true);

            setActualStartDate();
            //error: start time is greater than  end time
          } else {
            setshowTimeErr(false);
            setshowTimeErrMsg();

            setDisableCompleteTime(false);
            //close the error
            //handle the logic here..
            //if startdateTime+hours>=end <==> do not show error
          }
        }

        //  setshowTimeErr(false);
      } else {
        //clode error
        setshowTimeErr(false);
        setshowTimeErrMsg();

        setDisableCompleteTime(true);
      }
    } else {
      setDisableCompleteTime(true);
    }
  }, [actualEndDate, actualStartDate, iFSEqdata, iFSLbrdata]);

  useEffect(() => {
    if (singleWODetails.WorkActivityId) {
      let currentAsset = wAListCopy?.workActivities?.find(
        e => e?.WorkActivityId == singleWODetails.WorkActivityId,
      ).Assets;

      const result = currentAsset?.map(itemA =>
        assetDataFrid?.find(itemB => itemB.assetId === itemA.AssetId),
      );
      setCurrentAsset(result);
      console.log(result, "currentAsset0000000000000000000000000");
    }
  }, [wAListCopy]);

  return (
    <View>
      <Card style={styles.card}>
        <TouchableOpacity
          style={[styles.backButtonWrapper, styles.paddingRight]}
          onPress={handleBack}>
          <FontAwesome
            name="chevron-left"
            color={GlobalStyles.colors.eWhite.base}
            style={{ fontFamily: "NotoSans-Thin", marginTop: 2 }}
            size={22}
          />
          <Text
            style={{
              color: GlobalStyles.colors.eWhite.base,
              fontSize: 12,
              fontWeight: "700",
              fontFamily: "NotoSans-Bold",
            }}>
            {singleWODetails?.WamRefNum} {t("WORK_ACTIVITY_DETAILS")}
          </Text>
        </TouchableOpacity>
      </Card>
      <ScrollView>
        {activitiesList ? (
          <>
            {[singleWODetails]?.map((item, ind) => {
              console.log(item, "YYYYYYYYYYYYYYYYYYYYwwwYYY");
              return (
                <View key={ind}>
                  <TouchableOpacity onPress={() => handleToggle(ind, item)}>
                    <View style={styles.container}>
                      <View style={styles.activityItem}>
                        <View style={styles.activityItemInner}>
                          <View style={styles.iconContainer}>
                            {item?.WorkStatus === "pending" ? (
                              // <FontAwesome5Icon
                              //   name=""
                              //   size={24}
                              //   color={GlobalStyles.colors.eTertiary.base}
                              // />
                              <Icon
                                name={"FS-Open-Tasks-icon"}
                                color={GlobalStyles.colors.eTertiary.base}
                                size={28}
                              />
                            ) : item?.WorkStatus === "CO" ? (
                              <Icon
                                name={"FS-Completed-Tasks-icon"}
                                color={GlobalStyles.colors.eSecondary.base}
                                size={28}
                              />
                            ) : item?.WorkStatus === "I" ? (
                              <Icon
                                name={"FS-Open-Tasks-icon"}
                                color={GlobalStyles.colors.eTertiary.base}
                                size={28}
                              />
                            ) : (
                              <Icon
                                name={"FS-Open-Tasks-icon"}
                                color={GlobalStyles.colors.eTertiary.base}
                                size={28}
                              />
                            )}
                          </View>
                          <View style={styles.contentContainer}>
                            <View
                              style={[
                                styles.displayFlex,
                                { gap: 10, paddingVertical: 3 },
                              ]}>
                              {/* <Text style={styles.headerStyleNumber}>
                          {singleWODetails?.WorkOrderCode}
                        </Text> */}
                              <Text style={styles.headerStyleNumber}>
                                {item?.Title
                                  ? item?.Title
                                  : item?.ActivityTypeDescr}
                              </Text>
                            </View>
                            <View style={styles.subHeaderRow}>
                              <Text style={[styles.subHeaderRowMinWidth]}>
                                {moment(
                                  item?.PlannedStartDate,
                                  "YYYY-MM-DD HH:mm:ss",
                                ).format("DD-MM-YYYY HH:mm:ss")}
                              </Text>
                              {/* <Text style={[styles.subHeaderPriority]}>
                          {singleWODetails?.Priority === "H"
                            ? "High"
                            : singleWODetails?.Priority}
                        </Text> */}
                              <Text style={[styles.subHeaderPriority]}>
                                {singleWODetails?.WorkStatus === "CO"
                                  ? "Completed"
                                  : singleWODetails?.WorkStatus === "I"
                                  ? "In Progress"
                                  : singleWODetails?.WorkStatus == "OH"
                                  ? "On Hold"
                                  : singleWODetails?.WorkStatus == "SC"
                                  ? "Scheduled"
                                  : singleWODetails?.WorkStatus == "CL"
                                  ? "Cancelled"
                                  : singleWODetails?.WorkStatus}
                              </Text>
                            </View>
                            <View style={styles.subHeaderRowStatus}>
                              {/* <Text style={[styles.subHeaderRowMinWidth]}>
                          {/* {singleWODetails?.route} 
                        </Text> */}
                            </View>
                          </View>
                        </View>
                        <View style={styles.arrowIconStyle}>
                          <FontAwesome5Icon
                            name={
                              expandedSections.includes(ind)
                                ? "chevron-down"
                                : "chevron-right"
                            }
                            color={GlobalStyles.colors.eMedium.base}
                            size={12}
                            fontFamily="NotoSans-Bold"
                          />
                        </View>
                      </View>
                    </View>
                    <View style={styles.lineStyle} />
                  </TouchableOpacity>
                  {expandedSections.includes(ind) && (
                    <>
                      <View>
                        <View style={styles.container}>
                          <View style={[styles.rowFlexContainer]}>
                            <View style={styles.labelWidth}>
                              <Text style={[styles.labelStyle]}>
                                {t("WORK_ORDER_NUMBER")}
                              </Text>
                            </View>
                            <View style={styles.flexWrap}>
                              <Text style={[styles.valueStyle]}>
                                {wAListCopy?.WorkOrderCode}
                              </Text>
                            </View>
                          </View>
                          <View style={styles.flexWrap}>
                            {wAListCopy?.workActivities[ind]
                              .PlannedStartDate ? (
                              <Text style={[styles.valueStyle]}>
                                {moment(
                                  wAListCopy?.workActivities[ind]
                                    .PlannedStartDate,
                                ).format(DATE_FORMATS.DATETIME)}
                              </Text>
                            ) : (
                              <Text style={[styles.valueStyle]}>-</Text>
                            )}
                          </View>
                          {/* <View style={[styles.rowFlexContainer]}>
                            <View style={styles.labelWidth}>
                              <Text style={[styles.labelStyle]}>
                                {t("ASSET_ID")}
                              </Text>
                            </View>
                            <View style={styles.flexWrapAsset}>
                              <Text style={[styles.valueStyleAsset]}>
                                {
                                  wAListCopy?.workActivities?.find(
                                    e =>
                                      e?.WorkActivityId ==
                                      singleWODetails.WorkActivityId,
                                  ).AssetId
                                }
                              </Text>
                              {wAListCopy?.workActivities?.find(
                                e =>
                                  e?.WorkActivityId ==
                                  singleWODetails.WorkActivityId,
                              ).AssetId && (
                                <View
                                  style={{
                                    alignSelf: "center",
                                    marginLeft: 5,
                                  }}>
                                  <FontAwesomeEye
                                    name="eye"
                                    color={GlobalStyles.colors.ePrimary.base}
                                    size={22}
                                    onPress={() => {
                                      handleAssetView(
                                        wAListCopy?.workActivities?.find(
                                          e =>
                                            e?.WorkActivityId ==
                                            singleWODetails.WorkActivityId,
                                        ).AssetId,
                                      );
                                    }}
                                  />
                                </View>
                              )}
                            </View>
                          </View> */}
                          {/* <View style={[styles.rowFlexContainer]}>
                            <View style={styles.labelWidth}>
                              <Text style={[styles.labelStyle]}>
                                {t("ASSET_LOCATION")}
                              </Text>
                            </View>
                            <View style={styles.flexWrap}>
                              <Text style={[styles.valueStyle]}>
                                {
                                  wAListCopy?.workActivities?.find(
                                    e =>
                                      e?.WorkActivityId ==
                                      singleWODetails.WorkActivityId,
                                  ).AssetLocation
                                }
                              </Text>
                            </View>
                          </View> */}
                          <View style={[styles.rowFlexContainer]}>
                            <View style={styles.labelWidth}>
                              <Text style={[styles.labelStyle]}>
                                {t("PLANNED_START_DATE")}
                              </Text>
                            </View>
                            <View style={styles.flexWrap}>
                              <Text style={[styles.valueStyle]}>
                                {moment(
                                  item?.PlannedStartDate,
                                  "YYYY-MM-DD HH:mm:ss",
                                ).format("DD-MM-YYYY HH:mm:ss")}
                              </Text>
                            </View>
                          </View>
                          <View style={styles.flexWrap}>
                            {wAListCopy?.workActivities[ind].PlannedEndDate ? (
                              <Text style={[styles.valueStyle]}>
                                {moment(
                                  wAListCopy?.workActivities[ind]
                                    .PlannedEndDate,
                                ).format(DATE_FORMATS.DATETIME)}
                              </Text>
                            ) : (
                              <Text>-</Text>
                            )}
                          </View>

                          <View style={[styles.rowFlexContainerDate]}>
                            <View style={styles.btnContainer}>
                              <Button
                                onPress={() => {
                                  onStatusClick(singleWODetails, "OH");
                                }}
                                buttonbgColor={[
                                  styles.cancelBg,
                                  disableonHold && styles.disabledCancleStyle,
                                  !disableonHold && styles.cancelBgonHold,
                                ]}
                                textColor={[
                                  disableonHold
                                    ? styles.disableColor
                                    : styles.textColor,
                                ]}
                                disabled={disableonHold}>
                                {t("ON_HOLD")}
                                {progressLoad == "OH" && (
                                  <ActivityIndicator
                                    align="center"
                                    size={13}
                                    color={GlobalStyles.colors.eWhite.base}
                                  />
                                )}
                              </Button>
                              <Button
                                buttonbgColor={[
                                  styles.buttonBgColor,
                                  disableStart && styles.disabledCancleStyle,
                                ]}
                                textColor={[
                                  disableStart
                                    ? styles.disableColor
                                    : styles.textColor,
                                ]}
                                //  disabled={disableStart}
                                onPress={() => {
                                  onStatusClick(singleWODetails, "I");
                                }}>
                                {t("Start Activity")}
                                {progressLoad == "I" && (
                                  <ActivityIndicator
                                    align="center"
                                    size={13}
                                    color={GlobalStyles.colors.eWhite.base}
                                  />
                                )}
                                {/* {isLoading && (
                                  <ActivityIndicator
                                    align="center"
                                    size={13}
                                    color={GlobalStyles.colors.eWhite.base}
                                  />
                                )} */}
                              </Button>
                            </View>
                          </View>

                          <View style={[styles.rowFlexContainerDate]}>
                            {workModelType == "WA" ? (
                              <CalendarPicker
                                minDate={OffAvailableDates}
                                setCalendarDate={setActualStartDate}
                                //selectedDate={selectedDate}
                                setSelectedDate={setActualStartDate}
                                showCalendar={showCalendar}
                                setShowCalendar={setShowCalendar}
                                calendarDate={actualStartDate}
                                // requiredCardHeight={requiredHeight}
                                editable={disableStart}
                                setErrorOn={setErrorOn}
                                setAvailableOndates={setAvailableOndates}
                                setCalorText={setCalorText}
                                setTextDate={setTextDateOff}
                                outlinedLable={t("ACTUAL_START_DATE")}
                                index={ind}
                                setIndex={setIndex}
                              />
                            ) : (
                              <CalendarPickerWO
                                minDate={OffAvailableDates}
                                setCalendarDate={setActualStartDate}
                                //selectedDate={selectedDate}
                                setSelectedDate={setActualStartDate}
                                showCalendar={showCalendar}
                                setShowCalendar={setShowCalendar}
                                calendarDate={actualStartDate}
                                // requiredCardHeight={requiredHeight}
                                editable={disableStart}
                                setErrorOn={setErrorOn}
                                setAvailableOndates={setAvailableOndates}
                                setCalorText={setCalorText}
                                setTextDate={setTextDateOff}
                                outlinedLable={t("ACTUAL_START_DATE")}
                                index={ind}
                                setIndex={setIndex}
                              />
                            )}
                          </View>
                          {/* <View style={[styles.rowFlexContainer]}>
                          <View style={styles.labelWidth}>
                            <Text style={[styles.labelStyle]}>
                              {t("PLANNED_DURATION")}
                            </Text>
                          </View>
                          <View style={styles.flexWrap}>
                            <Text style={[styles.valueStyle]}>
                              {" "}
                              {plannedDuration}
                            </Text>
                          </View>
                        </View> */}
                          {/* <View style={[styles.rowFlexContainer]}>
                          <View style={styles.labelWidth}>
                            <Text style={[styles.labelStyle]}>
                              {t("ACTUAL_DURATION")}
                            </Text>
                          </View>
                          <View style={styles.flexWrap}>
                            <Text style={[styles.valueStyle]}>
                              {" "}
                              {actualDuration.days
                                ? actualDuration.days + " days"
                                : null}{" "}
                              {actualDuration.hours
                                ? actualDuration.hours + " hrs"
                                : null}
                              {actualDuration.minutes
                                ? actualDuration.minutes + " minutes"
                                : null}
                            </Text>
                          </View>
                        </View> */}
                        </View>
                      </View>
                      <View style={styles.containerDiv}>
                        {
                          <View
                            style={[
                              styles.rowFlexContainer,
                              {
                                paddingVertical: 10,
                                paddingLeft: 20,
                              },
                            ]}>
                            {/* <Text style={styles.labelHeader}>
                            {t("INFORMATION")}
                          </Text> */}
                          </View>
                        }
                        <View style={styles.lineStyleInfo} />
                        {
                          <>
                            {/* <View style={[styles.rowFlexContainer]}>
                            <View style={styles.labelWidth}>
                              <Text style={[styles.labelStyle]}>
                                {t("CONSUMER_NAME")}
                              </Text>
                            </View>
                            <View style={styles.flexWrap}>
                              <Text style={[styles.valueStyle]}>
                                {info?.ConsumerName}
                              </Text>
                            </View>
                          </View> */}
                            {info?.Address1 ? (
                              <>
                                <View style={[styles.rowFlexContainer]}>
                                  <View style={styles.labelWidth}>
                                    <Text style={[styles.labelStyle]}>
                                      {t("ADDRESS1")}
                                    </Text>
                                  </View>
                                  <View style={styles.flexWrap}>
                                    <Text style={[styles.valueStyle]}>
                                      {info?.Address1}
                                    </Text>
                                  </View>
                                </View>
                              </>
                            ) : null}
                            {info?.Address2 ? (
                              <>
                                <View style={[styles.rowFlexContainer]}>
                                  <View style={styles.labelWidth}>
                                    <Text style={[styles.labelStyle]}>
                                      {t("ADDRESS2")}
                                    </Text>
                                  </View>
                                  <View style={styles.flexWrap}>
                                    <Text style={[styles.valueStyle]}>
                                      {info?.Address2}
                                    </Text>
                                  </View>
                                </View>
                              </>
                            ) : null}
                            {info?.Address3 ? (
                              <>
                                <View style={[styles.rowFlexContainer]}>
                                  <View style={styles.labelWidth}>
                                    <Text style={[styles.labelStyle]}>
                                      {t("ADDRESS3")}
                                    </Text>
                                  </View>
                                  <View style={styles.flexWrap}>
                                    <Text style={[styles.valueStyle]}>
                                      {info?.Address3}
                                    </Text>
                                  </View>
                                </View>
                              </>
                            ) : null}
                            {info?.Address4 ? (
                              <>
                                <View style={[styles.rowFlexContainer]}>
                                  <View style={styles.labelWidth}>
                                    <Text style={[styles.labelStyle]}>
                                      {t("ADDRESS4")}
                                    </Text>
                                  </View>
                                  <View style={styles.flexWrap}>
                                    <Text style={[styles.valueStyle]}>
                                      {info?.Address4}
                                    </Text>
                                  </View>
                                </View>
                              </>
                            ) : null}
                            {consumnerDataLoad ? (
                              <>
                                <View
                                  style={[
                                    styles.rowFlexContainer,
                                    styles.marginVertical2,
                                  ]}>
                                  <View style={styles.labelWidth}>
                                    <Text style={[styles.labelStyle]}>
                                      {t("PHONE")}
                                    </Text>
                                  </View>
                                  <View style={styles.flexWrap}>
                                    <Text style={[styles.valueStyle]}>
                                      {/* {info?.Mobile} */}
                                      {consumnerData?.Mobile}
                                    </Text>
                                  </View>
                                </View>
                                <View style={[styles.rowFlexContainer]}>
                                  <View style={styles.labelWidth}>
                                    <Text style={[styles.labelStyle]}>
                                      {t("FULL_ADDRESS")}
                                    </Text>
                                  </View>
                                  <View style={styles.flexWrap}>
                                    <Text style={[styles.valueStyle]}>
                                      {/* {info?.FullAddress} */}
                                      {consumnerData?.FullAddress}
                                    </Text>
                                  </View>
                                </View>
                              </>
                            ) : null}
                          </>
                        }
                        {addressLoadAsset ? (
                          <>
                            <View style={[styles.rowFlexContainer]}>
                              <View style={styles.labelWidth}>
                                <Text style={[styles.labelStyle]}>
                                  {t("DEVICE_TYPE")}
                                </Text>
                              </View>
                              <View style={styles.flexWrap}>
                                <Text style={[styles.valueStyle]}>
                                  {infoAsset?.deviceType}
                                </Text>
                              </View>
                            </View>
                            <View style={[styles.rowFlexContainer]}>
                              <View style={styles.labelWidth}>
                                <Text style={[styles.labelStyle]}>
                                  {t("SERIAL_NUMBER")}
                                </Text>
                              </View>
                              <View style={styles.flexWrap}>
                                <Text style={[styles.valueStyle]}>
                                  {infoAsset?.serialNumber}
                                </Text>
                              </View>
                            </View>
                            <View style={[styles.rowFlexContainer]}>
                              <View style={styles.labelWidth}>
                                <Text style={[styles.labelStyle]}>
                                  {t("MANUFACTURER")}
                                </Text>
                              </View>
                              <View style={styles.flexWrap}>
                                <Text style={[styles.valueStyle]}>
                                  {infoAsset?.manufacturer}
                                </Text>
                              </View>
                            </View>
                            <View style={[styles.rowFlexContainer]}>
                              <View style={styles.labelWidth}>
                                <Text style={[styles.labelStyle]}>
                                  {t("MODEL")}
                                </Text>
                              </View>
                              <View style={styles.flexWrap}>
                                <Text style={[styles.valueStyle]}>
                                  {infoAsset?.model}
                                </Text>
                              </View>
                            </View>
                            <View style={[styles.rowFlexContainer]}>
                              <View style={styles.labelWidth}>
                                <Text style={[styles.labelStyle]}>
                                  {t("DEVICE_STATUS")}
                                </Text>
                              </View>
                              <View style={styles.flexWrap}>
                                <Text style={[styles.valueStyle]}>
                                  {infoAsset?.deviceStatus}
                                </Text>
                              </View>
                            </View>
                            <View
                              style={[
                                styles.rowFlexContainer,
                                styles.marginVertical2,
                              ]}>
                              <View style={styles.labelWidth}>
                                <Text style={[styles.labelStyle]}>
                                  {t("PHONE")}
                                </Text>
                              </View>
                              <View style={styles.flexWrap}>
                                <Text style={[styles.valueStyle]}>
                                  {infoAsset?.mobileNo}
                                </Text>
                              </View>
                            </View>
                            {infoAsset?.address1 !== null ? (
                              <>
                                <View style={[styles.rowFlexContainer]}>
                                  <View style={styles.labelWidth}>
                                    <Text style={[styles.labelStyle]}>
                                      {t("ADDRESS1")}
                                    </Text>
                                  </View>
                                  <View style={styles.flexWrap}>
                                    <Text style={[styles.valueStyle]}>
                                      {infoAsset?.address1}
                                    </Text>
                                  </View>
                                </View>
                              </>
                            ) : null}
                            {infoAsset?.address2 !== null ? (
                              <>
                                <View style={[styles.rowFlexContainer]}>
                                  <View style={styles.labelWidth}>
                                    <Text style={[styles.labelStyle]}>
                                      {t("ADDRESS2")}
                                    </Text>
                                  </View>
                                  <View style={styles.flexWrap}>
                                    <Text style={[styles.valueStyle]}>
                                      {infoAsset?.address2}
                                    </Text>
                                  </View>
                                </View>
                              </>
                            ) : null}
                            {infoAsset?.address3 !== null ? (
                              <>
                                <View style={[styles.rowFlexContainer]}>
                                  <View style={styles.labelWidth}>
                                    <Text style={[styles.labelStyle]}>
                                      {t("ADDRESS3")}
                                    </Text>
                                  </View>
                                  <View style={styles.flexWrap}>
                                    <Text style={[styles.valueStyle]}>
                                      {infoAsset?.address3}
                                    </Text>
                                  </View>
                                </View>
                              </>
                            ) : null}
                            {infoAsset?.address4 !== null ? (
                              <>
                                <View style={[styles.rowFlexContainer]}>
                                  <View style={styles.labelWidth}>
                                    <Text style={[styles.labelStyle]}>
                                      {t("ADDRESS4")}
                                    </Text>
                                  </View>
                                  <View style={styles.flexWrap}>
                                    <Text style={[styles.valueStyle]}>
                                      {infoAsset?.address4}
                                    </Text>
                                  </View>
                                </View>
                              </>
                            ) : null}
                            {infoAsset?.city !== null ? (
                              <>
                                <View style={[styles.rowFlexContainer]}>
                                  <View style={styles.labelWidth}>
                                    <Text style={[styles.labelStyle]}>
                                      {t("CITY")}
                                    </Text>
                                  </View>
                                  <View style={styles.flexWrap}>
                                    <Text style={[styles.valueStyle]}>
                                      {infoAsset?.city}
                                    </Text>
                                  </View>
                                </View>
                              </>
                            ) : null}

                            <>
                              <View style={[styles.rowFlexContainer]}>
                                <View style={styles.labelWidth}>
                                  <Text style={[styles.labelStyle]}>
                                    {t("COUNTRY")}
                                  </Text>
                                </View>
                                <View style={styles.flexWrap}>
                                  <Text style={[styles.valueStyle]}>
                                    {infoAsset?.county}
                                  </Text>
                                </View>
                              </View>
                            </>

                            <View
                              style={[
                                styles.rowFlexContainer,
                                styles.marginVertical2,
                              ]}>
                              <View style={styles.labelWidth}>
                                <Text style={[styles.labelStyle]}>
                                  {t("LOCATION")}
                                </Text>
                              </View>
                              <View style={styles.flexWrap}>
                                <Text style={[styles.valueStyle]}>
                                  <FontAwesome
                                    name="map-marker"
                                    size={20}
                                    color={GlobalStyles.colors.ePrimary.base}
                                  />{" "}
                                  {infoAsset?.geocodeLatitude},{" "}
                                  {infoAsset?.geocodeLongitude}
                                </Text>
                              </View>
                            </View>
                            {/* </View> */}
                          </>
                        ) : null}
                      </View>
                      <View style={styles.containerChecklist}>
                        <TouchableOpacity
                          style={{ marginTop: 10 }}
                          onPress={assetToggleHandler}>
                          <View style={styles.containerEquip}>
                            <View style={styles.activityItem}>
                              <View style={styles.activityItemInner}>
                                <Text style={styles.iconContainer}>
                                  {t("ASSETS")}
                                </Text>
                                <View style={styles.contentContainer}>
                                  <View
                                    style={[
                                      styles.displayFlex,
                                      { gap: 10, paddingVertical: 3 },
                                    ]}>
                                    {/* <Text style={styles.headerStyleNumber}>
                          {singleWODetails?.WorkOrderCode}
                        </Text> */}
                                  </View>
                                </View>
                              </View>
                              <View style={styles.arrowIconStyle}>
                                <FontAwesome5Icon
                                  name={
                                    assetToggle
                                      ? "chevron-down"
                                      : "chevron-right"
                                  }
                                  color={GlobalStyles.colors.eMedium.base}
                                  size={12}
                                  fontFamily="NotoSans-Bold"
                                />
                              </View>
                            </View>
                          </View>
                          <View style={styles.lineStyle} />
                        </TouchableOpacity>
                        <>
                          {assetToggle && (
                            <View style={{ paddingBottom: 10 }}>
                              <AssetsGrid
                                assetDataFrid={assetDataFrid}
                                assets={currentAsset}
                              />
                            </View>
                          )}
                        </>
                        <TouchableOpacity
                          style={{ marginTop: 10, marginBottom: 10 }}
                          onPress={labourToggleHandler}>
                          <View style={styles.containerEquip}>
                            <View style={styles.activityItem}>
                              <View style={styles.activityItemInner}>
                                <Text style={styles.iconContainer}>
                                  {t("LABOUR")}
                                </Text>
                                <View style={styles.contentContainer}>
                                  <View
                                    style={[
                                      styles.displayFlex,
                                      { gap: 10, paddingVertical: 3 },
                                    ]}>
                                    {/* <Text style={styles.headerStyleNumber}>
                          {singleWODetails?.WorkOrderCode}
                        </Text> */}
                                  </View>
                                </View>
                              </View>
                              <View style={styles.arrowIconStyle}>
                                <FontAwesome5Icon
                                  name={
                                    labourToggle
                                      ? "chevron-down"
                                      : "chevron-right"
                                  }
                                  color={GlobalStyles.colors.eMedium.base}
                                  size={12}
                                  fontFamily="NotoSans-Bold"
                                />
                              </View>
                            </View>
                          </View>
                          <View style={styles.lineStyle} />
                        </TouchableOpacity>
                        <>
                          {labourShow && labourToggle && (
                            <View style={{ paddingBottom: 10 }}>
                              <LabourTableGrid
                                workStatus={workStatus}
                                data={
                                  wAListCopy?.workActivities?.find(
                                    e =>
                                      e?.WorkActivityId ==
                                      singleWODetails.WorkActivityId,
                                  ).WorkOrderResources
                                }
                                setwamIFSLbrdata={setwamIFSLbrdata}
                                setIFSLbrdata={setIFSLbrdata}
                                wamIFSLbrdata={wamIFSLbrdata}
                                WamWorkActivityId={
                                  singleWODetails?.WamWorkActivityId
                                }
                                CrewId={singleWODetails?.CrewId}
                              />
                            </View>
                          )}
                        </>
                        <TouchableOpacity onPress={equipToggleHandler}>
                          <View style={styles.containerEquip}>
                            <View style={styles.activityItem}>
                              <View style={styles.activityItemInner}>
                                <Text style={styles.iconContainer}>
                                  {t("EQUIPMENT")}
                                </Text>
                                <View style={styles.contentContainer}>
                                  <View
                                    style={[
                                      styles.displayFlex,
                                      { gap: 10, paddingVertical: 3 },
                                    ]}>
                                    {/* <Text style={styles.headerStyleNumber}>
                          {singleWODetails?.WorkOrderCode}
                        </Text> */}
                                  </View>
                                </View>
                              </View>
                              <View style={styles.arrowIconStyle}>
                                <FontAwesome5Icon
                                  name={
                                    equipToggle
                                      ? "chevron-down"
                                      : "chevron-right"
                                  }
                                  color={GlobalStyles.colors.eMedium.base}
                                  size={12}
                                  fontFamily="NotoSans-Bold"
                                />
                              </View>
                            </View>
                          </View>
                          <View style={styles.lineStyle} />
                        </TouchableOpacity>
                        <>
                          {equipShow && equipToggle && (
                            <View style={{ paddingBottom: 10 }}>
                              <EquipmentTableGrid
                                setIFSEqpdata={setIFSEqpdata}
                                workStatus={workStatus}
                                data={
                                  wAListCopy?.workActivities?.find(
                                    e =>
                                      e?.WorkActivityId ==
                                      singleWODetails.WorkActivityId,
                                  ).WorkOrderResources
                                }
                                setwamIFSEqpdata={setwamIFSEqpdata}
                                setwamIFSQEqpdata={setwamIFSQEqpdata}
                                WamWorkActivityId={
                                  singleWODetails?.WamWorkActivityId
                                }
                                CrewId={singleWODetails?.CrewId}
                              />
                            </View>
                          )}
                        </>

                        <TouchableOpacity
                          style={{ marginTop: 10, marginBottom: 10 }}
                          onPress={storeToggleHandler}>
                          <View style={styles.containerEquip}>
                            <View style={styles.activityItem}>
                              <View style={styles.activityItemInner}>
                                <Text style={styles.iconContainer}>
                                  {t("MATERIAL")}
                                </Text>
                                <View style={styles.contentContainer}>
                                  <View
                                    style={[
                                      styles.displayFlex,
                                      { gap: 10, paddingVertical: 3 },
                                    ]}>
                                    {/* <Text style={styles.headerStyleNumber}>
                          {singleWODetails?.WorkOrderCode}
                        </Text> */}
                                  </View>
                                </View>
                              </View>
                              <View style={styles.arrowIconStyle}>
                                <FontAwesome5Icon
                                  name={
                                    storeToggle
                                      ? "chevron-down"
                                      : "chevron-right"
                                  }
                                  color={GlobalStyles.colors.eMedium.base}
                                  size={12}
                                  fontFamily="NotoSans-Bold"
                                />
                              </View>
                            </View>
                          </View>
                          <View style={styles.lineStyle} />
                        </TouchableOpacity>
                        <>
                          {storeShow && storeToggle && (
                            <View style={{ marginBottom: 10 }}>
                              <MaterialTableGrid
                                data={
                                  wAListCopy?.workActivities?.find(
                                    e =>
                                      e?.WorkActivityId ==
                                      singleWODetails.WorkActivityId,
                                  ).WorkOrderResources
                                }
                              />
                            </View>
                          )}
                        </>

                        <>
                          {false && (
                            <TouchableOpacity
                              style={{ marginBottom: 10 }}
                              onPress={otherToggleHandler}>
                              <View style={styles.containerEquip}>
                                <View style={styles.activityItem}>
                                  <View style={styles.activityItemInner}>
                                    <Text style={styles.iconContainer}>
                                      {t("OTHERS")}
                                    </Text>
                                    <View style={styles.contentContainer}>
                                      <View
                                        style={[
                                          styles.displayFlex,
                                          { gap: 10, paddingVertical: 3 },
                                        ]}>
                                        {/* <Text style={styles.headerStyleNumber}>
                          {singleWODetails?.WorkOrderCode}
                        </Text> */}
                                      </View>
                                    </View>
                                  </View>
                                  <View style={styles.arrowIconStyle}>
                                    <FontAwesome5Icon
                                      name={
                                        otherToggle
                                          ? "chevron-down"
                                          : "chevron-right"
                                      }
                                      color={GlobalStyles.colors.eMedium.base}
                                      size={12}
                                      fontFamily="NotoSans-Bold"
                                    />
                                  </View>
                                </View>
                              </View>
                              <View style={styles.lineStyle} />
                            </TouchableOpacity>
                          )}
                          {false && (
                            <>
                              {otherToggle && (
                                <View style={{ marginBottom: 10 }}>
                                  <DirectChargesGrid
                                    data={
                                      wAListCopy?.workActivities?.find(
                                        e =>
                                          e?.WorkActivityId ==
                                          singleWODetails.WorkActivityId,
                                      ).WorkOrderResources
                                    }
                                  />
                                </View>
                              )}
                            </>
                          )}
                        </>
                        <TouchableOpacity
                          style={{ marginBottom: 10 }}
                          onPress={serviceToggleHandler}>
                          <View style={styles.containerEquip}>
                            <View style={styles.activityItem}>
                              <View style={styles.activityItemInner}>
                                <Text style={styles.iconContainer}>
                                  {t("SERVICE_HISTORY")}
                                </Text>
                                <View style={styles.contentContainer}>
                                  <View
                                    style={[
                                      styles.displayFlex,
                                      { gap: 10, paddingVertical: 3 },
                                    ]}>
                                    {/* <Text style={styles.headerStyleNumber}>
                          {singleWODetails?.WorkOrderCode}
                        </Text> */}
                                  </View>
                                </View>
                              </View>
                              <View style={styles.arrowIconStyle}>
                                <FontAwesome5Icon
                                  name={
                                    serviceToggle
                                      ? "chevron-down"
                                      : "chevron-right"
                                  }
                                  color={GlobalStyles.colors.eMedium.base}
                                  size={12}
                                  fontFamily="NotoSans-Bold"
                                />
                              </View>
                            </View>
                          </View>
                          <View style={styles.lineStyle} />
                        </TouchableOpacity>
                        <>
                          {serviceShow && serviceToggle && (
                            <View style={{ paddingBottom: 10 }}>
                              <ServiceHostoryTableGrid
                                serviceToggle={serviceToggle}
                                items={items}
                                setItems={setItems}
                                checkboxData={checkboxData}
                                setCheckboxData={setCheckboxData}
                                safecheckboxData={safecheckboxData}
                                setSafecheckboxData={setSafeCheckboxData}
                                simpleTextData={simpleTextData}
                                setSimpleTextData={setSimpleTextData}
                                workStatus={workStatus}
                                disableStart={!allowCheckbox}
                                data={serviceShow}
                                setFinalShData={setFinalShData}
                              />
                            </View>
                          )}
                        </>
                        <TouchableOpacity
                          style={{ marginBottom: 10 }}
                          onPress={expenseToggleHandler}>
                          <View style={styles.containerEquip}>
                            <View style={styles.activityItem}>
                              <View style={styles.activityItemInner}>
                                <Text style={styles.iconContainer}>
                                  {t("Expenses")}
                                </Text>
                                <View style={styles.contentContainer}>
                                  <View
                                    style={[
                                      styles.displayFlex,
                                      { gap: 10, paddingVertical: 3 },
                                    ]}>
                                    {/* <Text style={styles.headerStyleNumber}>
                          {singleWODetails?.WorkOrderCode}
                        </Text> */}
                                  </View>
                                </View>
                              </View>
                              <View style={styles.arrowIconStyle}>
                                <FontAwesome5Icon
                                  name={
                                    expenseToggle
                                      ? "chevron-down"
                                      : "chevron-right"
                                  }
                                  color={GlobalStyles.colors.eMedium.base}
                                  size={12}
                                  fontFamily="NotoSans-Bold"
                                />
                              </View>
                            </View>
                          </View>
                          <View style={styles.lineStyle} />
                        </TouchableOpacity>
                        <>
                          {expenseToggle && (
                            <View style={{ paddingBottom: 10 }}>
                              <ActivityGrid
                                workStatus={workStatus}
                                expenseItems={expenseItems}
                                setExpenseItems={setExpenseItems}
                                data={
                                  wAListCopy?.workActivities?.find(
                                    e =>
                                      e?.WorkActivityId ==
                                      singleWODetails.WorkActivityId,
                                  ).WorkOrderResources
                                }
                              />
                            </View>
                          )}
                        </>

                        {/* {singleWODetails?.WorkOrderType ==
                        "Transformer Replacement" &&
                        wAListCopy?.workActivities[ind]?.Title.includes(
                          "Debrief",
                        ) && (
                          <>
                            <ActivityGrid />
                            <LabourTableGrid labourType={"Technician"} />
                            <EquipmentTableGrid />
                            <MaterialTableGrid />
                            <DirectChargesGrid />
                          </>
                        )} */}
                        {singleWODetails?.WorkOrderType ==
                          "Transformer Inspection" &&
                          wAListCopy?.workActivities[ind]?.Title.includes(
                            "Debrief",
                          ) && (
                            <>
                              <LabourTableGrid labourType={"Mechanic"} />
                              <DirectChargesGrid />
                            </>
                          )}

                        <View>
                          <View
                            style={[
                              styles.rowFlexContainerDate,
                              styles.marginVertical2,
                            ]}>
                            {workModelType == "WA" ? (
                              <CalendarPicker
                                disableCalendar={!allowCheckbox}
                                minDate={OffAvailableDates}
                                setCalendarDate={setActualEndDate}
                                selectedDate={selectedDate}
                                setSelectedDate={setActualEndDate}
                                showCalendar={showCalendar}
                                setShowCalendar={setShowCalendar}
                                calendarDate={actualEndDate}
                                // requiredCardHeight={requiredHeight}
                                editable={allowCheckbox}
                                setErrorOn={setErrorOn}
                                setAvailableOndates={setAvailableOndates}
                                setCalorText={setCalorText}
                                setTextDate={setTextDateOff}
                                outlinedLable={t("ACTUAL_END_DATE")}
                                index={ind}
                                setIndex={setIndex}
                              />
                            ) : (
                              <CalendarPickerWO
                                disableCalendar={!allowCheckbox}
                                minDate={OffAvailableDates}
                                setCalendarDate={setActualEndDate}
                                selectedDate={selectedDate}
                                setSelectedDate={setActualEndDate}
                                showCalendar={showCalendar}
                                setShowCalendar={setShowCalendar}
                                calendarDate={actualEndDate}
                                // requiredCardHeight={requiredHeight}
                                editable={allowCheckbox}
                                setErrorOn={setErrorOn}
                                setAvailableOndates={setAvailableOndates}
                                setCalorText={setCalorText}
                                setTextDate={setTextDateOff}
                                outlinedLable={t("ACTUAL_END_DATE")}
                                index={ind}
                                setIndex={setIndex}
                              />
                            )}
                          </View>
                          {/* <Button
                          onPress={cancelClick}
                          buttonbgColor={[
                            styles.cancelBg,
                            disableCancle && styles.disabledCancleStyle,
                          ]}
                          textColor={[
                            disableCancle
                              ? styles.disableColor
                              : styles.cancelText,
                          ]}
                          disabled={disableCancle}>
                          {t("CANCEL")}
                        </Button>
                        <Button
                          buttonbgColor={[
                            styles.buttonBgColor,
                            disableSubmit && styles.disabledStyle,
                          ]}
                          textColor={[
                            disableSubmit
                              ? styles.disableColor
                              : styles.textColor,
                          ]}
                          onPress={() =>
                            submitActivityClick(
                              wAListCopy?.workActivities[ind],
                              ind,
                            )
                          }
                          disabled={disableSubmit}>
                          {t("SAVE")}
                          {isActivityLoading && (
                            <ActivityIndicator
                              align="center"
                              size={13}
                              color={GlobalStyles.colors.eWhite.base}
                            />
                          )}
                        </Button> */}
                        </View>
                      </View>
                      {/* <View style={styles.containerSubmit}> */}

                      {/* </View> */}
                      {/* <TouchableOpacity
                      style={{ border: "solid red 1px" }}
                      onPress={equipToggleHandler}>
                      <View style={styles.arrowIconStyle}>
                        <FontAwesome5Icon
                          name={equipToggle ? "chevron-down" : "chevron-right"}
                          color={GlobalStyles.colors.eMedium.base}
                          size={12}
                          fontFamily="NotoSans-Bold"
                        />
                      </View>
                    </TouchableOpacity> */}
                    </>
                  )}
                  {/* <View style={styles.lineStyle} /> */}
                </View>
              );
            })}
          </>
        ) : null}
        {customerNumberError ? (
          <>
            <View style={[styles.rowFlexContainerError]}>
              <Text style={[styles.labelStyleError]}>{customerNumberText}</Text>
            </View>
          </>
        ) : null}
        {OTPConfirmationWO ? (
          <View style={styles.containerSubmit}>
            <View style={styles.btnContainer}>
              {/* <Button
                onPress={cancelClick}
                buttonbgColor={[
                  styles.cancelBg,
                  disableCancle && styles.disabledCancleStyle,
                ]}
                textColor={[styles.cancelText]}
                disabled={disableCancle}>
                {t("CANCEL")}
              </Button> */}
              <Button
                buttonbgColor={[
                  styles.buttonBgColor,
                  (disableComplete || !disableStart || disableCompleteTime) &&
                    styles.disabledCancleStyle,
                ]}
                textColor={[
                  disableComplete || !disableStart || disableCompleteTime
                    ? styles.disableColor
                    : styles.textColor,
                ]}
                onPress={submitClick}
                // disabled={
                //   disableComplete || !disableStart || disableCompleteTime
                // }
                //rules for disabling.
                //0.If Actual end time, actual star time is not provided, and actual start time is greater than actual end time.
                //1. if the Activity is not in InProgress starte.
                //2.If startDate time is less than end datetime.
                //3. if the Actual duration entered in Labour,Equipment grid less than tha entered Actual time, Actual end time.
                //4.If service history is not filled.
                //5.If Activity is already completed.
              >
                {t("COMPLETE")}
                {submitLoading && (
                  <ActivityIndicator
                    align="center"
                    size={13}
                    color={GlobalStyles.colors.eWhite.base}
                  />
                )}
              </Button>
            </View>
          </View>
        ) : (
          <View style={styles.containerSubmit}>
            <View style={styles.btnContainer}></View>
          </View>
        )}
        {(singleWODetails?.WorkStatus != "CO" ||
          singleWODetails?.WorkStatus != "CL") && (
          <Modal
            animationType="slide"
            transparent={true}
            visible={showTimeErr && singleWODetails?.WorkStatus == "I"}
            onRequestClose={() => {
              setshowTimeErr(!showTimeErr);
            }}>
            <View style={styles.centeredView}>
              <View style={styles.modalView}>
                <View style={styles.modalText}>
                  <View>
                    <Text
                      style={{
                        fontWeight: "bold",
                        fontSize: 20,
                        color: GlobalStyles.colors.eDanger.dark,
                      }}>
                      {t("Error")}
                    </Text>
                  </View>
                  <View>
                    <Icons
                      name="close"
                      size={20}
                      color={GlobalStyles.colors.eErrorIcon.base}
                      style={{
                        alignSelf: "flex-end",
                        alignItems: "flex-end",
                      }}
                      onPress={() => {
                        setshowTimeErr(false);
                      }}
                    />
                  </View>
                </View>
                <View style={styles.hr} />
                <ScrollView>
                  <Pressable>
                    <View style={{ paddingHorizontal: 10 }}>
                      <View>
                        <Text>{showTimeErrMsg}</Text>
                      </View>
                      <View
                        style={{
                          flexDirection: "row",
                          justifyContent: "flex-end", // Space buttons out or use 'center'
                          paddingVertical: 10,
                        }}>
                        <Button
                          color={GlobalStyles.colors.eSecondary.base}
                          buttonbgColor={[styles.cancelBgonHold]}
                          textColor={styles.textColor}
                          style={{
                            borderRadius: 15,
                          }}
                          onPress={() => setshowTimeErr(false)}>
                          {t("OK")}
                        </Button>
                      </View>
                    </View>
                  </Pressable>
                </ScrollView>
              </View>
            </View>
          </Modal>
        )}
      </ScrollView>
      <Portal>
        <Snackbar
          visible={visibleSnack}
          onDismiss={onDismissSnackBar}
          style={styles.snackbar}
          action={{
            label: "Dismiss",
            onPress: onDismissSnackBar,
          }}>
          {snackMsg}
        </Snackbar>
      </Portal>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    borderTopEndRadius: 10,
    borderTopStartRadius: 10,
    borderBottomEndRadius: 0,
    borderBottomStartRadius: 0,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    padding: 15,
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    margin: 10,
  },
  imgContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 0,
  },
  imgSideBySideView: {
    flex: 1,
    padding: 0,
  },
  imgSideBySideView2: {
    flex: 1,
    marginBottom: 10,
  },
  wrapDirection: {
    flexDirection: "row",
    flexWrap: "wrap",
    alignContent: "space-between",
  },
  bigTextHeader: {
    color: GlobalStyles.colors.eRich.base,
    textAlign: "left",
    fontSize: 14,
    justifyContent: "flex-start",
    // wordBreak: "break-all",
  },
  titleCard: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontWeight: "700",
    fontFamily: "NotoSans-Bold",
  },
  backButtonWrapper: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 3,
  },
  paddingRight: {
    paddingRight: 10,
  },
  scrollViewContent: {
    flexGrow: 1,
    padding: 20, // Adjust the padding as per your design
  },
  checkListDescription: {
    fontSize: 14,
    color: GlobalStyles.colors.ePrimary.base,
  },
  checkListDoneBtnStyle: {
    marginVertical: 10,
    marginTop: 20,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  checkListTextInput: {
    // minWidth: 150,
    flex: 1,
    height: 30,
    justifyContent: "center",
    marginRight: 10,
  },
  checkListIdLabel: {
    fontFamily: "NotoSans-SemiBold",
    fontSize: 13,
  },
  checkListStatus: {
    fontSize: 13,
    fontFamily: "NotoSans-SemiBold",
  },
  checkListItemContainer: {
    marginLeft: -25,
    marginVertical: -5,
  },
  camClass: {
    marginLeft: 25,
  },
  container: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: 10,
    borderRadius: 5,
  },
  containerEquip: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    // marginHorizontal: 10,
    // borderRadius: 5,
  },
  containerDiv: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: 10,
    marginTop: 0,
    borderRadius: 5,
  },
  containerChecklist: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: 10,
    // marginTop: 15,
    borderRadius: 5,
  },
  containerNoChecklist: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    alignItems: "center",
    marginVertical: 15,
    borderRadius: 5,
  },
  modalText: {
    fontSize: 13,
    paddingTop: 10,
    paddingHorizontal: 10,
    width: "100%",
    marginBottom: 15,
    textAlign: "flex-start",
    //backgroundColor: GlobalStyles.colors.ePrimary.base,
    color: GlobalStyles.colors.ePrimary.base,
    fontWeight: "bold",
    flexDirection: "row", // Horizontal alignment
    justifyContent: "space-between", // Space items evenly,
    fontWeight: "bold",
  },
  containerSubmit: {
    marginHorizontal: 10,
    marginBottom: 150,
    marginTop: 15,
  },
  containerStyle: {
    backgroundColor: "white",
    padding: 70,
    marginHorizontal: 20,
  },
  displayFlex: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 20,
  },
  ePrimary: {
    color: GlobalStyles.colors.ePrimary.base,
  },
  flexRowCenter: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  flexWrap: {
    flex: 1,
  },
  flexWrapAsset: {
    flex: 1,
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  workActivityDetailContainer: {
    paddingHorizontal: 8,
    paddingVertical: 10,
  },
  labelHeader: {
    fontFamily: "NotoSans-Bold",
    fontSize: 14,
    color: GlobalStyles.colors.ePrimary.base,
  },
  nolabelHeader: {
    fontFamily: "NotoSans-Bold",
    fontSize: 14,
    color: GlobalStyles.colors.ePrimary.base,
  },
  labelStyle: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eRich.base,
  },
  labelStyleError: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eDanger.dark,
  },
  inputContainer: {
    marginRight: 10,
    marginVertical: -20,
  },
  valueStyle: {
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  valueStyleAsset: {
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
    flexDirection: "row", // Aligns icon and text horizontally
    alignItems: "center",
    justifyContent: "center",
  },
  labelWidth: {
    flex: 1,
  },
  labelWidthMeter: {
    marginLeft: -25,
  },
  labelWidthDuration: {
    flex: 1,
  },
  labelWidthTime: {
    width: 150,
    marginHorizontal: 20,
    marginTop: 15,
    marginBottom: 10,
  },
  opacityDimmed: {
    opacity: 0.5,
  },
  otpTextInputStyle: {
    borderBottomWidth: 2,
    borderBottomColor: GlobalStyles.colors.ePrimary.base,
    color: GlobalStyles.colors.ePrimary.base,
  },
  overdueStyle: {
    color: GlobalStyles.colors.eTertiary.base,
  },
  hr: {
    borderBottomWidth: 1, // Thickness of the line
    borderBottomColor: "black", // Line color
    width: "100%", // Full width
    marginBottom: 10, // Space above and below the line
  },
  pastDueStyle: {
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderWidth: 1,
    borderColor: "#d5d5d5",
    backgroundColor: "#fef9e8",
    display: "flex",
    flexDirection: "row",
    gap: 10,
    alignItems: "center",
  },

  rowFlexContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 5,
  },
  rowFlexContainerError: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  centeredView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  rowFlexContainerDate: {
    marginHorizontal: 20,
    marginTop: 10,
  },
  snackbarWrapper: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
  textInputWrapper: {
    paddingVertical: 3,
    flex: 1,
    // width: Dimensions.get("screen").width - 100,
  },
  workOrderCompletionTextStyle: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 13,
  },
  activityItem: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 10,
    // marginTop: -5,
  },
  activityItemInner: {
    display: "flex",
    flexDirection: "row",
    flex: 1,
    gap: 20,
  },
  iconContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 10,
  },
  contentContainer: {
    flex: 1,
  },
  displayFlex: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  headerStyleNumber: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderRow: {
    display: "flex",
    flexDirection: "row",
    paddingVertical: 1,
    justifyContent: "space-between",
    paddingRight: 20,
    marginTop: -5,
    // textAlign: 'left'
  },
  subHeaderRowMinWidth: {
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderPriority: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eRich.base,
    textTransform: "capitalize",
    marginTop: -5,
    textAlign: "right",
  },
  subHeaderRowStatus: {
    display: "flex",
    flexDirection: "row",
    paddingVertical: 1,
    justifyContent: "space-between",
    paddingRight: 20,
    marginBottom: 10,
  },
  subHeaderRowMinWidth: {
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  modalView: {
    width: "95%",
    margin: 2,
    backgroundColor: "white",
    borderRadius: 10,
    borderWidth: 1,
    borderStyle: "solid",
    borderColor: GlobalStyles.colors.ePrimary.base,
    padding: 5,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  subHeaderRowMinWidth: {
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderStatus: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eRich.base,
    textTransform: "capitalize",
    marginTop: -15,
    textAlign: "right",
  },
  arrowIconStyle: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  lineStyle: {
    // borderWidth: 0.3,
    borderColor: GlobalStyles.colors.eMedium.hover,
    marginTop: 1,
    width: "90%",
  },
  lineStyleInfo: {
    borderWidth: 0.3,
    borderColor: GlobalStyles.colors.eSeparationLine.base,
    marginTop: 0,
    width: "100%",
  },
  lineStyleInfoCheckList: {
    borderWidth: 0.3,
    borderColor: GlobalStyles.colors.eSeparationLine.base,
    marginBottom: 5,
    width: "100%",
  },
  btnContainer: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 0,
    marginBottom: 15,
  },
  disabledCancleStyle: {
    // opacity: 0.5,
    backgroundColor: GlobalStyles.colors.ePage.base,
    borderColor: GlobalStyles.colors.eLight.selected,
    borderWidth: 1,
  },
  disableColor: {
    color: GlobalStyles.colors.eLight.selected,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  cancelText: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  onHoldText: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  bgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    marginLeft: 10,
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginLeft: 10,
  },
  cancelBgonHold: {
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    color: GlobalStyles.colors.eWhite.base,
    borderColor: GlobalStyles.colors.ePrimary.base,
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.ePrimary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 10,
  },
  disabledStyle: {
    opacity: 0.5,
    backgroundColor: GlobalStyles.colors.eBackground.selected,
  },
  snackbar: {
    backgroundColor: GlobalStyles.colors.ePrimary.base, // Custom background color (Purple)
  },
});

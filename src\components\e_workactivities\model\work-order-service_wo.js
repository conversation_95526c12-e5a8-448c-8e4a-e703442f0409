import axios from "axios";
import { config } from "../../../environment";

export const WorkOrderServiceWO = {
  getAllWorkOrderList,
  updateCIList,
  getAllWorkActivities,
  getAddress,
  getAssetAddress,
  requestOTP,
  OTPSubmit,
  UpdateWA,
  UpdateWAStatus,
  workActitityAPIcall,
  getMeterInspectionAddress,
  getConsumerDetails,
  acceptUpdate,
};

async function getAllWorkOrderList() {
  try {
    let url = config.urls.GET_WORK_ORDERS;
    const response = await axios.get(url, {
      params: {
        pageSize: 2000,
        getWorkActivities: true,
        getWorkOrderResources: true,
        getActivityChecklist: true,
        //orgHierarchyId: 1, - Discom Corporation - Testing Org Id
        //Division 1 - 21 - QA Test 21
      //  orgHierarchyId: 9,
      //  crewResourceId: 2,
      },
    });
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function updateCIList(CIdata) {
  const consumerNumber = CIdata.ConsumerNumber;
  let tempCIData = CIdata;
  let key = "ConsumerIndexingHistoryId";
  let key2 = "WorkorderId";
  delete tempCIData[key];
  delete tempCIData[key2];
  let url = config.urls.GET_CONSUMER_LIST;
  try {
    const response = await axios.put(
      url + consumerNumber + "",
      tempCIData,
      // Add your request data here if needed
    );
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function getAllWorkActivities(id) {
  try {
    let url = config.urls.GET_WORK_ORDERS;
    const response = await axios.get(
      "https://impresa-fieldwork-demo.abjayon.com/work-management/work-order?workOrderId=" +
        id +
        "&pageNumber=1&pageSize=1000&getWorkActivities=true&getActivityChecklist=true",
    );
    console.log("response", response);
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function getAddress(meterNo, workorderid) {
  try {
    let params = {};
    if (meterNo) {
      params["MeterNumber"] = meterNo;
    }
    if (workorderid) {
      params["WorkOrderId"] = workorderid;
    }
    const response = await axios.get(
      "https://impresa-fieldwork-demo.abjayon.com/asset/consumer-indexing/cih",
      { params: params },
    );
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function getMeterInspectionAddress(assetID) {
  try {
    const response = await axios.get(
      "https://impresa-fieldwork-demo.abjayon.com/asset/assets-managemenet/details?assetId=" +
        assetID +
        "",
    );

    return response.data.assetsDetails[0];
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}
async function getAssetAddress(assetID, workorderid) {
  try {
    const response = await axios.get(
      "https://impresa-fieldwork-demo.abjayon.com/asset/assets-managemenet/historydetails?assetId=" +
        assetID +
        "&WorkOrderId=" +
        workorderid +
        "",
    );
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}
async function requestOTP(customerNumber) {
  try {
    let customerNumber = "";
    if (customerNumber === undefined || null) {
      customerNumber = "8333011316";
    }
    const response = await axios.get(
      "https://impresa-fieldwork-demo.abjayon.com/work-management/work-order/otp?mobileNumber=" +
        customerNumber +
        "",
    );
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error.response.data);
    throw error;
  }
}

async function OTPSubmit(otp, customerNumber) {
  try {
    const response = await axios.get(
      "https://impresa-fieldwork-demo.abjayon.com/work-management/work-order/validate-otp?mobileNumber=" +
        customerNumber +
        "&otp=" +
        otp +
        "",
    );
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function UpdateWA(data) {
  try {
    const response = await axios.post(
      "https://impresa-fieldwork-demo.abjayon.com/work-management/work-order/updateWorkActivityAndChecklist",
      data,
    );
    console.log(response);
    return response;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function UpdateWAStatus(status, workOrderID, updatedBy) {
  try {
    const response = await axios.post(
      "https://impresa-fieldwork-demo.abjayon.com/work-management/work-order/addupdatewo",
      {
        WorkOrderId: workOrderID,
        Status: status,
        updatedBy: updatedBy,
        Comments: "",
      },
    );
    return response.status;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function workActitityAPIcall(data) {
  try {
    const response = await axios.post(
      "https://impresa-fieldwork-demo.abjayon.com/work-management/work-order/updateWorkActivityAndChecklist",
      data,
    );
    return response;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}
async function getConsumerDetails(id) {
  try {
    let url = config.urls.GET_CONSUMER_DETAILS;
    const response = await axios.get(url + "ConsumerIndexingId=" + id);
    console.log("hhhhhhhhhhhhhhhhhh", response, id);
    return response.data[0];
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}
async function acceptUpdate(workOrderId, status, updatedBy) {
  try {
    //console.log("in service", workOrderId, status, updatedBy);
    let url = config.urls.ACCEPTUPDATE;
    const response = await axios.post(url, {
      WorkOrderId: workOrderId,
      FtAcceptanceStatus: status,
      updatedBy: updatedBy,
    });
    return response.status;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

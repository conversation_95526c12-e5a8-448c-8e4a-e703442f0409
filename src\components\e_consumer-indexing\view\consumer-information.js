import React, { useEffect, useMemo, useState, useContext } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { ScrollView } from "react-native-gesture-handler";
import CustomTextInput from "./CustomTextInput";
import CustomDropDown from "./CustomDropdown";
import {
  CONSUMER_INDEXING_DROPDOWN_VALUES,
  MESSAGES,
} from "../../common/constants";
import { consumerPaginationContext } from "./new-consumer-index";
import { consumerIndexContext } from "../e_consumer-index";
import { useDispatch, useSelector } from "react-redux";
import { getInitialConsumerIndexingValues } from "../../common/util";
import { useIsFocused } from "@react-navigation/native";
import _ from "lodash";
import { GlobalStyles } from "../../app/global-styles";
import { Dimensions } from "react-native";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import { useTranslation } from "react-i18next";
import { config } from "../../../environment";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { stackContext } from "../../app/get_stack";

const windowWidth = Dimensions.get("window").width;
const windowHeight = Dimensions.get("window").height;
const ConsumerInfoPage = () => {
  const { workModelType } = React.useContext(stackContext);
  const { t } = useTranslation();
  const { IndexArray, setIndexArray, currentPage } = useContext(
    consumerPaginationContext,
  );
  let createCI;

  if (workModelType === "WA") {
    ({ createCI } = useContext(drawerContext));
  } else {
    ({ createCI } = useContext(drawerContextWO));
  }

  const {
    tempCIData,
    revert,
    setRevert,
    newCIData,
    setNewCIData,
    singleCI,
    setSingleCI,
  } = useContext(consumerIndexContext);
  useEffect(() => {
    if (newCIData) {
      if (
        !newCIData.ConsumerNumber ||
        !newCIData.ConsumerName ||
        !newCIData.ConsumerStatus ||
        !newCIData.EmailId ||
        !newCIData.FullAddress ||
        !newCIData.ServiceConnectionNumber ||
        !newCIData.SanctionLoad
      ) {
        const updatedIndexArray = IndexArray.map((item, index) =>
          index === currentPage - 1 ? { ...item, filled: false } : item,
        );
        setIndexArray(updatedIndexArray);
      } else {
        const updatedIndexArray = IndexArray.map((item, index) =>
          index === currentPage - 1 ? { ...item, filled: true } : item,
        );
        setIndexArray(updatedIndexArray);
      }
    }
  }, [currentPage, newCIData]);

  useEffect(() => {}, [newCIData]);

  useEffect(() => {
    if (revert) {
      setNewCIData(tempCIData);
      setRevert(false);
    }
  }, [revert]);
  return (
    <View style={styles.container}>
      <View style={styles.section}>
        <View style={styles.headerWrapper}>
          <Text style={styles.sectionHeaderText}>
            {IndexArray[currentPage - 1].name}
          </Text>
        </View>
        <View style={styles.sectionWrapper}>
          <ScrollView
            persistentScrollbar={true}
            contentContainerStyle={{ flexGrow: 1 }}
            key={tempCIData.ConsumerNumber}>
            {createCI ? (
              <>
                <View style={styles.sectionItemHeaderDrop}>
                  <Text style={styles.sectionItemLabel}>organization</Text>
                </View>
                <View style={styles.extendFlex}>
                  <CustomDropDown
                    data={CONSUMER_INDEXING_DROPDOWN_VALUES.ORGANIZATION}
                    selectedValue={""}
                    handleOnChange={item => {
                      setNewCIData(prev => ({
                        ...prev,
                        OrgHierarchyId: item.id,
                        OrgHierarchyName: item.value,
                      }));
                    }}
                  />
                </View>
              </>
            ) : (
              <></>
            )}
            {!createCI ? (
              <>
                <View style={[styles.sectionItemHeaderContainer]}>
                  <View style={[styles.sectionItemHeaderText]}>
                    <Text style={styles.sectionItemLabel}>
                      {t("ORGANIZATION")}
                    </Text>
                  </View>
                  <Text style={styles.colon}>:</Text>
                  <View style={styles.extendFlexText}>
                    {CONSUMER_INDEXING_DROPDOWN_VALUES.ORGANIZATION?.map(
                      item => {
                        if (item.id === tempCIData.OrgHierarchyId) {
                          return (
                            <Text
                              key={item.id}
                              style={styles.sectionItemLabelText}>
                              {item.label}
                            </Text>
                          );
                        }
                        return null;
                      },
                    )}
                  </View>
                  {/* {tempCIData.OrgHierarchyId} */}
                </View>
                <View style={[styles.sectionItemHeaderContainer]}>
                  <View style={[styles.sectionItemHeaderText]}>
                    <Text style={styles.sectionItemLabel}>
                      {t("WORK_ORDER_NUMBER")}
                    </Text>
                  </View>
                  <Text style={styles.colon}>:</Text>
                  <View style={styles.extendFlexText}>
                    <Text style={styles.sectionItemLabelText}>
                      {tempCIData.WorkorderId}
                    </Text>
                  </View>
                </View>
              </>
            ) : (
              <></>
            )}
            {!createCI ? (
              <>
                <View style={[styles.sectionItemHeaderContainer]}>
                  <View style={styles.sectionItemHeaderText}>
                    <Text style={styles.sectionItemLabel}>
                      {t("CONSUMER_NUMBER")}
                    </Text>
                  </View>
                  <Text style={styles.colon}>:</Text>
                  <View style={styles.extendFlex}>
                    <Text style={styles.sectionItemLabelText}>
                      {tempCIData.ConsumerNumber}
                    </Text>
                  </View>
                </View>
                {/* <View style={[styles.sectionItemHeaderContainer]}>
                  <View style={styles.sectionItemHeaderText}>
                    <Text style={styles.sectionItemLabel}>Consumer Status</Text>
                  </View>
                  <Text style={styles.colon}>:</Text>
                  <View style={styles.extendFlex}>
                    <Text style={styles.sectionItemLabelText}>
                      {tempCIData.ConsumerStatus}
                    </Text>
                  </View>
                </View> */}
              </>
            ) : (
              <>
                <View style={styles.sectionItemHeader}>
                  <Text style={styles.sectionItemLabel}>
                    {t("CONSUMER_NUMBER")}
                  </Text>
                </View>
                <View style={styles.extendFlex}>
                  <CustomTextInput
                    defaultValue={
                      newCIData?.ConsumerNumber
                        ? `${newCIData?.ConsumerNumber}`
                        : ""
                    }
                    value={
                      newCIData?.ConsumerNumber
                        ? `${newCIData?.ConsumerNumber}`
                        : ""
                    }
                    placeholder={t("ENTER_CONSUMER_NUMBER")}
                    keyboardType="numeric"
                    onChange={value => {
                      setNewCIData(prev => ({
                        ...prev,
                        ConsumerNumber: value,
                      }));
                    }}
                  />
                </View>
              </>
            )}
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t("CONSUMER_NAME")}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.ConsumerName ? `${newCIData?.ConsumerName}` : ""
                }
                value={
                  newCIData?.ConsumerName ? `${newCIData?.ConsumerName}` : ""
                }
                placeholder={t("ENTER_CONSUMER_NAME")}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, ConsumerName: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t("EMAIL_ID")}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={newCIData?.EmailId ? `${newCIData?.EmailId}` : ""}
                value={newCIData?.EmailId ? `${newCIData?.EmailId}` : ""}
                placeholder={t("EMAIL_ID")}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, EmailId: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t("FULL_ADDRESS")}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  tempCIData?.FullAddress ? `${tempCIData?.FullAddress}` : ""
                }
                value={
                  newCIData?.FullAddress ? `${newCIData?.FullAddress}` : ""
                }
                placeholder={t("ENTER_FULL_ADDRESS")}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, FullAddress: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeaderDrop}>
              <Text style={styles.sectionItemLabel}>
                {t("CONSUMER_STATUS")}
              </Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomDropDown
                data={CONSUMER_INDEXING_DROPDOWN_VALUES.CONSUMER_STATUS}
                selectedValue={
                  newCIData?.ConsumerStatus === "Active" ||
                  newCIData?.ConsumerStatus === "Stopped"
                    ? `${newCIData?.ConsumerStatus}`
                    : ""
                }
                handleOnChange={item => {
                  setNewCIData(prev => ({
                    ...prev,
                    ConsumerStatus: item.value,
                  }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>
                {t("SERVICE_CONNECTION_NUMBER")}
              </Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  tempCIData?.ServiceConnectionNumber
                    ? `${tempCIData?.ServiceConnectionNumber}`
                    : ""
                }
                placeholder={t("ENTER_SERVICE_CONNECTION_NUMBER")}
                keyboardType="numeric"
                value={
                  newCIData?.ServiceConnectionNumber
                    ? `${newCIData?.ServiceConnectionNumber}`
                    : ""
                }
                onChange={value => {
                  setNewCIData(prev => ({
                    ...prev,
                    ServiceConnectionNumber: value,
                  }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t("MOBILE_NUMBER")}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={newCIData?.Mobile ? `${newCIData?.Mobile}` : ""}
                value={newCIData?.Mobile ? `${newCIData?.Mobile}` : ""}
                placeholder={t("ENTER_MOBILE_NUMBER")}
                keyboardType="numeric"
                maxLength={10}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, Mobile: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t("SANCTION_LOAD")}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  tempCIData?.SanctionLoad ? `${tempCIData?.SanctionLoad}` : ""
                }
                placeholder={t("ENTER_SANCTION_LOAD")}
                value={
                  newCIData?.SanctionLoad ? `${newCIData?.SanctionLoad}` : ""
                }
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, SanctionLoad: value }));
                }}
              />
            </View>
          </ScrollView>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingLeft: 10,
  },
  extendFlex: {
    width: windowWidth - 40,
    paddingBottom: 10,
  },
  extendFlexText: {
    width: windowWidth - 40,
    paddingBottom: 10,
  },
  headerWrapper: {
    paddingBottom: 20,
  },
  flexRowCenter: {
    paddingHorizontal: 40,
  },
  section: {},
  sectionHeaderText: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.ePrimary.base,
  },
  sectionItemHeader: {
    width: "100%",
  },
  sectionItemHeaderDrop: {
    marginBottom: 5,
  },
  sectionItemHeaderText: {
    width: "30%",
  },
  sectionItemHeaderContainer: {
    flexDirection: "row",
    // justifyContent: 'space-between'
  },
  sectionItemInputStyle: {
    height: 35,
  },
  sectionItemLabel: {
    fontFamily: "NotoSans-Medium",
    fontSize: 10,
    color: GlobalStyles.colors.eRich.hover,
  },
  sectionItemLabelText: {
    fontFamily: "NotoSans-Medium",
    fontSize: 10,
    color: GlobalStyles.colors.ePrimary.hover,
    marginLeft: 10,
  },
  sectionWrapper: {
    height: windowHeight - 450,
  },
  submitBtnWrapper: {
    paddingVertical: 16,
    marginBottom: 10,
  },
});

export default ConsumerInfoPage;

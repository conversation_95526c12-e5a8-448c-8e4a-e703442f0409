import React, { useState, useEffect, useContext } from "react";
import { Card, Text } from "react-native-paper";
import {
  StyleSheet,
  View,
  Dimensions,
  Linking,
  ActivityIndicator,
} from "react-native";
import { GlobalStyles } from "../../app/global-styles";
import Map from "./_map";
import Dropdown from "../../common/_dropdown";
import { use } from "i18next";
import Button from "../../common/_button";
import { homeContext } from "../e_home";
import { homeContextWO } from "../e_home_wo";
import moment from "moment";
import { useTranslation } from "react-i18next";
import MapWO from "./_map_wo";
import { config } from "../../../environment";
import { stackContext } from "../../app/get_stack";

export default function JobLocationsWO() {
  const { workModelType } = React.useContext(stackContext);
  let markers,
    setMarkers,
    mainData,
    taskCount,
    markersCT,
    setMarkersCT,
    markersOT,
    setMarkersOT,
    markersODT,
    setMarkersODT;

  if (workModelType === "WA") {
    ({
      markers,
      setMarkers,
      mainData,
      taskCount,
      markersCT,
      setMarkersCT,
      markersOT,
      setMarkersOT,
      markersODT,
      setMarkersODT,
    } = useContext(homeContext));
  } else {
    ({
      markers,
      setMarkers,
      mainData,
      taskCount,
      markersCT,
      setMarkersCT,
      markersOT,
      setMarkersOT,
      markersODT,
      setMarkersODT,
    } = useContext(homeContextWO));
  }

  const { t } = useTranslation();
  const [markersList, setMarkersList] = useState(markers);

  useEffect(() => {
    setMarkersList(markers);
  }, [markersList, markers]);

  const showAllClick = async () => {
    setMarkers(mainData);
  };
  return (
    <Card style={styles.card}>
      <View style={{ minHeight: Dimensions.get("window").height / 11 }}>
        <View>
          <View style={styles.wrapDirection}>
            <View style={styles.titleWrapper}>
              <Text style={styles.titleCard}>
                {t("ROUTE")} ({moment().format("DD-MMM-YY")})
              </Text>
            </View>
            {/* <View style={styles.rightContent}>
              <Button
                buttonbgColor={styles.buttonBgColor}
                textColor={styles.whiteText}
                customBtnStyle={styles.customBtnStyle}
                customTextStyle={styles.buttonText}
                onPress={showAllClick}>
                Show All
              </Button>
            </View> */}
          </View>
          {/* {markersList.length != 0 ? (
            <> */}

          {workModelType == "WA" ? (
            <Map markers={markersList} />
          ) : (
            <MapWO markers={markersList} />
          )}
          {/* </>
          ) : null} */}
        </View>
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    width: "100%",
    padding: 15,
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    backgroundColor: GlobalStyles.colors.eWhite.base,
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    alignItems: "center",
    paddingHorizontal: 10,
    marginTop: -5,
    marginBottom: 5,
  },
  whiteText: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  customBtnStyle: {
    borderRadius: 5,
    paddingVertical: 4,
    paddingHorizontal: 6,
  },
  titleCard: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontWeight: "700",
    fontFamily: "NotoSans-Bold",
  },
  cardHeight: {
    alignSelf: "center",
    marginTop: "8%",
    fontSize: 12,
  },
  loadingUsage: {
    display: "flex",
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    height: 200,
  },
  wrapDirection: {
    flexDirection: "row",
    flexWrap: "wrap",
    alignContent: "space-between",
  },
  rightContent: {
    // width: "10%",
    // flexDirection: "row",
    // flexWrap: "wrap",
    // alignContent: "space-between",
    //marginTop: 0,
    flex: 1,
    justifyContent: "flex-end", // Align vertically to the bottom
    alignItems: "flex-end",
  },
  weekStyle: {
    width: 9,
    borderRadius: 25,
    height: 9,
    marginRight: "5%",
    marginTop: 5,
  },
  primaryColorStyle: {
    backgroundColor: GlobalStyles.colors.ePrimary.base,
  },
  secondaryColorStyle: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
  },
  fontText: {
    fontSize: 9,
    fontFamily: "NotoSans-Regular",
  },
  widthStyle80: {
    width: "80%",
  },
  titleWrapper: {
    fontSize: 12,
    paddingBottom: 10,
    fontWeight: "700",
    fontFamily: "NotoSans-Bold",
  },
});

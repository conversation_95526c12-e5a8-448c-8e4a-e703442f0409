import { config } from "../../../../environment";
import axios from "axios";
import { store } from "../../../../redux/store";

export const billPreferenceService = {
  getBillNotifyPreference,
  getBEbill,
  updateBEbill,
  updateBillNotifyPreference,
  getPaymentPreference,
  getUpdatePreference,
};

function getBillNotifyPreference(accountId) {
  let APIinput =
    `query{getBillNotifyPreference(input:{accountId:"` +
    accountId +
    `",tenantCode:"` +
    config.constants.BASE_TENANT_CODE +
    `"})}`;
  return new Promise((resolve, reject) => {
    axios
      .post(config.urls.AUTH_SERVICE, { query: APIinput })
      .then((response) => {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
}

function getBEbill(accountId) {
  let APIquery =
    `query{getBEbill(input:{accountId:"` +
    accountId +
    `",tenantCode:"` +
    config.constants.BASE_TENANT_CODE +
    `"})}`;
  return new Promise((resolve, reject) => {
    axios({
      url: config.urls.AUTH_SERVICE,
      method: "post",
      data: {
        query: APIquery,
      },
    })
      .then(function (response) {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
}

function getPaymentPreference(accountId) {
  let readQuery = `query {
    getAutoPayStatus 
    {
    description,
    status,
    notificationDesc
    }
}`;
  let Headers = { accountId: accountId };
  return new Promise((resolve, reject) => {
    axios
      .post(config.urls.USAGE_SERVICE_BASE_URL, {
        query: APIinput,
        header: Headers,
      })
      .then((response) => {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
  // return apolloClientService.ApolloClientgqlsUsage(readQuery,Headers,"network-only");
}

function getUpdatePreference(preferenceData) {
  let accountId = store.getState().meterDetails?.accountId;
  let APIquery =
    `query {
    updateAutoPayStatus(input: {status: "` +
    preferenceData.AutoPayBillData.status +
    `"}) {
        status
    }
} 
`;
  let Headers = { accountId: accountId };
  return new Promise((resolve, reject) => {
    axios
      .post(config.urls.USAGE_SERVICE_BASE_URL, {
        query: APIinput,
        header: Headers,
      })
      .then((response) => {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
  // return apolloClientService.ApolloClientgqlsUsage(APIquery,Headers,"network-only");
}

function updateBEbill(accountId, billRouteType, email) {
  let APIquery =
    `query{updateBEbill(input:{accountId:"` +
    accountId +
    `",tenantCode:"` +
    config.constants.BASE_TENANT_CODE +
    `",
    receivesCopyOfBill:"true",
     billRouteType:"` +
    billRouteType +
    `",emailAddress:"` +
    email +
    `"})
}`;
  return new Promise((resolve, reject) => {
    axios({
      url: config.urls.AUTH_SERVICE,
      method: "post",
      data: {
        query: APIquery,
      },
    })
      .then(function (response) {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
}

function updateBillNotifyPreference(data, accountId) {
  let dataString = JSON.stringify(data);
  const dataformate = dataString.replace(/"([^("")"]+)":/g, "$1:");
  let APIquery =
    `  query{updateBillNotifyPreference(input:{accountId:"` +
    accountId +
    `",tenantCode:"` +
    config.constants.BASE_TENANT_CODE +
    `",
    identityAttributes:{      
          notificationTypes:
             ` +
    dataformate +
    ` 
 }
}
  )}`;

  return new Promise((resolve, reject) => {
    axios({
      url: config.urls.AUTH_SERVICE,
      method: "post",
      data: {
        query: APIquery,
      },
    })
      .then(function (response) {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
}

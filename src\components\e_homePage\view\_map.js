import React, { useEffect, useState, useRef } from "react";
import {
  StyleSheet,
  View,
  Dimensions,
  Image,
  Alert,
  Linking,
  PermissionsAndroid,
} from "react-native";

import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "react-native-maps";
import MapViewDirections from "react-native-maps-directions";
import { GlobalStyles } from "../../app/global-styles";
import { Text } from "react-native-paper";
const { width, height } = Dimensions.get("window");
import Geolocation from "@react-native-community/geolocation";
import {
  check,
  request,
  PERMISSIONS,
  RESULTS,
  openSettings,
} from "react-native-permissions";
import FontAwesomeWarning from "react-native-vector-icons/AntDesign";
import { useTranslation } from "react-i18next";
//import IntentLauncher, { IntentConstant } from 'react-native-intent-launcher'

const ASPECT_RATIO = width / height;
const LATITUDE = 13.7967;
const LONGITUDE = 100.5553;
const LATITUDE_DELTA = 0.05;
const LONGITUDE_DELTA = LATITUDE_DELTA * ASPECT_RATIO;

const GOOGLE_MAPS_APIKEY = "AIzaSyCEki8XOhlK7BkZXxyIBxgruZS5c9PMcwI"; // Add your Google Maps API Key here

const locations = [
  // {
  //   latitude: 13.7975,
  //   longitude: 100.556,
  //   title: "Work Order 373",
  //   type: "Created",
  //   markerName: "A",
  // },
  {
    latitude: 13.7995,
    longitude: 100.5562,
    title: "Work Order 373",
    type: "Overdue",
    markerName: "A",
  },

  {
    latitude: 13.8036,
    longitude: 100.5539,
    title: "Work Order 374",
    type: "Created",
    markerName: "B",
  },
  // {
  //   latitude: 13.7998,
  //   longitude: 100.5616,
  //   title: "Work Order WO000236",
  // },
  // {
  //   latitude: 13.7723,
  //   longitude: 100.5488,
  //   title: "Work Order WO000311",
  //   type: "Created",
  // },
];

const mapStyle = [
  {
    featureType: "all",
    elementType: "labels",
    stylers: [
      {
        visibility: "off",
      },
    ],
  },
  {
    featureType: "poi",
    elementType: "labels",
    stylers: [
      {
        visibility: "off",
      },
    ],
  },
  {
    featureType: "poi",
    elementType: "geometry",
    stylers: [
      {
        visibility: "off",
      },
    ],
  },
];
const Map = ({ markers }) => {
  const { t } = useTranslation();

  const customStyle = [
    {
      elementType: "geometry",
      stylers: [
        {
          color: "#15537c",
        },
      ],
    },
    {
      elementType: "labels.text.fill",
      stylers: [
        {
          color: "#757575",
        },
      ],
    },
    {
      elementType: "labels.text.stroke",
      stylers: [
        {
          color: "#212121", // Text stroke color
        },
      ],
    },
    {
      featureType: "road",
      elementType: "geometry",
      stylers: [
        {
          color: "#383838", // Road color
        },
      ],
    },
  ];
  const [locationEnabled, setLocationEnabled] = useState(false);

  const [locationPermission, setLocationPermission] = useState(null);
  const [lat, setLat] = useState();
  const [lang, setLong] = useState();

  const intervalRef = useRef(null);
  const intervalRefOne = useRef(null);

  const [defaultLat, setDefaultLat] = useState();
  const [defaultLang, setDefaultLang] = useState();
  const [destLat, setDestLat] = useState();
  const [destLang, setDestLang] = useState();
  const [updatedMarker, setUpdatedMarker] = useState(markers);
  const segmentColors = [
    "green",
    "darkslategray",
    "orangered",
    "blue",
    "skyblue",
  ];

  const handleCheckPressed = async () => {
    if (Platform.OS === "android") {
      const checkEnabled = await isLocationEnabled();
    }
  };

  const getLocation = () => {
    Geolocation.getCurrentPosition(
      info => {
        setLong(info.coords.longitude);
        setLat(info.coords.latitude);
      },
      error => {
        setLong();
        setLat();
      },
    );
  };

  useEffect(() => {
    getLocation();
  }, [markers]);

  useEffect(() => {
    if (markers.length > 0) {
      let defLocation = markers?.find(e => {
        let [lati, longi] = e?.LatitudeLongitude.split(",");
        return Number(lati) != 0 && Number(longi) != 0;
      });
      if (defLocation) {
        let coordinates = defLocation?.LatitudeLongitude?.split(",");
        setDefaultLat(parseFloat(coordinates?.[0]));
        setDefaultLang(parseFloat(coordinates?.[1]));
      }
    }
  }, [markers]);

  useEffect(() => {
    // Start the interval
    intervalRef.current = setInterval(async () => {
      console.log(markers, markers?.length, "This runs every 5 seconds");
      if (markers.length > 0) {
        let defLocation = markers?.find(e => {
          let [lati, longi] = e?.LatitudeLongitude.split(",");
          return Number(lati) != 0 && Number(longi) != 0;
        });

        if (defLocation) {
          let coordinates = defLocation?.LatitudeLongitude?.split(",");

          setDefaultLat(parseFloat(coordinates?.[0]));
          setDefaultLang(parseFloat(coordinates?.[1]));
        }
      }
    }, 5000);

    // Cleanup on component unmount
    return () => clearInterval(intervalRef.current);
  }, []);

  useEffect(() => {
    // Start the interval
    intervalRefOne.current = setInterval(async () => {
      getLocation();
    }, 2000);

    // Cleanup on component unmount
    return () => clearInterval(intervalRefOne.current);
  }, []);

  const handleMarkerPress = (dLat, dLang) => {
   
    setDestLat(dLat);
    setDestLang(dLang);
  };

  return (
    <>
      {markers && defaultLang && defaultLat && (
        <View style={styles.container}>
          {!lat && !lang && (
            <View style={styles.locationWarning}>
              <FontAwesomeWarning
                name="warning"
                color={GlobalStyles.colors.eDanger.dark}
                size={20}
              />
              <Text style={styles.warningText}>{t("LOCATION_OFF")}</Text>
            </View>
          )}
          <MapView
            style={styles.map}
            // customMapStyle={mapStyle}
            initialRegion={{
              latitude: defaultLat ? defaultLat : LATITUDE,
              longitude: defaultLang ? defaultLang : LATITUDE,
              latitudeDelta: LATITUDE_DELTA,
              longitudeDelta: LONGITUDE_DELTA,
            }}>
            {lat && lang && (
              <Marker
                coordinate={{
                  latitude: lat,
                  longitude: lang,
                }}
                title={"Your are here"}>
                <Image
                  source={require("../../../../assets/dashboard-icons/map/completed.png")}
                  style={{ width: 18, height: 25 }}
                />
                <View style={styles.markerView}>
                  <Text style={styles.markerText}>You are here</Text>
                </View>
                <Image
                  source={require("../../../../assets/dashboard-icons/map/completed.png")}
                  style={{
                    width: 10,
                    height: 10,
                    position: "absolute",
                    top: 5,
                    left: 4,
                  }}
                />
              </Marker>
            )}
            {markers?.length > 0 &&
              markers.map((location, index) => {
                let coordinates = location?.LatitudeLongitude?.split(",");
                let latt = parseFloat(coordinates?.[0]);
                let langg = parseFloat(coordinates?.[1]);
                if (latt && langg) {
                  return (
                    <Marker
                      key={index}
                      onPress={() => {
                        handleMarkerPress(latt, langg);
                      }}
                      coordinate={{
                        latitude: latt,
                        longitude: langg,
                      }}
                      title={location.WamRefNum}>
                      <Image
                        source={require("../../../../assets/dashboard-icons/map/open.png")}
                        style={{ width: 18, height: 25 }}
                      />
                      <View style={styles.markerView}>
                        <Text style={styles.markerText}>
                          {location.WamRefNum}
                        </Text>
                      </View>
                    </Marker>
                  );
                }
              })}

            {lat &&
              lang &&
              destLat &&
              destLang &&
              markers.length == 1 &&
              markers.map((location, index) => {
                let coordinates = location?.LatitudeLongitude?.split(",");
                let latt = coordinates[0];
                let langg = coordinates[1];
                return (
                  <MapViewDirections
                    key={index}
                    origin={{
                      latitude: index == 0 ? lat : latt,
                      longitude: index == 0 ? lang : langg,
                    }}
                    destination={{ latitude: destLat, longitude: destLang }}
                    apikey={GOOGLE_MAPS_APIKEY}
                    strokeWidth={4}
                    strokeColor={segmentColors[index]}
                    // lineDashPattern={[10, 10]}
                    optimizeWaypoints={true}
                  />
                );
              })}

            {lat &&
              lang &&
              destLat &&
              destLang &&
              markers.length > 1 &&
              markers.slice(0, -1).map((location, index) => {
                let coordinates = location?.LatitudeLongitude?.split(",");
                let latt = coordinates?.[0];
                let langg = coordinates?.[1];
                if (latt && langg) {
                  return (
                    <MapViewDirections
                      key={index}
                      origin={{
                        latitude: index == 0 ? lat : latt,
                        longitude: index == 0 ? lang : langg,
                      }}
                      destination={{ latitude: destLat, longitude: destLang }}
                      apikey={GOOGLE_MAPS_APIKEY}
                      strokeWidth={4}
                      strokeColor={segmentColors[index]}
                      // lineDashPattern={[10, 10]}
                      // optimizeWaypoints={true}
                    />
                  );
                }
              })}

            {/* {destLang && destLat && lat && lang && (
              <MapViewDirections
                //key={index}17.427829746717855, 78.32474942405926
                origin={{
                  latitude: 17.427829,
                  longitude: 78.324749,
                }}
                destination={{
                  latitude: destLang,
                  longitude: destLang,
                }}
                onReady={handleDirectionsReady} // Success handler
                apikey={GOOGLE_MAPS_APIKEY}
                strokeWidth={4}
                strokeColor="hotpink"
                onError={handleDirectionsError}
              />
            )} */}
          </MapView>
        </View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderBottomEndRadius: 15, // Add border radius
    borderBottomStartRadius: 15, // Add border radius
    overflow: "hidden",
    marginHorizontal: -15,
    marginBottom: -15,
  },
  map: {
    flex: 1,
    height: 350,
    borderRadius: 15,
  },
  title: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontWeight: "700",
  },
  zoomControl: {
    position: "absolute",
    top: 16,
    right: 16,
  },
  markerText: {
    fontWeight: "bold",
    textDecorationLine: "underline",
    fontSize: 10,
  },
  markerView: {
    padding: 10,
    borderRadius: 30,
    backgroundColor: "white",
    borderStyle: "solid",
  },
  locationWarning: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 1,
    marginBottom: 2,
  },
  warningText: {
    fontWeight: "bold",
    color: GlobalStyles.colors.eDanger.dark,
  },
});

export default Map;

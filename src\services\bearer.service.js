//@flow

import { config } from "../environment";
import axios from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";

export function getBearerToken() {
  let confidentialClient = config;
  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.GENERATE_BEARER,
        {},
        {
          headers: {
            clientId: confidentialClient.constants.CLIENT_ID_KEYCLOCK,
            clientSecret:
              confidentialClient.constants.AUTH_UI_CLIENT_SECRET_KEYCLOCK,
            tenantCode: config.constants.BASE_TENANT_CODE,
          },
        },
      )
      .then(function (response) {
        resolve(response.data);

        AsyncStorage.removeItem("authbearer");
        AsyncStorage.setItem("authbearer", JSON.stringify(response.data));
      })
      .catch(function (error) {
        console.log("bearer error", error);
        reject(error);
      });
  });
}

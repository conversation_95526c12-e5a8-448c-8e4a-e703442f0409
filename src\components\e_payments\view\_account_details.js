import React, { useState, useEffect, useContext } from "react";
import { StyleSheet, View, Button, TextInput } from "react-native";
import { Card, Text, Checkbox, List } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import { FlatList } from "react-native-gesture-handler";
import { useSelector } from "react-redux";
import { PaymentContext } from "../e_payments";

export default function AccountDetails({ setTotalValue }) {
  const [currency, setCurrency] = useState();
  const accountData = useSelector(
    state =>
      state?.accountDetails?.accountDetails?.accountSummary?.accountDetails,
  );
  const [accountDetails, setaccountDetails] = useState();
  const meterDetails = useSelector(state => state?.meterDetails?.meterDetails);
  const { setSelectedPayments, payId } = useContext(PaymentContext);

  useEffect(() => {
    if (accountData && meterDetails) {
      let accountId = [];
      let tempAccountDetails = accountData.filter((item, k) => {
        if (
          item?.saDetails?.[0]?.isPrepaidSa ===
          meterDetails?.getSaInfo?.isPrepaidSa
        ) {
          const isDuplicate = accountId.includes(item?.accountId);
          if (!isDuplicate) {
            accountId.push(item?.accountId);
            return { ...item, isChecked: false, key: k };
          }
        }
      });
      setaccountDetails(tempAccountDetails);
      setCurrency(accountData?.[0]?.curSymbol);
    }
  }, [accountData, meterDetails, payId, meterDetails?.currentBalance]);

  const [checked, setChecked] = React.useState(false);
  const [totalAmount, setTotalAmount] = useState(0);

  const handleChange = id => {
    if(accountDetails){
    let temp = accountDetails.map(item => {
      if (item.accountId === id) {
        return { ...item, isChecked: !item.isChecked };
      } else return item;
    });
    setaccountDetails(temp);
    let filterData = temp.filter(item => item.isChecked === true);
    if (filterData.length === accountDetails.length) {
      setChecked(true);
    } else {
      setChecked(false);
    }
  }
  };

  React.useEffect(() => {
    if (accountDetails) {
      setTotalAmount(curr => {
        return accountDetails?.reduce((prev, ele) => {
          let tempVal = ele.isChecked ? parseFloat(ele.currentBalance) : 0;
          return prev + tempVal;
        }, 0);
      });

      setTotalValue(curr => {
        return accountDetails?.reduce((prev, ele) => {
          let tempVal = ele.isChecked ? parseFloat(ele.currentBalance) : 0;
          return prev + tempVal;
        }, 0);
      });

      let temp = [];
      accountDetails.forEach(element => {
        if (element.isChecked) {
          temp.push(element);
        }
      });
      setSelectedPayments(temp);
    }
  }, [accountDetails]);

  const selectAll = () => {
    setChecked(!checked);
    if (!checked) {
      let total = 0;
      let temp =
        accountDetails &&
        accountDetails.map(item => {
          total += parseFloat(item.currentBalance);
          return { ...item, isChecked: true };
        });
      setaccountDetails(temp);
      setTotalAmount(total);
      setTotalValue(total);
    } else {
      let temp = accountDetails.map(item => {
        return { ...item, isChecked: false };
      });
      setaccountDetails(temp);
      setTotalAmount(0);
      setTotalValue(0);
    }
  };
  return (
    <View>
      <Card style={styles.card}>
        <Text style={styles.titleCard}>ACCOUNT BALANCE</Text>
        <View style={styles.lineStyle} />
        {accountDetails?.length === 1 ? (
          <View style={styles.accountHeaderContent}>
            <View style={{ flex: 1 }}>
              <Text style={styles.singleHeader}>Account Number</Text>
            </View>
            <View style={{ flex: 1 }}>
              <Text style={styles.headerAmount}>Amount</Text>
            </View>
          </View>
        ) : (
          <View style={styles.accountHeaderContent}>
            <View style={styles.checkboxDiv}>
              <Checkbox.Item
                status={checked ? "checked" : "unchecked"}
                onPress={() => {
                  selectAll();
                }}
                mode="android"
                label="Account Number"
                position="leading"
                labelStyle={styles.bigTextHeader}
                color="#00AB6A"
                uncheckedColor="#ffffff"
              />
            </View>
            <View style={{ flex: 1 }}>
              <Text style={styles.headerAmount}>Amount</Text>
            </View>
          </View>
        )}
        <View>
          <FlatList
            data={accountDetails ? accountDetails : []}
            renderItem={(item, index) => (
              <View style={{ flexDirection: "row" }}>
                <View style={styles.checkboxDiv}>
                  <Checkbox.Item
                    status={item.item.isChecked ? "checked" : "unchecked"}
                    onPress={() => {
                      handleChange(item?.item?.accountId);
                    }}
                    mode="android"
                    label={item.item.accountId}
                    position="leading"
                    labelStyle={styles.accountsList}
                    color="#00AB6A"
                  />
                </View>
                <View style={{ flex: 1 }}>
                  <Text
                    style={[
                      styles.amountList,
                      item.item.currentBalance < 1 && styles.disableAmount,
                    ]}>
                    {currency ? (
                      <>
                        {currency.length > 1
                          ? item.item.currentBalance + " " + currency
                          : currency + item.item.currentBalance}
                      </>
                    ) : null}
                  </Text>
                </View>
              </View>
            )}
            keyExtractor={(item, index) => index}
          />
        </View>
        <View style={styles.content}></View>
        <View style={styles.lineStyleBottom} />
        <View style={styles.content}>
          <View style={styles.leftView}>
            <Text style={styles.totalAmount}> Total Amount </Text>
          </View>
          {/* <View style={styles.rightView}>
            <Text style={styles.total}>
              {currency && currency.length > 1
                ? (totalAmount === 0 ? totalAmount : totalAmount.toFixed(2)) +
                  " " +
                  currency
                : currency +
                  "" +
                  (totalAmount === 0 ? totalAmount : totalAmount.toFixed(2))}
            </Text>
          </View> */}
          <View style={styles.rightView}>
            <Text style={styles.total}>
              {currency ? (
                <>
                  {currency && currency.length > 1
                    ? (totalAmount === 0
                        ? totalAmount
                        : totalAmount.toFixed(2)) +
                      " " +
                      currency
                    : currency +
                      "" +
                      (totalAmount === 0
                        ? totalAmount
                        : totalAmount.toFixed(2))}
                </>
              ) : null}
            </Text>
          </View>
        </View>
      </Card>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 20,
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
  },
  titleCard: {
    color: GlobalStyles.colors.ePrimary.base,
    fontFamily: "NotoSans-Bold",
    fontSize: 12,
    textAlign: "left",
  },
  leftView: {
    float: "left",
    alignSelf: "stretch",
    width: "60%",
    marginTop: 5,
  },
  rightView: {
    float: "right",
    width: "40%",
    marginTop: 5,
  },
  accountHeaderContent: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    borderRadius: 4,
    justifyContent: "flex-start",
    width: "100%",
    textAlign: "right",
    textColor: GlobalStyles.colors.eWhite.base,
  },
  content: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
  },
  lineStyle: {
    borderWidth: 0.5,
    borderColor: GlobalStyles.colors.eLight.base,
    marginTop: 10,
    marginBottom: 6,
    marginHorizontal: -20,
    paddingHorizontal: -20,
    width: "112%",
  },
  lineStyleBottom: {
    borderWidth: 0.5,
    borderColor: GlobalStyles.colors.eLight.base,
    marginLeft: -5,
    marginBottom: 2,
    marginRight: -5,
    marginTop: 10,
  },
  accountsList: {
    justifyContent: "flex-start",
    fontSize: 12,
    width: "100%",
    textAlign: "left",
    fontFamily: "NotoSans-SemiBold",
  },
  totalAmount: {
    fontSize: 12,
    textAlign: "left",
    color: GlobalStyles.colors.ePrimary.base,
    fontFamily: "NotoSans-Bold",
  },
  total: {
    flexDirection: "row",
    fontSize: 15,
    textAlign: "right",
    color: GlobalStyles.colors.eSecondary.base,
    fontFamily: "NotoSans-Bold",
    marginRight: 10,
  },
  bigTextHeader: {
    color: GlobalStyles.colors.eWhite.base,
    textAlign: "left",
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
    justifyContent: "flex-start",
    // wordBreak: "break-all",
  },
  singleHeader: {
    color: GlobalStyles.colors.eWhite.base,
    textAlign: "center",
    fontFamily: "NotoSans-SemiBold",
    fontSize: 12,
    marginTop: 10,
    marginLeft: -15,
    justifyContent: "flex-start",
  },
  headerAmount: {
    justifyContent: "flex-end",
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
    textAlign: "right",
    color: GlobalStyles.colors.eWhite.base,
    paddingVertical: 10,
    paddingRight: 15,
  },
  amountList: {
    justifyContent: "flex-end",
    fontSize: 12,
    textAlign: "right",
    marginTop: 12,
    marginRight: 10,
    fontFamily: "NotoSans-SemiBold",
  },
  checkboxDiv: {
    flex: 1,
    flexDirection: "row",
    // alignItems: "center",
    // justifyContent: "left",
    marginLeft: -18,
    marginTop: -8,
    marginBottom: -8,
  },
});

import React, { useEffect, useState } from "react";
import AuthStack from "./auth/auth_stack";
import AuthenticatedLayout from "./authenticated/authenticated_layout";
import { useSelector } from "react-redux";
import setupAxiosInterceptors from "./axios-setup";
import { config } from "../../environment";
import AuthenticatedLayoutWO from "./authenticated/authenticated_layout_wo";
import { NotificationService } from "./authenticated/model/getNotification_service";
export const stackContext = React.createContext();
export default function GetStack() {
  const isUserLoggedIn = useSelector(
    state => state.authentication.isUserLoggedIn,
  );
  const [workModelType, setWorkModelType] = useState();
  useEffect(() => {
    // setupAxiosInterceptors();
  }, []);

  useEffect(() => {
    NotificationService.getLookups()
      .then(resp => {
        console.log(resp, "LLLLLLLLLLLLLLLLL12345");
        let workModelVal = resp?.find(each => each?.LookupKey == "WMT");
        console.log(workModelVal?.LookUpValue, "LLLLLLLLLLLLLLLLL12345");
        setWorkModelType(workModelVal?.LookUpValue);
        //setWorkModelType("WA"); for testing.
      })
      .catch(err => {
        console.log(err, "LLLLLLLLLLLLLLLLL12345");
      });
  }, []);

  return (
    <stackContext.Provider value={{ workModelType }}>
      {!isUserLoggedIn ? (
        <AuthStack />
      ) : workModelType === "WA" ? (
        <AuthenticatedLayout />
      ) : (
        <AuthenticatedLayoutWO />
      )}
    </stackContext.Provider>
  );
}

import { ScrollView, View, StyleSheet, Dimensions } from "react-native";
import ServiceRequestCard from "./_service_request_card";
import { useEffect, useState, useRef } from "react";
import { useSelector } from "react-redux";
import { SERVICE_TYPE_IDENTIFIER } from "../../account_switcher/constants";

export default function ServiceRequest() {
  let [card1, setCard1] = useState([]);
  let cardService = [
    {
      icon: "Exclamationmark-stroke-icon",
      label: "Report Outage",
      path: "ReportOutage",
    },
    {
      icon: "Turn-On_Off",
      label: "Turn On/Off",
      path: "TurnOnOff",
    },
    {
      icon: "Relocate-icon",
      label: "Relocate",
      path: "Relocate",
    },
    {
      icon: "more-stroke-icon",
      label: "More Services",
      path: "MORE",
    },
  ];
  let card2 = [
    {
      icon: "Payments-stroke-icon",
      label: "Payments",
      path: "PaymentsComplaint",
    },
    {
      icon: "bill-icon",
      label: "Billing",
      path: "Complaint_billing",
    },
    {
      icon: "General-icon",
      label: "General",
      path: "Complaint_General",
    },
    {
      icon: "User-stroke-icon",
      label: "Account",
      path: "Complaint_account",
    },
  ];
  const [allData, setAllData] = useState([]);

  const pathName = useSelector(state => state?.servicePath?.servicePath);
  const windowWidth = Dimensions.get("window").width;
  const [cardWidth, setCardWidth] = useState();
  const currentServiceTypeCd = useSelector(
    store => store?.meterDetails?.meterDetails?.getSaInfo?.serviceType,
  );
  useEffect(() => {
    if (currentServiceTypeCd === SERVICE_TYPE_IDENTIFIER?.WASTE_WATER) {
      let filterData = cardService.filter(item => item.path !== "TurnOnOff");
      setCard1(filterData);
    } else {
      setCard1(cardService);
    }
  }, [currentServiceTypeCd]);

  useEffect(() => {
    if (windowWidth) {
      if (windowWidth < 500) {
        setCardWidth(windowWidth - windowWidth / 16);
      } else {
        setCardWidth(400);
      }
    }
  }, [windowWidth]);
  useEffect(() => {
    scrollToFirstItem();
    if (card2 && card1) {
      let data = [
        { name: "SERVICE REQUESTS", data: card1 },
        { name: "COMPLAINTS", data: card2 },
      ];
      let contain = pathName && card2.filter(item => item.path === pathName);
      if (contain && contain.length > 0) {
        setAllData([data[1], data[0]]);
      } else {
        setAllData(data);
      }
    }
  }, [pathName, card1]);
  const scrollViewRef = useRef(null);
  const scrollToFirstItem = () => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({ x: 0, y: 0, animated: true });
    }
  };
  return (
    <ScrollView
      ref={scrollViewRef}
      horizontal={true}
      showsHorizontalScrollIndicator={false}
      pagingEnabled={true}>
      {allData &&
        allData.map((item, k) => {
          return (
            <View
              style={[
                k != 0 && styles.rightSpace,
                styles.card1,
                { width: cardWidth },
              ]}
              key={k}>
              <ServiceRequestCard title={item.name} data={item.data} />
            </View>
          );
        })}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  card1: {
    paddingLeft: 15,
  },
  rightSpace: {
    paddingRight: 15,
  },
});

import { createSlice } from "@reduxjs/toolkit";

const defaultBillRoutingReducer = createSlice({
    name: "defaultBillRouting",
    initialState: {
        defaultBillRouting :"",
    },
    reducers: {
        defaultBillRouting: (state, action) => {
            state.defaultBillRouting= action.payload
        },
    
    }
})

export const defaultBillRouting = defaultBillRoutingReducer.actions.defaultBillRouting;
export default defaultBillRoutingReducer.reducer;
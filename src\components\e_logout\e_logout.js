import { useEffect } from "react";
import { Pressable, StyleSheet, Text, View } from "react-native";

export default function Notifications(props) {

  useEffect(() => {
    props.navigation.navigate("Drawer")
  }, []);

  return (
    <>
     <Pressable style={styles.pressable} android_ripple={{color: "#ccc"}}>
      <View style={styles.container}>
        <Text style={styles.text}>LOGOUT</Text>
      </View>
      </Pressable>
      
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
    alignItems: "center",
    justifyContent: "center",    
  },
  pressable: {
    flex: 1,
    justifyContent: "center"
  }
});

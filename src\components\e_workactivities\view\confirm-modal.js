import { StyleSheet, View, Image, Platform } from "react-native";
import { GlobalStyles } from "../../app/global-styles";
import { Text, IconButton } from "react-native-paper";
import React, { useContext } from "react";
import TextLink from "react-native-text-link";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import Icon from "../../icon";
import { CIService } from "../../e_consumer-indexing/model/consumer-index-service";
import { useTranslation } from "react-i18next";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { config } from "../../../environment";
import { stackContext } from "../../app/get_stack";

export default function ConfirmModalScreen({ ticketNumber }) {
  const { t } = useTranslation();
  const { workModelType } = React.useContext(stackContext);
  const context = workModelType === "WA" ? drawerContext : drawerContextWO;
  const {
    setOpenMenu,
    setCIList,
    setSingleCI,
    setmenuFlag,
    setConfirmationModal,
    titlepopup,
    setOTPModal,
    confirmNote,
    updateCI,
    setupdateCI,
    setAllCIList,
    tempCIData,
    setTempCIData,
    reviewClose,
    setReviewClose,
    confirmModalType,
    setWOList,
    setSingleWO,
  } = useContext(context);

  const closeMenu = () => {
    if (confirmModalType === "workOrder") {
      setOTPModal(false);
      setConfirmationModal(false);
      setSingleWO(false);
      setWOList(true);
    } else {
      setConfirmationModal(false);
      setReviewClose(true);
      setOTPModal(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.titleRow}>
          <Text style={styles.title}>{t("CONFIRMATION")}</Text>
          <IconButton
            icon="close"
            color={GlobalStyles.colors.eBlack.base}
            onPress={closeMenu}
            style={styles.closeIcon}
            size={20}
          />
        </View>
        <View style={[styles.horizontalLineFirst, { marginTop: "2%" }]} />
        <View style={styles.imgViewCls}>
          <IconButton
            icon="check"
            iconColor={GlobalStyles.colors.eWhite.base}
            style={styles.greenBg}
            color={GlobalStyles.colors.eWhite.base}
            size={40}
          />
        </View>

        <View style={styles.contentTicket}>
          <Text style={styles.subtitle}>{confirmNote}</Text>
        </View>
        <View style={{ height: 30 }} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flext: 1,
    height: "100%",
    position: "relative",
    width: "100%",
    justifyContent: "center",
  },
  horizontalLineFirst: {
    borderBottomWidth: 0.6,
    width: "100%",
    borderColor: GlobalStyles.colors.eBackground3.base,
  },
  horizontalLineLast: {
    borderBottomWidth: 0.3,
    width: "100%",
    borderColor: GlobalStyles.colors.eOutline.selected,
  },
  content: {
    height: "100%",
    paddingVertical: 15,
  },
  titleRow: {
    paddingBottom: 15,
    paddingTop: 10,
  },
  contentLast: {
    paddingHorizontal: 15,
    paddingBottom: 15,
  },
  contentTicket: {
    paddingLeft: 20,
    paddingRight: 20,
    marginTop: 20,
  },
  title: {
    textAlign: "center",
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 16,
    fontFamily: "NotoSans-Bold",
    // textTransform: "capitalize",
    marginTop: 10,
  },
  subtitle: {
    textAlign: "center",
    color: GlobalStyles.colors.eSecondary.base,
    fontSize: 14,
    fontFamily: "NotoSans-SemiBold",
    marginBottom: 15,
    fontWeight: 600,
  },
  ticketReference: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 14,
    fontFamily: "NotoSans-Bold",
    paddingVertical: 10,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: GlobalStyles.colors.ePrimary.base,
    alignSelf: "center",
    paddingHorizontal: 30,
    fontWeight: 700,
  },
  subText: {
    textAlign: "center",
    color: GlobalStyles.colors.eBlack.base,
    fontSize: 11,
    fontFamily: "NotoSans-Medium",
    paddingTop: 10,
  },
  subText2: {
    textAlign: "center",
    color: GlobalStyles.colors.eBlack.base,
    fontSize: 11,
    fontFamily: "NotoSans-Medium",
    marginBottom: 20,
  },
  subLink: {
    textAlign: "center",
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 11,
    marginHorizontal: 15,
    marginTop: 20,
  },
  closeIcon: {
    position: "absolute",
    // top:0,
    right: 10,
    backgroundColor: GlobalStyles.colors.eFaint.selected,
  },
  imgCls: {
    height: 60,
    width: 60,
    padding: 5,
  },
  imgViewCls: {
    justifyContent: "center",
    alignItems: "center",
    marginTop: 30,
  },
  greenBg: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
  },
});

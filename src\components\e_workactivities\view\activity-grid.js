import React, { useState, useEffect } from "react";
import { DataTable, Divider, Text, TextInput } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import { ScrollView } from "react-native-gesture-handler";
import { StyleSheet, TouchableOpacity, View,Platform } from "react-native";
import { useTranslation } from "react-i18next";
import Ionicons from "react-native-vector-icons/Ionicons";
import Button from "../../common/_button";
const ActivityGrid = ({
  labourType,
  expenseItems,
  setExpenseItems,
  data,
  workStatus,
}) => {
  const { t } = useTranslation();
  const [page, setPage] = useState(0);
  const [numberOfItemsPerPageList] = useState([2, 3, 4]);
  const [itemsPerPage, onItemsPerPageChange] = useState(
    numberOfItemsPerPageList[0],
  );

  const [items, setItems] = useState([]);

  const [quantity, setQuantity] = useState("");

  const from = page * itemsPerPage;
  const to = Math.min((page + 1) * itemsPerPage, expenseItems.length);

  useEffect(() => {
    setPage(0);
  }, [itemsPerPage]);

  useEffect(() => {
    console.log(data, workStatus, "YYYYYYYYYYYYYYYYYYY 33");
    if (workStatus == "CO") {
      let objData = [];
      data.map(e => {
        if (e?.resourceClass === "UNPLANNED_RESOURCES") {
          let obj = {
            Description: e?.resourceType,
            Qunatity: e?.ActualDuration,
          };
          objData.push(obj);
        }
      });
      setItems(objData);
    }
  }, [data, workStatus]);

  const addRow = () => {
    setExpenseItems([...expenseItems, { name: "", mobile: "" }]);
  };
  
  // Update a specific row
  const updateRow = (index, field, value) => {
    const updatedRows = expenseItems.map((row, i) =>
      i === index ? { ...row, [field]: value } : row,
    );
    setExpenseItems(updatedRows);
  };

  console.log(items, "TTTTTTTTRRRRRRRRRRRRRRRRRRRRRrr");

  return (
    <>
      {/* <View>
        <Text
          style={{
            paddingLeft: 16,
          }}>
          {t('ACTIVITY')}
        </Text>
      </View> */}
      <DataTable
        style={{
          paddingBottom: 10,
          marginBottom: 10,
        }}>
        <Divider />
        <ScrollView
          horizontal
          persistentScrollbar
          contentContainerStyle={{ flexDirection: "column" }}>
          <DataTable.Header>
            {workStatus !== "CO" && (
              <DataTable.Title style={{ width: 50 }}>
                <Text style={{ width: 100, textWrap: "wrap" }}>{t("ADD")}</Text>
              </DataTable.Title>
            )}
            <DataTable.Title style={{ maxwidth: 200, width: 200 }}>
              <Text style={{ width: 100, textWrap: "wrap" }}>
                {t("DESCRIPTION")}
              </Text>
            </DataTable.Title>
            <DataTable.Title style={{ width: workStatus == "CO" ? 150 : 100 }}>
              <Text style={{ width: 100, textWrap: "wrap" }}>
                {t("AMOUNT")} ($)
              </Text>
            </DataTable.Title>
          </DataTable.Header>

          {workStatus !== "CO" &&
            expenseItems.map((item, index) => (
              <DataTable.Row
                key={item.index}
                style={{
                  flex: 1,
                  width: "100%",
                  height: 50,
                  marginBottom: 15,
                }}>
                {/* {item.activityType} */}
                <DataTable.Cell
                  style={{
                    width: 50,
                  }}>
                  <TouchableOpacity
                    onPress={addRow}
                    style={{ width: "80%", alignItems: "center" }}>
                    <Ionicons
                      name="add-circle-sharp"
                      color={GlobalStyles.colors.ePrimary.base}
                      size={32}
                    />
                  </TouchableOpacity>
                </DataTable.Cell>
                <DataTable.Cell
                  style={{
                    maxwidth: 200,
                    width: 200,
                  }}>
                  <View style={{ width: "80%", alignItems: "center" }}>
                    <TextInput 
                      placeholderTextColor="gray"
                      mode={Platform.OS=="ios"?"":"outlined"}
                      // multiline={true}
                      name="description"
                      value={item.description}
                      enablesReturnKeyAutomatically
                      // onChangeText={quantity => setQuantity(quantity)}
                      onChangeText={text =>
                        updateRow(index, "description", text)
                      }
                      outlineColor={GlobalStyles.colors.eDark.hover}
                      activeOutlineColor={GlobalStyles.colors.ePrimary.base}
                      persistentScrollbar={true}
                      style={{
                        width: 190,
                        height: 30,
                      }}
                    />
                  </View>
                </DataTable.Cell>
                <DataTable.Cell
                  style={{
                    width: 100,
                  }}>
                  <TextInput
                    key={item.index}
                    placeholderTextColor="gray"
                    mode={Platform.OS=="ios"?"":"outlined"}
                    keyboardType="numeric"
                    name="amount"
                    value={item?.amount}
                    enablesReturnKeyAutomatically
                    //  onChangeText={quantity => setQuantity(quantity)}
                    onChangeText={text => updateRow(index, "amount", text)}
                    outlineColor={GlobalStyles.colors.eDark.hover}
                    activeOutlineColor={GlobalStyles.colors.ePrimary.base}
                    persistentScrollbar={true}
                    style={{
                      height: 30,
                      width: 90,
                    }}
                  />
                </DataTable.Cell>
              </DataTable.Row>
            ))}

          {workStatus == "CO" &&
            items.map((item, index) => (
              <DataTable.Row
                key={item.index}
                style={{
                  flex: 1,
                  width: "100%",
                  height: 50,
                  marginBottom: 15,
                }}>
                {/* {item.activityType} */}
                <DataTable.Cell
                  style={{
                    maxwidth: 200,
                    width: 200,
                  }}>
                  <View style={{ width: "80%", alignItems: "center" }}>
                    <Text>{item?.Description}</Text>
                  </View>
                </DataTable.Cell>
                <DataTable.Cell
                  style={{
                    width: 130,
                  }}>
                  <Text>{item?.Qunatity}</Text>
                </DataTable.Cell>
              </DataTable.Row>
            ))}
        </ScrollView>
      </DataTable>
    </>
  );
};

const styles = StyleSheet.create({
  card: {
    borderTopEndRadius: 10,
    borderTopStartRadius: 10,
    borderBottomEndRadius: 0,
    borderBottomStartRadius: 0,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    padding: 15,
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    margin: 10,
  },
  imgContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 0,
  },
  imgSideBySideView: {
    flex: 1,
    padding: 0,
  },
  imgSideBySideView2: {
    flex: 1,
    marginBottom: 10,
  },
  wrapDirection: {
    flexDirection: "row",
    flexWrap: "wrap",
    alignContent: "space-between",
  },
  bigTextHeader: {
    color: GlobalStyles.colors.eRich.base,
    textAlign: "left",
    fontSize: 14,
    justifyContent: "flex-start",
    // wordBreak: "break-all",
  },
  titleCard: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontWeight: "700",
    fontFamily: "NotoSans-Bold",
  },
  backButtonWrapper: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 3,
  },
  paddingRight: {
    paddingRight: 10,
  },
  scrollViewContent: {
    flexGrow: 1,
    padding: 20, // Adjust the padding as per your design
  },
  checkListDescription: {
    fontSize: 14,
    color: GlobalStyles.colors.ePrimary.base,
  },
  checkListDoneBtnStyle: {
    marginVertical: 10,
    marginTop: 20,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  checkListTextInput: {
    // minWidth: 150,
    flex: 1,
    height: 30,
    justifyContent: "center",
    marginRight: 10,
  },
  checkListIdLabel: {
    fontFamily: "NotoSans-SemiBold",
    fontSize: 13,
  },
  checkListStatus: {
    fontSize: 13,
    fontFamily: "NotoSans-SemiBold",
  },
  checkListItemContainer: {
    marginLeft: -25,
    marginVertical: -5,
  },
  camClass: {
    marginLeft: 25,
  },
  container: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: 10,
    borderRadius: 5,
  },
  containerEquip: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    // marginHorizontal: 10,
    // borderRadius: 5,
  },
  containerDiv: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: 10,
    marginTop: 0,
    borderRadius: 5,
  },
  containerChecklist: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: 10,
    // marginTop: 15,
    borderRadius: 5,
  },
  containerNoChecklist: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    alignItems: "center",
    marginVertical: 15,
    borderRadius: 5,
  },
  modalText: {
    fontSize: 13,
    paddingTop: 10,
    paddingHorizontal: 10,
    width: "100%",
    marginBottom: 15,
    textAlign: "flex-start",
    //backgroundColor: GlobalStyles.colors.ePrimary.base,
    color: GlobalStyles.colors.ePrimary.base,
    fontWeight: "bold",
    flexDirection: "row", // Horizontal alignment
    justifyContent: "space-between", // Space expenseItems evenly,
    fontWeight: "bold",
  },
  containerSubmit: {
    marginHorizontal: 10,
    marginBottom: 150,
    marginTop: 15,
  },
  containerStyle: {
    backgroundColor: "white",
    padding: 70,
    marginHorizontal: 20,
  },
  displayFlex: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 20,
  },
  ePrimary: {
    color: GlobalStyles.colors.ePrimary.base,
  },
  flexRowCenter: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  flexWrap: {
    flex: 1,
  },
  flexWrapAsset: {
    flex: 1,
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  workActivityDetailContainer: {
    paddingHorizontal: 8,
    paddingVertical: 10,
  },
  labelHeader: {
    fontFamily: "NotoSans-Bold",
    fontSize: 14,
    color: GlobalStyles.colors.ePrimary.base,
  },
  nolabelHeader: {
    fontFamily: "NotoSans-Bold",
    fontSize: 14,
    color: GlobalStyles.colors.ePrimary.base,
  },
  labelStyle: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eRich.base,
  },
  labelStyleError: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eDanger.dark,
  },
  inputContainer: {
    marginRight: 10,
    marginVertical: -20,
  },
  valueStyle: {
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  valueStyleAsset: {
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
    flexDirection: "row", // Aligns icon and text horizontally
    alignItems: "center",
    justifyContent: "center",
  },
  labelWidth: {
    flex: 1,
  },
  labelWidthMeter: {
    marginLeft: -25,
  },
  labelWidthDuration: {
    flex: 1,
  },
  labelWidthTime: {
    width: 150,
    marginHorizontal: 20,
    marginTop: 15,
    marginBottom: 10,
  },
  opacityDimmed: {
    opacity: 0.5,
  },
  otpTextInputStyle: {
    borderBottomWidth: 2,
    borderBottomColor: GlobalStyles.colors.ePrimary.base,
    color: GlobalStyles.colors.ePrimary.base,
  },
  overdueStyle: {
    color: GlobalStyles.colors.eTertiary.base,
  },
  hr: {
    borderBottomWidth: 1, // Thickness of the line
    borderBottomColor: "black", // Line color
    width: "100%", // Full width
    marginBottom: 10, // Space above and below the line
  },
  pastDueStyle: {
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderWidth: 1,
    borderColor: "#d5d5d5",
    backgroundColor: "#fef9e8",
    display: "flex",
    flexDirection: "row",
    gap: 10,
    alignItems: "center",
  },

  rowFlexContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 5,
  },
  rowFlexContainerError: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  centeredView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  rowFlexContainerDate: {
    marginHorizontal: 20,
    marginTop: 10,
  },
  snackbarWrapper: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
  textInputWrapper: {
    paddingVertical: 3,
    flex: 1,
    // width: Dimensions.get("screen").width - 100,
  },
  workOrderCompletionTextStyle: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 13,
  },
  activityItem: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 10,
    // marginTop: -5,
  },
  activityItemInner: {
    display: "flex",
    flexDirection: "row",
    flex: 1,
    gap: 20,
  },
  iconContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 10,
  },
  contentContainer: {
    flex: 1,
  },
  displayFlex: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  headerStyleNumber: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderRow: {
    display: "flex",
    flexDirection: "row",
    paddingVertical: 1,
    justifyContent: "space-between",
    paddingRight: 20,
    marginTop: -5,
    // textAlign: 'left'
  },
  subHeaderRowMinWidth: {
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderPriority: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eRich.base,
    textTransform: "capitalize",
    marginTop: -5,
    textAlign: "right",
  },
  subHeaderRowStatus: {
    display: "flex",
    flexDirection: "row",
    paddingVertical: 1,
    justifyContent: "space-between",
    paddingRight: 20,
    marginBottom: 10,
  },
  subHeaderRowMinWidth: {
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  modalView: {
    width: "95%",
    margin: 2,
    backgroundColor: "white",
    borderRadius: 10,
    borderWidth: 1,
    borderStyle: "solid",
    borderColor: GlobalStyles.colors.ePrimary.base,
    padding: 5,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  subHeaderRowMinWidth: {
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderStatus: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eRich.base,
    textTransform: "capitalize",
    marginTop: -15,
    textAlign: "right",
  },
  arrowIconStyle: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  lineStyle: {
    // borderWidth: 0.3,
    borderColor: GlobalStyles.colors.eMedium.hover,
    marginTop: 1,
    width: "90%",
  },
  lineStyleInfo: {
    borderWidth: 0.3,
    borderColor: GlobalStyles.colors.eSeparationLine.base,
    marginTop: 0,
    width: "100%",
  },
  lineStyleInfoCheckList: {
    borderWidth: 0.3,
    borderColor: GlobalStyles.colors.eSeparationLine.base,
    marginBottom: 5,
    width: "100%",
  },
  btnContainer: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 0,
    marginBottom: 15,
  },
  disabledCancleStyle: {
    // opacity: 0.5,
    backgroundColor: GlobalStyles.colors.ePage.base,
    borderColor: GlobalStyles.colors.eLight.selected,
    borderWidth: 1,
  },
  disableColor: {
    color: GlobalStyles.colors.eLight.selected,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  cancelText: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  onHoldText: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  bgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    marginLeft: 10,
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginLeft: 10,
  },
  cancelBgonHold: {
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    color: GlobalStyles.colors.eWhite.base,
    borderColor: GlobalStyles.colors.ePrimary.base,
  },
  cancelBg: {
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    borderWidth: 1,
    borderColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 10,
  },
  disabledStyle: {
    opacity: 0.5,
    backgroundColor: GlobalStyles.colors.eBackground.selected,
  },
});

export default ActivityGrid;

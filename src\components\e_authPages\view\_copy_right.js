import { StyleSheet, View } from "react-native";
import { GlobalStyles } from "../../app/global-styles";
import { Text } from "react-native-paper";
import { useTranslation } from 'react-i18next';

export default function CopyRight() {
  const { t } = useTranslation();

  return (
    <View style={styles.mainView}>
      <Text style={styles.copyrightText}>
        {t('COPYRIGHT_MSG')}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  mainView: {
    justifyContent: "flex-end",
    alignItems: "center",
    height: 50,
    paddingBottom: "1.5%",
  },
  copyrightText: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
  },
});

import React, { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  Image,
  Dimensions,
  Pressable,
  Platform,
  StatusBar,
} from "react-native";
import { GlobalStyles } from "../app/global-styles";
import LoginPage from "../e_login/e_login_page";
import Register from "../e_register/e_register";
import CopyRight from "./view/_copy_right";
import AuthButtons from "./view/_auth_buttons";
import Icon from "../icon";
import { Card, Text } from "react-native-paper";
import RegisterSuccess from "../e_register/view/e_register_confirm";
import OTPVerification from "../e_register/view/e_register_OTP";
import VerificationScreen from "../e_register/view/e_verification-screen";
import LoginOTP from "../e_login_otp/e_login_otp";
import ForgotPassword from "../forgotPassword/e_forgot_password";
import ValidateOTP from "../forgotPassword/e_validate_otp";
import UpdatePassword from "../forgotPassword/e_update_password";
import UpdatePasswordSuccess from "../forgotPassword/e_update_password_success";
import { useTranslation } from 'react-i18next';

const width = Dimensions.get("window").width;
const { height } = Dimensions.get("window");

export const registerContextWO = React.createContext();
export default function AuthPagesWO() {
  const [verificationScreen, setVerificationScreen] = React.useState(false);
  const [verificationDetails, setVerifyDetails] = React.useState([]);
  const [showRegisterModal, setRegisterModal] = React.useState(false);
  const [showRegisterSuccess, setRegisterSuccess] = React.useState(false);
  const [email, setEmailUsername] = React.useState("");
  const [mobileNumber, setmobileNumber] = React.useState();
  const [closePopup, setClosePopup] = React.useState();
  const [isLoginPage, setIsLoginPage] = useState("Login");
  const { width } = Dimensions.get("window");
  const { height } = Dimensions.get("window");
  const { t } = useTranslation();

  const backHandler = () => {
    if (showRegisterModal) {
      setRegisterModal(false);
    }
    if (verificationScreen) {
      setVerificationScreen(false);
    }
  };
  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="light-content"
        hidden={false}
        backgroundColor={GlobalStyles.colors.eBlack.base}
        translucent={true}
      />
      <registerContextWO.Provider
        value={{
          showRegisterModal,
          setRegisterModal,
          showRegisterSuccess,
          setRegisterSuccess,
          setIsLoginPage,
          email,
          setEmailUsername,
          setVerificationScreen,
          setVerifyDetails,
          isLoginPage,
          setmobileNumber,
          mobileNumber,
        }}>
        <View
          style={{
            backgroundColor: GlobalStyles.colors.eWhite.base,
            height: height,

            // top: "2%",
            // position: "relative",
          }}>
          {showRegisterModal || verificationScreen ? (
            <Icon
              name="back"
              style={styles.blueBg}
              color={GlobalStyles.colors.eWhite.base}
              size={18}
              onPress={backHandler}
            />
          ) : null}
          <View style={styles.clientLogoView}>
            <Image
              source={require("../../../assets/rectangle.png")}
              style={styles.backgroundImage}
            />
            <Image
              source={require("../../../assets/pea-logo.png")}
              style={styles.clientLogo}
            />
          </View>
          <View style={styles.authBody}>
            <Image
              source={require("../../../assets/auth-background.png")}
              style={styles.authBackgroundImage}
            />
            <Image
              source={require("../../../assets/authRectangle.png")}
              style={styles.authRectangle}
            />
            <View style={styles.authPages}>
              {isLoginPage === "Login" || isLoginPage === "LoginOTP" ? (
                <>
                  <Text
                    variant="headlineLarge"
                    style={{ color: "white", textAlign: "center" }}>
                    {t('WELCOME')}
                  </Text>
                  <Text
                    variant="titleMedium"
                    style={{ color: "white", textAlign: "center" }}>
                    {t('LOGIN_MSG')}
                  </Text>
                  <View style={styles.loginPage}>
                    {isLoginPage == "LoginOTP" ? <LoginOTP /> : <LoginPage />}
                  </View>
                </>
              ) : isLoginPage === "ForgotPassword" ? (
                <ForgotPassword />
              ) : isLoginPage === "FORGOT_ValidateOtp" ? (
                <ValidateOTP />
              ) : isLoginPage === "UpdatePassword" ? (
                <UpdatePassword />
              ) : isLoginPage === "updatePasswordSuccess" ? (
                <UpdatePasswordSuccess />
              ) : (
                isLoginPage === "Register" &&
                !showRegisterModal &&
                !verificationScreen && (
                  <>
                    <View style={styles.registerPage}>
                      <Register />
                    </View>
                  </>
                )
              )}
              {showRegisterModal ? (
                <View style={styles.OTPPage}>
                  <OTPVerification onPressBack={backHandler} />
                </View>
              ) : null}

              {verificationScreen ? (
                <>
                  <View style={styles.verificationPage}>
                    <VerificationScreen
                      verificationDetails={verificationDetails}
                      onPressBack={backHandler}
                    />
                  </View>
                  {/* <Icon
                  name="Login-Bottom-curve-graphic"
                  size={width / 3.6}
                  color={GlobalStyles.colors.eSecondary.base}
                  style={{
                    bottom: -40,
                    // Platform.OS === "ios" ? 106 : 110,
                    position: "absolute",
                  }}
                /> */}
                  {/* <Icon
                  name="Login-Bottom-curve-graphic"
                  size={width / 3.6}
                  color={GlobalStyles.colors.ePrimary.base}
                  style={{
                    bottom: -60,
                    //  Platform.OS === "ios" ? 86 : 90,
                    position: "absolute",
                  }}
                /> */}
                  {/* <View style={styles.copyRight}>
                <CopyRight />
              </View> */}
                </>
              ) : null}
            </View>
            <View style={styles.appLogoView}>
              <Image
                style={styles.appLogo}
                source={require("../../../assets/impresaWhiteLogo.png")}
              />
            </View>
            <View>
              <View style={styles.copyRight}>
                <CopyRight />
              </View>
            </View>
          </View>
          {showRegisterSuccess ? (
            <>
              <Pressable
                style={styles.successDrawerOpacity}
                onPress={() => setShowpopup(false)}></Pressable>
              <Card style={styles.accountStyle}>
                <RegisterSuccess
                  showProfilePopup={showRegisterModal}
                  hideModel={closePopup}
                />
              </Card>
            </>
          ) : null}
        </View>
      </registerContextWO.Provider>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    position: "relative",
    marginTop: Platform.OS === "ios" ? -20 : 0,
  },
  authBody: {
    position: "relative",
  },
  authRectangle: {
    position: "absolute",
    justifyContent: "center",
    width: "100%",
    opacity: 1,
  },

  authPages: {
    position: "absolute",
    justifyContent: "center",
    width: "100%",
    alignContent: "center",
    paddingTop: "5%",
  },
  loginPage: {
    marginTop: "10%",
    flex: 1,
    justifyContent: "center",
  },

  clientLogoView: {
    alignItems: "center",
    width: "100%",
  },
  authBackgroundImage: {
    height: "100%",
    width: "100%",
  },
  backgroundImage: {
    height: 130,
  },
  clientLogo: {
    height: 40,
    width: 248,
    marginTop: "13%",
    position: "absolute",
    right: "10%",
  },
  appLogoView: {
    height: height / 3.3,
    position: "absolute",
    justifyContent: "center",
    alignContent: "center",
    bottom: "14%",
    marginLeft: "28%",
  },
  appLogo: {
    height: 40,
    width: 200,
  },
  copyRight: {
    position: "absolute",
    height: height / 4.3,
    bottom: 1,
    marginLeft: "20%",
  },
});

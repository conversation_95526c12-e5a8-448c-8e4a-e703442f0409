import React, { useState } from "react";
import { View, StyleSheet, Image, Linking } from "react-native";
import { Card, Title } from "react-native-paper";
import MapView, { <PERSON><PERSON>, ZoomControl } from "react-native-maps";
import { GlobalStyles } from "../../app/global-styles";

const Map = markers => {
  const initialRegion = {
    latitude: 13.850599,
    longitude: 100.554966,
    latitudeDelta: 0.1,
    longitudeDelta: 0.1,
  };
  const handleMarkerPress = marker => {
    // Extract the coordinates from the marker
    const { latitude, longitude } = marker.coordinate;

    // Use the Linking API to open Google Maps with directions
    const url = `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`;
    Linking.openURL(url);
  };
  return (
    <View style={styles.container}>
      <MapView style={styles.map} initialRegion={initialRegion}>
        {markers.markers.map((marker, index) => (
          <Marker
            key={marker.id || index}
            coordinate={marker.coordinate}
            title={marker.title}
            //pinColor={marker.color}
            onPress={() => handleMarkerPress(marker)}>
            <Image
              source={
                marker.type === "Completed"
                  ? require("../../../../assets/dashboard-icons/map/completed.png")
                  : marker.type === "Created"
                  ? require("../../../../assets/dashboard-icons/map/open.png")
                  : require("../../../../assets/dashboard-icons/map/overdue.png")
              }
              style={{ width: 18, height: 25 }}
            />
            <Image
              source={require("../../../../assets/dashboard-icons/map/marker.png")}
              style={{
                width: 10,
                height: 10,
                position: "absolute",
                top: 5,
                left: 4,
              }}
            />
          </Marker>
        ))}
      </MapView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderBottomEndRadius: 15, // Add border radius
    borderBottomStartRadius: 15, // Add border radius
    overflow: "hidden",
    marginHorizontal: -15,
    marginBottom: -15,
  },
  map: {
    height: 250,

    borderRadius: 15,
  },
  title: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontWeight: "700",
  },
  zoomControl: {
    position: "absolute",
    top: 16,
    right: 16,
  },
});

export default Map;

import { Card, Text } from "react-native-paper";
import {
  StyleSheet,
  View,
  Dimensions,
  Linking,
  ActivityIndicator,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { GlobalStyles } from "../../app/global-styles";
import { LineChart } from "react-native-chart-kit";
import Svg, { Line } from "react-native-svg";
import moment from "moment";
import TextLink from "react-native-text-link";
import { useEffect, useState } from "react";
import usageWeekGraph from "../model/service";
import { useSelector } from "react-redux";
import { DATE_FORMAT, USAGE_DAYS_LABEL, USAGE_LEGENDS } from "../constants";
import { config } from "../../../environment";

export default function WeeklyComparisonUsage() {
  const accountId = useSelector(
    state => state?.meterDetails?.meterDetails?.accountId,
  );
  const saId = useSelector(
    state => state?.meterDetails?.meterDetails?.getSaInfo?.saId,
  );
  const [usageData, setUsageData] = useState({});
  const [loadingUsage, setLoadingUsage] = useState(false);
  const [bearer, setBearer] = useState({});
  const [unit, setUnit] = useState(false);

  const fetchWeeklyUsageData = async () => {
    try {
      const dates = {
        from_date: moment()
          .subtract(1, "weeks")
          .startOf("isoWeek")
          .format(DATE_FORMAT),
        to_date: moment().endOf("isoWeek").format(DATE_FORMAT),
      };

      if (dates && saId && accountId) {
        setLoadingUsage(true);
        const response = await usageWeekGraph(dates, saId, accountId);
        if (response?.data?.getUsageWeek) {
          setUsageData(response?.data?.getUsageWeek);
        } else {
          setLoadingUsage(false);
          setUsageData({});
          setData(defaultData)
        }
      }
    } catch (error) {
      setLoadingUsage(false);
      setUsageData({});
      console.log(error);
    }
  };

  useEffect(() => {
    if (saId && accountId) {
      setLoadingUsage(true);
      fetchWeeklyUsageData();
    }
  }, [saId, accountId]);

  useEffect(() => {
    AsyncStorage.getItem("bearer").then(bearer => {
      bearer = JSON.parse(bearer);
      setBearer(bearer);
    });
  }, []);

  const [smallDigit, setSmallDigit] = useState(false);
  const [usageUnit, setUsageUnit] = useState({});
  const [previousWeekData, SetPrevWeek] = useState({});
  const [currentWeekData, setCurrWeek] = useState({});
  const defaultData = {
    labels: USAGE_DAYS_LABEL,
    datasets: [
      {
        data: [],
        strokeWidth: 2,
        color: (opacity = 1) => `rgba(21, 83, 124, ${opacity})`,
        strokeOpacity: 1,
      },
      {
        data: [],
        strokeWidth: 2,
        color: (opacity = 1) => `rgba(0, 171, 106, ${opacity})`,
        strokeOpacity: 1,
      },
    ],
    // legend: USAGE_LEGENDS,
  };
  const [data, setData] = useState(defaultData);
  useEffect(() => {
    if (usageData?.usage) {
      setUsageUnit(usageData?.usageUnit);
      SetPrevWeek(usageData?.usage?.[0]);
      setCurrWeek(usageData?.usage?.[1]);
    }
  }, [usageData, saId, accountId]);
  useEffect(() => {
    if (previousWeekData && currentWeekData) {
      setData({
        labels: USAGE_DAYS_LABEL,
        datasets: [
          {
            data: previousWeekData?.usage || [],
            strokeWidth: 2,
            color: (opacity = 1) => `rgba(21, 83, 124, ${opacity})`,
            strokeOpacity: 1,
          },
          {
            data: currentWeekData?.usage || [],
            strokeWidth: 2,
            color: (opacity = 1) => `rgba(0, 171, 106, ${opacity})`,
            strokeOpacity: 1,
          },
        ],
        // legend: USAGE_LEGENDS,
      });
      setLoadingUsage(false);
    }
  }, [currentWeekData, previousWeekData, USAGE_DAYS_LABEL, saId, accountId]);
  useEffect(() => {
    if (data && data?.datasets) {
      let pushDigit = [];
      let nonZero = [];
      data.datasets.map(item => {
        item.data.map(digit => {
          Math.floor(digit).toString().length < 4
            ? pushDigit.push(true)
            : pushDigit.push(false);
        });
        nonZero.push(...item.data.filter(i => i !== 0));
      });
      pushDigit.includes(false) ? setSmallDigit(false) : setSmallDigit(true);
      nonZero.length > 0 ? setUnit(true) : setUnit(false);
    }
  }, [data?.datasets, saId, accountId]);
  const allConsumption = () => {
    if (bearer && bearer.acessToken) {
      Linking.openURL(
        config.constants.BASE_URL +
          "/nativeRedirect?nativeAppToken=" +
          bearer.acessToken +
          "&path=usage-stats" +
          "&accountNumber=" +
          accountId +
          "&serviceID=" +
          saId,
      );
    }
  };

  return (
    <Card style={styles.card}>
      <View style={{ minHeight: Dimensions.get("window").height / 11 }}>
        {loadingUsage ? (
          <View style={styles.loadingUsage}>
            <ActivityIndicator
              size="large"
              color={GlobalStyles.colors.ePrimary.base}
            />
          </View>
        ) : (
          <View>
            <View style={styles.wrapDirection}>
              <View style={{ width: "65%" }}>
                <Text style={styles.titleCard}>WEEKLY USAGE COMPARISON</Text>
              </View>
              <View style={styles.rightContent}>
                <View style={[styles.weekStyle, styles.primaryColorStyle]} />
                <View style={styles.widthStyle80}>
                  <Text style={styles.fontText}>Previous week</Text>
                </View>
                <View style={[styles.weekStyle, styles.secondaryColorStyle]} />
                <View style={styles.widthStyle80}>
                  <Text style={styles.fontText}>Current week</Text>
                </View>
              </View>
            </View>
            <View
              style={{
                flex: 1,
                width: "100%",
                flexDirection: "row",
              }}>
              {usageUnit && unit && (
                <View
                  style={{
                    width: 8,
                    height: 220,
                    position: "absolute",
                    zIndex: 1,
                  }}>
                  <Svg height={80} width={10}>
                    <Line
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="70"
                      stroke={GlobalStyles.colors.eLight.selected}
                      strokeWidth="1"
                    />
                  </Svg>
                  <Text
                    style={{
                      textAlign: "center",
                      height: 60,
                      width: 40,
                      color: GlobalStyles.colors.eDark.hover,
                      transform: [{ rotate: "-90deg" }],
                      fontSize: 12,
                    }}>
                    {usageUnit}
                  </Text>
                  <Svg height={80} width={10}>
                    <Line
                      x1="0"
                      y1="0"
                      x2="0"
                      y2="70"
                      stroke={GlobalStyles.colors.eLight.selected}
                      strokeWidth="1"
                    />
                  </Svg>
                </View>
              )}
              <View style={{ marginLeft: smallDigit ? -10 : 10 }}>
                <LineChart
                  data={data}
                  width={Dimensions.get("window").width - 70}
                  height={220}
                  yAxisSuffix=""
                  yAxisLabel=""
                  yAxisInterval={1} // optional, defaults to 1
                  withShadow={false}
                  fromZero={true}
                  withVerticalLines={false}
                  strokeOpacity={1}
                  chartConfig={{
                    useShadowColorFromDataset: true,
                    backgroundColor: GlobalStyles.colors.eWhite.base,
                    backgroundGradientFrom: GlobalStyles.colors.eWhite.base,
                    backgroundGradientFromOpacity: 1,
                    backgroundGradientTo: GlobalStyles.colors.eWhite.base,
                    backgroundGradientToOpacity: 0.5,
                    decimalPlaces: 1, // optional, defaults to 2dp
                    color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
                    labelColor: (opacity = 1) => `rgba(92, 94, 96, ${opacity})`,
                    propsForLabels: {
                      fontSize: 12, // Adjust the font size for the legend labels
                    },
                    style: {
                      borderRadius: 10,
                    },
                    propsForDots: {
                      r: "4",
                      strokeWidth: "2",
                    },
                    propsForBackgroundLines: {
                      strokeDasharray: "",
                      strokeDashoffset: 15,
                      opacity: 0.3,
                      color: "#D9DBEA",
                    },
                  }}
                  style={{
                    marginVertical: 5,
                    borderRadius: 10,
                    marginLeft: -5,
                    paddingBottom: 0,
                    alignSelf: "center",
                  }}
                />
              </View>
            </View>
          </View>
        )}
      </View>
      {!loadingUsage && data ? (
        <TextLink
          textStyle={{
            textDecorationLine: "underline",
            textAlign: "center",
            fontSize: 12,
            fontWeight: "400",
            fontFamily: "NotoSans-Regular",
          }}
          pressingLinkStyle={{ color: GlobalStyles.colors.ePrimary.base }}
          textLinkStyle={{
            color: GlobalStyles.colors.ePrimary.base,
          }}
          links={[
            {
              text: "View all consumptions",
              onPress: allConsumption,
            },
          ]}>
          View all consumptions
        </TextLink>
      ) : null}
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    width: "100%",
    padding: 15,
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    backgroundColor: GlobalStyles.colors.eWhite.base,
  },
  titleCard: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontWeight: "700",
    fontFamily: "NotoSans-Bold",
  },
  cardHeight: {
    alignSelf: "center",
    marginTop: "8%",
    fontSize: 12,
  },
  loadingUsage: {
    display: "flex",
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    height: 200,
  },
  wrapDirection: {
    flexDirection: "row",
    flexWrap: "wrap",
    alignContent: "space-between",
  },
  rightContent: {
    width: "35%",
    flexDirection: "row",
    flexWrap: "wrap",
    alignContent: "space-between",
  },
  weekStyle: {
    width: 9,
    borderRadius: 25,
    height: 9,
    marginRight: "5%",
    marginTop: 5,
  },
  primaryColorStyle: {
    backgroundColor: GlobalStyles.colors.ePrimary.base,
  },
  secondaryColorStyle: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
  },
  fontText: {
    fontSize: 9,
    fontFamily: "NotoSans-Regular",
  },
  widthStyle80: {
    width: "80%",
  },
});

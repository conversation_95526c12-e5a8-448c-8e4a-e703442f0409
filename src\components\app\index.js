import { Navigation<PERSON>ontainer } from "@react-navigation/native";
import { useEffect } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import GetStack from "./get_stack";
import { useDispatch } from "react-redux";
import { updateLoggedInStatus } from "../../redux/slices/authenticationReducer";
import { StatusBar } from "react-native";
import { GlobalStyles } from "./global-styles";
import SplashScreen from "react-native-splash-screen";

export default function AppMain() {
  const dispatch = useDispatch();

  useEffect(() => {
    fetchToken();
  }, []);

  const onNavigationReady = () => {
    setTimeout(() => {
      SplashScreen.hide(); // just hide the splash screen after navigation ready
    }, 1000);
  };

  const fetchToken = async () => {
    let bearer = null;
    try {
      bearer = await AsyncStorage.getItem("bearer");
    } catch (error) {
      console.log("Unable to fetch bearer token from async storage", error);
    }
    if (bearer) {
      dispatch(updateLoggedInStatus(true));
    }
  };

  return (
    <>
      <StatusBar
        barStyle="light-content"
        hidden={false}
        backgroundColor={GlobalStyles.colors.ePrimary.base}
        translucent={true}
      />
      <NavigationContainer onReady={onNavigationReady}>
        <GetStack />
      </NavigationContainer>
    </>
  );
}

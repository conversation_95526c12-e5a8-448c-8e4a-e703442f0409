import axios from "axios";
import { config } from "../../../../environment";

export const complaintsService = {
  getTicketDropdowns,
  getTicketReferenceNumber,
};

function getTicketReferenceNumber(
  accountId,
  ticketTypeCode,
  issueDesc,
  ticketAttachments,
  ticketCategoryCode
) {
  for (var i = 0; i < issueDesc.length; ++i) {
    if (issueDesc[i] == "\n") {
      issueDesc = issueDesc.replace(/\n/, " ");
    }
  }
  let headers = {
    accountId: accountId,
  };
  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.COMMUNICATION_SERVICE_BASE_URL,
        {
          query: `query
          createTicket($input:CreateTicketInput!){
                  createTicket(input:$input)
                {
                ticketId
                }} `,
          variables: {
            input: {
              ticketTypeCd: ticketTypeCode,
              ticketDescription: issueDesc.toString(),
              ticketTypeCategoryCd: ticketCategoryCode,
              ticketAttachments: ticketAttachments,
            },
          },
        },
        {
          headers: headers,
        }
      )
      .then(function (response) {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
}

function getTicketDropdowns(languageCode) {
  let APIQuery =
    `{getTicketDropdowns(input:{languageCode:"` + languageCode + `"})}`;
  return new Promise((resolve, reject) => {
    axios
      .post(config.urls.COMMUNICATION_SERVICE_BASE_URL, { query: APIQuery })
      .then(function (response) {
        resolve(response.data);
      })
      .catch(function (err) {
        reject(err);
      });
  });
}

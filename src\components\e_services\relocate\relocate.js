import React, { useEffect } from "react";
import { useState, useContext } from "react";
import { Text, Card, TextInput, HelperText } from "react-native-paper";
import {
  View,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Dimensions,
} from "react-native";
import { GlobalStyles } from "../../app/global-styles";
import Button from "../../common/_button";
import createRelocateServiceTicket from "./model/service";
import { useSelector } from "react-redux";
import { ticketContext } from "../e_services";
import { countryData } from "../../common/country-state";
import { Alert } from "react-native";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import DropDown from "../../common/_dropdown";
import { config } from "../../../environment";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { stackContext } from "../../app/get_stack";

export default function Relocate({
  accountId,
  saId,
  readOnly,
  pastTicketDetails,
  requiredCardHeight,
  requiredHeight,
}) {
  const { workModelType } = React.useContext(stackContext);
  const {
    openDrawer,
    setOpenMenu,
    menuFlag,
    setmenuFlag,
    submitLoader,
    setSubmitLoader,
    setTitlepopup,
    setShowPopup,
    setTicketNumber,
  } =
    workModelType === "WA"
      ? useContext(drawerContext)
      : useContext(drawerContextWO);

  const [email, setEmail] = useState();
  const [mobileNo, setMobileNo] = useState();
  const [address1, setAddress1] = useState();
  const [address2, setAddress2] = useState("");
  const [address3, setAddress3] = useState("");
  const [countryList, setCountryList] = useState();
  const [stateList, setStateList] = useState([]);
  const [totalState, setTotalState] = useState([]);
  const [state, setState] = useState();
  const [city, setCity] = useState();
  const [zipCode, setZipCode] = useState();

  const [isLoading, setLoading] = useState(false);
  const [ticketErr, setTicketErr] = useState();
  const [emailReq, setEmailReq] = useState(false);
  const [emailErr, setEmailErr] = useState(false);
  const [contactNoReq, setContactNoReq] = useState(false);
  const [contactNoErr, setContactNoErr] = useState(false);
  const [add1Req, setAdd1Req] = useState(false);
  const [countryReq, setCountryReq] = useState(false);
  const [stateReq, setStateReq] = useState(false);
  const [cityReq, setCityReq] = useState(false);
  const [zipCodeReq, setZipCodeReq] = useState(false);
  const [zipCodeErr, setZipCodeErr] = useState(false);
  const [totalCountry, setTotalCountry] = useState();
  const [selectedCountry, setSelectedCountry] = useState();
  const [selectedCountryObject, setSelectedCountryObject] = useState();
  const [disableSubmit, setDisableSubmit] = useState(true);
  const { height } = Dimensions.get("window");
  const [close, setClose] = useState(false);
  const [closeState, setCloseState] = useState(false);
  const [disableCancle, setDisableCancle] = useState(true);

  let countryCode = useSelector(
    state => state?.parameter?.parameter?.ParameterLookup?.TENANT_COUNTRY,
  );
  const personId = useSelector(
    state => state?.meterDetails?.meterDetails?.personId,
  );

  const emailRegex = useSelector(
    state => state?.parameter?.parameter?.ParameterLookup?.E_EMAIL_REGEX,
  );

  const mobile_length = useSelector(
    state => state?.parameter?.parameter?.ParameterLookup?.MOBILE_LENGTH,
  );

  // useEffect(() => {
  //   if (countryCode) {
  //     setCountryIsoCode(countryCode);
  //   }
  // }, [countryCode]);

  useEffect(() => {
    if (
      email ||
      mobileNo ||
      address1 ||
      address2 ||
      address3 ||
      selectedCountry ||
      state ||
      city ||
      zipCode
    ) {
      setDisableCancle(false);
    } else {
      setDisableCancle(true);
    }
    if (
      emailErr === false &&
      emailReq === false &&
      contactNoErr === false &&
      contactNoReq === false &&
      add1Req === false &&
      email &&
      mobileNo &&
      address1 &&
      selectedCountry &&
      state &&
      stateReq === false &&
      city &&
      zipCode &&
      zipCodeReq === false &&
      zipCodeErr === false
    ) {
      setDisableSubmit(false);
    } else {
      setDisableSubmit(true);
    }
  }, [
    email,
    emailErr,
    emailReq,
    mobileNo,
    contactNoErr,
    contactNoReq,
    address1,
    add1Req,
    selectedCountry,
    countryReq,
    state,
    stateReq,
    city,
    cityReq,
    zipCode,
    zipCodeErr,
    address2,
    address3,
  ]);

  useEffect(() => {
    const total =
      countryData &&
      countryData.map(country => {
        let name = country.name;
        let iso3 = country.iso3;
        let states = country.states;
        return { name, iso3, states };
      });
    setCountryList(total && total.map(i => i.name));
    setTotalCountry(total);
  }, [countryData]);

  // console.log("toal", totalCountry, selectedCountry);

  useEffect(() => {
    if (totalCountry && selectedCountry) {
      setSelectedCountryObject(
        totalCountry.filter(i => i.name === selectedCountry),
      );
      setCloseState(true);
      setState("");
    }
  }, [selectedCountry]);

  useEffect(() => {
    if (selectedCountryObject) {
      setTotalState(selectedCountryObject?.[0]?.states || null);
    }
  }, [selectedCountryObject]);

  useEffect(() => {
    if (totalState && totalState.length > 0) {
      setStateList(totalState.map(i => i.name));
    } else {
      setStateList([]);
      setState("");
    }
  }, [totalState]);

  // console.log("state", stateList);
  const emailBlur = emailId => {
    setEmail(emailId);
    //let regex = new RegExp("^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\.[A-Z|a-z]{2,}$");
    let regex = new RegExp(emailRegex);
    if (emailId) {
      setEmailReq(false);
      if (regex) {
        const emailReg = new RegExp(regex);
        if (!emailReg.test(emailId)) {
          setEmailErr(true);
        } else {
          setEmailErr(false);
        }
      }
    } else {
      setEmailReq(true);
    }
  };

  const contactNoUpate = contactNo => {
    console.log("onchange");
    setMobileNo(contactNo);
    if (contactNo && contactNo.length > 0) {
      setContactNoReq(false);
      if (mobile_length && contactNo.length === Number(mobile_length)) {
        setContactNoErr(false);
      } else {
        setContactNoErr(true);
      }
    } else {
      setContactNoReq(true);
    }
  };

  const address1Blur = add1 => {
    setAddress1(add1);
    if (add1 && add1.length > 0) {
      setAdd1Req(false);
    } else {
      setAdd1Req(true);
    }
  };

  const countryBlur = countryV => {
    if (countryV) {
      setCountryReq(false);
    } else {
      setCountryReq(true);
    }
  };

  const stateBlur = stateValue => {
    if (stateValue) {
      setStateReq(false);
    } else {
      setStateReq(true);
    }
  };

  const cityBlur = cityValue => {
    if (cityValue) {
      setCityReq(false);
    } else {
      setCityReq(true);
    }
  };

  const zipBlur = zip => {
    setZipCode(zip);
    const regex = /^[0-9 ]*$/;

    if (zip) {
      setZipCodeReq(false);
      if (regex.test(zip)) {
        if (zip.length === 6) {
          setZipCodeErr(false);
        } else {
          setZipCodeErr(true);
        }
      } else {
        setZipCodeErr(true);
      }
    } else {
      setZipCodeReq(true);
    }
  };

  const cancelClick = () => {
    setDisableCancle(true);
    setEmail("");
    setMobileNo("");
    setAddress1("");
    setAddress2("");
    setAddress3("");
    setState("");
    setCity("");
    setZipCode("");
    setEmailReq(false);
    setEmailErr(false);
    setContactNoReq(false);
    setContactNoErr(false);
    setAdd1Req(false);
    setCountryReq(false);
    setStateReq(false);
    setCityReq(false);
    setZipCodeReq(false);
    setZipCodeErr(false);
    setClose(true);
    setCloseState(true);
    setLoading(false);
    setSubmitLoader(false);
    setStateList([]);
    setSelectedCountry();
  };

  const submitClick = () => {
    emailBlur(email);
    contactNoUpate(mobileNo);
    address1Blur(address1);
    stateBlur(state);
    cityBlur(city);
    zipBlur(zipCode);
    countryBlur(selectedCountry);
    // console.log(
    //   emailErr,
    //   emailReq,
    //   contactNoErr,
    //   contactNoReq,
    //   add1Req,
    //   email,
    //   mobileNo,
    //   address1,
    //   selectedCountry,
    //   state,
    //   city,
    //   zipCode,
    //   zipCodeReq,
    //   zipCodeErr
    // );
    if (
      emailErr === false &&
      emailReq === false &&
      contactNoErr === false &&
      contactNoReq === false &&
      add1Req === false &&
      email &&
      mobileNo &&
      address1 &&
      selectedCountry &&
      state &&
      city &&
      zipCode &&
      zipCodeReq === false &&
      zipCodeErr === false
    ) {
      setLoading(true);
      setSubmitLoader(true);
      createRelocateServiceTicket(
        email,
        mobileNo,
        address1,
        address2,
        address3,
        selectedCountry,
        state,
        city,
        zipCode,
        accountId,
        saId,
        personId,
      )
        .then(res => {
          setLoading(false);
          setSubmitLoader(false);
          setShowPopup(true);
          setTicketNumber(res);
          cancelClick();
          setTitlepopup("Relocate - Service Request");
          setTicketErr();
        })
        .catch(err => {
          setLoading(false);
          setSubmitLoader(false);
          setTicketErr(err);
        });
    }
  };

  const pastticketsFn = () => {
    setOpenMenu(true);
    setmenuFlag(false);
  };

  return (
    <>
      <View style={{ height: requiredHeight }}>
        <Card style={[styles.cardStyle, { height: requiredCardHeight }]}>
          <ScrollView style={styles.scrollStyle}>
            <View style={styles.container}>
              <Text style={styles.text}>
                Email Id<Text style={styles.textStar}>*</Text>:
              </Text>
              <TextInput
                placeholderTextColor={GlobalStyles.colors.eDark.base}
                mode="outlined"
                //value={email}
                value={
                  pastTicketDetails
                    ? pastTicketDetails?.userSubmittedValues?.EMAIL
                    : email
                }
                onChangeText={e => emailBlur(e)}
                placeholder="Enter Email Id"
                dense
                keyboardType={pastTicketDetails ? "default" : "email-address"}
                theme={{ colors: { text: GlobalStyles.colors.eDark.base } }}
                // onBlur={(e) => emailBlur(e.nativeEvent.text)}
                error={emailReq || emailErr}
                outlineColor={GlobalStyles.colors.eBackground.base}
                activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
                style={styles.input}
                editable={readOnly}
              />
              <HelperText
                type="error"
                visible={emailErr || emailReq}
                style={{
                  fontSize: 10,
                  color: GlobalStyles.colors.eDanger.dark,
                }}>
                {emailErr
                  ? "Email Id is not valid"
                  : emailReq && "Email Id is required"}
              </HelperText>
            </View>
            <View style={styles.container}>
              <Text style={styles.text}>
                Contact Number<Text style={styles.textStar}>*</Text>:
              </Text>
              <TextInput
                placeholderTextColor={GlobalStyles.colors.eDark.base}
                mode="outlined"
                // value={mobileNo}
                value={
                  pastTicketDetails
                    ? pastTicketDetails?.userSubmittedValues?.MOBILE_NUMBER
                    : mobileNo
                }
                returnKeyType="done"
                keyboardType={pastTicketDetails ? "default" : "numeric"}
                onChangeText={e => contactNoUpate(e)}
                placeholder="Enter Contact Number"
                dense
                maxLength={10}
                //onBlur={(e) => contactvalidation(e.nativeEvent.default)}
                theme={{ colors: { text: GlobalStyles.colors.eDark.base } }}
                error={contactNoErr || contactNoReq}
                outlineColor={GlobalStyles.colors.eBackground.base}
                activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
                style={styles.input}
                editable={readOnly}
              />
              <HelperText
                type="error"
                visible={contactNoErr || contactNoReq}
                style={{
                  fontSize: 10,
                  color: GlobalStyles.colors.eDanger.dark,
                }}>
                {contactNoErr
                  ? "Contact Number is not valid"
                  : contactNoReq && "Contact Number is required"}
              </HelperText>
            </View>
            <View style={styles.container}>
              <Text style={styles.text}>
                Address1<Text style={styles.textStar}>*</Text>:
              </Text>
              <TextInput
                // placeholderTextColor={GlobalStyles.colors.eDark.base}
                mode="outlined"
                // value={address1}
                value={
                  pastTicketDetails
                    ? pastTicketDetails?.userSubmittedValues?.ADDRESS1
                    : address1
                }
                onChangeText={e => address1Blur(e)}
                placeholder="Enter Address1"
                dense
                theme={{ colors: { text: GlobalStyles.colors.eDark.base } }}
                // onBlur={(e) => address1Blur(e.nativeEvent.text)}
                error={add1Req}
                outlineColor={GlobalStyles.colors.eBackground.base}
                activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
                style={styles.input}
                editable={readOnly}
              />
              <HelperText
                type="error"
                visible={add1Req}
                style={{
                  fontSize: 10,
                  color: GlobalStyles.colors.eDanger.dark,
                }}>
                {add1Req && "Address1 is required"}
              </HelperText>
            </View>
            <View style={styles.container}>
              <Text style={styles.text}>Address2:</Text>
              <TextInput
                placeholderTextColor={GlobalStyles.colors.eDark.base}
                mode="outlined"
                // value={address2}
                value={
                  pastTicketDetails
                    ? pastTicketDetails?.userSubmittedValues?.ADDRESS2
                    : address2
                }
                onChangeText={e => setAddress2(e)}
                placeholder="Enter Address2"
                dense
                theme={{ colors: { text: GlobalStyles.colors.eDark.base } }}
                outlineColor={GlobalStyles.colors.eBackground.base}
                activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
                style={styles.input}
                editable={readOnly}
              />
            </View>
            <View style={styles.container}>
              <Text style={styles.text}>Address3:</Text>
              <TextInput
                placeholderTextColor={GlobalStyles.colors.eDark.base}
                mode="outlined"
                // value={address3}
                value={
                  pastTicketDetails
                    ? pastTicketDetails?.userSubmittedValues?.ADDRESS3
                    : address3
                }
                onChangeText={e => setAddress3(e)}
                placeholder="Enter Address3"
                dense
                theme={{ colors: { text: GlobalStyles.colors.eDark.base } }}
                outlineColor={GlobalStyles.colors.eBackground.base}
                activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
                style={styles.input}
                editable={readOnly}
              />
            </View>
            <View style={styles.container}>
              <Text style={styles.text}>
                Country<Text style={styles.textStar}>*</Text>:
              </Text>
              <DropDown
                data={countryList}
                onChange={setSelectedCountry}
                defaultvalue={
                  pastTicketDetails
                    ? pastTicketDetails?.userSubmittedValues?.COUNTRY
                    : selectedCountry
                }
                title={
                  pastTicketDetails
                    ? pastTicketDetails?.userSubmittedValues?.COUNTRY
                    : "Select country"
                }
                close={close}
                setClose={setClose}
                disabled={!!pastTicketDetails}
              />
              <HelperText
                type="error"
                visible={countryReq}
                style={{
                  fontSize: 10,
                  color: GlobalStyles.colors.eDanger.dark,
                }}>
                {countryReq && "Country is required"}
              </HelperText>
            </View>
            <View style={styles.container}>
              <Text style={styles.text}>
                State<Text style={styles.textStar}>*</Text>:
              </Text>
              {stateList.length === 0 && selectedCountry ? (
                <TextInput
                  placeholderTextColor={GlobalStyles.colors.eDark.base}
                  mode="outlined"
                  // value={state}
                  value={
                    pastTicketDetails
                      ? pastTicketDetails?.userSubmittedValues?.STATE
                      : state
                  }
                  onChangeText={e => {
                    setState(e);
                    setStateReq(false);
                  }}
                  placeholder="Enter State"
                  dense
                  theme={{ colors: { text: GlobalStyles.colors.eDark.base } }}
                  //onBlur={(e) => stateBlur(e.nativeEvent.text)}
                  error={stateReq}
                  outlineColor={GlobalStyles.colors.eBackground.base}
                  activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
                  style={styles.input}
                  editable={readOnly}
                />
              ) : (
                <DropDown
                  data={stateList}
                  onChange={setState}
                  defaultvalue={
                    pastTicketDetails
                      ? pastTicketDetails?.userSubmittedValues?.STATE
                      : state
                  }
                  title={
                    pastTicketDetails
                      ? pastTicketDetails?.userSubmittedValues?.STATE
                      : "Select state"
                  }
                  close={closeState}
                  setClose={setCloseState}
                  disabled={stateList.length === 0 || !!pastTicketDetails}
                />
              )}
              {/* {console.log(stateList.length === 0 && !selectedCountry)} */}
              {pastTicketDetails ? null : stateList.length === 0 &&
                !selectedCountry ? (
                <Text
                  style={{
                    color: GlobalStyles.colors.eTertiary.base,
                    fontSize: 10,
                  }}>
                  *Select a country first
                </Text>
              ) : null}

              <HelperText
                type="error"
                visible={stateReq}
                style={{
                  fontSize: 10,
                  color: GlobalStyles.colors.eDanger.dark,
                }}>
                {stateReq && "State is required"}
              </HelperText>
            </View>

            <View style={styles.container}>
              <Text style={styles.text}>
                City<Text style={styles.textStar}>*</Text>:
              </Text>
              <TextInput
                placeholderTextColor={GlobalStyles.colors.eDark.base}
                mode="outlined"
                // value={city}
                value={
                  pastTicketDetails
                    ? pastTicketDetails?.userSubmittedValues?.CITY
                    : city
                }
                onChangeText={e => {
                  setCity(e);
                  setCityReq(false);
                }}
                placeholder="Enter City"
                dense
                maxLength={50}
                theme={{ colors: { text: GlobalStyles.colors.eDark.base } }}
                // onBlur={(e) => cityBlur(e.nativeEvent.text)}
                error={cityReq}
                outlineColor={GlobalStyles.colors.eBackground.base}
                activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
                style={styles.input}
                editable={readOnly}
              />
              <HelperText
                type="error"
                visible={cityReq}
                style={{
                  fontSize: 10,
                  color: GlobalStyles.colors.eDanger.dark,
                }}>
                {cityReq && "City is required"}
              </HelperText>
            </View>

            <View style={styles.container}>
              <Text style={styles.text}>
                Zipcode<Text style={styles.textStar}>*</Text>:
              </Text>
              <TextInput
                placeholderTextColor={GlobalStyles.colors.eDark.base}
                mode="outlined"
                // value={zipCode}
                value={
                  pastTicketDetails
                    ? pastTicketDetails?.userSubmittedValues?.ZIP
                    : zipCode
                }
                onChangeText={zipBlur}
                placeholder="Enter Zip Code"
                dense
                theme={{ colors: { text: GlobalStyles.colors.eDark.base } }}
                // onBlur={(e) => zipBlur(e.nativeEvent.text)}
                error={zipCodeReq || zipCodeErr}
                outlineColor={GlobalStyles.colors.eBackground.base}
                activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
                style={styles.input}
                editable={readOnly}
                returnKeyType="done"
                keyboardType="numeric"
                maxLength={6}
              />
              <HelperText
                type="error"
                visible={zipCodeReq || zipCodeErr}
                style={{
                  fontSize: 10,
                  color: GlobalStyles.colors.eDanger.dark,
                }}>
                {zipCodeReq
                  ? "Zip Code is required"
                  : zipCodeErr
                  ? "Zip Code is not valid"
                  : ""}
              </HelperText>
            </View>
          </ScrollView>
        </Card>

        {pastTicketDetails ? (
          <>
            <View style={styles.btnContainer}>
              <Button
                buttonbgColor={styles.backBg}
                textColor={styles.backText}
                onPress={pastticketsFn}>
                Back to past tickets
              </Button>
            </View>
          </>
        ) : (
          <>
            <View style={styles.btnContainer}>
              <Button
                onPress={cancelClick}
                buttonbgColor={[
                  styles.cancelBg,
                  disableCancle && styles.disabledCancleStyle,
                ]}
                textColor={[
                  disableCancle ? styles.disableColor : styles.cancelText,
                ]}
                disabled={disableCancle}>
                Cancel
              </Button>
              <Button
                buttonbgColor={[
                  styles.buttonBgColor,
                  disableSubmit && styles.disabledStyle,
                ]}
                textColor={[
                  disableSubmit ? styles.disableColor : styles.textColor,
                ]}
                // textColor={styles.textColor}
                onPress={submitClick}
                disabled={disableSubmit}>
                Submit
                {isLoading && (
                  <ActivityIndicator
                    align="center"
                    size={13}
                    color={GlobalStyles.colors.eWhite.base}
                  />
                )}
              </Button>
            </View>
          </>
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  cardStyle: {
    borderRadius: 20,
    borderColor: "white",
    backgroundColor: GlobalStyles.colors.eFaint.base,
    paddingVertical: 20,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
  },
  disabledStyle: {
    opacity: 0.5,
    backgroundColor: GlobalStyles.colors.eBackground.selected,
  },
  scrollStyle: {
    paddingHorizontal: 20,
  },
  text: {
    fontFamily: "NotoSans-SemiBold",
    fontSize: 12,
  },
  textStar: {
    fontFamily: "NotoSans-SemiBold",
    fontSize: 12,
    color: GlobalStyles.colors.eDanger.dark,
  },
  btnContainer: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 15,
  },
  disabledCancleStyle: {
    opacity: 0.5,
    backgroundColor: GlobalStyles.colors.ePage.base,
    borderColor: GlobalStyles.colors.eLight.base,
    borderWidth: 1,
  },
  disableColor: {
    color: GlobalStyles.colors.eLight.selected,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  textColor: {
    color: GlobalStyles.colors.eSecondary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  bgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    marginLeft: 10,
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginLeft: 10,
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 10,
  },
  backBg: {
    borderColor: GlobalStyles.colors.ePrimary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 10,
  },
  cancelText: {
    color: GlobalStyles.colors.eSecondary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  backText: {
    color: GlobalStyles.colors.ePrimary.base,
  },
  container: {
    marginVertical: "2%",
  },
  input: {
    // boxShadow: "rgb(0 0 0 / 15%) 1px 1px 1px 1px",
    color: GlobalStyles.colors.eDark.base,
    backgroundColor: GlobalStyles.colors.eBackground.base,
    fontSize: 14,
  },
});

import React, { useEffect, useState } from "react";
import { StyleSheet, View } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { servicePath } from "../../redux/slices/servicePath";
import BlueCard from "../common/blueCard/blueCard";
import PreferenceContent from "./_preference_content";

export default function Preferences() {
  let name = "MANAGE PREFERENCES";
  const [finalData, setFinalData] = useState();
  const dispatch = useDispatch();
  const [defaultValue, setDefault] = useState();
  const isPrepaid = useSelector(
    store => store?.meterDetails?.meterDetails?.getSaInfo?.isPrepaidSa,
  );
  const pathName = useSelector(state => state?.servicePath?.servicePath);
  let data = [
    {
      icon: "bill-icon",
      label: "Bills",
      path: "bills_preference",
      fillIcon: "bill-icon-fill",
    },
    {
      icon: "Payments-stroke-icon",
      label: "Payments",
      path: "payments_preference",
      fillIcon: "Payments-fill-icon",
    },
    // {
    //   icon: "more-stroke-icon",
    //   label: "Others",
    //   path: "Others",
    //   fillIcon: "more-fill-icon",
    // },
  ];

  let prepaidData = [
    {
      icon: "Preapaid-billicon-stroke",
      label: "Prepaid Account",
      path: "prepaid_account_preference",
      fillIcon: "Preapaid-billicon-fill",
    },
    {
      icon: "more-stroke-icon",
      label: "Others",
      path: "Others",
      fillIcon: "more-fill-icon",
    },
  ];
  useEffect(() => {
    if (pathName === "payments_preference" && isPrepaid === "N") {
      setDefault(pathName);
    } else if (
      finalData &&
      finalData.length > 0 &&
      pathName === "PreferencesTab" &&
      isPrepaid === "N"
    ) {
      setDefault(finalData?.[0]?.path);
      dispatch(servicePath(finalData?.[0]?.path));
    } else if (
      finalData &&
      finalData.length > 0 &&
      pathName === "PreferencesTab" &&
      isPrepaid === "Y"
    ) {
      setDefault(finalData?.[0]?.path);
      dispatch(servicePath(finalData?.[0]?.path));
    } else if (pathName === "prepaid_account_preference" && isPrepaid === "N") {
      setDefault(finalData?.[0]?.path);
      dispatch(servicePath(finalData?.[0]?.path));
    } else {
      setDefault(pathName);
    }
  }, [finalData, pathName, isPrepaid]);

  useEffect(() => {
    if (isPrepaid === "Y") {
      setFinalData(prepaidData);

      setDefault(prepaidData?.[0]?.path);
      dispatch(servicePath(prepaidData?.[0]?.path));
    } else {
      setFinalData(data);
      // dispatch(servicePath(data?.[0]?.path));
    }
  }, [isPrepaid]);
  return (
    <View style={{ position: "relative" }}>
      <View style={styles.spaceAroundCard}>
        <BlueCard
          title={name}
          data={finalData}
          defaultSelected={defaultValue}
        />
      </View>
      <PreferenceContent />
    </View>
  );
}

const styles = StyleSheet.create({
  spaceAroundCard: {
    width: "94%",
    margin: "3%",
  },
});

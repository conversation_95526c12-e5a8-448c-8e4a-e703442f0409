import { useNavigation, useRoute } from "@react-navigation/native";
import { View, StyleSheet, Linking } from "react-native";
import { Text, FAB, Badge } from "react-native-paper";
import { useDispatch, useSelector } from "react-redux";
import Icon from "../../icon";
import { GlobalStyles } from "../../app/global-styles";
import { useEffect, useState } from "react";
import LinearGradient from "react-native-linear-gradient";
import { servicePath } from "../../../redux/slices/servicePath";
import { config } from "../../../environment";

export default function BlueCard({
  data,
  title,
  defaultSelected,
  meterUnread,
  billUnread,
  announcementUnread,
}) {
  const [clickColor, setClickColor] = useState(defaultSelected);
  const dispatch = useDispatch();
  const pathName = useSelector(state => state?.servicePath?.servicePath);
  const route = useRoute();
  const [unreadMeter, setUnreadMeter] = useState(0);
  const [unreadBill, setUnreadBill] = useState(0);
  const [unreadAnn, setUnreadAnn] = useState(0);

  useEffect(() => {
    if (defaultSelected) {
      setClickColor(defaultSelected);
    }
  }, [defaultSelected]);

  useEffect(() => {
    setUnreadMeter(meterUnread);
  }, [meterUnread]);
  useEffect(() => {
    setUnreadBill(billUnread);
  }, [billUnread]);
  useEffect(() => {
    setUnreadAnn(announcementUnread);
  }, [announcementUnread]);

  useEffect(() => {
    if (pathName == "PreferencesTab") {
      setClickColor(defaultSelected);
    } else if (pathName == "Notifications") {
      setClickColor("METER");
    }
  }, [pathName, defaultSelected]);

  const itemClick = path => {
    if (path === "MORE" || path === "Others") {
      Linking.openURL(config.constants.BASE_URL); // base url =>({baseUrl}+"?key="+{bearer})
    } else {
      dispatch(servicePath(path));
      // navigation.navigate(path);
      setClickColor(path);
    }
  };

  return (
    <>
      <LinearGradient
        colors={[
          GlobalStyles.colors.ePrimary.hover,
          GlobalStyles.colors.ePrimary.selected,
        ]}
        style={styles.background}
      />
      <View style={styles.card}>
        <View style={{ flexDirection: "row", flexWrap: "wrap" }}>
          <View style={styles.leftView}>
            <Text style={styles.titleCard}>{title}</Text>
          </View>
        </View>
        <View
          style={[
            styles.content,
            data && data.length < 3
              ? { justifyContent: "center" }
              : { justifyContent: "space-between" },
          ]}>
          {data &&
            data.map((item, k) => {
              return (
                <View
                  style={[
                    styles.centerIcon,
                    data && data.length < 3 && { paddingHorizontal: "9%" },
                  ]}
                  key={k}>
                  <FAB
                    icon={() => (
                      <Icon
                        name={
                          clickColor === item.path ? item.fillIcon : item.icon
                        }
                        size={22}
                        color={GlobalStyles.colors.ePrimary.base}
                      />
                    )}
                    style={[
                      styles.fabSize,
                      clickColor === item.path
                        ? styles.clickFab
                        : styles.iconFab,
                    ]}
                    onPress={() => itemClick(item.path)}
                    animated={false}
                  />

                  {item.path === "BILL"
                    ? unreadBill > 0 && (
                        <Badge style={styles.badgeStyle}>
                          {unreadBill > 99 ? "99+" : unreadBill}
                        </Badge>
                      )
                    : item.path === "METER"
                    ? unreadMeter > 0 && (
                        <Badge style={styles.meterBadgeStyle}>
                          {unreadMeter > 99 ? "99+" : unreadMeter}
                        </Badge>
                      )
                    : item.path === "ANNOUNCEMENT" &&
                      unreadAnn > 0 && (
                        <Badge style={styles.AnnbadgeStyle}>
                          {unreadAnn > 99 ? "99+" : unreadAnn}
                        </Badge>
                      )}

                  <Text style={styles.blueText}>{item.label}</Text>
                </View>
              );
            })}
        </View>
      </View>
    </>
  );
}
const styles = StyleSheet.create({
  card: {
    borderRadius: 20,
    width: "100%",
    paddingHorizontal: 15,
    paddingTop: "3%",
    height: 140,
  },
  titleCard: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
  },
  content: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "93%",
    marginHorizontal: "3%",
    marginVertical: "7%",
  },
  iconFab: {
    backgroundColor: GlobalStyles.colors.eWhite.hover,
  },
  clickFab: {
    backgroundColor: GlobalStyles.colors.ePastelColor2.base,
  },
  fabSize: {
    width: 46,
    height: 46,
    justifyContent: "center",
    alignItems: "center",
  },
  blueText: {
    color: GlobalStyles.colors.eWhite.base,
    marginTop: "5%",
    fontSize: 10,
    textAlign: "center",
    fontFamily: "NotoSans-Regular",
  },
  centerIcon: {
    alignItems: "center",
  },
  // container: {
  //     flex: 1,
  //     alignItems: "center",
  //     height: "100%",
  //     // paddingBottom: "2%",
  //     justifyContent: "center",
  // },
  background: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    borderRadius: 20,
    height: "100%",
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
  },
  leftView: {
    float: "left",
  },
  badgeStyle: {
    position: "absolute",
    right: 0,
    top: 0,
  },
  AnnbadgeStyle: {
    position: "absolute",
    right: 5,
    top: 0,
  },
  meterBadgeStyle: {
    position: "absolute",
    top: 0,
    right: -6,
  },
});

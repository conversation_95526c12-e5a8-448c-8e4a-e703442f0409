import { StyleSheet, View, Image } from "react-native";
import { GlobalStyles } from "../../app/global-styles";
import { Text, IconButton } from "react-native-paper";
import { useContext } from "react";
import { profileContext } from "../e_profile";
import TextLink from "react-native-text-link";
import Icon from "../../icon";

export default function ConfirmProfileModal({ showProfilePopup }) {
  const { setProfileConfirmPopup } = useContext(profileContext);
  const closeMenu = () => {
    setProfileConfirmPopup(false);
  };

  const okClick = () => {
    console.log("ok");
  };

  return (
    <>
      <View style={styles.container}>
        <View style={styles.content}>
          <Text style={styles.title}>confirmation</Text>
          <IconButton
            icon="close"
            color={GlobalStyles.colors.eBlack.base}
            onPress={closeMenu}
            style={styles.closeIcon}
            size={20}
          />
        </View>
        <View style={styles.horizontalLineFirst} />
        <View style={styles.imgViewCls}>
          <Icon
            name="Sucess-icon"
            color={GlobalStyles.colors.eSecondary.base}
            size={60}
          />
        </View>

        <View style={styles.contentTicket}>
          <Text style={styles.subtitle}>
            Phone number has been updated successfully!
          </Text>
        </View>

        <View style={styles.lineStyleBottom}></View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flext: 1,
    height: "50%",
    position: "relative",
  },
  content: {
    padding: 30,
    paddingBottom: 13,
  },
  contentLast: {
    marginBottom: 10,
  },
  contentTicket: {
    paddingTop: 20,
    paddingBottom: 10,
    paddingLeft: 20,
    paddingRight: 20,
  },
  title: {
    textAlign: "center",
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 16,
    fontFamily: "NotoSans-Bold",
  },
  subtitle: {
    textAlign: "center",
    color: GlobalStyles.colors.eSecondary.base,
    fontSize: 14,
    fontFamily: "NotoSans-SemiBold",
  },
  ticketReference: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 16,
    fontFamily: "NotoSans-Bold",
    padding: 10,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: GlobalStyles.colors.ePrimary.base,
    marginTop: 10,
  },
  subText: {
    textAlign: "center",
    color: GlobalStyles.colors.eBlack.base,
    fontSize: 11,
  },
  subText2: {
    textAlign: "center",
    color: GlobalStyles.colors.eBlack.base,
    fontSize: 11,
  },
  subLink: {
    textAlign: "center",
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 11,
    paddingTop: 10,
    paddingBottom: 10,
  },
  closeIcon: {
    position: "absolute",
    top: 0,
    right: 13,
    backgroundColor: GlobalStyles.colors.eFaint.selected,
  },
  imgCls: {
    height: 60,
    width: 60,
    padding: 5,
  },
  imgViewCls: {
    justifyContent: "center",
    alignItems: "center",
  },
  lineStyleBottom: {
    borderWidth: 0.5,
    borderColor: "#DEE2E4",
    marginLeft: -3,
    marginBottom: 2,
    marginRight: -3,
    marginTop: 42,
  },
  greenBg: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
  },
  horizontalLineFirst: {
    borderBottomWidth: 0.6,
    width: "100%",
    borderColor: GlobalStyles.colors.eBackground3.base,
    marginTop: 5,
    marginBottom: 42,
  },
});

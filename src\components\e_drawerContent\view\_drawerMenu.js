import { ScrollView, StyleSheet } from "react-native";
import DrawerMenuItem from "./_drawerItem";

export default function DrawerMenuItems(props) {

    return (
        <ScrollView style={styles.menuContainer}>
            <DrawerMenuItem name={"Profile"} navigationTab={"ProfileTab"} imageSrc="User-stroke-icon"/>
            <DrawerMenuItem name={"Preferences"} navigationTab={"PreferencesTab"} imageSrc="General-icon"/>
            <DrawerMenuItem name={"Change Password"} navigationTab={"ProfileTab"} imageSrc="HB-ChangePw-lock-stroke-icon" />
            <DrawerMenuItem name={"View Past Tickets"} navigationTab={"Services"} imageSrc="PastTickets-icon" />
            <DrawerMenuItem name={"Downloads"} navigationTab={"DownloadsTab"} imageSrc="Download-icon"/>
            <DrawerMenuItem name={"Self-Help"} navigationTab={"HelpTab"} imageSrc="help-icon"/>
        </ScrollView>
    );
}

const styles = StyleSheet.create({
    menuContainer: {
        paddingTop: 10
    }
});
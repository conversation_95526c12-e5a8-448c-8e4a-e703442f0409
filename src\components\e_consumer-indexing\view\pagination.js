import React, { useState, useContext, useEffect } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import ConsumerInfoPage from "./consumer-information";
import UtilityInfoPage from "./utility-division";
import { GlobalStyles } from "../../app/global-styles";
import { consumerPaginationContext } from "./new-consumer-index";
import NetworkInfoPage from "./network-information";
import MeterInfoPage from "./meter-information";
import OtherInfoPage from "./other-information";
import { consumerIndexContext } from "../e_consumer-index";

const PaginationExample = () => {
  const { IndexArray, SetIndexArray, currentPage, setCurrentPage, pageArray } =
    useContext(consumerPaginationContext);

  const { filled, setfilled } = useContext(consumerIndexContext);

  const handlePageChange = pageNumber => {
    setCurrentPage(pageNumber);
  };

  useEffect(() => {}, [IndexArray]);

  const renderContent = currentPage => {
    switch (currentPage) {
      case 1:
        return <ConsumerInfoPage />;
      case 2:
        return <UtilityInfoPage />;
      case 3:
        return <NetworkInfoPage />;
      case 4:
        return <MeterInfoPage />;
      case 5:
        return <OtherInfoPage />;
      default:
        return null;
    }
  };

  useEffect(() => {}, [filled]);

  return (
    <View style={styles.container}>
      <View style={styles.paginationContainer}>
        {IndexArray.map((page, index) => (
          <View key={page.no} style={styles.pageContainer}>
            <TouchableOpacity onPress={() => handlePageChange(page.no)}>
              <Text
                style={[
                  styles.pageNumber,
                  currentPage === page.no
                    ? styles.activePageNumber
                    : IndexArray[index].filled
                    ? styles.activeFilledPageNumber
                    : styles.inactivePageNumber,
                ]}>
                {page.no}
              </Text>
            </TouchableOpacity>
            <Text
              numberOfLines={2}
              style={[
                styles.pageName,
                currentPage === page.no
                  ? styles.activePageName
                  : IndexArray[index].filled
                  ? styles.activeFilledPageName
                  : styles.inactivePageName,
              ]}>
              {page.name}
            </Text>
            {index !== pageArray.length - 1 && (
              <Text
                style={[
                  styles.dottedLine,
                  currentPage === page.no
                    ? styles.activePageName
                    : IndexArray[index].filled
                    ? styles.activeFilledPageName
                    : styles.inactivePageName,
                ]}>
                ---------
              </Text>
            )}
          </View>
        ))}
      </View>
      <View style={styles.contentContainer}>{renderContent(currentPage)}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: "center",
    alignItems: "center",
  },
  pageNumber: {
    width: 40,
    height: 40,
    borderRadius: 20,
    textAlign: "center",
    lineHeight: 40,
    borderWidth: 0,
    marginHorizontal: 5,
    backgroundColor: GlobalStyles.colors.eSuccess.dark,
    color: GlobalStyles.colors.eWhite.base,
  },
  pageName: {
    textAlign: "center",
    marginTop: 5,
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    width: 65,
  },
  activePageNumber: {
    backgroundColor: GlobalStyles.colors.eTertiary.base,
    color: GlobalStyles.colors.eWhite.base,
  },
  activeFilledPageNumber: {
    backgroundColor: GlobalStyles.colors.eSuccess.dark,
    color: GlobalStyles.colors.eWhite.base,
  },
  inactivePageNumber: {
    backgroundColor: GlobalStyles.colors.eLight.selected,
    color: GlobalStyles.colors.eWhite.base,
  },
  activePageName: {
    color: GlobalStyles.colors.eTertiary.base,
  },
  inactivePageName: {
    color: GlobalStyles.colors.eWhite.selected,
  },
  activeFilledPageName: {
    color: GlobalStyles.colors.eSuccess.dark,
  },
  contentContainer: {
    justifyContent: "center",
    alignItems: "center",
    marginTop: 20,
  },
  paginationContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 20,
    height: 70,
    width: 100,
  },
  pageContainer: {
    flexDirection: "column",
    alignItems: "center",
    marginHorizontal: 5,
  },
  dottedLine: {
    color: "black",
    top: -65,
    left: 40,
  },
});

export default PaginationExample;

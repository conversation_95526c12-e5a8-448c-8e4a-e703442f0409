import { createSlice } from "@reduxjs/toolkit";

const servicePathReducer = createSlice({
    name: "servicePath",
    initialState: {
        servicePath:"",
    },
    reducers: {
        servicePath: (state, action) => {
            state.servicePath= action.payload
        },
    
    }
})

export const servicePath = servicePathReducer.actions.servicePath;
export default servicePathReducer.reducer;
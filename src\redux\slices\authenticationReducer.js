import { createSlice } from "@reduxjs/toolkit";

const authenticationSlice = createSlice({
    name: "authentication",
    initialState: {
        isUserLoggedIn: false
    },
    reducers: {
        updateLoggedInStatus: (state, action) => {
            state.isUserLoggedIn = action.payload
        }
    }
})

export const updateLoggedInStatus = authenticationSlice.actions.updateLoggedInStatus
export default authenticationSlice.reducer;
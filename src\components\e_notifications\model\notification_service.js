import axios from "axios";
import { config } from "../../../environment";

export default function meterAlertsCall(
  startDate,
  endDate,
  origin,
  accountId,
  customerClass,
  announcementEndDate,
  languageCode,
) {
  let APIinput = "";
  let headers = {
    languageCode: languageCode,
  };
  const ANNOUNCEMENT_VALUE = "ANNOUNCEMENT";
  if (origin === ANNOUNCEMENT_VALUE) {
    APIinput =
      `query{getAnnouncementList(input:{IsPublic:"LOGGED_IN",StartDate:"` +
      startDate +
      `",EndDate:"` +
      announcementEndDate +
      `",CustomerClass:"` +
      customerClass +
      `"}){AnnouncementId,AnnouncementTitle,AnnouncementDescription,IsPublic,ExpirationDateTime,CreateDateTime,StartDate,AnnouncementType,AnnouncementStatus,CustomerClass,IsPublic,IsRead,ContentIconName}}`;
  } else {
    APIinput =
      `{getAlerts(input:{origin:"` +
      origin +
      `",
        startDate:"` +
      startDate +
      `",
        endDate:"` +
      endDate +
      `",
        accountId: "` +
      accountId +
      `"  
        })}`;
  }
  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.COMMUNICATION_SERVICE_BASE_URL,
        { query: APIinput },
        {
          headers: headers,
        },
      )
      .then(response => {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
}

export function updateData(origin, id, accountId) {
  let APIinput = "";
  if (origin === "ANNOUNCEMENT") {
    APIinput =
      `query{updateAnnouncementStatus(input:{announcementId:` + id + `})} `;
  } else {
    APIinput =
      `query{updateAlertStatus(input:{alertId:` +
      id +
      `,accountId:"` +
      accountId +
      `"})}`;
  }
  // return apolloClientService.ApolloClientgqlsCommunication(APIinput);

  return new Promise((resolve, reject) => {
    axios
      .post(config.urls.COMMUNICATION_SERVICE_BASE_URL, { query: APIinput })
      .then(response => {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
}

// // // i18n.js
// // import i18n from 'i18next';
// // import { initReactI18next } from 'react-i18next';
// // import HttpApi from 'i18next-http-backend';
// // import config from './environment';

// // const END_POINT = "https://impresacx-dev.abjayon.com/assets/";
// // const TENANT_CODE = "ABJ";

// // console.log(END_POINT, TENANT_CODE);

// // i18n
// //   .use(HttpApi)
// //   .use(initReactI18next)
// //   .init({
// //     debug: true,
// //     fallbackLng: 'en',
// //     interpolation: {
// //       escapeValue: false, // not needed for React
// //     },
// //     backend: {
// //       loadPath: 'https://impresacx-dev.abjayon.com/assets/i18n/locales/ABJ/en/Electric_label-E_ACCOUNT_SA_SWITCHER_MD.js',
// //     },
// //   });


// import i18n from 'i18next';
// import { initReactI18next } from 'react-i18next';
// import { Platform } from 'react-native';
// import HttpApi from 'i18next-http-backend';
// import translations from './translations'; // Update this with your translations file
// import * as RNLocalize from 'react-native-localize';

// const languageDetector = {
//   type: 'languageDetector',
//   async: true,
//   detect: (callback) => {
//     const { languageTag } = RNLocalize.getLocales()[0];
//     callback(languageTag);
//   },
//   init: () => {},
//   cacheUserLanguage: () => {},
// };

// i18n
//   .use(languageDetector)
//   .use(HttpApi)
//   .use(initReactI18next)
//   .init({
//     resources: translations,
//     fallbackLng: 'en',
//     debug: true,
//     interpolation: {
//       escapeValue: false,
//     },
//     backend: {
//       //oadPath: `${END_POINT}/i18n/locales/${TENANT_CODE}/{{lng}}/{{ns}}.json`,
//     },
//   });

// export default i18n;
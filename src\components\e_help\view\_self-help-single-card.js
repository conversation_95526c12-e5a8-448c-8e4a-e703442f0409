import React, { useEffect } from "react";
import { StyleSheet, View, Text, Dimensions } from "react-native";
import { FAB } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import LinearGradient from "react-native-linear-gradient";
import { useRoute } from "@react-navigation/native";
import { useState } from "react";
import { servicePath } from "../../../redux/slices/servicePath";
import { useDispatch, useSelector } from "react-redux";
import Icon from "../../icon";
import { CARDS } from "../constants";

export default function SelfHelpSingleCard({ title, data = [] }) {
  const [clickColor, setClickColor] = useState(CARDS[0].path);
  const dispatch = useDispatch();
  const { height } = Dimensions.get("window");
  let servicePathName = useSelector(state => state?.servicePath?.servicePath);
  useEffect(() => {
    if (servicePathName === "HelpTab") {
      setClickColor(CARDS[0].path);
    }
  }, [servicePathName]);

  const itemClick = path => {
    dispatch(servicePath(path));
    setClickColor(path);
  };

  return (
    <View style={[styles.container]}>
      <LinearGradient
        colors={[
          GlobalStyles.colors.ePrimary.hover,
          GlobalStyles.colors.ePrimary.selected,
        ]}
        style={styles.background}
      />
      <View style={styles.card}>
        <View style={{ flexDirection: "row", flexWrap: "wrap" }}>
          <View style={styles.leftView}>
            <Text style={styles.titleCard}>{title}</Text>
          </View>
        </View>
        <View style={styles.content}>
          {data.map((item, k) => {
            return (
              <View style={styles.centerIcon} key={item.path}>
                <FAB
                  icon={() => (
                    <Icon
                      name={item.icon}
                      size={22}
                      color={GlobalStyles.colors.ePrimary.base}
                    />
                  )}
                  style={[
                    styles.fabSize,
                    clickColor === item.path ? styles.clickFab : styles.iconFab,
                  ]}
                  onPress={() => itemClick(item.path)}
                  animated={false}
                />
                <Text style={styles.blueText}>{item.label}</Text>
              </View>
            );
          })}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    borderRadius: 20,
    width: "100%",
    paddingHorizontal: 15,
    paddingTop: 7,
    height: 135,
  },
  titleCard: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
  },
  content: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
    marginTop: 10,
    justifyContent: "space-between",
  },

  iconFab: {
    backgroundColor: GlobalStyles.colors.eFaint.base,
  },
  clickFab: {
    backgroundColor: GlobalStyles.colors.ePastelColor2.base,
  },
  btnPress: {
    backgroundColor: "red",
  },
  fabSize: {
    width: 46,
    height: 46,
    justifyContent: "center",
    alignItems: "center",
  },
  blueText: {
    color: GlobalStyles.colors.eWhite.base,
    marginTop: "5%",
    fontSize: 10,
    width: 60,
    textAlign: "center",
    fontFamily: "NotoSans-Regular",
  },
  centerIcon: {
    alignItems: "center",
  },
  container: {
    flex: 1,
    alignItems: "center",
    height: "100%",
    // paddingBottom: "2%",
    justifyContent: "center",
  },
  background: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    borderRadius: 20,
    height: "100%",
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
  },
  linkWhite: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 10,
    fontFamily: "NotoSans-Regular",
    textDecorationLine: "underline",
    paddingVertical: 6,
    paddingHorizontal: 12,
    // textAlign:"end"
    alignSelf: "flex-end",
    paddingRight: 0,
  },
  leftView: {
    float: "left",
    width: "50%",
  },
  rightView: {
    float: "right",
    width: "50%",
    marginTop: "-2%",
  },
});

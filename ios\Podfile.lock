PODS:
  - boost (1.76.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - CocoaAsyncSocket (7.6.5)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.71.19)
  - FBReactNativeSpec (0.71.19):
    - RCT-Folly (= 2021.07.22.00)
    - RCTRequired (= 0.71.19)
    - RCTTypeSafety (= 0.71.19)
    - React-Core (= 0.71.19)
    - React-jsi (= 0.71.19)
    - ReactCommon/turbomodule/core (= 0.71.19)
  - Flipper (0.125.0):
    - Flipper-Folly (~> 2.6)
    - Flipper-RSocket (~> 1.4)
  - Flipper-Boost-iOSX (********.11)
  - Flipper-DoubleConversion (*******)
  - Flipper-Fmt (7.1.7)
  - Flipper-Folly (2.6.10):
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt (= 7.1.7)
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.1100)
  - Flipper-Glog (*******)
  - Flipper-PeerTalk (0.0.4)
  - Flipper-RSocket (1.4.3):
    - Flipper-Folly (~> 2.6)
  - FlipperKit (0.125.0):
    - FlipperKit/Core (= 0.125.0)
  - FlipperKit/Core (0.125.0):
    - Flipper (~> 0.125.0)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
    - SocketRocket (~> 0.6.0)
  - FlipperKit/CppBridge (0.125.0):
    - Flipper (~> 0.125.0)
  - FlipperKit/FBCxxFollyDynamicConvert (0.125.0):
    - Flipper-Folly (~> 2.6)
  - FlipperKit/FBDefines (0.125.0)
  - FlipperKit/FKPortForwarding (0.125.0):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.125.0)
  - FlipperKit/FlipperKitLayoutHelpers (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutIOSDescriptors (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutPlugin (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - FlipperKit/FlipperKitLayoutIOSDescriptors
    - FlipperKit/FlipperKitLayoutTextSearchable
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutTextSearchable (0.125.0)
  - FlipperKit/FlipperKitNetworkPlugin (0.125.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.125.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.125.0):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - fmt (6.2.1)
  - glog (0.3.5)
  - hermes-engine (0.71.19):
    - hermes-engine/Pre-built (= 0.71.19)
  - hermes-engine/Pre-built (0.71.19)
  - libevent (2.1.12)
  - OpenSSL-Universal (1.1.1100)
  - razorpay-pod (1.3.8)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.71.19)
  - RCTTypeSafety (0.71.19):
    - FBLazyVector (= 0.71.19)
    - RCTRequired (= 0.71.19)
    - React-Core (= 0.71.19)
  - React (0.71.19):
    - React-Core (= 0.71.19)
    - React-Core/DevSupport (= 0.71.19)
    - React-Core/RCTWebSocket (= 0.71.19)
    - React-RCTActionSheet (= 0.71.19)
    - React-RCTAnimation (= 0.71.19)
    - React-RCTBlob (= 0.71.19)
    - React-RCTImage (= 0.71.19)
    - React-RCTLinking (= 0.71.19)
    - React-RCTNetwork (= 0.71.19)
    - React-RCTSettings (= 0.71.19)
    - React-RCTText (= 0.71.19)
    - React-RCTVibration (= 0.71.19)
  - React-callinvoker (0.71.19)
  - React-Codegen (0.71.19):
    - FBReactNativeSpec
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.71.19):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.19)
    - React-cxxreact (= 0.71.19)
    - React-hermes
    - React-jsi (= 0.71.19)
    - React-jsiexecutor (= 0.71.19)
    - React-perflogger (= 0.71.19)
    - Yoga
  - React-Core/CoreModulesHeaders (0.71.19):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.19)
    - React-hermes
    - React-jsi (= 0.71.19)
    - React-jsiexecutor (= 0.71.19)
    - React-perflogger (= 0.71.19)
    - Yoga
  - React-Core/Default (0.71.19):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.71.19)
    - React-hermes
    - React-jsi (= 0.71.19)
    - React-jsiexecutor (= 0.71.19)
    - React-perflogger (= 0.71.19)
    - Yoga
  - React-Core/DevSupport (0.71.19):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.19)
    - React-Core/RCTWebSocket (= 0.71.19)
    - React-cxxreact (= 0.71.19)
    - React-hermes
    - React-jsi (= 0.71.19)
    - React-jsiexecutor (= 0.71.19)
    - React-jsinspector (= 0.71.19)
    - React-perflogger (= 0.71.19)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.71.19):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.19)
    - React-hermes
    - React-jsi (= 0.71.19)
    - React-jsiexecutor (= 0.71.19)
    - React-perflogger (= 0.71.19)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.71.19):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.19)
    - React-hermes
    - React-jsi (= 0.71.19)
    - React-jsiexecutor (= 0.71.19)
    - React-perflogger (= 0.71.19)
    - Yoga
  - React-Core/RCTBlobHeaders (0.71.19):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.19)
    - React-hermes
    - React-jsi (= 0.71.19)
    - React-jsiexecutor (= 0.71.19)
    - React-perflogger (= 0.71.19)
    - Yoga
  - React-Core/RCTImageHeaders (0.71.19):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.19)
    - React-hermes
    - React-jsi (= 0.71.19)
    - React-jsiexecutor (= 0.71.19)
    - React-perflogger (= 0.71.19)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.71.19):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.19)
    - React-hermes
    - React-jsi (= 0.71.19)
    - React-jsiexecutor (= 0.71.19)
    - React-perflogger (= 0.71.19)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.71.19):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.19)
    - React-hermes
    - React-jsi (= 0.71.19)
    - React-jsiexecutor (= 0.71.19)
    - React-perflogger (= 0.71.19)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.71.19):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.19)
    - React-hermes
    - React-jsi (= 0.71.19)
    - React-jsiexecutor (= 0.71.19)
    - React-perflogger (= 0.71.19)
    - Yoga
  - React-Core/RCTTextHeaders (0.71.19):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.19)
    - React-hermes
    - React-jsi (= 0.71.19)
    - React-jsiexecutor (= 0.71.19)
    - React-perflogger (= 0.71.19)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.71.19):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact (= 0.71.19)
    - React-hermes
    - React-jsi (= 0.71.19)
    - React-jsiexecutor (= 0.71.19)
    - React-perflogger (= 0.71.19)
    - Yoga
  - React-Core/RCTWebSocket (0.71.19):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.71.19)
    - React-cxxreact (= 0.71.19)
    - React-hermes
    - React-jsi (= 0.71.19)
    - React-jsiexecutor (= 0.71.19)
    - React-perflogger (= 0.71.19)
    - Yoga
  - React-CoreModules (0.71.19):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.19)
    - React-Codegen (= 0.71.19)
    - React-Core/CoreModulesHeaders (= 0.71.19)
    - React-jsi (= 0.71.19)
    - React-RCTBlob
    - React-RCTImage (= 0.71.19)
    - ReactCommon/turbomodule/core (= 0.71.19)
  - React-cxxreact (0.71.19):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.19)
    - React-jsi (= 0.71.19)
    - React-jsinspector (= 0.71.19)
    - React-logger (= 0.71.19)
    - React-perflogger (= 0.71.19)
    - React-runtimeexecutor (= 0.71.19)
  - React-hermes (0.71.19):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.71.19)
    - React-jsi
    - React-jsiexecutor (= 0.71.19)
    - React-jsinspector (= 0.71.19)
    - React-perflogger (= 0.71.19)
  - React-jsi (0.71.19):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.71.19):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.71.19)
    - React-jsi (= 0.71.19)
    - React-perflogger (= 0.71.19)
  - React-jsinspector (0.71.19)
  - React-logger (0.71.19):
    - glog
  - react-native-config (1.5.2):
    - react-native-config/App (= 1.5.2)
  - react-native-config/App (1.5.2):
    - React-Core
  - react-native-date-picker (4.4.2):
    - React-Core
  - react-native-document-picker (8.2.2):
    - React-Core
  - react-native-geolocation (3.2.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-maps (1.15.6):
    - React-Core
  - react-native-razorpay (2.3.0):
    - razorpay-pod
    - React
  - react-native-render-html (6.3.4):
    - React-Core
  - react-native-safe-area-context (4.10.5):
    - React-Core
  - react-native-splash-screen (3.3.0):
    - React-Core
  - React-perflogger (0.71.19)
  - React-RCTActionSheet (0.71.19):
    - React-Core/RCTActionSheetHeaders (= 0.71.19)
  - React-RCTAnimation (0.71.19):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.19)
    - React-Codegen (= 0.71.19)
    - React-Core/RCTAnimationHeaders (= 0.71.19)
    - React-jsi (= 0.71.19)
    - ReactCommon/turbomodule/core (= 0.71.19)
  - React-RCTAppDelegate (0.71.19):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.71.19):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.71.19)
    - React-Core/RCTBlobHeaders (= 0.71.19)
    - React-Core/RCTWebSocket (= 0.71.19)
    - React-jsi (= 0.71.19)
    - React-RCTNetwork (= 0.71.19)
    - ReactCommon/turbomodule/core (= 0.71.19)
  - React-RCTImage (0.71.19):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.19)
    - React-Codegen (= 0.71.19)
    - React-Core/RCTImageHeaders (= 0.71.19)
    - React-jsi (= 0.71.19)
    - React-RCTNetwork (= 0.71.19)
    - ReactCommon/turbomodule/core (= 0.71.19)
  - React-RCTLinking (0.71.19):
    - React-Codegen (= 0.71.19)
    - React-Core/RCTLinkingHeaders (= 0.71.19)
    - React-jsi (= 0.71.19)
    - ReactCommon/turbomodule/core (= 0.71.19)
  - React-RCTNetwork (0.71.19):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.19)
    - React-Codegen (= 0.71.19)
    - React-Core/RCTNetworkHeaders (= 0.71.19)
    - React-jsi (= 0.71.19)
    - ReactCommon/turbomodule/core (= 0.71.19)
  - React-RCTSettings (0.71.19):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.71.19)
    - React-Codegen (= 0.71.19)
    - React-Core/RCTSettingsHeaders (= 0.71.19)
    - React-jsi (= 0.71.19)
    - ReactCommon/turbomodule/core (= 0.71.19)
  - React-RCTText (0.71.19):
    - React-Core/RCTTextHeaders (= 0.71.19)
  - React-RCTVibration (0.71.19):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.71.19)
    - React-Core/RCTVibrationHeaders (= 0.71.19)
    - React-jsi (= 0.71.19)
    - ReactCommon/turbomodule/core (= 0.71.19)
  - React-runtimeexecutor (0.71.19):
    - React-jsi (= 0.71.19)
  - ReactCommon/turbomodule/bridging (0.71.19):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.19)
    - React-Core (= 0.71.19)
    - React-cxxreact (= 0.71.19)
    - React-jsi (= 0.71.19)
    - React-logger (= 0.71.19)
    - React-perflogger (= 0.71.19)
  - ReactCommon/turbomodule/core (0.71.19):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.71.19)
    - React-Core (= 0.71.19)
    - React-cxxreact (= 0.71.19)
    - React-jsi (= 0.71.19)
    - React-logger (= 0.71.19)
    - React-perflogger (= 0.71.19)
  - rn-fetch-blob (0.12.0):
    - React-Core
  - RNCAsyncStorage (1.23.1):
    - React-Core
  - RNDateTimePicker (7.7.0):
    - React-Core
  - RNGestureHandler (2.16.2):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNLocalize (3.2.0):
    - React-Core
  - RNPermissions (3.10.1):
    - React-Core
  - RNReanimated (2.17.0):
    - DoubleConversion
    - FBLazyVector
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-callinvoker
    - React-Core
    - React-Core/DevSupport
    - React-Core/RCTWebSocket
    - React-CoreModules
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-RCTActionSheet
    - React-RCTAnimation
    - React-RCTBlob
    - React-RCTImage
    - React-RCTLinking
    - React-RCTNetwork
    - React-RCTSettings
    - React-RCTText
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (3.32.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-RCTImage
  - RNSVG (13.14.1):
    - React-Core
  - SocketRocket (0.6.1)
  - Yoga (1.14.0)
  - YogaKit (1.18.1):
    - Yoga (~> 1.14)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Flipper (= 0.125.0)
  - Flipper-Boost-iOSX (= ********.11)
  - Flipper-DoubleConversion (= *******)
  - Flipper-Fmt (= 7.1.7)
  - Flipper-Folly (= 2.6.10)
  - Flipper-Glog (= *******)
  - Flipper-PeerTalk (= 0.0.4)
  - Flipper-RSocket (= 1.4.3)
  - FlipperKit (= 0.125.0)
  - FlipperKit/Core (= 0.125.0)
  - FlipperKit/CppBridge (= 0.125.0)
  - FlipperKit/FBCxxFollyDynamicConvert (= 0.125.0)
  - FlipperKit/FBDefines (= 0.125.0)
  - FlipperKit/FKPortForwarding (= 0.125.0)
  - FlipperKit/FlipperKitHighlightOverlay (= 0.125.0)
  - FlipperKit/FlipperKitLayoutPlugin (= 0.125.0)
  - FlipperKit/FlipperKitLayoutTextSearchable (= 0.125.0)
  - FlipperKit/FlipperKitNetworkPlugin (= 0.125.0)
  - FlipperKit/FlipperKitReactPlugin (= 0.125.0)
  - FlipperKit/FlipperKitUserDefaultsPlugin (= 0.125.0)
  - FlipperKit/SKIOSNetworkPlugin (= 0.125.0)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - OpenSSL-Universal (= 1.1.1100)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-config (from `../node_modules/react-native-config`)
  - react-native-date-picker (from `../node_modules/react-native-date-picker`)
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - "react-native-geolocation (from `../node_modules/@react-native-community/geolocation`)"
  - react-native-maps (from `../node_modules/react-native-maps`)
  - react-native-razorpay (from `../node_modules/react-native-razorpay`)
  - react-native-render-html (from `../node_modules/react-native-render-html`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - rn-fetch-blob (from `../node_modules/rn-fetch-blob`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNLocalize (from `../node_modules/react-native-localize`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - CocoaAsyncSocket
    - Flipper
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - Flipper-RSocket
    - FlipperKit
    - fmt
    - libevent
    - OpenSSL-Universal
    - razorpay-pod
    - SocketRocket
    - YogaKit

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-config:
    :path: "../node_modules/react-native-config"
  react-native-date-picker:
    :path: "../node_modules/react-native-date-picker"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-geolocation:
    :path: "../node_modules/@react-native-community/geolocation"
  react-native-maps:
    :path: "../node_modules/react-native-maps"
  react-native-razorpay:
    :path: "../node_modules/react-native-razorpay"
  react-native-render-html:
    :path: "../node_modules/react-native-render-html"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  rn-fetch-blob:
    :path: "../node_modules/rn-fetch-blob"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNLocalize:
    :path: "../node_modules/react-native-localize"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 7dcd2de282d72e344012f7d6564d024930a6a440
  BVLinearGradient: 880f91a7854faff2df62518f0281afb1c60d49a3
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: 038aa5ab388c9eec7a6a65b71596afd9ff99c949
  FBReactNativeSpec: 25c3e937fdcfe4d7c898449051e1f30b93cc99ce
  Flipper: 26fc4b7382499f1281eb8cb921e5c3ad6de91fe0
  Flipper-Boost-iOSX: fd1e2b8cbef7e662a122412d7ac5f5bea715403c
  Flipper-DoubleConversion: 2dc99b02f658daf147069aad9dbd29d8feb06d30
  Flipper-Fmt: 60cbdd92fc254826e61d669a5d87ef7015396a9b
  Flipper-Folly: 584845625005ff068a6ebf41f857f468decd26b3
  Flipper-Glog: 70c50ce58ddaf67dc35180db05f191692570f446
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  Flipper-RSocket: d9d9ade67cbecf6ac10730304bf5607266dd2541
  FlipperKit: cbdee19bdd4e7f05472a66ce290f1b729ba3cb86
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  hermes-engine: 1468b458e81705fcd55ed6e29c32ff069f04b69c
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  OpenSSL-Universal: ebc357f1e6bc71fa463ccb2fe676756aff50e88c
  razorpay-pod: c7064a37dd15fbc0e5f26776314c9a8842df1269
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: 79b5f823c2b4b865451ba9ddb33c676786760ea8
  RCTTypeSafety: 4f6d5414413d8848923ccd448c4cef4b6ffcd403
  React: d277ab7d840987a460690fcbe979e9234785ad63
  React-callinvoker: 2d15c3bc682ef720a9a23428606aab643df74622
  React-Codegen: f00947eb19953c13e78c38d73233706e4d6f9bcb
  React-Core: b3d9f6a3a406fb3a6ee84f98df0a3e45df3aed99
  React-CoreModules: a70aed68d7cf8bb94f368cbfc8da02d9586e6a65
  React-cxxreact: 12d097fb82c333c4a193cd2cd2111b0295257453
  React-hermes: fa22fb2131e784d87105343833d88c1dc6971676
  React-jsi: a7a06cf56e5af973a35972aa57443629d5621aed
  React-jsiexecutor: 7263801e44e004967685dd40672bfcedcda00834
  React-jsinspector: e591d9ecb571456fc929ee10409d8847a442d6a7
  React-logger: 70ddbe0e07179c8adb94c4bffc36c6abc952d3d4
  react-native-config: d7d8a0c65f7fa523197879f6b777997abbfc987e
  react-native-date-picker: 312c387d2ff873c66c5e4cf78ff6827fa91644e2
  react-native-document-picker: cd4d6b36a5207ad7a9e599ebb9eb0c2e84fa0b87
  react-native-geolocation: 1279fd4390e311ec3f2fc2878279934147fdd061
  react-native-maps: 7b2c2cee95271592a92ba485eea1dec66a363a6e
  react-native-razorpay: 0272dd684bc9148828c54a72e05059e3e2e8b813
  react-native-render-html: 984dfe2294163d04bf5fe25d7c9f122e60e05ebe
  react-native-safe-area-context: a240ad4b683349e48b1d51fed1611138d1bdad97
  react-native-splash-screen: 4312f786b13a81b5169ef346d76d33bc0c6dc457
  React-perflogger: 0ba097528e325435aca94b32b5330f58f6acb6fb
  React-RCTActionSheet: 805b3a83f3dd7ae5a1213ea9df0ed748eeb74b85
  React-RCTAnimation: 2a0233681dee47e468302b8233652b35b68329cb
  React-RCTAppDelegate: 3171d94d0ce2ed7fcf982689cd6bbaf4a8721ce8
  React-RCTBlob: 8c6962fcab4a5a3d0078a3a4a98bb914a7703abf
  React-RCTImage: 015ce2b9d2ad78c24deee5c807d88574225974f3
  React-RCTLinking: 6dd57d2d99ecdf6ee016c5a742ed9b68cdc8680a
  React-RCTNetwork: f14e9681496cd394196c2417966769d10b45e674
  React-RCTSettings: ce9d4d7dda6ad00acb1460a1a3a286d7e4e950bd
  React-RCTText: 4cbca7004176dd0ed495afa60a3978acf040059d
  React-RCTVibration: 5b2bfab941e938ee5060acefdd7598077b394975
  React-runtimeexecutor: b9be1f58ce9a8b849cb8a37dd998f04ca97e3619
  ReactCommon: bb3d7051c9b8c58ee34f9af76f883bd0fd7c9c31
  rn-fetch-blob: f065bb7ab7fb48dd002629f8bdcb0336602d3cba
  RNCAsyncStorage: 826b603ae9c0f88b5ac4e956801f755109fa4d5c
  RNDateTimePicker: 4f3c4dbd4f908be32ec8c93f086e8924bd4a2e07
  RNGestureHandler: d678f94a8f74de70ce3d9f5d23eb0f97b686e943
  RNLocalize: b77875884750cb6a58cd6865863fe2ba2729b72b
  RNPermissions: 4e3714e18afe7141d000beae3755e5b5fb2f5e05
  RNReanimated: f186e85d9f28c9383d05ca39e11dd194f59093ec
  RNScreens: 0bd9eec783bed1032e02a4db9976dae1664a5c7b
  RNSVG: af3907ac5d4fa26a862b75a16d8f15bc74f2ceda
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  Yoga: d4f5b037672e6b68ba484c7cf7fe6790122df792
  YogaKit: f782866e155069a2cca2517aafea43200b01fd5a

PODFILE CHECKSUM: af5fdacbd91be4d7d659cd89dc99a66cc7c3d6f4

COCOAPODS: 1.15.2

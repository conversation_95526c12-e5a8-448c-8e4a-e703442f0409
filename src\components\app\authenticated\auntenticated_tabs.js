// import { createTabNavigator } from "@react-navigation/drawer";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import Home from "../../e_homePage/e_home";
import Workactivities from "../../e_workactivities/e_workactivities";
import ConsumerIndexingMain from "../../e_consumer-indexing/e_consumer-index";
import Services from "../../e_services/e_services";
import Notifications from "../../e_notifications/e_notifications";
import Profile from "../../e_profile/e_profile";
import Preferences from "../../e_preferences/e_preferences";
import ViewPastTickets from "../../e_viewPastTickets/e_viewPastTickets";
import Downloads from "../../e_downloads/e_downloads";
import Help from "../../e_help/e_help";
import AssetsPage from "../../e_assets/e_assets";
import { config } from "../../../environment";
import HomeWO from "../../e_homePage/e_home_wo";
import { stackContext } from "../get_stack";
import React from "react";

const Tab = createBottomTabNavigator();

export default function AuthenticatedTab(props) {
  const { workModelType } = React.useContext(stackContext);
  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarHideOnKeyboard: true,
        tabBarStyle: { display: "none" },
        scrollEnabled: true,
      }}>
      {workModelType == "WA" ? (
        <Tab.Screen name="Home" component={Home} />
      ) : (
        <Tab.Screen name="Home" component={HomeWO} />
      )}
      <Tab.Screen name="WorkActivities" component={Workactivities} />
      <Tab.Screen
        name="ConsumerIndexingMain"
        component={ConsumerIndexingMain}
      />
      <Tab.Screen name="Assets" component={AssetsPage} />
      <Tab.Screen name="Notifications" component={Notifications} />
      <Tab.Screen name="ProfileTab" component={Profile} />
      <Tab.Screen name="PreferencesTab" component={Preferences} />
      <Tab.Screen name="ChangePasswordTab" component={Profile} />
      <Tab.Screen name="ViewPastTicketsTab" component={ViewPastTickets} />
      <Tab.Screen name="DownloadsTab" component={Downloads} />
      <Tab.Screen name="HelpTab" component={Help} />
      <Tab.Screen name="Services" component={Services} />
    </Tab.Navigator>
  );
}

import { createSlice } from "@reduxjs/toolkit";

const unreadBillNotificationReducer = createSlice({
  name: "unreadBillInfo",
  initialState: {
    unreadBillInfo: [],
  },
  reducers: {
    unreadBillInfo: (state, action) => {
      state.unreadBillInfo = action.payload;
    },
  },
});

export const unreadBillInfo =
  unreadBillNotificationReducer.actions.unreadBillInfo;
export default unreadBillNotificationReducer.reducer;

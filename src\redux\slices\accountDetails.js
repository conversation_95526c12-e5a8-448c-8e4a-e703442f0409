import { createSlice } from "@reduxjs/toolkit";

const accountDetailsReducer = createSlice({
    name: "accountDetails",
    initialState: {
        accountDetails:[]
    },
    reducers: {
        accountDetailsInfo: (state, action) => {
            state.accountDetails = action.payload
        },
    }
})

export const accountDetailsInfo = accountDetailsReducer.actions.accountDetailsInfo;
export default accountDetailsReducer.reducer;
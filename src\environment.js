// const BASE_URL = "https://impresacx-dev.abjayon.com";
// https://impresa-fieldwork-dev.abjayon.com
// https://impresa-fieldwork-salesdemo.abjayon.com
import { Config } from "react-native-config";
const { BASE_URL, CLIENT_ID_KEYCLOCK, AUTH_UI_CLIENT_SECRET_KEYCLOCK } = Config;
const BASE_TENANT_CODE = "FS";
const WORK_MODEL_TYPE = "WA";
const ASSEST_MANAGER_URL = "https://impresacx-dev.abjayon.com/assets";
const constants = {
  urls: {
    WORK_ACTIVITIES_URL: `${BASE_URL}/dummyData/manageWorkOrders.json`,
    ASSEST_URL_ENDPOINT: ASSEST_MANAGER_URL,
    GENERATE_BEARER: `${BASE_URL}/auth/login/generate-bearer`,
    LOGIN: `${BASE_URL}/auth/login`,
    LOGIN_VIA_OTP: `${BASE_URL}/auth/login/userName`,
    RESET: `${BASE_URL}/auth/login/forgot-password`,
    RESENDOTP: `${BASE_URL}/auth/otp/resend`,
    VERIFYOTP: `${BASE_URL}/auth/otp/validate`,
    GET_CONSUMER_LIST: `${BASE_URL}/asset/consumer-indexing/`,
    UPDATE_CONSUMER_LIST: `${BASE_URL}/asset/consumer-indexing/`,
    GET_CONSUMER_DETAILS: `${BASE_URL}/asset/consumer-indexing/oci?`,
    GET_WORK_ORDERS: `${BASE_URL}/work-management/work-order`,
    // ACCEPTUPDATE: `${BASE_URL}/work-management/work-order/addupdatewo`,
    ACCEPTUPDATE: `https://impresa-fieldwork-demo.abjayon.com/work-management/work-order/addupdatewo`,
    WORK_MANAGEMENT:
      "https://impresa-fieldwork-demo.abjayon.com/work-management",
    WORK_MODEL_TYPE_URL: `${BASE_URL}/field-setup/lookup/list`,
    SAVETOKEN_URL:`${BASE_URL}/work-management/push-notifications/saveToken`
  },
  constants: {
    WORK_MODEL_TYPE,
    BASE_URL,
    BASE_TENANT_CODE,
    CLIENT_ID_KEYCLOCK,
    AUTH_UI_CLIENT_SECRET_KEYCLOCK,
  },
};

export const config = constants;

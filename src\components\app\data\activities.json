[{"id": 1, "ticketId": 6670, "title": "Consumer Indexing", "startDate": "09-Aug-2023 09-11 AM", "priority": "Medium", "route": "RT-1", "status": "Open", "checklists": [{"id": "ID10", "title": "", "done": false, "description": "Verify power spply to the meter is switched off, Disconnected Existing Meter", "endTime": "08/10/2023 11:30 AM"}, {"id": "ID20", "title": "", "done": false, "description": "Install Mounting Hardware, Connect Wiring", "endTime": "08/10/2023 12:30 PM"}, {"id": "ID30", "title": "", "done": false, "description": "Configure Communication, Test Functionality", "endTime": "08/10/2023 12:45 PM"}, {"id": "ID40", "title": "", "done": false, "description": "Seal and Secure", "endTime": "08/10/2023 12:45 PM"}, {"id": "ID50", "title": "", "done": false, "description": "Document Installation", "endTime": "08/10/2023 1:20 PM"}]}, {"id": 2, "ticketId": 1234, "title": "Meter Installation", "startDate": "09-Aug-2023 09-11 AM", "priority": "High", "route": "RT-2", "status": "Overdue", "checklists": [{"id": "ID10", "title": "", "done": false, "description": "Verify power spply to the meter is switched off, Disconnected Existing Meter", "endTime": "08/10/2023 11:30 AM"}, {"id": "ID20", "title": "", "done": false, "description": "Install Mounting Hardware, Connect Wiring", "endTime": "08/10/2023 12:30 PM"}, {"id": "ID30", "title": "", "done": false, "description": "Configure Communication, Test Functionality", "endTime": "08/10/2023 12:45 PM"}, {"id": "ID40", "title": "", "done": false, "description": "Seal and Secure", "endTime": "08/10/2023 12:45 PM"}, {"id": "ID50", "title": "", "done": false, "description": "Document Installation", "endTime": "08/10/2023 1:20 PM"}]}, {"id": 3, "ticketId": 6754, "title": "Meter Installation", "startDate": "09-Aug-2023 02-04 PM", "priority": "High", "route": "RT-3", "status": "Closed", "checklists": [{"id": "ID10", "title": "", "done": false, "description": "Verify power spply to the meter is switched off, Disconnected Existing Meter", "endTime": "08/10/2023 11:30 AM"}, {"id": "ID20", "title": "", "done": false, "description": "Install Mounting Hardware, Connect Wiring", "endTime": "08/10/2023 12:30 PM"}, {"id": "ID30", "title": "", "done": false, "description": "Configure Communication, Test Functionality", "endTime": "08/10/2023 12:45 PM"}, {"id": "ID40", "title": "", "done": false, "description": "Seal and Secure", "endTime": "08/10/2023 12:45 PM"}, {"id": "ID50", "title": "", "done": false, "description": "Document Installation", "endTime": "08/10/2023 1:20 PM"}]}, {"id": 4, "ticketId": 3421, "title": "Meter Installation", "startDate": "10-Aug-2023 09-11 AM", "priority": "Low", "route": " RT-4", "status": "In Progress", "checklists": [{"id": "ID10", "title": "", "done": false, "description": "Verify power spply to the meter is switched off, Disconnected Existing Meter", "endTime": "08/10/2023 11:30 AM"}, {"id": "ID20", "title": "", "done": false, "description": "Install Mounting Hardware, Connect Wiring", "endTime": "08/10/2023 12:30 PM"}, {"id": "ID30", "title": "", "done": false, "description": "Configure Communication, Test Functionality", "endTime": "08/10/2023 12:45 PM"}, {"id": "ID40", "title": "", "done": false, "description": "Seal and Secure", "endTime": "08/10/2023 12:45 PM"}, {"id": "ID50", "title": "", "done": false, "description": "Document Installation", "endTime": "08/10/2023 1:20 PM"}]}, {"id": 6, "ticketId": 6730, "title": "Consumer Indexing", "startDate": "10-Aug-2023 02-04 PM", "priority": "High", "route": "RT-5", "status": "In Progress", "checklists": [{"id": "ID10", "title": "", "done": false, "description": "Verify power spply to the meter is switched off, Disconnected Existing Meter", "endTime": "08/10/2023 11:30 AM"}, {"id": "ID20", "title": "", "done": false, "description": "Install Mounting Hardware, Connect Wiring", "endTime": "08/10/2023 12:30 PM"}, {"id": "ID30", "title": "", "done": false, "description": "Configure Communication, Test Functionality", "endTime": "08/10/2023 12:45 PM"}, {"id": "ID40", "title": "", "done": false, "description": "Seal and Secure", "endTime": "08/10/2023 12:45 PM"}, {"id": "ID50", "title": "", "done": false, "description": "Document Installation", "endTime": "08/10/2023 1:20 PM"}]}, {"id": 7, "ticketId": 1024, "title": "Consumer Indexing", "startDate": "10-Aug-2023 02-04 PM", "priority": "Medium", "route": "RT-6", "status": "Closed", "checklists": [{"id": "ID10", "title": "", "done": false, "description": "Verify power spply to the meter is switched off, Disconnected Existing Meter", "endTime": "08/10/2023 11:30 AM"}, {"id": "ID20", "title": "", "done": false, "description": "Install Mounting Hardware, Connect Wiring", "endTime": "08/10/2023 12:30 PM"}, {"id": "ID30", "title": "", "done": false, "description": "Configure Communication, Test Functionality", "endTime": "08/10/2023 12:45 PM"}, {"id": "ID40", "title": "", "done": false, "description": "Seal and Secure", "endTime": "08/10/2023 12:45 PM"}, {"id": "ID50", "title": "", "done": false, "description": "Document Installation", "endTime": "08/10/2023 1:20 PM"}]}, {"id": 8, "ticketId": 8756, "title": "Meter Installation", "startDate": "10-Aug-2023 02-04 PM", "priority": "High", "route": "RT-7", "status": "Closed", "checklists": [{"id": "ID10", "title": "", "done": false, "description": "Verify power spply to the meter is switched off, Disconnected Existing Meter", "endTime": "08/10/2023 11:30 AM"}, {"id": "ID20", "title": "", "done": false, "description": "Install Mounting Hardware, Connect Wiring", "endTime": "08/10/2023 12:30 PM"}, {"id": "ID30", "title": "", "done": false, "description": "Configure Communication, Test Functionality", "endTime": "08/10/2023 12:45 PM"}, {"id": "ID40", "title": "", "done": false, "description": "Seal and Secure", "endTime": "08/10/2023 12:45 PM"}, {"id": "ID50", "title": "", "done": false, "description": "Document Installation", "endTime": "08/10/2023 1:20 PM"}]}, {"id": 9, "ticketId": 1532, "title": "Consumer Indexing", "startDate": "11-Aug-2023 09-11 AM", "priority": "High", "route": "RT-1", "status": "Cancelled", "checklists": [{"id": "ID10", "title": "", "done": false, "description": "Verify power spply to the meter is switched off, Disconnected Existing Meter", "endTime": "08/10/2023 11:30 AM"}, {"id": "ID20", "title": "", "done": false, "description": "Install Mounting Hardware, Connect Wiring", "endTime": "08/10/2023 12:30 PM"}, {"id": "ID30", "title": "", "done": false, "description": "Configure Communication, Test Functionality", "endTime": "08/10/2023 12:45 PM"}, {"id": "ID40", "title": "", "done": false, "description": "Seal and Secure", "endTime": "08/10/2023 12:45 PM"}, {"id": "ID50", "title": "", "done": false, "description": "Document Installation", "endTime": "08/10/2023 1:20 PM"}]}]
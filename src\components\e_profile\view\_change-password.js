import React, { useState, useEffect, useContext } from "react";
import { StyleSheet, View, ActivityIndicator } from "react-native";
import {
  Card,
  Text,
  Checkbox,
  List,
  IconButton,
  TextInput,
  HelperText,
} from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import { FlatList } from "react-native-gesture-handler";
import { useDispatch, useSelector } from "react-redux";
import FlatButton from "../../common/_flat_button";
import Button from "../../common/_button";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { ChangePasswordService } from "../model/_change-password-service";
import { profileContext } from "../e_profile";
import ChangePasswordModal from "./_change-password-modal";
import Icon from "../../icon";

export default function ChangePassword() {
  const [oldPassword, setOldPassword] = React.useState("");
  const [newPassword, setNewPassword] = React.useState("");
  const [confirmPassword, setConfirmPassword] = React.useState("");
  const { showProfilePopup, setShowCPasswordPopup } =
    useContext(profileContext);
  const [error, setError] = React.useState(false);
  const [errorText, setErrorText] = React.useState();
  const [oldPasswordErr, setOldPasswordErr] = React.useState(false);
  const [newPasswordErr, setNewPasswordErr] = React.useState(false);
  const [confirmPasswordErr, setConfirmPasswordErr] = React.useState(false);
  const [passwordNotMatchErr, setPasswordNotMatchErr] = React.useState(false);
  const [passowrdSecurity, setPassowrdSecurityNew] = React.useState(true);
  const [passowrdSecurityConfirm, setPassowrdSecurityConfirm] =
    React.useState(true);
  const [oldPasswordSecurity, setOldPasswordSecurity] = React.useState(true);
  const [disableSubmit, setDisableSubmit] = useState(false);
  const [newPwdStrengthErr, setNewPwdStrengthErr] = useState(false);
  const dispatch = useDispatch();
  const [isLoading, setLoading] = useState(false);
  let accountId = useSelector(
    store => store?.meterDetails?.meterDetails?.accountId,
  );
  const { showChangepasswordPopup } = useContext(profileContext);

  const pathName = useSelector(state => state?.servicePath?.servicePath);
  useEffect(() => {
    closePassword();
  }, [pathName]);

  useEffect(() => {
    if (
      oldPassword.length > 0 &&
      oldPasswordErr === false &&
      newPassword &&
      newPasswordErr === false &&
      confirmPassword &&
      confirmPasswordErr === false &&
      newPwdStrengthErr === false
    ) {
      setDisableSubmit(true);
    } else {
      setDisableSubmit(false);
    }
  }, [
    oldPassword,
    oldPasswordErr,
    newPassword,
    newPasswordErr,
    confirmPassword,
    confirmPasswordErr,
    newPwdStrengthErr,
  ]);
  useEffect(() => {
    closePassword();
  }, [showChangepasswordPopup]);

  useEffect(() => {
    if (confirmPassword && newPassword) {
      if (confirmPassword == newPassword) {
        setConfirmPasswordErr(false);
      } else {
        setConfirmPasswordErr(true);
      }
    }
  }, [confirmPassword, newPassword]);

  const checkOldPassword = oldPassword => {
    if (oldPassword.length === 0) {
      setOldPasswordErr(true);
    } else {
      setOldPasswordErr(false);
    }
  };
  const checkNewPassword = newPassword => {
    if (newPassword.length === 0) {
      setNewPasswordErr(true);
    } else {
      validatePwdStrength(newPassword);
      setNewPasswordErr(false);
    }
  };

  const validatePwdStrength = newPwd => {
    var regData = "^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{6,})";
    var strongRegex = new RegExp(regData);
    var test = strongRegex.test(newPwd);
    if (test) {
      setNewPwdStrengthErr(false);
    } else {
      setNewPwdStrengthErr(true);
    }
  };

  const checkConfirmPassword = confirmPassword => {
    if (confirmPassword.length === 0) {
      setConfirmPasswordErr(true);
    } else {
      setConfirmPasswordErr(false);
    }

    if (confirmPassword.length !== 0 && newPassword.length !== 0) {
      if (newPassword !== confirmPassword) {
        setPasswordNotMatchErr(true);
      } else {
        setPasswordNotMatchErr(false);
      }
    }
  };

  const ChangePassword = async () => {
    if (oldPassword.length > 0) {
      setOldPasswordErr(false);
    } else {
      setOldPasswordErr(true);
    }
    if (newPassword && newPassword.length > 0) {
      setNewPasswordErr(false);
    } else {
      setNewPasswordErr(true);
    }

    if (confirmPassword.length > 0) {
      setConfirmPasswordErr(false);
    } else {
      setConfirmPasswordErr(true);
    }
    if (
      oldPassword &&
      newPassword &&
      confirmPassword &&
      oldPasswordErr === false &&
      newPasswordErr === false &&
      confirmPasswordErr === false
    ) {
      validatePwdStrength(newPassword);
      if (
        oldPassword.length !== 0 &&
        newPassword.length !== 0 &&
        confirmPassword.length !== 0 &&
        newPwdStrengthErr === false
      ) {
        // setShowCPasswordPopup(true);
        // setOldPassword("");
        // setNewPassword("");
        // setConfirmPassword("");
        setLoading(true);
        let bearer = await AsyncStorage.getItem("bearer");
        bearer = JSON.parse(bearer);
        const userName = bearer.userName;

        try {
          const res = await ChangePasswordService.changePassword(
            userName,
            oldPassword,
            newPassword,
            accountId,
          );
          if (res?.data?.isOk) {
            setShowCPasswordPopup(true);
            setOldPassword("");
            setNewPassword("");
            setConfirmPassword("");
            setError(false);
            setNewPasswordErr(false);
            setOldPasswordErr(false);
            setConfirmPasswordErr(false);
            setLoading(false);
          } else {
            setError(true);
            setErrorText("Please try after sometime.");
            setLoading(false);
          }
        } catch (err) {
          setError(true);
          setErrorText(err.response.data.message);
          setLoading(false);
        }
      }
    }
  };
  const closePassword = () => {
    setOldPassword("");
    setNewPassword("");
    setConfirmPassword("");
    setError(false);
    setErrorText();
    setPasswordNotMatchErr(false);
    setOldPasswordErr(false);
    setNewPasswordErr(false);
    setConfirmPasswordErr(false);
    setLoading(false);
    setNewPwdStrengthErr(false);
  };
  const secureTextEntryNew = () => {
    setPassowrdSecurityNew(passowrdSecurity ? false : true);
  };
  const secureTextEntryConfirm = () => {
    setPassowrdSecurityConfirm(passowrdSecurityConfirm ? false : true);
  };

  const secureTextEntryOld = () => {
    setOldPasswordSecurity(oldPasswordSecurity ? false : true);
  };

  return (
    <>
      <View>
        <Card style={styles.card}>
          <Text style={styles.titleCard}>CHANGE PASSWORD</Text>
          <View style={{ marginTop: 5 }}>
            <Text variant="headlineSmall" style={styles.lable}>
              Old Password:
            </Text>
            <TextInput
              placeholderTextColor={GlobalStyles.colors.eDark.base}
              mode="outlined"
              dense
              value={oldPassword}
              onChangeText={oldPassword => setOldPassword(oldPassword)}
              placeholder="Enter Old Password"
              outlineColor={GlobalStyles.colors.eLight.base}
              right={
                oldPasswordSecurity ? (
                  <TextInput.Icon
                    icon={() => (
                      <Icon
                        name=""
                        size={20}
                        color={GlobalStyles.colors.ePrimary.base}
                      />
                    )}
                    onPress={secureTextEntryOld}
                    color={GlobalStyles.colors.ePrimary.base}
                    size={20}
                    // style={styles.icon}
                  />
                ) : (
                  <TextInput.Icon
                    icon={() => (
                      <Icon
                        name="Eye-fill-icon"
                        size={20}
                        color={GlobalStyles.colors.ePrimary.base}
                      />
                    )}
                    fontSize="small"
                    onPress={secureTextEntryOld}
                    color={GlobalStyles.colors.ePrimary.base}
                    size={20}
                    style={styles.icon}
                  />
                )
              }
              onBlur={() => checkOldPassword(oldPassword)}
              theme={{
                roundness: 5,
                colors: { text: GlobalStyles.colors.eDark.base },
              }}
              style={styles.textBox}
              error={oldPasswordErr}
              secureTextEntry={oldPasswordSecurity}
              activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
            />
            <HelperText
              type="error"
              visible={oldPasswordErr}
              style={styles.helperText}>
              *Please Enter Old Password
            </HelperText>
          </View>
          <View style={{ marginTop: -5 }}>
            <Text variant="headlineSmall" style={styles.lable}>
              New Password:
            </Text>
            <TextInput
              placeholderTextColor={GlobalStyles.colors.eDark.base}
              mode="outlined"
              value={newPassword}
              dense
              onChangeText={newPassword => setNewPassword(newPassword)}
              placeholder="Enter New Password"
              outlineColor={GlobalStyles.colors.eLight.base}
              theme={{
                roundness: 5,
                colors: { text: GlobalStyles.colors.eDark.base },
              }}
              style={styles.textBox}
              activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
              right={
                passowrdSecurity ? (
                  <TextInput.Icon
                    icon={() => (
                      <Icon
                        name=""
                        size={20}
                        color={GlobalStyles.colors.ePrimary.base}
                      />
                    )}
                    onPress={secureTextEntryNew}
                    color={GlobalStyles.colors.ePrimary.base}
                    size={20}
                    style={styles.icon}
                  />
                ) : (
                  <TextInput.Icon
                    icon={() => (
                      <Icon
                        name="Eye-fill-icon"
                        size={20}
                        color={GlobalStyles.colors.ePrimary.base}
                      />
                    )}
                    onPress={secureTextEntryNew}
                    color={GlobalStyles.colors.ePrimary.base}
                    size={20}
                    style={styles.icon}
                  />
                )
              }
              onBlur={() => checkNewPassword(newPassword)}
              secureTextEntry={passowrdSecurity}
              error={newPasswordErr || newPwdStrengthErr}
            />
            <HelperText
              type="error"
              visible={newPasswordErr || newPwdStrengthErr}
              style={styles.helperText}>
              {newPwdStrengthErr
                ? "*Password is case-sensitive, it must contain a minimum of six characters with atleast one capital letter, one small letter, one number and one special character."
                : newPasswordErr && "*Please Enter New Password"}
            </HelperText>
          </View>
          <View style={{ marginTop: -5 }}>
            <Text variant="headlineSmall" style={styles.lable}>
              Confirm Password:
            </Text>
            <TextInput
              placeholderTextColor={GlobalStyles.colors.eDark.base}
              mode="outlined"
              value={confirmPassword}
              dense
              onChangeText={confirmPassword =>
                setConfirmPassword(confirmPassword)
              }
              placeholder="Enter Confirm Password"
              outlineColor={GlobalStyles.colors.eLight.base}
              theme={{
                roundness: 5,
                colors: { text: GlobalStyles.colors.eDark.base },
              }}
              activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
              right={
                passowrdSecurityConfirm ? (
                  <TextInput.Icon
                    icon={() => (
                      <Icon
                        name=""
                        size={20}
                        color={GlobalStyles.colors.ePrimary.base}
                      />
                    )}
                    onPress={secureTextEntryConfirm}
                    color={GlobalStyles.colors.ePrimary.base}
                    size={20}
                    style={styles.icon}
                  />
                ) : (
                  <TextInput.Icon
                    icon={() => (
                      <Icon
                        name="Eye-fill-icon"
                        size={20}
                        color={GlobalStyles.colors.ePrimary.base}
                      />
                    )}
                    onPress={secureTextEntryConfirm}
                    color={GlobalStyles.colors.ePrimary.base}
                    size={20}
                    style={styles.icon}
                  />
                )
              }
              onBlur={() => checkConfirmPassword(confirmPassword)}
              secureTextEntry={passowrdSecurityConfirm}
              error={confirmPasswordErr || passwordNotMatchErr}
              style={styles.textBox}
            />
            <HelperText
              type="error"
              visible={confirmPasswordErr || passwordNotMatchErr}
              style={styles.helperText}>
              {confirmPasswordErr
                ? "*Please Enter Confirm Password"
                : passwordNotMatchErr
                ? "*New Password and Confirm password should be same"
                : ""}
            </HelperText>
          </View>
          {error ? (
            <Text style={styles.errorCls}>{errorText}</Text>
          ) : (
            <Text style={styles.errorCls}></Text>
          )}
        </Card>
        <View style={styles.centerButtons}>
          <Button
            buttonbgColor={styles.cancelBg}
            textColor={styles.greenText}
            onPress={closePassword}>
            Cancel
          </Button>
          <Button
            buttonbgColor={styles.buttonBgColor}
            textColor={styles.whiteText}
            onPress={ChangePassword}
            // disabled={true}
          >
            Submit
            {isLoading && (
              <ActivityIndicator
                align="center"
                size={13}
                color={GlobalStyles.colors.eWhite.base}
              />
            )}
          </Button>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  titleCard: {
    fontSize: 12,
    color: GlobalStyles.colors.ePrimary.base,
    fontFamily: "NotoSans-Bold",
  },
  lable: {
    fontSize: 12,
    color: GlobalStyles.colors.eRich.base,
    fontFamily: "NotoSans-SemiBold",
  },
  card: {
    marginHorizontal: 17,
    paddingHorizontal: 17,
    paddingVertical: 10,
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eBackground.base,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    textColor: GlobalStyles.colors.eWhite.base,
  },
  centerButtons: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 15,
    marginBottom: 10,
  },
  leftButton: {
    marginRight: 20,
  },
  whiteBgColor: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingVertical: 5,
    marginRight: 10,
    marginVertical: -1,
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 10,
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginLeft: 10,
  },
  greenText: {
    color: GlobalStyles.colors.eSecondary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  whiteText: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  errorCls: {
    color: GlobalStyles.colors.eDanger.dark,
    fontSize: 12,
    maxHeight: 70,
  },
  textBox: {
    // flex: 1,
    fontSize: 14,
    color: GlobalStyles.colors.eDark.base,
    backgroundColor: GlobalStyles.colors.eBackground.base,
  },
  helperText: {
    color: GlobalStyles.colors.eDanger.dark,
  },
});

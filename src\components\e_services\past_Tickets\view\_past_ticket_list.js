import {
  View,
  StyleSheet,
  ScrollView,
  Text,
  Dimensions,
  Platform,
} from "react-native";
import { GlobalStyles } from "../../../app/global-styles";
import { ActivityIndicator, Card } from "react-native-paper";
import React, { useEffect, useState, useContext } from "react";
import Button from "../../../common/_button";
import { ticketService } from "../../model/ticket_service";
import { useDispatch, useSelector } from "react-redux";
import moment from "moment";
import FlatButton from "../../../common/_flat_button";
import { servicePath } from "../../../../redux/slices/servicePath";
import { ticketID } from "../../../../redux/slices/pastTicketId";
import { turnOnOffID } from "../../../../redux/slices/pastTurnOnOff";
import { drawerContext } from "../../../app/authenticated/authenticated_layout";
import Icon from "../../../icon";
import _ from "lodash";
const { width, height } = Dimensions.get("window");
import {
  Collapse,
  CollapseHeader,
  CollapseBody,
} from "accordion-collapse-react-native";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import { drawerContextWO } from "../../../app/authenticated/authenticated_layout_wo";
import { config } from "../../../../environment";
import { stackContext } from "../../../app/get_stack";

export default function PastTicketList() {
  const { workModelType } = React.useContext(stackContext);
  let setOpenMenu, setmenuFlag;

  if (workModelType === "WA") {
    ({ setOpenMenu, setmenuFlag } = useContext(drawerContext));
  } else {
    ({ setOpenMenu, setmenuFlag } = useContext(drawerContextWO));
  }

  const [ticketList, setTicketList] = useState([]);
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();

  let accountId = useSelector(
    store => store?.meterDetails?.meterDetails?.accountId,
  );
  let saDate = useSelector(
    store => store?.meterDetails?.meterDetails?.getSaInfo?.saStartDate,
  );

  const viewDetails = Item => {
    switch (Item.ticketType) {
      case "CM-BCOPY":
        dispatch(servicePath("Complaint_billing"));
        break;
      case "CM-RCPTCPY":
        dispatch(servicePath("PaymentsComplaint"));
        break;
      case "CM-OLUACUP":
        dispatch(servicePath("Complaint_account"));
        break;
      case "CM-TURNONOFF":
        dispatch(turnOnOffID(Item.caseId));
        dispatch(servicePath("TurnOnOff"));
        break;
      case "CM-RELOCSVC":
        dispatch(ticketID(Item.caseId));
        dispatch(servicePath("Relocate"));
        break;
    }
    setOpenMenu(false);
    setmenuFlag(false);
  };

  const onToggleList = caseId => {
    const updatedList = ticketList.map((item, index) => ({
      ...item,
      isExpanded: caseId === item.caseId ? !item.isExpanded : item.isExpanded,
    }));
    setTicketList(updatedList);
  };

  const fetchAndSortTicket = async (saDate, accountId) => {
    try {
      setLoading(true);
      let dateFrom = moment().format("DD-MM-YYYY");
      let dateTo = moment(saDate).format("DD-MM-YYYY");
      const res = await ticketService.getAllTicket(dateTo, dateFrom, accountId);
      sortTicketResponse(res?.data?.getTicketList);
    } catch (err) {
      console.log("Error in fetching and sorting tickets");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (accountId && saDate) {
      fetchAndSortTicket(saDate, accountId);
    }
  }, [accountId, saDate]);

  const sortTicketResponse = ticketResponse => {
    const ticketList = ticketResponse
      .slice()
      .sort(
        (a, b) =>
          moment(
            b.createdDate ? b.createdDate : b.lastUpdDttm,
            "YYYY-MM-DD-HH.mm.ss",
          ).valueOf() -
          moment(
            a.createdDate ? a.createdDate : a.lastUpdDttm,
            "YYYY-MM-DD-HH.mm.ss",
          ).valueOf(),
      );
    setTicketList(ticketList);
  };

  return (
    <Card style={styles.card}>
      <ScrollView style={styles.scrollStyle}>
        {loading ? (
          <View style={styles.activityLoaderContainer}>
            <ActivityIndicator
              size="large"
              color={GlobalStyles.colors.ePrimary.base}
            />
          </View>
        ) : (
          <View style={{}}>
            {ticketList.map((item, k) => {
              let statusTitle = item.caseStatus.replace(/\w\S*/g, txt => {
                return (
                  txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
                );
              });
              return (
                <Collapse
                  key={item.caseId}
                  isExpanded={item.isExpanded}
                  onToggle={expanded => {
                    onToggleList(item.caseId);
                  }}>
                  <CollapseHeader>
                    <View style={styles.collapsingHeader}>
                      {item.isExpanded ? (
                        <View style={styles.collapsingHeaderExpanded}>
                          <View>
                            <Text>{"#" + item.caseId}</Text>
                          </View>
                          <View style={styles.statusBtnContainer}>
                            <View>
                              {item.isExpanded && (
                                <View
                                  style={[
                                    styles.statusBtnView,
                                    {
                                      backgroundColor:
                                        item.caseStatus === "ACTIVE"
                                          ? GlobalStyles.colors.eActiveButton
                                              .base
                                          : GlobalStyles.colors.eCompleteButton
                                              .base,
                                      borderColor:
                                        item.caseStatus === "ACTIVE"
                                          ? GlobalStyles.colors.eActiveButton
                                              .base
                                          : GlobalStyles.colors.eCompleteButton
                                              .base,
                                    },
                                  ]}>
                                  <Text
                                    style={[
                                      styles.statusTitle,
                                      {
                                        color:
                                          item.caseStatus === "ACTIVE"
                                            ? GlobalStyles.colors.eTertiary.base
                                            : GlobalStyles.colors.eSecondary
                                                .base,
                                      },
                                    ]}>
                                    {statusTitle}
                                  </Text>
                                </View>
                              )}
                            </View>
                            <View>
                              <FontAwesome
                                name="chevron-up"
                                color={"#808080"}
                                size={13}
                              />
                            </View>
                          </View>
                        </View>
                      ) : (
                        <View style={styles.unExpandedContainer}>
                          <View style={styles.unExpandedIconContiner}>
                            <View>
                              <Icon
                                name={
                                  item.caseStatus !== "ACTIVE"
                                    ? "Sucess-icon"
                                    : "Hourglass-icon-new"
                                }
                                style={[
                                  item.caseStatus !== "ACTIVE"
                                    ? styles.greenBg
                                    : styles.orangeBg,
                                ]}
                                color={
                                  item.caseStatus !== "ACTIVE"
                                    ? GlobalStyles.colors.eSecondary.base
                                    : GlobalStyles.colors.eTertiary.base
                                }
                                size={18}
                              />
                            </View>
                            <View>
                              <Text
                                style={{
                                  fontSize: 13,
                                }}>
                                {"#" + item.caseId}
                              </Text>
                            </View>
                          </View>
                          <View>
                            <FontAwesome
                              name="chevron-down"
                              color={"#808080"}
                              size={13}
                              fontFamily="NotoSans-Light"
                            />
                          </View>
                        </View>
                      )}
                    </View>
                  </CollapseHeader>
                  <CollapseBody>
                    <View style={styles.itemStyle}>
                      <View style={styles.rowSpace}>
                        <Text style={styles.labelStyle}>Ticket Details:</Text>
                        <Text style={styles.caseId}>{"#" + item.caseId}</Text>
                      </View>
                      <View style={styles.rowSpace}>
                        <Text style={styles.labelStyle}>Created On:</Text>
                        <Text style={styles.valueStyle}>
                          {" "}
                          {moment(
                            item.createdDate,
                            "yyyy-MM-DD-hh.mm.ss",
                          ).format("MMM DD, yyyy hh:mm:ss a")}
                        </Text>
                      </View>
                      <View style={styles.rowSpace}>
                        <Text style={styles.labelStyle}>Account ID:</Text>
                        <Text style={styles.valueStyle}> {accountId}</Text>
                      </View>
                      <View style={styles.rowSpace}>
                        <Text style={styles.labelStyle}>Type:</Text>
                        <Text style={styles.ticketTypeDesc}>
                          {item?.ticketTypeDesc}
                        </Text>
                      </View>
                      {item.ticketAttachments !== null &&
                      item.ticketAttachments?.fileName == undefined
                        ? item.ticketAttachments.map((itemIcon, k) => (
                            <View style={styles.rowSpace} key={k}>
                              <Text style={styles.labelStyle}>
                                {k === 1 && "Attachments:"}
                              </Text>
                              <View style={styles.attachStyle}>
                                <Icon
                                  name="Attachment-icon"
                                  size={15}
                                  color={GlobalStyles.colors.eWhite.base}
                                />
                                <Text style={styles.fileName}>
                                  {itemIcon?.fileName}
                                </Text>
                              </View>
                            </View>
                          ))
                        : item.ticketAttachments !== null && (
                            <View style={styles.rowSpace} key={k}>
                              <Text style={styles.labelStyle}>
                                Attachments:
                              </Text>
                              <View style={styles.attachStyle}>
                                <Icon
                                  name="Attachment-icon"
                                  size={15}
                                  color={GlobalStyles.colors.eWhite.base}
                                  style={{ padding: "2%" }}
                                />

                                <Text style={styles.attachmentFileName}>
                                  {item?.ticketAttachments?.fileName}
                                </Text>
                              </View>
                            </View>
                          )}
                      {item.ticketCategory === "Service Request" ? (
                        <View style={styles.rowSpace}>
                          <View style={styles.labelStyle}></View>
                          <FlatButton
                            onPress={() => viewDetails(item)}
                            textStyles={styles.viewdetails}>
                            View Details
                          </FlatButton>
                        </View>
                      ) : null}
                    </View>
                  </CollapseBody>
                </Collapse>
              );
            })}
          </View>
        )}
      </ScrollView>
    </Card>
  );
}

const styles = StyleSheet.create({
  itemStyle: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    borderBottomLeftRadius: 6,
    borderBottomRightRadius: 6,
    boxShadow: "rgb(0 0 0 / 4%) 0px 11px 13px 0px",
    borderTopWidth: 1,
    borderTopColor: GlobalStyles.colors.ePastelColor2.selected,
    marginTop: -3,
    marginHorizontal: 10,
    paddingHorizontal: 8,
    paddingVertical: 10,
  },
  rowSpace: {
    flexDirection: "row",
    width: "100%",
    // marginBottom: "2%",
  },
  labelStyle: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
    width: "40%",
    // wordBreak: "break-word",
    whiteSpace: "break-spaces",
  },
  caseId: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    textAlign: "left",
    // wordBreak: "break-word",
    whiteSpace: "break-spaces",
    width: "60%",
  },
  valueStyle: {
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
    textAlign: "left",
    width: "60%",
    color: GlobalStyles.colors.eRich.base,
  },
  attachStyle: {
    backgroundColor: GlobalStyles.colors.ePastelColor2.selected,
    color: GlobalStyles.colors.eWhite.base,
    flexDirection: "row",
    borderRadius: 6,
    width: "60%",
    justifyContent: "space-between",
    padding: "1%",
    marginTop: 5,
  },
  viewdetails: {
    color: GlobalStyles.colors.ePrimary.base,
    textDecorationLine: "underline",
    fontSize: 12,
    width: "100%",
  },
  activeBtn: {
    borderColor: GlobalStyles.colors.eActiveButton.base,
    backgroundColor: GlobalStyles.colors.eActiveButton.base,
  },
  completeBtn: {
    borderColor: GlobalStyles.colors.eCompleteButton.base,
    backgroundColor: GlobalStyles.colors.eCompleteButton.base,
  },
  textActiveButton: {
    color: GlobalStyles.colors.eTertiary.base,
    fontSize: 10,
  },
  textCompleteBtn: {
    color: GlobalStyles.colors.eSecondary.base,
    fontSize: 10,
  },
  card: {
    paddingVertical: 10,
    height: "100%",
    borderWidth: 5,
    borderBottomWidth: 0,
    borderRadius: 0,
    borderColor: GlobalStyles.colors.ePrimary.base,
    backgroundColor: GlobalStyles.colors.eFaint.base,
  },
  activityLoaderContainer: {
    height: height,
    justifyContent: "center",
    alignItems: "center",
  },
  collapsingHeader: {
    borderRadius: 5,
    marginVertical: 5,
    marginHorizontal: 8,
    paddingVertical: 10,
    paddingHorizontal: 10,
    color: GlobalStyles.colors.eRich.base,
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
    backgroundColor: GlobalStyles.colors.eWhite.base,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    fontFamily: "NotoSans-SemiBold",
  },
  collapsingHeaderExpanded: {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    display: "flex",
    justifyContent: "space-between",
  },
  statusBtnContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
  },
  unExpandedContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  unExpandedIconContiner: {
    display: "flex",
    flexDirection: "row",
    gap: 8,
    alignItems: "center",
  },
  statusBtnView: {
    borderWidth: 1,
    borderRadius: 5,
    paddingHorizontal: 8,
    paddingBottom: 6,
    paddingTop: 3,
    display: "flex",
    alignItems: "center",
  },
  statusTitle: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
  },
  ticketTypeDesc: {
    maxWidth: 130,
    overflow: "hidden",
    fontSize: 11,
    fontFamily: "NotoSans-SemiBold",
    textAlign: "left",
    color: GlobalStyles.colors.eRich.base,
  },
  attachmentFileName: {
    color: GlobalStyles.colors.eWhite.base,
    width: "80%",
  },
  fileName: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 13,
  },
});

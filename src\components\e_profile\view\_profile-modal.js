import React, { useRef } from "react";
import { StyleSheet, View, Image, ActivityIndicator } from "react-native";
import { GlobalStyles } from "../../app/global-styles";
import { Text, IconButton } from "react-native-paper";
import { useContext } from "react";
import { profileContext } from "../e_profile";
import Button from "../../common/_button";
import OTPTextInput from "react-native-otp-textinput";
import FlatButton from "../../common/_flat_button";
import { UpdateMobileDetails } from "../model/_profile-service";
import { useState } from "react";
import { CountdownCircleTimer } from "react-native-countdown-circle-timer";
import { useSelector } from "react-redux";

export default function ProfileModal({ showChangepasswordPopup }) {
  const {
    setProfilePopup,
    setOpen,
    mobileNumber,
    showProfileConfirmPopup,
    setProfileConfirmPopup,
    setMobileNumber,
  } = useContext(profileContext);
  const [OTPError, setOTPError] = useState(false);
  const [OTPErrorMsg, setOTPErrorMsg] = useState();
  const [OTP, setOTP] = useState();
  const [loading, setLoading] = useState();
  let otpInput = useRef(null);
  const [isLoading, setALoading] = useState(false);
  const [showTimer, setShowTimer] = useState(true);
  const [isPlaying, setIsPlaying] = useState(true);
  const [error, setError] = useState(false);
  const [errorMSG, setErrorMSG] = useState(false);
  const [countdownTime, setCountDownTime] = useState(30);
  const [key, setKey] = useState(0);
  const [disableResend, setDisableResend] = useState(true);
  const [reachedMax, setReachedMax] = useState(false);
  const [disableButton, setDisableButton] = useState(true);
  const closeMenu = () => {
    setProfilePopup(false);
    setMobilePhoneNumber();
  };

  const personContactDetail = useSelector(
    state =>
      state?.accountDetails?.accountDetails?.accountSummary?.personDetailList?.[
        "C1-Person"
      ]?.personContactDetail,
  );

  const setMobilePhoneNumber = () => {
    let number = null;
    personContactDetail.map(item => {
      if (item.personContactType === "CELLPHONE") {
        number = item.contactDetailValue.replace(/\D/g, "");
      }
    });
    setMobileNumber(number);
  };

  const okClick = e => {
    // setProfilePopup(false);
    // setProfileConfirmPopup(true);
    let otp = "";
    otpInput.state.otpText.forEach(element => {
      otp += element;
    });
    setOTP(otp);
    if (otp.length !== 0) {
      setALoading(true);
      UpdateMobileDetails.OTPSubmitDetails(otp)
        .then(res => {
          if (res?.data) {
            setProfilePopup(false);
            setProfileConfirmPopup(true);
          } else {
            setOTPError(true);
            setOTPErrorMsg(
              "Something went wrong.Please try again after sometime.",
            );
            setMobilePhoneNumber();
          }
          setALoading(false);
        })
        .catch(error => {
          setOTPError(true);
          setOTPErrorMsg(error?.response?.data?.message);
          setALoading(false);
          setMobilePhoneNumber();
        });
    }
  };
  const resend = () => {
    setShowTimer(true);
    setIsPlaying(true);
    setDisableResend(true);
    setError(false);
    // let otp = "";
    // otpInput.state.otpText.forEach(element => {
    //   otp += element;
    // });
    // setOTP(otp);

    // if (otp.length !== 0) {
    UpdateMobileDetails.ResendOTPDetails(mobileNumber)
      .then(res => {
        if (res.data) {
          //setProfilePopup(false);
          setLoading(false);
          otpInput = null;
        }
      })
      .catch(error => {
        if (error.response.status === 500) {
          setError(true);
          setErrorMSG(error.response.data.message);
          setShowResend(false);
        }
        if (error.response.status === 400) {
          setError(true);
          setErrorMSG(error.response.data.message);
        }
        if (error.response.status === 403) {
          setError(true);
          setReachedMax(true);
          setErrorMSG("You have reached the maximum number of OTP attempts");
          setDisableResend(true);
          setIsPlaying(false);
          setShowTimer(false);
        }
        setLoading(false);
      });
    //}
  };
  const countDown = totalElapsedTime => {
    if (!reachedMax) {
      setKey(prevKey => prevKey + 1);
      setIsPlaying(false);
      setDisableResend(false);
      setShowTimer(false);
    } else {
      setIsPlaying(false);
      setShowTimer(false);
    }
  };
  const handleTextChange = input => {
    if (input.length === 6) {
      setDisableButton(false);
    } else {
      setDisableButton(true);
    }
  };
  return (
    <>
      <View style={styles.container}>
        <View style={styles.content}>
          <Text style={styles.title}>Verify Number</Text>
          <IconButton
            icon="close"
            color={GlobalStyles.colors.eBlack.base}
            onPress={closeMenu}
            style={styles.closeIcon}
            size={15}
          />
        </View>
        <View style={styles.horizontalLineFirst} />
        <View style={styles.contentTicket}>
          <Text style={styles.subtitleHeader}>Enter the 6 digit code</Text>
          <Text style={styles.subtitleHeader}>
            we just sent to your mobile number.
          </Text>
        </View>

        <View>
          <OTPTextInput
            returnKeyType="done"
            inputCount={6}
            ref={e => (otpInput = e)}
            handleTextChange={input => handleTextChange(input)}
          />
        </View>

        {OTPError ? <Text style={styles.errortext}>{OTPErrorMsg}</Text> : null}
        <View style={styles.contentFlex}>
          <Text style={styles.subtitleHeader}>
            Didn't receive the code?{" "}
            {loading ? (
              <ActivityIndicator
                size="small"
                color={GlobalStyles.colors.ePrimary.base}
              />
            ) : null}
          </Text>
          <FlatButton
            textStyles={disableResend ? styles.disableLike : styles.linkWhite}
            onPress={resend}
            disable={disableResend}>
            Resend
          </FlatButton>
        </View>
        {showTimer ? (
          <>
            <View style={styles.contentSub}>
              <Text style={styles.subText}>Time Remaining</Text>
            </View>
            <View
              style={[
                styles.contentSubText,
                !showTimer ? styles.contentSubText2 : null,
              ]}>
              <CountdownCircleTimer
                isPlaying={isPlaying}
                duration={countdownTime}
                colors={["#004777", "#A30000", "#A30000", "#F7B801"]}
                key={key}
                colorsTime={[7, 5, 2, 0]}
                size={50}
                strokeWidth={5}
                onComplete={totalElapsedTime => countDown(totalElapsedTime)}
                //      onComplete={() =>{
                //       setKey(prevKey => prevKey +1)
                //       setIsPlaying(false)
                //  }}
              >
                {({ remainingTime }) => (
                  <>
                    <Text
                      color={GlobalStyles.colors.eSecondary.base}
                      style={styles.countdown}>
                      00:{remainingTime}
                    </Text>
                  </>
                )}
              </CountdownCircleTimer>
            </View>
          </>
        ) : (
          <View style={{ height: 94 }}></View>
        )}
        {error ? (
          <>
            <View style={styles.contentError}>
              <Text style={styles.error}>{errorMSG}</Text>
            </View>
          </>
        ) : null}
        <View style={styles.lineStyleBottom}></View>
        <View style={styles.btnContainer}>
          <Button
            onPress={closeMenu}
            buttonbgColor={styles.cancelBg}
            textColor={styles.cancelText}>
            Cancel
          </Button>
          <Button
            onPress={okClick}
            buttonbgColor={disableButton ? styles.diableCls : styles.bgColor}
            textColor={styles.textColor}>
            Submit
            {isLoading && (
              <ActivityIndicator
                align="center"
                size={13}
                color={GlobalStyles.colors.eWhite.base}
              />
            )}
          </Button>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  bgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 5,
    marginLeft: 10,
  },
  cancelText: {
    color: GlobalStyles.colors.eSecondary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    paddingHorizontal: 20,
    paddingVertical: 5,
    marginRight: 10,
  },
  btnContainer: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 10,
    marginBottom: 10,
  },
  container: {
    flext: 1,
    height: "100%",
    position: "relative",
  },
  content: {
    paddingTop: 30,
    paddingBottom: 13,
  },
  contentLast: {
    marginBottom: 10,
  },
  contentFlex: {
    marginBottom: 10,
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 10,
  },
  contentTicket: {
    paddingTop: 15,
    paddingBottom: 10,
    paddingLeft: 20,
    paddingRight: 20,
  },
  title: {
    textAlign: "center",
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 16,
    fontFamily: "NotoSans-Bold",
    fontWeight: 600,
  },
  subtitle: {
    textAlign: "center",
    color: GlobalStyles.colors.eRich.base,
    fontSize: 15,
    fontFamily: "NotoSans-SemiBold",
  },
  subtitleHeader: {
    textAlign: "center",
    color: GlobalStyles.colors.eRich.base,
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
  },
  errortext: {
    textAlign: "center",
    color: GlobalStyles.colors.eDanger.dark,
    fontSize: 14,
  },
  ticketReference: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 16,
    fontFamily: "NotoSans-Bold",
    padding: 10,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: GlobalStyles.colors.ePrimary.base,
    marginTop: 10,
  },
  subText: {
    textAlign: "center",
    color: GlobalStyles.colors.eBlack.base,
    fontSize: 11,
  },
  subText2: {
    textAlign: "center",
    color: GlobalStyles.colors.eBlack.base,
    fontSize: 11,
  },
  subLink: {
    textAlign: "center",
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 11,
    paddingTop: 10,
    paddingBottom: 10,
  },
  closeIcon: {
    position: "absolute",
    top: 6,
    right: 13,
    backgroundColor: GlobalStyles.colors.eFaint.selected,
  },
  imgCls: {
    height: 60,
    width: 60,
    padding: 5,
  },
  imgViewCls: {
    justifyContent: "center",
    alignItems: "center",
  },
  centerButtons: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 15,
  },
  leftButton: {
    marginRight: 20,
  },
  whiteBgColor: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingVertical: 5,
    marginRight: 10,
  },
  greenText: {
    color: GlobalStyles.colors.eSecondary.base,
    fontSize: 12,
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 5,
    marginLeft: 10,
  },
  whiteText: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
  },
  linkWhite: {
    color: GlobalStyles.colors.eTertiary.base,
    fontSize: 12,
    textDecorationLine: "underline",
    marginTop: 0,
    marginLeft: 0,
    fontFamily: "NotoSans-SemiBold",
  },
  disableLike: {
    color: GlobalStyles.colors.eMedium.hover.base,
    fontSize: 12,
    textDecorationLine: "underline",
    marginTop: 0,
    marginLeft: 0,
    fontFamily: "NotoSans-SemiBold",
  },
  diableCls: {
    backgroundColor: GlobalStyles.colors.eLight.selected,
    color: GlobalStyles.colors.eWhite.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 5,
    marginLeft: 10,
  },
  lineStyleBottom: {
    borderWidth: 0.5,
    borderColor: "#DEE2E4",
    marginLeft: -3,
    marginRight: -3,
  },
  horizontalLineFirst: {
    borderBottomWidth: 0.6,
    width: "100%",
    borderColor: GlobalStyles.colors.eBackground3.base,
  },
  contentSubText: {
    marginTop: 10,
    marginBottom: 15,
    flexDirection: "row",
    justifyContent: "center",
  },
  contentSubText2: {
    marginVertical: 33,
    flexDirection: "row",
    justifyContent: "center",
  },
  contentSub: {
    marginVertical: 2,
    flexDirection: "row",
    justifyContent: "center",
  },
});

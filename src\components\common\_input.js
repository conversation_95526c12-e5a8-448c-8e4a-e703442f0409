import { View, StyleSheet } from "react-native";
import { GlobalStyles } from "../app/global-styles";
import { TextInput, Text } from "react-native-paper";
import React, { useEffect } from "react";
//import Icon from "../icon";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";
// import Icon from "react-native-vector-icons/FontAwesome";

function Input({
  label,
  keyboardType,
  secure,
  onUpdateValue,
  value,
  isInvalid,
  onBlur,
  errorMsg,
  name,
  placeholder,
  mode,
  type,
  maxLength,
}) {
  const [passowrdSecurity, setPassowrdSecurityNew] = React.useState(false);

  const secureTextEntryNew = () => {
    setPassowrdSecurityNew(passowrdSecurity ? false : true);
  };
  return (
    <View style={styles.inputContainer}>
      <Text style={styles.label}>{label}</Text>
      <TextInput
        theme={{
          colors: {
            text: GlobalStyles.colors.eDark.base,
            primary: GlobalStyles.colors.eDark.base,
            accent: GlobalStyles.colors.eDark.base,
          },
        }}
        type={type}
        autoCapitalize={"none"}
        style={[styles.input, isInvalid && styles.inputInvalid]}
        keyboardType={keyboardType}
        secureTextEntry={secure && !passowrdSecurity}
        onChangeText={onUpdateValue}
        value={value}
        onBlur={onBlur}
        returnKeyLabel="done"
        returnKeyType="done"
        // activeUnderlineColor={'none'}
        maxLength={maxLength}
        placeholder={placeholder}
        mode="outlined"
        outlineColor={GlobalStyles.colors.eBackground.base}
        activeOutlineColor={GlobalStyles.colors.ePrimary.hover}
        selectionColor={GlobalStyles.colors.eDark.base}
        dense
        left={
          name !== undefined ? (
            <TextInput.Icon
              icon={() => (
                <Icon
                  name={name}
                  color={GlobalStyles.colors.ePrimary.base}
                  size={20}
                />
              )}
              color={GlobalStyles.colors.ePrimary.base}
              style={styles.leftIconStyle}
            />
          ) : (
            ""
          )
        }
        right={
          secure ? (
            !passowrdSecurity ? (
              <TextInput.Icon
                icon={() => (
                  <Icon
                    name="eye-off"
                    size={20}
                    color={GlobalStyles.colors.ePrimary.base}
                  />
                )}
                onPress={secureTextEntryNew}
                style={styles.iconStyle}
                color={GlobalStyles.colors.ePrimary.base}
              />
            ) : (
              <TextInput.Icon
                style={styles.iconStyle}
                icon={() => (
                  <Icon
                    name="eye"
                    size={20}
                    color={GlobalStyles.colors.ePrimary.base}
                  />
                )}
                onPress={secureTextEntryNew}
                color={GlobalStyles.colors.ePrimary.base}
              />
            )
          ) : null
        }
      />
      {isInvalid ? (
        <Text style={styles.labelInvalid}>{errorMsg()}</Text>
      ) : (
        <View style={styles.labelInvalid} />
      )}
    </View>
  );
}

export default Input;

const styles = StyleSheet.create({
  label: {
    color: GlobalStyles.colors.eRich.base,
    marginBottom: 4,
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
  },
  labelInvalid: {
    color: GlobalStyles.colors.eWhite.base,
    minHeight: 30,
    fontSize: 12,
  },
  input: {
    paddingTop: 2,
    paddingBottom: 5,
    backgroundColor: GlobalStyles.colors.eBackground.base,
    fontSize: 14,
    color: GlobalStyles.colors.eDark.base,
    paddingLeft: 0,
  },
  iconStyle: {
    marginRight: 0, // Add this line to remove right margin
    marginLeft: 0,
  },
  inputInvalid: {
    borderBottomColor: GlobalStyles.colors.eWhite.base,
  },
  leftIconStyle: {
    marginRight: 0, // Add this line to remove right margin
    marginLeft: 0,
  },
});

import React, { useRef, useState, useEffect } from "react";
import { StyleSheet, View, Image, ActivityIndicator } from "react-native";
import { GlobalStyles } from "../../app/global-styles";
import { Text, IconButton } from "react-native-paper";
import { useContext } from "react";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import TextLink from "react-native-text-link";
import Button from "../../common/_button";
import OTPTextInput from "react-native-otp-textinput";
import FlatButton from "../../common/_flat_button";
import { registerService } from "../model/_register-service";
import { CountdownCircleTimer } from "react-native-countdown-circle-timer";
import { WorkOrderService } from "../model/work-order-service";
import { useTranslation } from "react-i18next";
import { config } from "../../../environment";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { WorkOrderServiceWO } from "../model/work-order-service_wo";
import { stackContext } from "../../app/get_stack";

export default function OTPModalScreen({ showPopup }) {
  const { t } = useTranslation();
  const { workModelType } = React.useContext(stackContext);
  const context = workModelType === "WA" ? drawerContext : drawerContextWO;

  const {
    setSingleWorkOrder,
    allActivities,
    setAllActivities,
    singleWorkOrder,
    setWorkOrder,
    consumerIndex,
    setConsumerIndex,
    confirmationModal,
    setConfirmationModal,
    setOTPModal,
    workOrder,
    customerNumber,
    OTPVerification,
    setOTPVerification,
    updateWAObj,
    setAllWOList,
  } = useContext(context);

  const [OTPError, setOTPError] = useState(false);
  const [OTPErrorMsg, setOTPErrorMsg] = useState();
  const [OTP, setOTP] = useState();
  const [loading, setLoading] = useState();
  const [isLoading, setisLoading] = useState(false);
  const [timerCount, setTimer] = useState();
  const [timerNeed, setTimerNeed] = useState(false);
  let otpInput = useRef(null);
  const [showTimer, setShowTimer] = useState(true);
  const [disableButton, setDisableButton] = useState(true);
  const [disableResend, setDisableResend] = useState(true);
  const [reachedMax, setReachedMax] = useState(false);
  const [isPlaying, setIsPlaying] = useState(true);
  const [countdownTime, setCountDownTime] = useState(30);
  const [key, setKey] = useState(0);

  useEffect(() => {
    let interval = setInterval(() => {
      setTimer(lastTimerCount => {
        lastTimerCount <= 1 && clearInterval(interval);
        return lastTimerCount - 1;
      });
    }, 60000);
    return () => clearInterval();
  }, []);

  useEffect(() => {
    if (timerCount === 0) {
    }
  }, timerCount);

  useEffect(() => {}, [customerNumber]);

  const closeMenu = () => {
    setConfirmationModal(false);
    setOTPModal(false);
  };

  const countDown = totalElapsedTime => {
    if (!reachedMax) {
      setKey(prevKey => prevKey + 1);
      setIsPlaying(false);
      if (timerNeed) {
        setDisableResend(true);
      } else {
        setDisableResend(false);
      }
      setShowTimer(false);
    } else {
      setIsPlaying(false);
      setShowTimer(false);
    }
  };

  const okClick = e => {
    setisLoading(true);
    // setConfirmationModal(true);
    // setOTPModal(false);
    let currOtp = "";
    otpInput.state.otpText.forEach(element => {
      currOtp += element;
    });

    setOTP(currOtp);
    if (currOtp.length !== 0) {
      WorkOrderService.OTPSubmit(currOtp, customerNumber)
        .then(res => {
          setisLoading(false);

          if (res.status === 200) {
            //updateWA();
            setOTPVerification(true);
            setConfirmationModal(true);
            setOTPModal(false);
            fetchWorkOrderList();
          }
        })
        .catch(error => {
          setisLoading(false);
          setOTPVerification(true);
          setConfirmationModal(true);
          setOTPModal(false);
          fetchWorkOrderList();
        });
    }
  };

  const updateWA = () => {
    WorkOrderService.UpdateWA(updateWAObj)
      .then(res => {
        if (res.status === 201) {
          setLoading(false);
          updateWOStatus();
        }
        if (res.status === 400) {
          setLoading(false);
          setOTPError(res.message);
        }
      })
      .catch(error => {
        console.log(error.response.data.message);
      });
  };

  const updateWOStatus = () => {
    const status = updateWAObj.workActivities.every(activity =>
      activity.WorkOrderActivityChecklist.every(
        checklist => checklist.answer === "yes",
      ),
    )
      ? "CO"
      : "I";
    const workOrderID = updateWAObj.workActivities[0].WorkOrderId;

    const updatedBy = 2;
    WorkOrderService.UpdateWAStatus(status, workOrderID, updatedBy)
      .then(res => {
        if (res === 201) {
          setLoading(false);
          setOTPVerification(true);
          setConfirmationModal(true);
          setOTPModal(false);
          fetchWorkOrderList();
        }
        if (res === 400) {
          setLoading(false);
          setOTPError(res.message);
        }
      })
      .catch(error => {
        console.log(error.response.data.message);
      });
  };
  const fetchWorkOrderList = async () => {
    try {
      setLoading(true);

      let res;

      if (workModelType == "WA") {
        res = await WorkOrderService.getAllWorkOrderList();
        res.workActivities = res?.workActivities?.filter(
          each => each["WamRefNum"] != undefined,
        );
        res.workActivities?.sort((a, b) => {
          // Sort by WorkActivityId in descending order
          return b.WorkActivityId - a.WorkActivityId;
        });
      } else {
        res = await WorkOrderServiceWO.getAllWorkOrderList();
        res.workOrders = res?.workOrders?.filter(
          each => each["WorkOrderType"] != null,
        );
        res?.workOrders.sort((a, b) => {
          return moment(b.PlannedStartDate).diff(moment(a.PlannedStartDate));
        });
      }

      setAllWOList(res);
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
      setLoading(false);
    }
  };
  const resend = () => {
    setShowTimer(true);
    setIsPlaying(true);
    setDisableResend(true);
    //setError(false);
    // registerService
    //   .ResendOTPDetails(email)
    //   .then((res) => {
    //     console.log("ressssss", res);
    //     if (res.data) {
    //       // setProfilePopup(false);
    //       setLoading(false);
    //       otpInput = null;
    //     }
    //   })
    //   .catch((error) => {
    //     console.log(error.response.data.message);
    //     setOTPError(true);
    //     setOTPErrorMsg(error.response.data.message);
    //     setLoading(false);
    //   });
    // }
  };

  return (
    <>
      <View style={styles.container}>
        <View style={styles.content}>
          <Text style={styles.title}>{t("CONFIRMATION")}</Text>
          <IconButton
            icon="close"
            color={GlobalStyles.colors.eBlack.base}
            onPress={closeMenu}
            style={styles.closeIcon}
            size={20}
          />
        </View>
        <View style={styles.lineStyle} />
        <View style={styles.contentTicket}>
          <Text style={styles.subtitle}>{t("TASK_OTP_MSG")}</Text>
        </View>

        <View style={styles.otpclass}>
          <OTPTextInput inputCount={4} ref={e => (otpInput = e)} />
        </View>

        {OTPError ? <Text style={styles.errortext}>{OTPErrorMsg}</Text> : null}
        {timerCount !== 0 ? (
          <View style={styles.contentFlex}>
            <Text style={styles.subtitle}>
              {t("OTP_FAIL")}{" "}
              {loading ? (
                <ActivityIndicator
                  size="small"
                  color={GlobalStyles.colors.ePrimary.base}
                />
              ) : null}
            </Text>
            <FlatButton
              textStyles={disableResend ? styles.disableLike : styles.linkWhite}
              onPress={resend}
              disable={disableResend}>
              {t("RESEND_OTP")}
            </FlatButton>
          </View>
        ) : null}
        {showTimer ? (
          <>
            <View style={styles.contentSub}>
              <Text style={styles.subText}>{t("TIME_REMAINING")}</Text>
            </View>
            <View
              style={[
                styles.contentSubText,
                !showTimer ? styles.contentSubText2 : null,
              ]}>
              <CountdownCircleTimer
                isPlaying={isPlaying}
                duration={countdownTime}
                colors={["#004777", "#A30000", "#A30000", "#F7B801"]}
                key={key}
                colorsTime={[7, 5, 2, 0]}
                size={50}
                strokeWidth={5}
                onComplete={totalElapsedTime => countDown(totalElapsedTime)}
                //      onComplete={() =>{
                //       setKey(prevKey => prevKey +1)
                //       setIsPlaying(false)
                //  }}
              >
                {({ remainingTime }) => (
                  <>
                    <Text
                      color={GlobalStyles.colors.eSecondary.base}
                      style={styles.countdown}>
                      00:{remainingTime}
                    </Text>
                  </>
                )}
              </CountdownCircleTimer>
            </View>
          </>
        ) : null}

        <View style={styles.lineStyleBottom}></View>
        <View style={styles.btnContainer}>
          <Button
            onPress={closeMenu}
            buttonbgColor={styles.cancelBg}
            textColor={styles.cancelText}>
            {t("CANCEL")}
          </Button>
          <Button
            onPress={okClick}
            buttonbgColor={styles.bgColor}
            textColor={styles.textColor}>
            {t("SUBMIT")}{" "}
            {isLoading && (
              <ActivityIndicator
                align="center"
                size="small"
                color={GlobalStyles.colors.eWhite.base}
              />
            )}
          </Button>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  bgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    marginLeft: 10,
  },
  lineStyle: {
    borderWidth: 0.5,
    borderColor: GlobalStyles.colors.ePastelColor2.hover,
    width: "150%",
  },
  cancelText: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.ePrimary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingVertical: 5,
    marginRight: 10,
  },
  btnContainer: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 15,
    marginBottom: 20,
  },
  container: {
    flext: 1,
    height: "100%",
    position: "relative",
  },
  content: {
    padding: 20,
  },
  contentLast: {
    marginBottom: 10,
  },
  contentFlex: {
    marginBottom: 30,
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 30,
  },
  contentTicket: {
    paddingTop: 15,
    paddingBottom: 10,
    paddingLeft: 20,
    paddingRight: 20,
  },
  title: {
    textAlign: "center",
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 16,
    fontFamily: "NotoSans-Bold",
  },
  subtitle: {
    textAlign: "center",
    color: GlobalStyles.colors.eRich.base,
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
  },
  errortext: {
    textAlign: "center",
    color: GlobalStyles.colors.eDanger.dark,
    fontSize: 14,
  },
  ticketReference: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 16,
    fontFamily: "NotoSans-Bold",
    padding: 10,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: GlobalStyles.colors.ePrimary.base,
    marginTop: 10,
  },
  subText: {
    textAlign: "center",
    color: GlobalStyles.colors.eBlack.base,
    fontSize: 11,
  },
  subText2: {
    textAlign: "center",
    color: GlobalStyles.colors.eBlack.base,
    fontSize: 11,
  },
  subLink: {
    textAlign: "center",
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 11,
    paddingTop: 10,
    paddingBottom: 10,
  },
  closeIcon: {
    position: "absolute",
    top: 6,
    right: 13,
    backgroundColor: GlobalStyles.colors.eFaint.selected,
  },
  imgCls: {
    height: 60,
    width: 60,
    padding: 5,
  },
  imgViewCls: {
    justifyContent: "center",
    alignItems: "center",
  },
  centerButtons: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 15,
  },
  leftButton: {
    marginRight: 20,
  },
  whiteBgColor: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingVertical: 5,
    marginRight: 10,
  },
  greenText: {
    color: GlobalStyles.colors.eSecondary.base,
    fontSize: 12,
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 5,
    marginLeft: 10,
  },
  whiteText: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
  },
  linkWhite: {
    color: GlobalStyles.colors.eTertiary.base,
    fontSize: 12,
    textDecorationLine: "underline",
    marginTop: 4,
    marginLeft: 0,
  },
  disableLike: {
    //color: GlobalStyles.colors.eTertiary.base,
    fontSize: 12,
    textDecorationLine: "underline",
    marginTop: 4,
    marginLeft: 0,
  },
  lineStyleBottom: {
    borderWidth: 0.5,
    borderColor: "#DEE2E4",
    marginLeft: -3,
    marginBottom: 2,
    marginRight: -3,
  },
  otpclass: {
    marginHorizontal: 60,
  },
  contentSubText: {
    marginVertical: 10,
    flexDirection: "row",
    justifyContent: "center",
  },
  contentSubText2: {
    marginVertical: 0,
    flexDirection: "row",
    justifyContent: "center",
  },
  contentSub: {
    flexDirection: "row",
    justifyContent: "center",
  },
});

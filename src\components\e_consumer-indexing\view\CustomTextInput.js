import { StyleSheet } from "react-native";
import { TextInput } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import * as _ from "lodash";
import { useState } from "react";

const CustomTextInput = ({
  onChange,
  keyboardType,
  value,
  defaultValue,
  placeholder,
  maxLength,
  ...inputProps
}) => {
  return (
    <>
      <TextInput
        mode="outlined"
        keyboardType={keyboardType}
        placeholder={placeholder}
        activeOutlineColor={GlobalStyles.colors.ePrimary.base}
        outlineColor={GlobalStyles.colors.eRich.hover}
        style={[styles.sectionItemInputStyle]}
        value={value}
        defaultValue={defaultValue}
        onChangeText={text => {
          onChange(text);
        }}
        maxLength={maxLength}
        {...inputProps}
      />
    </>
  );
};

const styles = StyleSheet.create({
  sectionItemInputStyle: {
    backgroundColor: GlobalStyles.colors.eFaint.base,
    fontSize: 10,
    color: GlobalStyles.colors.eDark.selected,

    // height: 54,
  },
  errorText: {
    fontSize: 11,
    color: "red",
  },
  errorInput: {
    borderColor: "red",
    borderWidth: 1,
  },
});

export default CustomTextInput;

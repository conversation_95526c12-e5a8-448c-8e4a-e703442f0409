import React, { useRef } from "react";
import { StyleSheet, View, Image, ActivityIndicator } from "react-native";
import { GlobalStyles } from "../../app/global-styles";
import { Text, IconButton } from "react-native-paper";
import { useContext } from "react";
import { registerContext } from "../../e_authPages/e_auth_pages";
import TextLink from "react-native-text-link";
import Button from "../../common/_button";
import OTPTextInput from "react-native-otp-textinput";
import FlatButton from "../../common/_flat_button";
import { registerService } from "../model/_register-service";
import { useState } from "react";
import { config } from "../../../environment";
import { registerContextWO } from "../../e_authPages/e_auth_pages_wo";
import { stackContext } from "../../app/get_stack";

export default function RegisterModal({ showPopup }) {
  const { workModelType } = React.useContext(stackContext);
  let showRegisterModal,
    setRegisterModal,
    showRegisterSuccess,
    setRegisterSuccess,
    email;

  if (workModelType === "WA") {
    ({
      showRegisterModal,
      setRegisterModal,
      showRegisterSuccess,
      setRegisterSuccess,
      email,
    } = useContext(registerContext));
  } else {
    ({
      showRegisterModal,
      setRegisterModal,
      showRegisterSuccess,
      setRegisterSuccess,
      email,
    } = useContext(registerContextWO));
  }

  const [OTPError, setOTPError] = useState(false);
  const [OTPErrorMsg, setOTPErrorMsg] = useState();
  const [OTP, setOTP] = useState();
  const [loading, setLoading] = useState();
  const [isLoading, setisLoading] = useState(false);
  let otpInput = useRef(null);
  const closeMenu = () => {
    setRegisterSuccess(false);
    setRegisterModal(false);
  };

  const okClick = e => {
    setisLoading(true);
    // setRegisterModal(false);
    // setRegisterSuccess(true);
    let otp = "";
    otpInput.state.otpText.forEach(element => {
      otp += element;
    });

    setOTP(otp);
    if (otp.length !== 0) {
      let flowType = "SIGNUP";
      registerService
        .OTPSubmitDetails(otp, flowType, email)
        .then(res => {
          setisLoading(false);
          if (res.data) {
            setRegisterModal(false);
            setRegisterSuccess(true);
          }
        })
        .catch(error => {
          setisLoading(false);
          if (error.response.data.statusCode === 409) {
            setOTPError(true);
            setOTPErrorMsg(error.response.data.message);
          }
        });
    }
  };
  const resend = () => {
    registerService
      .ResendOTPDetails(email)
      .then(res => {
        if (res.data) {
          // setProfilePopup(false);
          setLoading(false);
          otpInput = null;
        }
      })
      .catch(error => {
        console.log(error.response.data.message);
        setOTPError(true);
        setOTPErrorMsg(error.response.data.message);
        setLoading(false);
      });
    // }
  };

  return (
    <>
      <View style={styles.container}>
        <View style={styles.content}>
          <Text style={styles.title}>Verify Account</Text>
          <IconButton
            icon="close"
            color={GlobalStyles.colors.eBlack.base}
            onPress={closeMenu}
            style={styles.closeIcon}
            size={20}
          />
        </View>

        <View style={styles.contentTicket}>
          <Text style={styles.subtitle}>Enter the 6 digit code</Text>
          <Text style={styles.subtitle}>
            we just sent to your mobile number.
          </Text>
        </View>

        <View>
          <OTPTextInput inputCount={6} ref={e => (otpInput = e)} />
        </View>

        {OTPError ? <Text style={styles.errortext}>{OTPErrorMsg}</Text> : null}
        <View style={styles.contentFlex}>
          <Text style={styles.subtitle}>
            Didn't receive the code?{" "}
            {loading ? (
              <ActivityIndicator
                size="small"
                color={GlobalStyles.colors.ePrimary.base}
              />
            ) : null}
          </Text>
          <FlatButton textStyles={styles.linkWhite} onPress={resend}>
            RESEND OTP
          </FlatButton>
        </View>
        <View style={styles.lineStyleBottom}></View>
        <View style={styles.btnContainer}>
          <Button
            onPress={closeMenu}
            buttonbgColor={styles.cancelBg}
            textColor={styles.cancelText}>
            Cancel
          </Button>
          <Button
            onPress={okClick}
            buttonbgColor={styles.bgColor}
            textColor={styles.textColor}>
            Submit{" "}
            {isLoading && (
              <ActivityIndicator
                align="center"
                size="small"
                color={GlobalStyles.colors.eWhite.base}
              />
            )}
          </Button>
        </View>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
  },
  bgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 5,
    marginLeft: 10,
  },
  cancelText: {
    color: GlobalStyles.colors.eSecondary.base,
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingVertical: 5,
    marginRight: 10,
  },
  btnContainer: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 15,
    marginBottom: 20,
  },
  container: {
    flext: 1,
    height: "100%",
    position: "relative",
  },
  content: {
    padding: 30,
  },
  contentLast: {
    marginBottom: 10,
  },
  contentFlex: {
    marginBottom: 30,
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 30,
  },
  contentTicket: {
    paddingTop: 15,
    paddingBottom: 10,
    paddingLeft: 20,
    paddingRight: 20,
  },
  title: {
    textAlign: "center",
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 20,
    fontFamily: "NotoSans-Bold",
  },
  subtitle: {
    textAlign: "center",
    color: GlobalStyles.colors.eRich.base,
    fontSize: 15,
    fontFamily: "NotoSans-SemiBold",
  },
  errortext: {
    textAlign: "center",
    color: GlobalStyles.colors.eDanger.dark,
    fontSize: 14,
  },
  ticketReference: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 16,
    fontFamily: "NotoSans-Bold",
    padding: 10,
    borderWidth: 1,
    borderRadius: 5,
    borderColor: GlobalStyles.colors.ePrimary.base,
    marginTop: 10,
  },
  subText: {
    textAlign: "center",
    color: GlobalStyles.colors.eBlack.base,
    fontSize: 11,
  },
  subText2: {
    textAlign: "center",
    color: GlobalStyles.colors.eBlack.base,
    fontSize: 11,
  },
  subLink: {
    textAlign: "center",
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 11,
    paddingTop: 10,
    paddingBottom: 10,
  },
  closeIcon: {
    position: "absolute",
    top: 6,
    right: 13,
    backgroundColor: GlobalStyles.colors.eFaint.selected,
  },
  imgCls: {
    height: 60,
    width: 60,
    padding: 5,
  },
  imgViewCls: {
    justifyContent: "center",
    alignItems: "center",
  },
  centerButtons: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 15,
  },
  leftButton: {
    marginRight: 20,
  },
  whiteBgColor: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingVertical: 5,
    marginRight: 10,
  },
  greenText: {
    color: GlobalStyles.colors.eSecondary.base,
    fontSize: 12,
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 5,
    marginLeft: 10,
  },
  whiteText: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
  },
  linkWhite: {
    color: GlobalStyles.colors.eTertiary.base,
    fontSize: 15,
    textDecorationLine: "underline",
    // marginTop: -4,
    marginLeft: 0,
  },
  lineStyleBottom: {
    borderWidth: 0.5,
    borderColor: "#DEE2E4",
    marginLeft: -3,
    marginBottom: 2,
    marginRight: -3,
  },
});

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>FieldWork</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>37</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationCategoryType</key>
	<string/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>tez</string>
		<string>phonepe</string>
		<string>paytmmp</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSExceptionDomains</key>
		<dict>
			<key>localhost</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string/>
	<key>UIAppFonts</key>
	<array>
		<string>NotoSans-Black.ttf</string>
		<string>NotoSans-BlackItalic.ttf</string>
		<string>NotoSans-Bold.ttf</string>
		<string>NotoSans-BoldItalic.ttf</string>
		<string>NotoSans-ExtraBold.ttf</string>
		<string>NotoSans-ExtraBoldItalic.ttf</string>
		<string>NotoSans-ExtraLight.ttf</string>
		<string>NotoSans-ExtraLightItalic.ttf</string>
		<string>NotoSans-Italic.ttf</string>
		<string>NotoSans-Light.ttf</string>
		<string>NotoSans-LightItalic.ttf</string>
		<string>NotoSans-Medium.ttf</string>
		<string>NotoSans-MediumItalic.ttf</string>
		<string>NotoSans-Regular.ttf</string>
		<string>NotoSans-SemiBold.ttf</string>
		<string>NotoSans-SemiBoldItalic.ttf</string>
		<string>NotoSans-Thin.ttf</string>
		<string>NotoSans-ThinItalic.ttf</string>
		<string>custom-icons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>Ionicons.ttf</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>

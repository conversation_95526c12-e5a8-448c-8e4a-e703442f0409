import AppMain from "./src/components/app";
import { store } from "./src/redux/store";
import { Provider as StoreProvider } from "react-redux";
import { GestureHandlerRootView } from "react-native-gesture-handler";
// import i18n from './src/i18nService';
// import { I18nextProvider } from 'react-i18next';
// import * as RNLocalize from 'react-native-localize';
// import translations from './src/translations';
import { I18nextProvider } from "react-i18next";
import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import "intl-pluralrules";
import nextI18NextConfig from "./next-i18next.config";
import enTranslation from "./assets/locales/en/common.json";
import maTranslation from "./assets/locales/ma/common.json";
import thTranslation from "./assets/locales/th/common.json";
import messaging from "@react-native-firebase/messaging";
import { firebase } from "@react-native-firebase/app";
import {
  configureFonts,
  MD3LightTheme as DefaultTheme,
  PaperProvider,
} from "react-native-paper";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useEffect, useState } from "react";
import { NotificationService } from "./src/components/app/authenticated/model/getNotification_service";
import { Alert } from "react-native";

export default function App() {
  const [selectedLn, setSelectedLn] = useState("en");
  const [token, setToken] = useState("");
  // const PROJECT_ID = "pushnotificationsapk";
  // const PATH = `/v1/projects/${PROJECT_ID}/messages:send`;
  // const MESSAGING_SCOPE = "https://www.googleapis.com/auth/firebase.messaging";
  // const SCOPES = [MESSAGING_SCOPE];
  async function requestUserPermission() {
    const authStatus = await messaging().requestPermission();
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (enabled) {
      console.log("Authorization status:", authStatus);
    }
  }

  // Function to fetch selected language from AsyncStorage
  const fetchSelectedLanguage = async () => {
    try {
      const language = await AsyncStorage.getItem("selectedLanguage");
      if (language && language !== selectedLn) {
        setSelectedLn(language); // Update state if the language changes
      }
    } catch (error) {
      console.error("Failed to load selected language:", error);
    }
  };

  useEffect(() => {
    requestUserPermission();
    getDeviceToken();
  }, []);

  useEffect(() => {
    const unsubscribe = messaging().onMessage(async remoteMessage => {
      console.log(remoteMessage, "remoteMessage-----------");
      console.log(
        remoteMessage.notification.title,
        remoteMessage.notification.body,
        "remoteMessage-----------",
      );
      Alert.alert(
        remoteMessage.notification.title,
        remoteMessage.notification.body,
        [{ text: "OK", onPress: () => console.log("OK Pressed") }],
      );
    });
    return unsubscribe;
  }, []);

  const getDeviceToken = async () => {
    const tokenStr = await messaging().getToken();
    setToken(tokenStr);
    console.log("token:", tokenStr);
  };
  //Set an interval to check for updates every second (1000ms)
  useEffect(() => {
    const intervalId = setInterval(() => {
      fetchSelectedLanguage();
    }, 1000); // Polling every 1 second

    return () => clearInterval(intervalId); // Cleanup on unmount
  }, [selectedLn]); // Dependency on selectedLn to ensure reactivity

  useEffect(() => {
    if (token) {
      NotificationService.saveToken(1, token)
        .then(resp => {})
        .catch(err => {
          console.error(err);
        });
    }
  }, [token]);

  const fontConfig = {
    default: {
      regular: {
        fontFamily: "NotoSans-Regular",
        fontSize: "normal",
      },
      medium: {
        fontFamily: "NotoSans-Bold",
        fontWeight: "normal",
      },
      light: {
        fontFamily: "NotoSans-Regular",
        fontWeight: "normal",
      },
      thin: {
        fontFamily: "NotoSans-Regular",
        fontWeight: "normal",
      },
    },
  };

  fontConfig.ios = fontConfig.default;
  fontConfig.android = fontConfig.default;
  fontConfig.web = fontConfig.default;

  const theme = {
    ...DefaultTheme,
    fonts: configureFonts(fontConfig),
    isV3: false,
  };

  const onLayoutRootView = () => {};

  // const getLanguage = () => {
  //   const locales = RNLocalize.getLocales();
  //   return locales[0].languageCode;
  // };

  // const language = getLanguage();
  // const { greeting } = translations[language] || translations.en;

  i18n
    .use(initReactI18next) // Bind i18n to React
    .init({
      lng: selectedLn ? selectedLn : "en",
      fallbackLng: nextI18NextConfig.i18n.defaultLocale,
      supportedLngs: nextI18NextConfig.i18n.locales,
      resources: {
        en: {
          translation: enTranslation, // Load English translations
        },
        ma: {
          translation: maTranslation, // Load Marathi translations
        },
        th: {
          translation: thTranslation, // Load Thai translations
        },
      },
      react: {
        useSuspense: false, // Disable suspense
      },
    });

  return (
    <StoreProvider store={store} onLayout={onLayoutRootView}>
      <PaperProvider theme={theme}>
        <I18nextProvider i18n={i18n}>
          <GestureHandlerRootView style={{ flex: 1 }}>
            <AppMain />
          </GestureHandlerRootView>
        </I18nextProvider>
      </PaperProvider>
    </StoreProvider>
  );
}

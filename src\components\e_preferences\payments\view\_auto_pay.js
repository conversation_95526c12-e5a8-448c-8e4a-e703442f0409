import React, { useContext } from "react";
import { View } from "react-native";
import { Text, StyleSheet } from "react-native";
import { Card } from "react-native-paper";
import { drawerContext } from "../../../app/authenticated/authenticated_layout";
import { GlobalStyles } from "../../../app/global-styles";
import Icon from "../../../icon";
import { config } from "../../../../environment";
import { drawerContextWO } from "../../../app/authenticated/authenticated_layout_wo";
import { stackContext } from "../../../app/get_stack";

export default function AutoPay({ setAutoData, autoData }) {
  const { workModelType } = React.useContext(stackContext);
  let setPopup,
    setTitle,
    setContent,
    setError,
    setButton,
    setIcon,
    setPopupCode;

  if (workModelType === "WA") {
    ({
      setPopup,
      setTitle,
      setContent,
      setError,
      setButton,
      setIcon,
      setPopupCode,
    } = useContext(drawerContext));
  } else {
    ({
      setPopup,
      setTitle,
      setContent,
      setError,
      setButton,
      setIcon,
      setPopupCode,
    } = useContext(drawerContextWO));
  }

  const addRecord = () => {
    //add record
    const editData =
      autoData && autoData.filter(item => item.editable === true);
    if (editData.length === 0) {
      if (autoData.length < 5) {
        const emptyRecord = {
          autopaySource: "",
          endDate: "",
          entityName: "",
          expireDate: "",
          externalAccountId: "",
          startDate: "",
          status: "",
          editable: true,
        };
        setAutoData(prev => [emptyRecord, ...prev]);
      } else {
        setPopup(true);
        setTitle("Add Auto Pay");
        setContent("Sorry!! we can only add 5 records.");
        setError("ERROR");
        setButton(false);
        setIcon("Exclamationmark-fill-icon");
        setPopupCode();
      }
    }
  };

  return (
    <Card style={styles.cardStyle}>
      <View
        style={{
          flexDirection: "row",
          flexWrap: "wrap",
          width: "100%",
          alignContent: "space-between",
        }}>
        <Text style={styles.blueTextTitle}>Auto Pay</Text>
        <Icon
          name="Add-icon"
          size={20}
          color={GlobalStyles.colors.ePrimary.base}
          onPress={addRecord}
        />
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  cardStyle: {
    marginHorizontal: "3%",
    borderRadius: 10,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    // boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    paddingVertical: 15,
    paddingHorizontal: 20,
    paddingBottom: 10,
    borderColor: GlobalStyles.colors.eFaint.base,
  },
  blueTextTitle: {
    color: GlobalStyles.colors.ePrimary.base,
    fontFamily: "NotoSans-SemiBold",
    fontSize: 14,
    float: "left",
    width: "90%",
  },
});

import React, { useContext, useEffect, useState } from "react";
import { StyleSheet, View, ActivityIndicator } from "react-native";
import { Card } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import { PaymentContext } from "../e_payments";
import Button from "../../common/_button";
import getaddressDownloadData from "../../e_downloads/model/download_service";
import { servicePath } from "../../../redux/slices/servicePath";
import { useDispatch, useSelector } from "react-redux";
import { useNavigation } from "@react-navigation/native";
import { convertBase64ToPDF } from "../../common/_base64toPDF";

export default function DownloadCard({ onRetryPayment = () => {} }) {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const {
    confirmScreen,
    setConfirmScreen,
    setAccountDetailsScreen,
    setErrorScreen,
    selectedAccount,
    payId,
  } = useContext(PaymentContext);
  const [email, setEmail] = useState();
  const [isLoading, setIsloading] = useState(false);
  const [isLoadingPrint, setIsloadingPrint] = useState(false);
  const personDetails = useSelector(
    state =>
      state?.accountDetails?.accountDetails?.accountSummary?.personDetailList?.[
        "C1-Person"
      ]?.personContactDetail,
  );
  const languageCode = useSelector(
    state =>
      state?.parameter?.parameter?.ParameterLookup?.TENANT_PRIMARY_LANGUAGE,
  );

  useEffect(() => {
    if (personDetails) {
      personDetails.map(item => {
        item?.personContactType === "PRIMARYEMAIL" &&
          setEmail(item?.contactDetailValue);
      });
    }
  }, [personDetails]);

  const download = async () => {
    await getBase64("D");
  };
  const requestHelp = () => {
    navigation.navigate("HelpTab");
    dispatch(servicePath("HelpTab"));
  };

  const printReceipt = async () => {
    await getBase64("P");
  };

  const getBase64 = uniqueId => {
    uniqueId === "P" ? setIsloadingPrint(true) : setIsloading(true);
    getaddressDownloadData(
      selectedAccount?.[0]?.accountId,
      "PAYMENT_RECEIPT",
      "",
      payId,
      email,
      "",
      "",
      languageCode,
    ).then(async res => {
      let base64Data = res?.data?.data?.getBase64Document;
      const fileType = "Payment Receipt";
      await convertBase64ToPDF(base64Data, fileType);
      uniqueId === "P" ? setIsloadingPrint(false) : setIsloading(false);
    });
  };

  const retryPayment = () => {
    setConfirmScreen(false);
    setAccountDetailsScreen(true);
    setErrorScreen(false);
    onRetryPayment && onRetryPayment();
  };
  return (
    <View>
      <Card style={styles.card}>
        <View style={styles.aroundMargin}>
          <View style={styles.content}>
            <View style={styles.leftView}>
              <Button
                buttonbgColor={styles.downloadColor}
                textColor={styles.downloadText}
                onPress={confirmScreen ? download : requestHelp}>
                {confirmScreen ? "DOWNLOAD" : "REQUEST HELP"}
                {isLoading && (
                  <ActivityIndicator
                    align="center"
                    size={13}
                    color={GlobalStyles.colors.eWhite.base}
                    style={{ paddingLeft: 2 }}
                  />
                )}
              </Button>
            </View>

            <View style={styles.leftView}>
              <Button
                buttonbgColor={styles.whiteBgColor}
                textColor={styles.greenText}
                onPress={confirmScreen ? printReceipt : retryPayment}>
                {confirmScreen ? "SHARE RECIEPT" : "RETRY PAYMENT"}
                {isLoadingPrint && (
                  <ActivityIndicator
                    align="center"
                    size={13}
                    color={GlobalStyles.colors.ePrimary.base}
                    style={{ paddingLeft: 2 }}
                  />
                )}
              </Button>
            </View>
          </View>
        </View>
      </Card>
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    width: "100%",
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    color: GlobalStyles.colors.eWhite.base,
  },
  aroundMargin: {
    marginLeft: 5,
    marginRight: 5,
  },
  leftView: {
    width: "50%",
    marginTop: 20,
    marginBottom: 20,
  },
  content: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
    justifyContent: "space-between",
  },
  buttonReceipt: {
    fontFamily: "NotoSans-Bold",
    width: "85%",
  },
  whiteBgColor: {
    borderColor: GlobalStyles.colors.ePrimary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 20,
  },
  downloadColor: {
    borderColor: GlobalStyles.colors.eWhite.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginLeft: 20,
  },
  greenText: {
    color: GlobalStyles.colors.ePrimary.base,
    fontFamily: "NotoSans-Bold",
    whiteSpace: "nowrap",
    padding: 5,
    fontSize: 13,
  },
  downloadText: {
    color: GlobalStyles.colors.eWhite.base,
    fontFamily: "NotoSans-Bold",
    whiteSpace: "nowrap",
    padding: 5,
    fontSize: 13,
  },
});

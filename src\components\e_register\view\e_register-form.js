import React from "react";
import { useState, useRef, useContext } from "react";
import { Text, Card, TextInput } from "react-native-paper";
import {
  View,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import { GlobalStyles } from "../../app/global-styles";
import Button from "../../common/_button";
import { useEffect } from "react";
import { useSelector } from "react-redux";
// import Recaptcha from "react-grecaptcha";
import { config } from "../../../environment";
import { registerService } from "../model/_register-service";
import Input from "../../common/_input";
import { registerContext } from "../../e_authPages/e_auth_pages";
import { registerContextWO } from "../../e_authPages/e_auth_pages_wo";
import { stackContext } from "../../app/get_stack";

export default function RegisterForm() {
  const { workModelType } = React.useContext(stackContext);
  const [fName, setfName] = useState("");
  const [lName, setlName] = useState("");
  const [accountNo, setAccountNo] = useState("");
  const [email, setEmail] = useState("");
  const [fNameError, setfNameError] = useState(false);
  const [lNameError, setlNameError] = useState(false);
  const [accountNoError, setAccountNoError] = useState(false);
  const [emailError, setEmailError] = useState(false);
  const [mobileNo, setMobileNo] = useState("");
  const [mobileNoError, setMobileNoError] = useState(false);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [confirmPasswordError, setConfirmPasswordError] = useState(false);
  const [confirmPWDCheck, setConfirmPWDCheck] = useState(false);
  const [passwordError, setPasswordError] = useState(false);
  const [passowrdSecurity, setPassowrdSecurityNew] = useState(true);
  const [registerError, setRegisterError] = useState(false);
  const [registerErrorMessage, setRegisterErrorMessage] = useState("");
  let setRegisterModal,
    setEmailUsername,
    setVerificationScreen,
    setVerifyDetails,
    setmobileNumber;

  if (workModelType === "WA") {
    ({
      setRegisterModal,
      setEmailUsername,
      setVerificationScreen,
      setVerifyDetails,
      setmobileNumber,
    } = useContext(registerContext));
  } else {
    ({
      setRegisterModal,
      setEmailUsername,
      setVerificationScreen,
      setVerifyDetails,
      setmobileNumber,
    } = useContext(registerContextWO));
  }

  const { setRegisterSuccess } = useContext(registerContext);
  const [isLoading, setLoading] = useState(false);
  const [passowrdSecurityConfirm, setPassowrdSecurityConfirm] =
    React.useState(true);
  const [emailRequire, setEmailRequire] = useState(false);
  // const [emailError, setEmailError] = useState(false);
  const [recaptchaValue, setRecaptchaValue] = useState();

  const [newPwdStrengthErr, setNewPwdStrengthErr] = useState(false);

  const param = useSelector(
    state =>
      state?.parameter?.parameter?.ParameterLookup
        ?.TENANT_IDENTIFICATION_NUMBER,
  );
  const [isVerification, setIsVerification] = useState(false);

  const [tenantNumber, setTenantNumber] = useState();
  const FLNameValue = useSelector(
    state =>
      state?.parameter?.parameter?.ParameterLookup?.FIRST_LAST_NAME_FIELD,
  );
  const [FLName, setFLName] = useState();
  const emailRegex = useSelector(
    state => state?.parameter?.parameter?.ParameterLookup?.EMAIL_REGEX,
  );
  const verificationCode = useSelector(
    state =>
      state?.parameter?.parameter?.ParameterLookup?.IS_VERIFICATION_FAILURE,
  );
  const accountNumberLength = useSelector(
    state =>
      state?.parameter?.parameter?.ParameterLookup?.E_ACCOUNT_NUMBER_LENGTH,
  );
  const mobileLength = useSelector(
    state => state?.parameter?.parameter?.ParameterLookup?.E_MOBILE_LENGTH,
  );
  useEffect(() => {
    if (param) {
      setTenantNumber(param);
    }
    if (FLNameValue) {
      setFLName(FLNameValue);
    }
  }, [param, FLNameValue]);

  useEffect(() => {
    if (verificationCode) {
      setIsVerification(true);
    }
  }, [verificationCode]);
  const registerSubmit = () => {
    //setRegisterModal(true);
    //setVerificationScreen(true);
    //setRegisterSuccess(true);

    if (fName.length === 0) {
      setfNameError(true);
    } else {
      setfNameError(false);
    }
    if (lName.length === 0) {
      setlNameError(true);
    } else {
      setlNameError(false);
    }
    if (accountNo.length === 0) {
      setAccountNoError(true);
    } else {
      setAccountNoError(false);
    }
    if (email.length === 0) {
      setEmailError(true);
    } else {
      setEmailError(false);
    }
    if (mobileNo.length === 0) {
      setMobileNoError(true);
    } else {
      setMobileNoError(false);
    }
    if (password.length === 0) {
      setPasswordError(true);
    } else {
      setPasswordError(false);
    }
    validatePwdStrength(password);
    if (confirmPassword.length === 0) {
      setConfirmPasswordError(true);
    } else {
      setConfirmPasswordError(false);
    }

    if (password.length !== 0 && confirmPassword.length !== 0) {
      if (password !== confirmPassword) {
        setConfirmPWDCheck(true);
        return false;
      } else {
        setConfirmPWDCheck(false);
      }
    }

    if (
      fName.length !== 0 &&
      lName.length !== 0 &&
      accountNo.length !== 0 &&
      email.length !== 0 &&
      mobileNo.length !== 0 &&
      password.length !== 0 &&
      confirmPassword.length !== 0 &&
      confirmPWDCheck === false &&
      newPwdStrengthErr === false
      // && recaptchaValue !== undefined
    ) {
      let user = {
        firstname: fName,
        lastname: lName,
        accountNumber: accountNo,
        registeredEmail: email,
        registeredCell: mobileNo,
        password: password,
        confirmPassword: confirmPassword,
      };

      setLoading(true);
      registerService
        .register(user, tenantNumber, FLName)
        .then(response => {
          if (response.status === 201) {
            setRegisterModal(true);
            setLoading(false);
            setEmailUsername(email);
            let mobileNumber = mobileNo.replace(/.(?=.{4,}$)/g, "*");
            setmobileNumber(mobileNumber);
          }

          if (response.data.isVerified === "false") {
            localStorage.setItem("verification", JSON.stringify(response.data));
            history.push(
              routePaths.LANDING + "?flowCode=userVerificationFailure",
            );
          } else {
            setLoading(false);
            localStorage.setItem("flowType", "SIGNUP");
            let mobile = user.registeredCell.slice(
              user.registeredCell.length - 4,
              user.registeredCell.length,
            );
            const state = store.getState();
            let isOTPOn = false;
            this.props.propsMain.getPath(routePaths.REGISTER, response, mobile);
            const widgets = state.parametersWidgets.WidgetLookUp;
            if (widgets) {
              widgets.forEach(widgetItem => {
                if (
                  widgetItem.widgetCode === "SIGNUP_OTP_VALIDATION" &&
                  widgetItem.widgetDefaultValue === "true"
                ) {
                  isOTPOn = true;
                }
              });
            }
            if (isOTPOn) {
              history.push(routePaths.AUTH_OTP);
            } else {
              history.push(routePaths.SIGNUP_SUCCESS);
            }
            this.setState({
              isSignupLoading: false,
            });
          }
        })

        .catch(error => {
          setLoading(false);

          if (error.response && error.response.data.statusCode === 400) {
            setRegisterError(true);
            setRegisterErrorMessage(error.response.data.message);
            setLoading(false);
          } else if (error.response && error.response.status === 404) {
            // setRegisterError(true);
            // setRegisterErrorMessage('Mobile Number not matched');
            setLoading(false);
            setVerificationScreen(true);
            setVerifyDetails(error.response.data);
          } else if (error.response && error.response.data.statusCode === 500) {
            setRegisterError(true);
            setRegisterErrorMessage(error.response.data.message);
            setLoading(false);
          } else {
            setRegisterError(true);
            //setRegisterErrorMessage(error);
            setLoading(false);
          }
        });
    }
  };
  const checkFname = fName => {
    if (fName.length > 0) {
      setfNameError(false);
    } else {
      setfNameError(true);
    }
  };
  const checkLname = lName => {
    if (lName.length > 0) {
      setlNameError(false);
    } else {
      setlNameError(true);
    }
  };
  const checkAccountNumber = accountNo => {
    if (accountNumberLength) {
      if (accountNo.length === parseInt(accountNumberLength)) {
        setAccountNoError(false);
      } else {
        setAccountNoError(true);
      }
    } else if (accountNo.length === 10) {
      setAccountNoError(false);
    } else {
      setAccountNoError(true);
    }
  };
  const checkEmail = email => {
    if (email) {
      setEmailRequire(false);
      setEmailError(false);
      var emailRegExp = new RegExp(emailRegex);
      var emailTest = emailRegExp.test(email);

      if (emailTest) {
        setEmailRequire(false);
      } else {
        setEmailRequire(true);
      }
    } else {
      setEmailError(true);
      setEmailRequire(false);
    }
  };

  const getfNameErrorMsg = () => {
    if (fNameError) {
      return "Please Enter First Name";
    } else return;
  };

  const getlNameErrorMsg = () => {
    if (lNameError) {
      return "Please Enter Last Name";
    } else return;
  };

  const getEmailErrorMsg = () => {
    if (emailError) {
      return "Please Enter Email ID";
    } else if (emailRequire) {
      return "Please Enter Valid Email ID";
    } else return;
  };

  const getAccountNumberErrorMsg = () => {
    if (accountNoError) {
      return "Please Enter Account Number";
    } else return;
  };

  const getMobileErrorMsg = () => {
    if (mobileNoError) {
      return "Please Enter Mobile Number";
    } else return;
  };

  const getPasswordErrorMsg = () => {
    if (passwordError) {
      return "Please Enter Password";
    } else if (newPwdStrengthErr) {
      return "Password is case-sensitive, it must contain a minimum of six characters with atleast one capital letter, one small letter, one number and one special character.";
    } else return;
  };

  const getCPasswordErrorMsg = () => {
    if (passwordError) {
      return "Please Enter Confirm Password";
    } else if (confirmPWDCheck) {
      return "New Password and Confirm password should be same";
    } else return;
  };

  const checkMobile = mobileNo => {
    if (mobileNo.length === 10) {
      setMobileNoError(false);
    } else {
      setMobileNoError(true);
    }
  };
  const checkPassword = password => {
    if (password.length > 0) {
      setPasswordError(false);
      validatePwdStrength(password);
    } else {
      setPasswordError(true);
    }
  };

  const validatePwdStrength = newPwd => {
    var regData = "^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{6,})";
    var strongRegex = new RegExp(regData);
    var test = strongRegex.test(newPwd);
    if (test) {
      setNewPwdStrengthErr(false);
    } else {
      setNewPwdStrengthErr(true);
    }
  };
  const checkConfirmPassword = confirmPassword => {
    if (confirmPassword.length > 0) {
      setConfirmPasswordError(false);
    } else {
      setConfirmPasswordError(true);
    }
    if (password.length !== 0 && confirmPassword.length !== 0) {
      if (password !== confirmPassword) {
        setConfirmPWDCheck(true);
      } else {
        setConfirmPWDCheck(false);
      }
    }
  };
  const secureTextEntryNew = () => {
    setPassowrdSecurityNew(passowrdSecurity ? false : true);
  };
  const secureTextEntryConfirm = () => {
    setPassowrdSecurityConfirm(passowrdSecurityConfirm ? false : true);
  };
  const verifyCallback = response => {
    setRecaptchaValue(response);
  };
  const expiredCallback = () => {};

  const fNameChange = e => {
    const cleanedText = e.replace(/[^a-zA-Z]/g, "");
    setfName(cleanedText);
  };

  const lNameChange = e => {
    const cleanedText = e.replace(/[^a-zA-Z]/g, "");
    setlName(cleanedText);
  };

  const accountChange = e => {
    const cleanedNumber = e.replace(/[^0-9]/g, "");
    setAccountNo(cleanedNumber);
  };

  const handleMobileNumberChange = e => {
    const cleanedNumber = e.replace(/[^0-9]/g, "");
    setMobileNo(cleanedNumber);
  };

  return (
    <TouchableWithoutFeedback>
      {/* onPress={Keyboard.dismiss} accessible={false}> */}
      <>
        <ScrollView style={styles.scrollStyle}>
          <View style={{ paddingHorizontal: "5%" }}>
            <Input
              label="First Name:"
              onUpdateValue={e => fNameChange(e)}
              value={fName ? fName : ""}
              keyboardType="default"
              onBlur={() => checkFname(fName)}
              isInvalid={fNameError}
              errorMsg={getfNameErrorMsg}
              secure={false}
              placeholderTextColor="#5C5E60"
              placeholder="Enter First Name"
              // activeUnderlineColor={GlobalStyles.colors.ePrimary.base}
            />

            <Input
              label="Last Name:"
              onUpdateValue={e => lNameChange(e)}
              value={lName ? lName : ""}
              keyboardType="default"
              onBlur={() => checkLname(lName)}
              isInvalid={lNameError}
              errorMsg={getlNameErrorMsg}
              secure={false}
              placeholderTextColor="#5C5E60"
              placeholder="Enter Last Name"
              // activeUnderlineColor={GlobalStyles.colors.ePrimary.base}
            />

            <Input
              label="Account Number:"
              onUpdateValue={e => accountChange(e)}
              value={accountNo ? accountNo : ""}
              keyboardType="numeric"
              onBlur={() => checkAccountNumber(accountNo)}
              isInvalid={accountNoError}
              errorMsg={getAccountNumberErrorMsg}
              secure={false}
              placeholderTextColor="#5C5E60"
              placeholder="Enter Account Number"
              maxLength={
                accountNumberLength ? parseInt(accountNumberLength) : 10
              }
              // activeUnderlineColor={GlobalStyles.colors.ePrimary.base}
            />

            <Input
              label="Your Email:"
              onUpdateValue={e => setEmail(e)}
              value={email ? email : ""}
              keyboardType="email-address"
              onBlur={() => checkEmail(email)}
              isInvalid={emailRequire || emailError}
              errorMsg={getEmailErrorMsg}
              secure={false}
              placeholderTextColor="#5C5E60"
              placeholder="Enter Your Email"
              // activeUnderlineColor={GlobalStyles.colors.ePrimary.base}
            />

            <Input
              label="Mobile Number:"
              onUpdateValue={e => handleMobileNumberChange(e)}
              value={mobileNo ? mobileNo : ""}
              keyboardType="numeric"
              // type="number"
              onBlur={() => checkMobile(mobileNo)}
              isInvalid={mobileNoError}
              errorMsg={getMobileErrorMsg}
              secure={false}
              maxLength={mobileLength ? parseInt(mobileLength) : 10}
              placeholderTextColor="#5C5E60"
              placeholder="Enter Mobile Number"
              // activeUnderlineColor={GlobalStyles.colors.ePrimary.base}
            />

            <Input
              label="Password:"
              onUpdateValue={e => setPassword(e)}
              secure={true}
              value={password ? password : ""}
              onBlur={() => checkPassword(password)}
              isInvalid={passwordError || newPwdStrengthErr}
              errorMsg={getPasswordErrorMsg}
              placeholder="Enter Password"
            />

            <Input
              label="Confirm Password:"
              onUpdateValue={e => setConfirmPassword(e)}
              secure={true}
              value={confirmPassword ? confirmPassword : ""}
              onBlur={() => checkConfirmPassword(confirmPassword)}
              placeholder="Enter Confirm Password"
              isInvalid={confirmPasswordError || confirmPWDCheck}
              errorMsg={getCPasswordErrorMsg}
            />
            {registerError ? (
              registerErrorMessage === "ACCOUNT_NUMBER_NOT_FOUND" ? (
                <>
                  <Text
                    style={[styles.errorText, { fontFamily: "NotoSans-Bold" }]}>
                    Account ID not found
                  </Text>
                  <Text style={styles.errorText}>
                    The Account ID you have provided is not matching with any
                    records.
                  </Text>
                  <Text style={styles.errorText}>
                    Tip: Please check your existing bill to find the Account ID.
                  </Text>
                </>
              ) : (
                <>
                  <Text
                    style={[styles.errorText, { fontFamily: "NotoSans-Bold" }]}>
                    {registerErrorMessage}
                  </Text>
                </>
              )
            ) : (
              <Text style={styles.errorText}></Text>
            )}
          </View>
          {/* <View style={styles.recaptcha}>
            <Recaptcha
              sitekey={config.recaptcha.SITE_KEY}
              callback={verifyCallback}
              expiredCallback={expiredCallback}
              data-theme="light"
            />
          </View> */}
        </ScrollView>

        <View style={styles.buttons}>
          <Button
            onPress={registerSubmit}
            buttonbgColor={styles.buttonbgColor}
            textColor={styles.textColor}>
            Register{" "}
            {isLoading && (
              <ActivityIndicator
                align="center"
                size="small"
                color={GlobalStyles.colors.eWhite.base}
              />
            )}
          </Button>
        </View>
      </>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  countdown: {
    fontSize: 10,
  },
  container: {
    flex: 1,
    width: "100%",
    // paddingBottom: 10,
    paddingHorizontal: 12,
    elevation: 2,
    marginTop: 3,
    alignContent: "center",
    justifyContent: "center",
  },
  scrollStyle: {
    height: "100%",
    paddingHorizontal: 20,
    marginTop: 5,
  },
  text: {
    fontFamily: "NotoSans-Bold",
    fontSize: 15,
  },
  btnContainer: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 15,
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
  },
  bgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
  },
  buttons: {
    marginTop: "1%",
    paddingVertical: 10,
    paddingHorizontal: "10%",
  },
  buttonbgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    paddingVertical: "3%",
    paddingHorizontal: "3%",
    width: "100%",
    alignItems: "center",
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 16,
  },
  errorText: {
    color: GlobalStyles.colors.eDanger.dark,
    fontSize: 12,
  },
  recaptcha: {
    marginBottom: 10,
  },
});

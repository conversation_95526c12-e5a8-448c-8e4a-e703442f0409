import { useContext, useEffect, useState } from "react";
import React from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  TouchableWithoutFeedback,
  Keyboard,
} from "react-native";
import { GlobalStyles } from "../../app/global-styles";
import FlatButton from "../../common/_flat_button";
import Button from "../../common/_button";
import Input from "../../common/_input";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { loginService } from "../model/_login_service";
import { updateLoggedInStatus } from "../../../redux/slices/authenticationReducer";
import { useDispatch, useSelector } from "react-redux";
import { Text } from "react-native-paper";
import { registerContext } from "../../e_authPages/e_auth_pages";
import { Dropdown } from "react-native-element-dropdown";
import { useTranslation } from "react-i18next";
import { registerContextWO } from "../../e_authPages/e_auth_pages_wo";
import { config } from "../../../environment";
import { stackContext } from "../../app/get_stack";

export default function LoginForm({ onSubmit }) {
  const { workModelType } = React.useContext(stackContext);
  const { t } = useTranslation();

  let setIsLoginPage;

  if (workModelType === "WA") {
    ({ setIsLoginPage } = useContext(registerContext));
  } else {
    ({ setIsLoginPage } = useContext(registerContextWO));
  }

  const [enteredEmail, setEnteredEmail] = useState(
    "<EMAIL>",
  );
  const [enteredPassword, setEnteredPassword] = useState("password");
  const [emailRequire, setEmailRequire] = useState(false);
  const [emailError, setEmailError] = useState(false);
  const [passwordRequire, setPasswordRequire] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [loginError, setLoginError] = useState("");
  const [loginErrorMessage, setLoginErrorMessage] = useState("");
  const [timeCountdown, setTimeCountdown] = useState();
  const [lockDuration, setLockDuration] = useState(null);
  const [longError, setLongError] = useState(false);
  const [disable, setdisable] = useState(false);
  const lockDurationDefault = useSelector(
    state => state?.parameter?.parameter?.ParameterLookup?.LOCK_DURATION,
  );
  const [selectedLanguage, setSelectedLanguage] = useState("en");

  const languageData = [
    { label: "English", value: "en" },
    { label: "Marathi", value: "ma" },
    { label: "Thai", value: "th" },
  ];

  useEffect(() => {
    setLockDuration(lockDurationDefault);
  }, [lockDurationDefault]);
  const dispatch = useDispatch();

  const updateInputValueHandler = (inputType, enteredValue) => {
    setLoginError(false);
    setLoginErrorMessage("");
    switch (inputType) {
      case "email":
        setEnteredEmail(enteredValue);
        break;
      case "password":
        setEnteredPassword(enteredValue);
        break;
    }
  };

  useEffect(() => {}, [emailRequire, emailError, passwordRequire]);

  const submitHandler = () => {
    onSubmit({
      email: enteredEmail,
      password: enteredPassword,
    });
  };

  const handleForgotPassword = () => {
    // Todo
    setIsLoginPage("ForgotPassword");
  };
  // Load selected language from AsyncStorage when component mounts
  useEffect(() => {
    const loadLanguage = async () => {
      try {
        const storedLanguage = await AsyncStorage.getItem("selectedLanguage");
        console.log(storedLanguage);
        if (storedLanguage) {
          setSelectedLanguage(storedLanguage);
        }
      } catch (error) {
        console.error("Failed to load language from storage:", error);
      }
    };

    loadLanguage();
  }, []);

  let emailBlur = () => {
    if (enteredEmail) {
      setEmailRequire(false);
      setEmailError(false);
      var emailRegExp = new RegExp(
        "^([a-z0-9\\+_\\-]+)(\\.[a-z0-9\\+_\\-]+)*@([a-z0-9\\-]+\\.)+[a-z]{2,6}$",
      );
      var emailTest = emailRegExp.test(enteredEmail);

      if (emailTest) {
        setEmailError(false);
      } else {
        setEmailError(true);
      }
    } else {
      setEmailRequire(true);
      setEmailError(false);
    }
  };

  let passwordBlur = () => {
    if (enteredPassword) {
      setPasswordRequire(false);
    } else {
      setPasswordRequire(true);
    }
  };

  const getEmailErrorMsg = () => {
    if (emailRequire) {
      return t("EMAIL_REQ_MSG");
    } else if (emailError) {
      return t("EMAIL_VALIDATION_MSG");
    } else return;
  };

  const getPasswordErrorMsg = () => {
    if (passwordRequire) {
      return t("PASSWORD_REQ_MSG");
    } else return;
  };

  let loginSubmit = e => {
    // e.preventDefault();
    emailBlur();
    passwordBlur();
    if (enteredEmail && enteredPassword) {
      if (
        emailRequire === false &&
        emailError === false &&
        passwordRequire === false
      ) {
        setLoading(true);
        setdisable(true);
        loginService
          .login(enteredEmail, enteredPassword)
          .then(response => {
            if (response.acessToken) {
              AsyncStorage.clear();
              AsyncStorage.setItem("bearer", JSON.stringify(response));

              dispatch(updateLoggedInStatus(true));
            }
            setLoading(false);
            setdisable(false);
            setLoginError(false);
          })
          .catch(error => {
            if (error.response && error.response.data.statusCode === 403) {
              setLoginError(true);
              setLoading(false);
              setdisable(false);
              setLoginErrorMessage(error?.response?.data?.message);
              let text = t("ACCOUNT_BLOCKED_MSG");
              if (error.response.data.message === "User Blocked") {
                setLockDuration(lockDurationDefault);
                setLoginErrorMessage(text.replace("***", lockDurationDefault));
              } else {
                let min = error.response.data.message;
                if (isNaN(min)) {
                  setLockDuration(lockDurationDefault);
                  setLoginErrorMessage(
                    text.replace("***", lockDurationDefault),
                  );
                } else {
                  setLockDuration(min);
                  setLoginErrorMessage(text.replace("***", min));
                }
              }
            } else if (error.response) {
              if (
                error.response.data.statusCode === 500 ||
                error.response.status === 404
              ) {
                setLoginError(true);
                setLoginErrorMessage(t("SYSTEM_DOWN_MSG"));
                setLoading(false);
                setdisable(false);
              } else {
                setLoginError(true);

                setLoginErrorMessage(
                  error?.response?.data?.message
                    ? error?.response?.data?.message
                    : t("SYSTEM_DOWN_MSG"),
                );
                setLoading(false);
                setdisable(false);
              }
            }
          });
      }
    }
  };
  function singInUsingOTP() {
    // Todo
    setIsLoginPage("LoginOTP");
  }

  // Handle language selection and store in AsyncStorage
  const selectLanguage = async languageCode => {
    setSelectedLanguage(languageCode);
    try {
      await AsyncStorage.setItem("selectedLanguage", languageCode);
    } catch (error) {
      console.error("Failed to store language:", error);
    }
  };
  return (
    <TouchableWithoutFeedback>
      <>
        <ScrollView style={styles.scrollStyle}>
          <View style={styles.container}>
            <View style={styles.dropdownContainer}>
              <Dropdown
                style={styles.dropdown}
                placeholderStyle={styles.placeholderStyle}
                //selectedTextStyle={styles.selectedTextStyle}
                data={languageData}
                labelField="label"
                valueField="value"
                placeholder="Select Language"
                value={selectedLanguage} // Set the default value
                onChange={item => {
                  selectLanguage(item.value);
                }}
              />
            </View>
            <Input
              onUpdateValue={enteredVal =>
                updateInputValueHandler("email", enteredVal)
              }
              value={enteredEmail}
              keyboardType="email-address"
              onBlur={emailBlur}
              isInvalid={emailRequire || emailError}
              errorMsg={getEmailErrorMsg}
              secure={false}
              placeholder={t("ENTER_EMAIL")}
              name="account-circle"
            />
            <Input
              onUpdateValue={enteredVal =>
                updateInputValueHandler("password", enteredVal)
              }
              secure={true}
              value={enteredPassword}
              onBlur={passwordBlur}
              isInvalid={passwordRequire}
              errorMsg={getPasswordErrorMsg}
              name="lock"
              placeholder={t("ENTER_PASSWORD")}
            />
            <View style={styles.flatButtonView}>
              <View style={styles.questionView}>
                <Text style={styles.flatButtonQuestion}>
                  {t("CANT_LOGIN")}?
                </Text>
              </View>
              <FlatButton
                onPress={handleForgotPassword}
                textStyles={styles.flatButtonforgot}>
                {t("FORGOT_PASSWORD")}
              </FlatButton>
            </View>
          </View>
          {loginError ? (
            <ScrollView style={styles.errorContainer}>
              <Text style={styles.errorStyle}>{loginErrorMessage}</Text>
            </ScrollView>
          ) : (
            <View style={{ padding: "4%" }} />
          )}
        </ScrollView>
        <View style={styles.buttons}>
          <Button
            onPress={!isLoading && loginSubmit}
            buttonbgColor={styles.buttonbgColor}
            textColor={styles.textColor}
            disabled={disable}>
            {t("LOGIN")} {console.log(disable)}
            {isLoading && (
              <ActivityIndicator
                align="center"
                size={13}
                color={GlobalStyles.colors.eWhite.base}
                style={{ paddingTop: 6 }}
              />
            )}
          </Button>
          <View style={styles.buttonsLogin}>
            <View style={styles.leftSide}>
              <FlatButton
                onPress={singInUsingOTP}
                textStyles={styles.flatButtonOtp}>
                {t("LOGIN_OTP")}
              </FlatButton>
            </View>
            <View style={styles.rightSide}>
              <FlatButton
                onPress={handleForgotPassword}
                textStyles={styles.flatButtonforgot}>
                {t("REGISTER")}
              </FlatButton>
            </View>
          </View>
        </View>
      </>
    </TouchableWithoutFeedback>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: "100%",
    // paddingBottom: 10,
    paddingHorizontal: 12,
    elevation: 2,
    marginTop: 3,
    alignContent: "center",
    justifyContent: "center",
  },
  dropdownContainer: {
    marginVertical: 15, // Space between dropdown and other fields
    backgroundColor: "#fff", // White background for the dropdown
    borderRadius: 4,
    padding: 4,
    width: "40%",
    alignSelf: "center",
  },
  leftSide: {
    justifyContent: "flex-start",
  },
  rightSide: {
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  buttonsLogin: {
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
  },
  divider: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    fontSize: 12,
  },
  line: {
    borderTopWidth: 1,
    borderColor: GlobalStyles.colors.ePrimary.base,
    width: 100,
  },
  lineText: {
    color: GlobalStyles.colors.ePrimary.base,
    padding: 10,
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
  },
  flatButtonOtp: {
    textAlign: "center",
    color: GlobalStyles.colors.eTertiary.base,
    textDecorationLine: "underline",
    paddingVertical: "1%",
    paddingHorizontal: 12,
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
  },
  buttons: {
    paddingHorizontal: "10%",
  },
  flatButtonforgot: {
    color: GlobalStyles.colors.eWhite.base,
    textDecorationLine: "underline",
    paddingVertical: 6,
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
  },
  flatButtonView: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginBottom: 10,
    marginTop: -25,
  },
  questionView: {
    justifyContent: "flex-end",
    alignItems: "flex-end",
  },
  flatButtonQuestion: {
    color: GlobalStyles.colors.eWhite.base,
    paddingVertical: 6,
    paddingHorizontal: 3,
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
  },
  buttonbgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    paddingVertical: "3%",
    paddingHorizontal: "3%",
    width: "100%",
    alignItems: "center",
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 16,
    fontFamily: "NotoSans-Medium",
  },
  errorContainer: {
    width: "92%",
    backgroundColor: GlobalStyles.colors.eDanger.light,
    maxHeight: 48,
    marginLeft: "4%",
    marginBottom: "2%",
    overflow: "scroll",
  },
  errorStyle: {
    paddingVertical: 10,
    paddingHorizontal: 38,
    fontSize: 12,
    color: GlobalStyles.colors.eDanger.dark,
  },
  scrollStyle: {
    height: "100%",
    paddingHorizontal: "2%",
  },
});

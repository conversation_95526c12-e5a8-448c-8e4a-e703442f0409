import moment from "moment";
import React, { useContext } from "react";
import { useEffect, useState } from "react";
import { Text, StyleSheet, ActivityIndicator, View } from "react-native";
import { Card, Checkbox } from "react-native-paper";
import { useSelector } from "react-redux";
import { drawerContext } from "../../../app/authenticated/authenticated_layout";
import { GlobalStyles } from "../../../app/global-styles";
import { EAutoPayServices } from "../model/auto_pay_service";
import { config } from "../../../../environment";
import { drawerContextWO } from "../../../app/authenticated/authenticated_layout_wo";
import { stackContext } from "../../../app/get_stack";

export default function AutoPayment({ autoData, checked, setChecked }) {
  const { workModelType } = React.useContext(stackContext);
  const [disabled, setDisabled] = useState(false);
  const [loading, setLoading] = useState(false);
  const accountId = useSelector(
    state => state?.meterDetails?.meterDetails?.accountId,
  );
  let setPopup,
    setTitle,
    setContent,
    setError,
    setButton,
    setIcon,
    setPopupCode;
  let yesDone, popupCode, popup, setYesDone;

  if (workModelType === "WA") {
    ({
      setPopup,
      setTitle,
      setContent,
      setError,
      setButton,
      setIcon,
      setPopupCode,
      yesDone,
      popupCode,
      popup,
      setYesDone,
    } = useContext(drawerContext));
  } else {
    ({
      setPopup,
      setTitle,
      setContent,
      setError,
      setButton,
      setIcon,
      setPopupCode,
      yesDone,
      popupCode,
      popup,
      setYesDone,
    } = useContext(drawerContextWO));
  }

  useEffect(() => {
    if (autoData && autoData.length === 0) {
      setChecked(false);
    }
    if (autoData.length === 1 && autoData[0].editable === true) {
      setChecked(false);
    }
    if (autoData) {
      autoData.map(autoPayItem => {
        if (
          moment().isBetween(autoPayItem.startDate, autoPayItem.endDate) &&
          autoPayItem.status === "INACTIVE"
        ) {
          setChecked(false);
        }
      });
    }
  }, [autoData]);

  useEffect(() => {
    if (popup && popupCode === "CHECKBOX_CONFIRMATION" && yesDone) {
      setPopup(false);
      setYesDone(false);
      setLoading(true);
      setPopupCode();
      accountId &&
        EAutoPayServices.getUpdatePreference(!checked, accountId)
          .then(res => {
            setLoading(false);
            setChecked(prev => !prev);
          })
          .catch(err => {
            setLoading(false);
            setYesDone(false);
            setPopup(true);
            setTitle("Failure");
            setContent(
              err?.response?.data?.errors?.[0]?.message
                ? err?.response?.data?.errors?.[0]?.message
                : "Something went wrong.Please try again later.",
            );
            setError("ERROR");
            setButton(false);
            setIcon("Exclamationmark-fill-icon");
            setPopupCode();
          });
    }
  }, [popup, popupCode, yesDone]);

  const handleChange = () => {
    setPopup(true);
    setTitle("Confirmation");
    setContent("Are you sure you want to change preference?");
    setError("WARNING");
    setIcon();
    setButton(true);
    setPopupCode("CHECKBOX_CONFIRMATION");
  };

  return (
    <Card style={styles.cardStyle}>
      <Text style={styles.blueTextTitle}>Auto Payment</Text>
      <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
        <View style={{ width: "90%" }}>
          <Checkbox.Item
            label="Enabled"
            position="leading"
            mode="android"
            status={checked == true ? "checked" : "unchecked"}
            onPress={autoData && autoData.length > 0 && handleChange}
            labelStyle={styles.bigTextHeader}
            style={{ padding: 0 }}
            color={GlobalStyles.colors.eSecondary.base}
            uncheckedColor={GlobalStyles.colors.eRich.hover}
          />
        </View>
        {loading && (
          <ActivityIndicator color={GlobalStyles.colors.ePrimary.base} />
        )}
      </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  cardStyle: {
    marginHorizontal: "3%",
    borderRadius: 10,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    paddingVertical: 15,
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  blueTextTitle: {
    color: GlobalStyles.colors.ePrimary.base,
    fontFamily: "NotoSans-SemiBold",
    fontSize: 14,
    marginBottom: "2%",
  },
  bigTextHeader: {
    color: GlobalStyles.colors.eRich.base,
    textAlign: "left",
    fontSize: 12,
    justifyContent: "flex-start",
    // wordBreak: "break-all",
  },
});

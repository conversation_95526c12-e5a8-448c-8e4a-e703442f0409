import { Pressable, StyleSheet, View } from 'react-native';
import { Text } from "react-native-paper"

function FlatButton({ children, onPress, textStyles, disable }) {
  return (
    <Pressable
      style={({ pressed }) => [pressed && styles.pressed]}
      onPress={onPress}
      disabled={disable}
    >
      <View>
        <Text style={textStyles}>{children}</Text>
      </View>
    </Pressable>
  );
}

export default FlatButton;

const styles = StyleSheet.create({
  pressed: {
    opacity: 0.7,
  },
});
import AsyncStorage from "@react-native-async-storage/async-storage";
import moment from "moment";
import React, { useContext, useEffect } from "react";
import { useState } from "react";
import { StyleSheet, Text, View } from "react-native";
import TextLink from "react-native-text-link";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import { GlobalStyles } from "../../app/global-styles";
import Icon from "../../icon";
import FilterPopup from "./_filterPopup";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { config } from "../../../environment";
import { stackContext } from "../../app/get_stack";

export default function FilterData({
  setStartDate,
  setEndDate,
  startDate,
  endDate,
  setReadData,
  readData,
}) {
  const { workModelType } = React.useContext(stackContext);
  let setPopup,
    setTitle,
    setContent,
    setError,
    setButton,
    setIcon,
    setPopupCode,
    popup,
    popupCode,
    yesDone,
    setYesDone,
    setNotificatioError;

  if (workModelType === "WA") {
    ({
      setPopup,
      setTitle,
      setContent,
      setError,
      setButton,
      setIcon,
      setPopupCode,
      popup,
      popupCode,
      yesDone,
      setYesDone,
      setNotificatioError,
    } = useContext(drawerContext));
  } else {
    ({
      setPopup,
      setTitle,
      setContent,
      setError,
      setButton,
      setIcon,
      setPopupCode,
      popup,
      popupCode,
      yesDone,
      setYesDone,
      setNotificatioError,
    } = useContext(drawerContextWO));
  }

  const linksOpen = () => {
    setPopup(true);
    setTitle("Filter");
    setContent(
      <FilterPopup
        startDate={startDate}
        endDate={endDate}
        readData={readData}
        setNotificatioError={setNotificatioError}
      />,
    );
    setError("WARNING");
    setButton(true);
    setIcon();
    setPopupCode("NOTIFICATION_FILTER");
  };

  useEffect(() => {
    if (
      popup === true &&
      popupCode === "NOTIFICATION_FILTER" &&
      yesDone === true
    ) {
      AsyncStorage.getItem("startDate").then(startD =>
        setStartDate(moment(startD, "DD-MM-YYYY").format("YYYY-MM-DD")),
      );
      AsyncStorage.getItem("endDate").then(endD =>
        setEndDate(moment(endD, "DD-MM-YYYY").format("YYYY-MM-DD")),
      );
      AsyncStorage.getItem("read").then(read => setReadData(read));
      AsyncStorage.removeItem("startDate");
      AsyncStorage.removeItem("endDate");
      AsyncStorage.removeItem("read");
      setPopup(false);
      setYesDone(false);
      setPopupCode();
    }
  }, [popup, popupCode, yesDone]);

  return (
    <View style={styles.content}>
      <TextLink
        textStyle={styles.textLinkStyle}
        pressingLinkStyle={{ color: GlobalStyles.colors.ePrimary.base }}
        textLinkStyle={{
          color: GlobalStyles.colors.ePrimary.base,
          marginRight: "2%",
        }}
        links={[
          {
            text: "Filter",
            onPress: () => linksOpen(),
          },
        ]}>
        Filter
      </TextLink>
      <Icon
        name="Filter-stroke-icon"
        size={15}
        color={GlobalStyles.colors.ePrimary.base}
        style={styles.filterIcon}
        onPress={linksOpen}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  content: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
    justifyContent: "flex-end",
    right: "3%",
  },
  textLinkStyle: {
    textAlign: "center",
    fontSize: 12,
    width: "12%",
    fontFamily: "NotoSans-SemiBold",
  },
  filterIcon: {
    marginTop: 2,
    marginRight: 10,
  },
});

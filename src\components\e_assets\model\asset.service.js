import axios from "axios";
import { config } from "../../../environment";

export const AssetService = {
  getAssetLists,
};

async function getAssetLists() {
  try {
   // let gettURL = config.urls.GET_CONSUMER_LIST;
    let gettURL = "https://impresa-fieldwork-demo.abjayon.com/asset/assets-managemenet/details?page=1&limit=10000"
    console.log(gettURL);
    const response = await axios.get(
      gettURL,
      // Add headers if needed
      // {
      //   headers: {
      //     'Authorization': 'Bearer YOUR_ACCESS_TOKEN',
      //     'Content-Type': 'application/json',
      //   },
      // }
    );
    console.log("Response Data:.....", response.data);
    return response.data;
  } catch (error) {
    // Handle any errors
    console.log("An error occurred....:", error);
    throw error;
  }
}


import axios from "axios";
import { config } from "../../../../environment";

export const EAutoPayServices = {
  getAutoPayRecords,
  deleteAutoPayRecord,
  updateAutoPayRecords,
  createAndSaveNewRecord,
  getSourceList,
  getDropdownpData,
  getPaymentPreference,
  getUpdatePreference,
};
async function getAutoPayRecords(accountId) {
  let APIquery = `query{ 
        getAutoPayData
    }
    `;
  let Headers = { accountId: accountId };
  return await new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.USAGE_SERVICE_BASE_URL,
        {
          query: APIquery,
        },
        {
          headers: Headers,
        },
      )
      .then(function (response) {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
}
function getSourceList(accountId) {
  let APIquery = `query{
            getSourceCdList 
        }
        `;
  let Headers = { accountId: accountId };
  // return apolloClientService.ApolloClientgqlsUsage(APIquery, Headers);
  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.USAGE_SERVICE_BASE_URL,
        {
          query: APIquery,
        },
        {
          headers: Headers,
        },
      )
      .then(function (response) {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
}
function deleteAutoPayRecord(accountId, autoPayId) {
  let deleteAPIQuery =
    `query{deleteAutoPay(input:{
        accountId: "` +
    accountId +
    `", 
        autoPayId: "` +
    autoPayId +
    `",
        tenantCode: "` +
    config.constants.BASE_TENANT_CODE +
    `"
    })}`;

  let Headers = { accountId: accountId };
  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.USAGE_SERVICE_BASE_URL,
        {
          query: deleteAPIQuery,
        },
        {
          headers: Headers,
        },
      )
      .then(function (response) {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
  // return apolloClientService.ApolloClientgqlsUsage(deleteAPIQuery, Headers);
}

function updateAutoPayRecords(accountId, modifiedRecord) {
  let Headers = { accountId: accountId };
  let date = modifiedRecord.expireDate;
  let month;
  let year;
  let modifiedFinalRecord = {};
  let withDrawal_amount = modifiedRecord.autopayMaxWithdrawalAmount;
  if (date && date.includes("/") && date.split("/").length > 2) {
    year = date.split("/")[1];
    month = date.split("/")[0];
  } else if (date && date.includes("-") && date.split("-").length > 2) {
    month = date.split("-")[1];
    year = date.split("-")[0];
  } else if (date === "" && month === undefined && year === undefined) {
    month = "";
    year = "";
  }
  if (withDrawal_amount === "") {
    withDrawal_amount = 0;
  }
  if (modifiedRecord?.autoPayMethod === "Bank") {
    month = "";
    year = "";
  }
  if (modifiedRecord?.endDate === null) {
    modifiedFinalRecord = { ...modifiedRecord, ["endDate"]: "" };
  } else {
    modifiedFinalRecord = modifiedRecord;
  }
  let updateAPIQuery =
    `query{updateAutoPay(input:{
        startDate: "` +
    modifiedRecord.startDate +
    `",
        endDate: "` +
    modifiedFinalRecord.endDate +
    `",    
        autoPaySourceCode: "` +
    modifiedRecord.autopaySource +
    `",    
        extAcctId: "` +
    modifiedRecord.externalAccountId +
    `",    
        expiresOnMonth: "` +
    month +
    `",  
        expiresOnYear: "` +
    year +
    `",  
        name: "` +
    modifiedRecord.entityName +
    `",    
        maxWithdrawlAmt: "` +
    withDrawal_amount +
    `",    
        autoPayId: "` +
    modifiedRecord.accountAutopayId +
    `",    
        mode: "` +
    modifiedRecord.autoPayMethod +
    `",
        comments: "` +
    modifiedRecord.comments +
    `"
    
    })}`;
  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.USAGE_SERVICE_BASE_URL,
        {
          query: updateAPIQuery,
        },
        {
          headers: Headers,
        },
      )
      .then(function (response) {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
  // return apolloClientService.ApolloClientgqlsUsage(updateAPIQuery, Headers, "network-only");
}

function createAndSaveNewRecord(
  accountId,
  startDate,
  endDate,
  cardName,
  cardNumber,
  expiresOn,
  sourceCode,
  MAX_LIMIT_TENENT_CODE,
) {
  let Headers = { accountId: accountId };
  let month;
  let year;
  if (expiresOn && expiresOn.includes("-")) {
    month = expiresOn.split("-")[0];

    year = expiresOn.split("-")[1];
  } else if (expiresOn === "" && month === undefined && year === undefined) {
    month = "";
    year = "";
  }

  if (endDate === undefined) {
    endDate = "";
  }
  let createAPIQuery =
    `query{createAutoPay(input:{startDate: "` +
    startDate +
    `",
    endDate: "` +
    endDate +
    `",
    autoPaySourceCode: "` +
    sourceCode +
    `",
    extAcctId: "` +
    cardNumber +
    `",
    expiresOnMonth: "` +
    month +
    `",             
    expiresOnYear:"` +
    year +
    `"
    name: "` +
    cardName +
    `",
    maxWithdrawlAmt: "` +
    MAX_LIMIT_TENENT_CODE +
    `",
    mode: "",
    comments: ""
})}`;
  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.USAGE_SERVICE_BASE_URL,
        {
          query: createAPIQuery,
        },
        {
          headers: Headers,
        },
      )
      .then(function (response) {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
  // return apolloClientService.ApolloClientgqlsUsage(createAPIQuery, Headers, "network-only");
}

function getDropdownpData(groupCode, languageCode) {
  let APIquery =
    `{  getDropdownData(input:{groupCode:"` +
    groupCode +
    `",languageCode:"` +
    languageCode +
    `"})
     }`;
  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.ADMIN_SERVICE_BASE_URL,
        {
          query: updateAPIQuery,
        },
        {
          headers: Headers,
        },
      )
      .then(function (response) {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
  // return apolloClientService.ApolloClientgqls(APIquery);
}

function getPaymentPreference(accountId) {
  let readQuery = `query {
    getAutoPayStatus 
    {
    description,
    status,
    notificationDesc
    }
}`;
  let Headers = { accountId: accountId };
  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.USAGE_SERVICE_BASE_URL,
        {
          query: readQuery,
        },
        {
          headers: Headers,
        },
      )
      .then(function (response) {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
  // return apolloClientService.ApolloClientgqlsUsage(readQuery, Headers, "network-only");
}

function getUpdatePreference(preferenceData, accountId) {
  let APIquery =
    `query {
    updateAutoPayStatus(input: {status: "` +
    preferenceData +
    `"}) {
        status
    }
} 
`;
  let Headers = { accountId: accountId };
  return new Promise((resolve, reject) => {
    axios
      .post(
        config.urls.USAGE_SERVICE_BASE_URL,
        {
          query: APIquery,
        },
        {
          headers: Headers,
        },
      )
      .then(function (response) {
        resolve(response.data);
      })
      .catch(function (error) {
        reject(error);
      });
  });
  // return apolloClientService.ApolloClientgqlsUsage(APIquery, Headers, "network-only");
}

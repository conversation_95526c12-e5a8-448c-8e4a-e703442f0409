import * as React from "react";
import { DataTable, Divider, Text, TextInput } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import { ScrollView } from "react-native-gesture-handler";
import { View } from "react-native";
import { useTranslation } from 'react-i18next';

const LabourTableGridWO = ({ labourType }) => {
  const { t } = useTranslation();
  const [page, setPage] = React.useState(0);
  const [numberOfItemsPerPageList] = React.useState([2, 3, 4]);
  const [itemsPerPage, onItemsPerPageChange] = React.useState(
    numberOfItemsPerPageList[0],
  );

  const [numberOfPeople, setNumberOfPeople] = React.useState("");
  const [hours, setHours] = React.useState("");

  const [items] = React.useState([
    {
      key: 1,
      labourType: labourType,
      numberOfLabour: 4,
      UOM: "Hrs.",
    },
  ]);

  const from = page * itemsPerPage;
  const to = Math.min((page + 1) * itemsPerPage, items.length);

  React.useEffect(() => {
    setPage(0);
  }, [itemsPerPage]);

  return (
    <>
      <View>
        <Text
          style={{
            paddingLeft: 16,
          }}>
          {t('LABOUR')}
        </Text>
      </View>
      <DataTable
        style={{
          paddingBottom: 10,
          marginBottom: 10,
        }}>
        <Divider />
        <ScrollView
          horizontal
          persistentScrollbar
          contentContainerStyle={{ flexDirection: "column" }}>
          <DataTable.Header>
            <DataTable.Title style={{ width: 100 }}>
              {t('LABOUR_TYPE')}
            </DataTable.Title>
            <DataTable.Title style={{ width: 90 }}>
              {t('TIME_PLANNED')}
            </DataTable.Title>
            <DataTable.Title style={{ width: 80 }}>{t('TIME_ACTUAL')}</DataTable.Title>
            <DataTable.Title style={{ width: 100 }}>{t('UOM')}</DataTable.Title>
          </DataTable.Header>

          {items.slice(from, to).map(item => (
            <DataTable.Row
              key={item.key}
              style={{ flex: 1, width: "100%", height: 50 }}>
              <DataTable.Cell style={{ width: 100 }}>
                {item.labourType}
              </DataTable.Cell>

              <DataTable.Cell style={{ width: 90 }}>
                {item.numberOfLabour}
              </DataTable.Cell>
              <DataTable.Cell
                style={{
                  width: 80,
                }}>
                <TextInput
                  placeholderTextColor="gray"
                  mode="outlined"
                  value={hours}
                  enablesReturnKeyAutomatically
                  onChangeText={hours => setHours(hours)}
                  placeholder="0"
                  outlineColor={GlobalStyles.colors.eDark.hover}
                  activeOutlineColor={GlobalStyles.colors.ePrimary.base}
                  persistentScrollbar={true}
                  style={{
                    height: 25,
                  }}
                />
              </DataTable.Cell>
              <DataTable.Cell style={{ width: 100 }}>{item.UOM}</DataTable.Cell>
            </DataTable.Row>
          ))}
        </ScrollView>
      </DataTable>
    </>
  );
};

export default LabourTableGridWO;

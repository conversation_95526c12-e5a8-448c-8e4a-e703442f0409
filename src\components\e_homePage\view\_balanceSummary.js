import { useEffect, useState } from "react";
import { StyleSheet, ImageBackground, View, Dimensions } from "react-native";
import { Text, DataTable, Card } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import Button from "../../common/_button";
import FlatButton from "../../common/_flat_button";
import moment from "moment";
import { useDispatch, useSelector } from "react-redux";
import { useNavigation } from "@react-navigation/native";
import { servicePath } from "../../../redux/slices/servicePath";
import { Linking } from "react-native";
import { config } from "../../../environment";
import AsyncStorage from "@react-native-async-storage/async-storage";

export default function BalanceSummary() {
  const [currency, setCurrency] = useState();
  const [balance, setBalance] = useState();
  const [dueDate, setDueDate] = useState();
  const accountData = useSelector((state) => state.meterDetails?.meterDetails);
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const [bearer, setbearer] = useState();
  const isPrepaid = useSelector(
    (state) => state?.meterDetails?.meterDetails?.getSaInfo?.isPrepaidSa
  );
  const accountId = useSelector(
    (state) => state?.meterDetails?.meterDetails?.accountId
  );
  const saId = useSelector(
    (state) => state?.meterDetails?.meterDetails?.getSaInfo?.saId
  );
  const autoPayLink = () => {
    dispatch(servicePath("payments_preference"));
    navigation.navigate("PreferencesTab");
  };

  useEffect(() => {
    if (accountData) {
      setCurrency(accountData?.curSymbol);
      setBalance(accountData?.currentBalance);
      setDueDate(accountData?.latestDueDate);
    }
  }, [accountData]);

  useEffect(() => {
    AsyncStorage.getItem("bearer").then((bearer) => {
      bearer = JSON.parse(bearer);
      setbearer(bearer);
    });
  }, []);

  const makePayments = () => {
    navigation.navigate("Payments");
    dispatch(servicePath("Payments"));
  };
  return (
    <ImageBackground
      source={require("../../../../assets/bg.png")}
      style={styles.bgImage}
      imageStyle={styles.imgStyle}
      resizeMode="cover"
    >
      <Text style={styles.boldFont}>BALANCE SUMMARY</Text>
      {/* <View style={styles.content}>
        <View style={styles.leftView}>
          <FlatButton
            textStyles={styles.linkWhite}
            onPress={() => {
              Linking.openURL(
                config.constants.BASE_URL +
                  "/nativeRedirect?nativeAppToken=" +
                  bearer.acessToken +
                  "&path=bill-details" +
                  "&accountNumber=" +
                  accountId +
                  "&serviceID=" +
                  saId
              );
            }}
          >
            Latest Bill
          </FlatButton>
          <FlatButton
            textStyles={styles.linkWhite}
            onPress={() =>
              Linking.openURL(
                config.constants.BASE_URL +
                  "/nativeRedirect?nativeAppToken=" +
                  bearer.acessToken +
                  "&path=billing" +
                  "&accountNumber=" +
                  accountId +
                  "&serviceID=" +
                  saId
              )
            }
          >
            Past Payments
          </FlatButton>
          {isPrepaid !== undefined && isPrepaid === "N" && (
            <FlatButton textStyles={styles.linkWhite} onPress={autoPayLink}>
              Auto Pay
            </FlatButton>
          )}
        </View>
        <View style={styles.rightView}>
          <Text style={styles.italictext}>Total Outstanding</Text>
          <Text style={styles.bigText}>
            {currency && balance ? (
              <>
                {currency && currency.length > 1
                  ? balance + " " + currency
                  : currency + "" + balance}
              </>
            ) : null}
          </Text>
          {dueDate !== undefined &&
            isPrepaid !== undefined &&
            isPrepaid === "N" && (
              <View style={styles.rightAlign}>
                <Text style={[styles.italictextDue]}>Due on:</Text>
                <Text style={styles.text}>
                  {moment(dueDate, "YYYY-MM-DD").format("DD MMM YYYY")}
                </Text>
              </View>
            )}
          <View style={styles.buttonStyle}>
            <Button
              buttonbgColor={styles.buttonBgColor}
              textColor={styles.blueText}
              disabled={false}
              onPress={() => makePayments()}
            >
              {isPrepaid !== undefined && isPrepaid === "N"
                ? "Make Payments"
                : "Recharge"}
            </Button>
          </View>
        </View>
      </View> */}
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  bgImage: {
    padding: 15,
    paddingRight: 18,
  },
  imgStyle: {
    borderRadius: 20,
    // flex:1
    // boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
  },
  text: {
    color: GlobalStyles.colors.eTertiary.base,
    fontFamily: "NotoSans-Bold",
    fontSize: 14,
    marginTop: -3,
  },
  boldFont: {
    fontSize: 12,
    color: GlobalStyles.colors.eWhite.base,
    fontFamily: "NotoSans-Bold",
  },
  buttonBgColor: {
    width: "80%",
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingVertical: "4%",
    alignSelf: "flex-end",
  },
  blueText: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 11,
    fontFamily: "NotoSans-Medium",
  },
  leftView: {
    float: "left",
    marginTop: "6%",
    alignSelf: "stretch",
    width: "40%",
  },
  rightView: {
    float: "right",
    width: "60%",
  },
  content: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
  },
  linkWhite: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 10,
    textDecorationLine: "underline",
    paddingVertical: "5%",
    paddingHorizontal: "5%",
  },
  italictext: {
    fontStyle: "italic",
    color: GlobalStyles.colors.eWhite.base,
    textAlign: "right",
    fontSize: 10,
    marginRight: 12,
    fontFamily: "NotoSans-Medium",
  },
  italictextDue: {
    fontFamily: "NotoSans-MediumItalic",
    color: GlobalStyles.colors.eWhite.base,
    textAlign: "left",
    fontSize: 9,
    marginRight: 20,
  },
  bigText: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 22,
    textAlign: "right",
    fontFamily: "NotoSans-SemiBold",
    marginRight: 10,
  },
  bigTextTest: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 22,
    textAlign: "right",
    fontFamily: "NotoSans-Bold",
    marginRight: 10,
  },
  rightAlign: {
    alignSelf: "flex-end",
    right: "7%",
    marginTop: 20,
  },
  buttonStyle: {
    marginTop: "5%",
    marginRight: "5%",
    marginBottom: "5%",
  },
  leftAlign: {
    textAlign: "left",
  },
});

import { Pressable, StyleSheet, Text, View } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { GlobalStyles } from "../../app/global-styles";
import { IconButton } from "react-native-paper";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import React, { useContext, useState } from "react";
import { useDispatch } from "react-redux";
import { servicePath } from "../../../redux/slices/servicePath";
import Icon from "../../icon";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { config } from "../../../environment";
import { stackContext } from "../../app/get_stack";

export default function DrawerMenuItem({ name, navigationTab, imageSrc }) {
  const { workModelType } = React.useContext(stackContext);
  const navigation = useNavigation();
  const [colorChange, setColorChange] = useState(false);
  let openDrawer, setOpenMenu, menuFlag, setmenuFlag;

  if (workModelType === "WA") {
    ({ openDrawer, setOpenMenu, menuFlag, setmenuFlag } =
      useContext(drawerContext));
  } else {
    ({ openDrawer, setOpenMenu, menuFlag, setmenuFlag } =
      useContext(drawerContextWO));
  }

  const dispatch = useDispatch();

  const onPressItem = () => {
    setColorChange(true);
    navigation.navigate(navigationTab);
    setOpenMenu(false);
    if (navigationTab === "Services") {
      setOpenMenu(true);
      setmenuFlag(false);
      dispatch(servicePath("PastTickets"));
    } else {
      dispatch(servicePath(navigationTab));
    }
  };

  return (
    <Pressable onPress={onPressItem}>
      <View
        style={[
          styles.navItemStyle,
          colorChange && { backgroundColor: GlobalStyles.colors.ePrimary.base },
        ]}>
        <View
          style={[
            styles.iconView,
            colorChange
              ? { backgroundColor: GlobalStyles.colors.eWhite.base }
              : { backgroundColor: GlobalStyles.colors.ePrimary.base },
          ]}>
          <Icon
            name={imageSrc}
            size={20}
            color={
              colorChange
                ? GlobalStyles.colors.ePrimary.base
                : GlobalStyles.colors.eWhite.base
            }
          />
        </View>
        <Text style={styles.navTextStyle}>{name}</Text>
      </View>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  iconView: {
    height: 40,
    width: 40,
    borderRadius: 25,
    marginLeft: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  navItemStyle: {
    height: 50,
    margin: 5,
    flexDirection: "row",
    alignItems: "center",
  },
  navTextStyle: {
    marginLeft: 15,
    fontSize: 13,
    marginTop: -1,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  iconStyle: {
    height: "100%",
    margin: 0,
  },
});

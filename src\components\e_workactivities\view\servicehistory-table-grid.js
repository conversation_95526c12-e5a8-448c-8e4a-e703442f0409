import React, { useEffect, useRef, useState } from "react";
import { Text, TextInput, Checkbox, RadioButton } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import { View, StyleSheet, Modal, Pressable, Button } from "react-native";
import { useTranslation } from "react-i18next";
import { SafeAreaView, SafeAreaProvider } from "react-native-safe-area-context";
import Icon from "react-native-vector-icons/FontAwesome";
import { ScrollView } from "react-native-gesture-handler";

const ServiceHostoryTableGrid = props => {
  let {
    serviceToggle, //to enable pop up only when this page is opened
    data,
    setFinalShData,
    disableStart,
    workStatus,
    items,
    setItems,
    checkboxData,
    setCheckboxData,
    simpleTextData,
    setSimpleTextData,
    setSafecheckboxData,
    safecheckboxData,
  } = props;
  console.log(data, "service history object-------------12");
  const { t } = useTranslation();

  const [page, setPage] = React.useState(0);
  const [itemsPerPage, onItemsPerPageChange] = React.useState(10);
  const [loadItems, setLoadItems] = React.useState(false); //to ensure only questions are loaded only once into checkboxData,simpleTextData

  const [disableTextInp, setDisableTextInp] = useState(true);
  const [showText, setShowText] = useState(false);

  const [modalVisible, setModalVisible] = useState(true);
  React.useEffect(() => {
    let tempData = data;
    tempData = JSON.parse(tempData);
    let resp = getActivityCheckList(tempData);
    if (items?.length == 0) {
      // if items already exisitng, do not load the items again. // this will help to prevent the state even accordian is closed and opened again.
      setItems(resp);
      setFinalShData(resp);
    }
  }, []);

  React.useEffect(() => {
    if (items?.length > 0 && loadItems == false) {
      items?.forEach(each => {
        console.log(each, "UUUUUUUUUUUUUUUUUUUUUUUUUU----------50");
        if (each?.description?.toLowerCase().includes("safety checklist")) {
          if (Array.isArray(each?.questionnaireDetails.sections.section)) {
            each?.questionnaireDetails.sections.section?.forEach(eachQue => {
              console.log(eachQue, "eachQue");
              let inputType =
                eachQue.questions.question.style ||
                eachQue.questions.question[0].style ||
                eachQue.questions.question.style ||
                eachQue.questions.question[0]?.style;
              if (inputType == "W1CB") {
                setSafecheckboxData(prevData => ({
                  ...prevData,
                  [eachQue.shortText]: {
                    status: eachQue.questions.question?.responseCheckbox,
                    disabled: disableStart,
                  },
                }));
              } else if (inputType == "W1RB") {
                setSafecheckboxData(prevData => ({
                  ...prevData,
                  [eachQue.shortText]: {
                    status: eachQue.questions.question?.responseRadio,
                    disabled: disableStart,
                  },
                }));
              }
            });
          }
        }
        if (Array.isArray(each?.questionnaireDetails.sections.section)) {
          each?.questionnaireDetails.sections.section?.forEach(eachQue => {
            console.log(eachQue, "eachQue");
            let inputType =
              eachQue.questions.question.style ||
              eachQue.questions.question[0].style ||
              eachQue.questions.question.style ||
              eachQue.questions.question[0]?.style;
            if (inputType == "W1CB") {
              setCheckboxData(prevData => ({
                ...prevData,
                [eachQue.shortText]: {
                  status: eachQue.questions.question?.responseCheckbox,
                  disabled: disableStart,
                },
              }));
            } else if (inputType == "W1RB") {
              setCheckboxData(prevData => ({
                ...prevData,
                [eachQue.shortText]: {
                  status: eachQue.questions.question?.responseRadio,
                  disabled: disableStart,
                },
              }));
            } else if (inputType == "W1ST") {
              setSimpleTextData(prevData => ({
                ...prevData,
                [eachQue.shortText]: {
                  answer: eachQue.questions.question?.response
                    ? eachQue.questions.question?.response
                    : eachQue.shortText?.answer
                    ? eachQue.shortText?.answer
                    : "",
                  disabled: true,
                },
              }));
            }
          });
        }
      });
      setLoadItems(true);
    }
  }, [loadItems, items, disableStart]);
  const handleCheckboxToggle = (
    key,
    each,
    questionCode,
    questionType,
    newRadioCbValue,
  ) => {
    // Toggle the checkbox state
    //key==question
    //each== question object
    //  each.answerResp = !checkboxData[key].status;

    setCheckboxData(prev => ({
      ...prev,
      [key]: {
        //status: !checkboxData[key].status,
        status: newRadioCbValue,
        disabled: disableStart,
      },
    }));
    if (safecheckboxData[key]) {
      setSafecheckboxData(prev => ({
        ...prev,
        [key]: {
          //status: !checkboxData[key].status,
          status: newRadioCbValue,
          disabled: disableStart,
        },
      }));
    }
    // checkboxStates.current[key] = !checkboxStates.current[key];
    console.log(items, "UUUUUUUUUUUUUUUUUUUUUUUUUUUUUU 81");

    let tempUpdate = items?.map(e => {
      e?.questionnaireDetails?.sections?.section?.map(ques => {
        console.log(ques, "YYYYYYYYYYYYYYYYYYYYYYYY 93");
        let question = ques?.shortText["__cdata"]
          ? ques?.shortText["__cdata"]
          : ques?.shortText;
        if (question == key) {
          if (
            Array.isArray(ques.questions.question) &&
            ques.questions.question[0]
          ) {
            if (questionType == "W1RB") {
              if (ques.questions.question[0]?.validValue) {
                // pass one among valid values..
                let validVals = ques.questions.question?.validValue?.map(
                  e => e?.text,
                );
                // if (temp?.includes("Yes")) {
                //   ques.questions.question.responseRadio =
                //     !checkboxData[key].status == true ? "Yes" : "No";
                //   return {
                //     ...ques,
                //   };
                // } else if (temp.includes("OK")) {
                //   ques.questions.question.responseRadio =
                //     !checkboxData[key].status == true ? "OK" : "Not OK";
                //   return {
                //     ...ques,
                //   };
                // }
                console.log(
                  validVals,
                  ques.questions.question?.validValue,
                  "ques.questions.question----------------------173",
                );
              } else {
                //response radio will be the code..
                ques.questions.question.responseRadio =
                  !checkboxData[key].status == true
                    ? ques.questions.question["code"]
                    : "";
                return {
                  ...ques,
                };
              }
              ques.questions.question[0].responseRadio =
                !checkboxData[key].status == true ? "OK" : "Not OK";
              return {
                ...ques,
              };
            } else if (questionType == "W1CB") {
              if (ques.questions.question) {
                let temp = ques.questions.question;
                // if (temp?.includes("Yes")) {
                //   ques.questions.question.responseRadio =
                //     !checkboxData[key].status == true ? "Yes" : "No";
                //   return {
                //     ...ques,
                //   };
                // } else if (temp.includes("OK")) {
                //   ques.questions.question.responseRadio =
                //     !checkboxData[key].status == true ? "OK" : "Not OK";
                //   return {
                //     ...ques,
                //   };
                // }
                console.log(
                  temp,
                  "ques.questions.question----------------------173",
                );
              } else {
                ques.questions.question.responseCheckbox =
                  !checkboxData[key].status == true
                    ? // ? ques.questions.question["code"]
                      "Y"
                    : "N";
                return {
                  ...ques,
                };
              }
              ques.questions.question[0].responseCheckbox =
                !checkboxData[key].status == true ? "Y" : "N";
              return {
                ...ques,
              };
            }
          } else {
            if (questionType == "W1RB") {
              if (ques.questions.question?.validValue) {
                let temp = ques.questions.question?.validValue?.map(
                  e => e?.text,
                );
                if (temp?.includes("Yes")) {
                  ques.questions.question.responseRadio =
                    !checkboxData[key].status == true ? "Yes" : "No";
                  return {
                    ...ques,
                  };
                } else if (temp.includes("OK")) {
                  ques.questions.question.responseRadio =
                    !checkboxData[key].status == true ? "OK" : "Not OK";
                  return {
                    ...ques,
                  };
                }
                console.log(
                  temp,
                  ques.questions.question?.validValue,
                  "ques.questions.question----------------------173",
                );
              } else {
                console.log(
                  ques.questions.question["code"],
                  "ques.questions.question----------------------178",
                );
                ques.questions.question.responseRadio =
                  !checkboxData[key].status == true
                    ? ques.questions.question["code"]
                    : "";
                return {
                  ...ques,
                };
              }
            } else if (questionType == "W1CB") {
              if (ques.questions.question?.validValue) {
                let temp = ques.questions.question?.validValue?.map(
                  e => e?.text,
                );
                if (temp?.includes("Yes")) {
                  ques.questions.question.responseCheckbox =
                    !checkboxData[key].status == true ? "Y" : "N";
                  return {
                    ...ques,
                  };
                } else if (temp.includes("OK")) {
                  ques.questions.question.responseCheckbox =
                    !checkboxData[key].status == true ? "OK" : "Not OK";
                  return {
                    ...ques,
                  };
                }
                console.log(
                  temp,
                  ques.questions.question?.validValue,
                  "ques.questions.question----------------------173",
                );
                console.log(
                  ques.questions.question?.validValue,
                  "ques.questions.question----------------------177",
                );
              } else {
                console.log(
                  ques.questions.question["code"],
                  "ques.questions.question----------------------182",
                );
                ques.questions.question.responseCheckbox =
                  !checkboxData[key].status == true
                    ? //? ques.questions.question["code"]
                      "Y"
                    : "N";
                return {
                  ...ques,
                };
              }
              // ques.questions.question.responseRadio =
              //   !checkboxData[key].status == true ? "Yes" : "No";
              // return {
              //   ...ques,
              // };
            }
          }
        }
      });
      return e;
    });
    setItems(tempUpdate);
    setFinalShData(tempUpdate);
  };

  const getActivityCheckList = data => {
    let serviceHistoryTypes = data?.filter(
      e => e?.serviceHistoryBo == "W1-QuestionnaireSvcHist",
    );
    return serviceHistoryTypes;
  };

  React.useEffect(() => {}, [data]);

  React.useEffect(() => {
    setPage(0);
  }, [itemsPerPage]);

  const handleInputChange = (value, que) => {
    setSimpleTextData(prev => ({
      ...prev,
      [que]: value,
    }));
    let tempUpdate = items?.map(e => {
      e?.questionnaireDetails?.sections?.section?.map(ques => {
        let question = ques?.shortText["__cdata"]
          ? ques?.shortText["__cdata"]
          : ques?.shortText;
        if (question == que) {
          if (ques.questions.question[0]) {
            ques.questions.question[0].response = value; //setting response here..
            return {
              ...ques,
            };
          } else {
            ques.questions.question.response = value; //setting response here..
            return ques;
          }
        }
      });
      return e;
    });
    setItems(tempUpdate);
    setFinalShData(tempUpdate);

    //each.answerObj.question.response = value;
    //inputRefs.current[key] = value;
  };

  useEffect(() => {
    console.log(
      serviceToggle,
      Object.keys(checkboxData).length,
      "UUUUUUUUUUUUUUUUUUUUUUUUU99990",
    );
    if (serviceToggle == true) {
      if (Object.keys(safecheckboxData).length > 0) {
        setModalVisible(true);
        const areAllStatusesTrue = Object.values(safecheckboxData).every(
          item => item.status !== undefined,
        );

        if (areAllStatusesTrue) {
          setShowText(true);
          if (workStatus != "CO") {
            setDisableTextInp(false);
          }
        } else {
          if (workStatus == "CO" || workStatus == "CL") {
            setShowText(true);
          } else {
            setShowText(false);
          }
          setDisableTextInp(true);
        }
      } else if (Object.keys(safecheckboxData).length == 0) {
        setShowText(true);
        setModalVisible(false);
        if (workStatus == "CO" || workStatus == "CL") {
          setDisableTextInp(true);
        } else {
          setDisableTextInp(false);
        }
      }
    }
  }, [serviceToggle, Object.keys(safecheckboxData).length > 0]);

  useEffect(() => {
    const areAllStatusesTrue = Object.values(safecheckboxData).every(
      item => item.status !== undefined,
    );
    if (areAllStatusesTrue) {
      setShowText(true);
    } else {
      setShowText(false);
    }
    if (workStatus == "CO" || workStatus == "CL") {
      setDisableTextInp(true);
    } else {
      setDisableTextInp(false);
    }
  }, [serviceToggle, safecheckboxData]);

  const getRadioButtonQuestions = (each, data) => {
    if (each?.description?.toLowerCase().includes("safety checklist")) {
      return data?.map(ques => {
        let questionType = ques?.questions?.question?.style
          ? ques?.questions?.question?.style
          : ques?.questions?.question[0]?.style;
        let question = ques?.shortText["__cdata"]
          ? ques?.shortText["__cdata"]
          : ques?.shortText;
        let questionCode = ques?.code;
        // if (questionType == "W1RB") {
        //   console.log(
        //     ques?.questions?.question?.validValue?.length,
        //     "KKKKKKKKKKKKKKKKKKKKKKKKKKUUUUUUUUUU567890",
        //   );
        // }

        console.log(
          checkboxData,
          safecheckboxData,
          "KKKKKKKKKKKKKKKKKKKKKKKKKKUUUUUUUUUU376376",
        );

        return (
          <>
            {questionType == "W1RB" ? (
              <View>
                <Text style={{ fontWeight: "bold" }}>{question}</Text>
                <View>
                  {ques?.questions?.question?.validValue?.length > 0 ? (
                    <RadioButton.Group
                      onValueChange={newRadioVal =>
                        handleCheckboxToggle(
                          question,
                          each,
                          questionCode,
                          questionType,
                          newRadioVal,
                        )
                      }
                      value={checkboxData[question]?.status}>
                      {ques?.questions?.question?.validValue?.map(eachValid => {
                        return (
                          <View>
                            <RadioButton.Item
                              disabled={disableStart}
                              color={GlobalStyles.colors.ePrimary.base}
                              style={{
                                flexDirection: "row",
                                alignItems: "center",
                                position: "relative",
                              }}
                              key={question + eachValid?.shortText}
                              label={eachValid?.shortText}
                              value={eachValid?.shortText}
                            />
                          </View>
                        );
                      })}
                    </RadioButton.Group>
                  ) : (
                    <RadioButton.Group
                      onValueChange={newRadioVal =>
                        handleCheckboxToggle(
                          question,
                          each,
                          questionCode,
                          questionType,
                          newRadioVal,
                        )
                      }
                      disabled={disableStart}
                      value={checkboxData[question]?.status}>
                      <RadioButton.Item
                        color={GlobalStyles.colors.ePrimary.base}
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                          position: "relative",
                        }}
                        key={question + ques?.questions?.question?.shortText}
                        label={ques?.questions?.question?.shortText}
                        value={ques?.questions?.question?.shortText}
                      />
                    </RadioButton.Group>
                  )}
                </View>
              </View>
            ) : questionType == "W1CB" ? (
              <View>
                <Text style={{ fontWeight: "bold" }}>{question}</Text>
                <View>
                  {ques?.questions?.question?.length > 0 ? (
                    ques?.questions?.question?.map(eachValid => {
                      console.log(
                        eachValid,
                        "TTTTTTTTTTTTTTTTRRRRRRRRRRRRRRRRRRRR 552",
                      );
                      return (
                        eachValid?.style == "W1CB" && (
                          <Checkbox.Item
                            color={GlobalStyles.colors.ePrimary.base}
                            key={question + eachValid?.shortText}
                            ref={ref => setCheckboxRef(question, ref)}
                            label={eachValid?.shortText}
                            labelStyle={styles.labelCheck}
                            status={
                              checkboxData[question]?.status == "Y"
                                ? "checked"
                                : "unchecked"
                            }
                            disabled={disableStart}
                            onPress={() =>
                              handleCheckboxToggle(
                                question,
                                each,
                                questionCode,
                                questionType,
                                checkboxData[question]?.status == undefined
                                  ? "Y"
                                  : checkboxData[question]?.status == "Y"
                                  ? "N"
                                  : "Y",
                              )
                            }
                          />
                        )
                      );
                    })
                  ) : (
                    <>
                      <Checkbox.Item
                        color={GlobalStyles.colors.ePrimary.base}
                        key={ques?.questions?.question?.shortText}
                        ref={ref => setCheckboxRef(question, ref)}
                        label={ques?.questions?.question?.shortText}
                        labelStyle={styles.labelCheck}
                        status={
                          checkboxData[question]?.status == "Y"
                            ? "checked"
                            : "unchecked"
                        }
                        disabled={disableStart}
                        onPress={() =>
                          handleCheckboxToggle(
                            question,
                            each,
                            questionCode,
                            questionType,
                            checkboxData[question]?.status == undefined
                              ? "Y"
                              : checkboxData[question]?.status == "Y"
                              ? "N"
                              : "Y",
                          )
                        }
                      />
                    </>
                  )}
                </View>
              </View>
            ) : null}
          </>
        );
      });
    } //safety checklist end
  };

  const getRadioButtonQuestionsWithOutSafetyCheckList = (each, data) => {
    console.log(each?.description, data, "355ques-------------------180");
    if (!each?.description?.toLowerCase().includes("safety checklist")) {
      return data?.map(ques => {
        console.log(ques, "444ques-------------------180");
        let questionType = ques?.questions?.question?.style
          ? ques?.questions?.question?.style
          : ques?.questions?.question[0]?.style;
        let question = ques?.shortText["__cdata"]
          ? ques?.shortText["__cdata"]
          : ques?.shortText;
        let questionCode = ques?.code;
        // if (questionType == "W1RB") {
        //   console.log(
        //     ques?.questions?.question?.validValue?.length,
        //     "KKKKKKKKKKKKKKKKKKKKKKKKKKUUUUUUUUUU567890",
        //   );
        // }

        console.log(checkboxData, "KKKKKKKKKKKKKKKKKKKKKKKKKKUUUUUUUUUU376376");

        return (
          <>
            {questionType == "W1RB" ? (
              <View>
                <Text style={{ fontWeight: "bold" }}>{question}</Text>
                <View>
                  {ques?.questions?.question?.validValue?.length > 0 ? (
                    <RadioButton.Group
                      onValueChange={newRadioVal =>
                        handleCheckboxToggle(
                          question,
                          each,
                          questionCode,
                          questionType,
                          newRadioVal,
                        )
                      }
                      value={checkboxData[question]?.status}>
                      {ques?.questions?.question?.validValue?.map(eachValid => {
                        return (
                          <View>
                            <RadioButton.Item
                              disabled={disableStart}
                              color={GlobalStyles.colors.ePrimary.base}
                              style={{
                                flexDirection: "row",
                                alignItems: "center",
                                position: "relative",
                              }}
                              key={question + eachValid?.shortText}
                              label={eachValid?.shortText}
                              value={eachValid?.shortText}
                            />
                          </View>
                        );
                      })}
                    </RadioButton.Group>
                  ) : (
                    <RadioButton.Group
                      onValueChange={newRadioVal =>
                        handleCheckboxToggle(
                          question,
                          each,
                          questionCode,
                          questionType,
                          newRadioVal,
                        )
                      }
                      disabled={disableStart}
                      value={checkboxData[question]?.status}>
                      <RadioButton.Item
                        color={GlobalStyles.colors.ePrimary.base}
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                          position: "relative",
                        }}
                        key={question + ques?.questions?.question?.shortText}
                        label={ques?.questions?.question?.shortText}
                        value={ques?.questions?.question?.shortText}
                      />
                    </RadioButton.Group>
                  )}
                </View>
              </View>
            ) : questionType == "W1CB" ? (
              <View>
                <Text style={{ fontWeight: "bold" }}>{question}</Text>
                <View>
                  {ques?.questions?.question?.length > 0 ? (
                    ques?.questions?.question?.map(eachValid => {
                      console.log(
                        eachValid,
                        "TTTTTTTTTTTTTTTTRRRRRRRRRRRRRRRRRRRR 552",
                      );
                      return (
                        eachValid?.style == "W1CB" && (
                          <Checkbox.Item
                            color={GlobalStyles.colors.ePrimary.base}
                            key={question + eachValid?.shortText}
                            ref={ref => setCheckboxRef(question, ref)}
                            label={eachValid?.shortText}
                            labelStyle={styles.labelCheck}
                            status={
                              checkboxData[question]?.status == "Y"
                                ? "checked"
                                : "unchecked"
                            }
                            disabled={disableStart}
                            onPress={() =>
                              handleCheckboxToggle(
                                question,
                                each,
                                questionCode,
                                questionType,
                                checkboxData[question]?.status == "Y"
                                  ? "N"
                                  : "Y",
                              )
                            }
                          />
                        )
                      );
                    })
                  ) : (
                    <>
                      <Checkbox.Item
                        color={GlobalStyles.colors.ePrimary.base}
                        key={ques?.questions?.question?.shortText}
                        ref={ref => setCheckboxRef(question, ref)}
                        label={ques?.questions?.question?.shortText}
                        labelStyle={styles.labelCheck}
                        status={
                          checkboxData[question]?.status == "Y"
                            ? "checked"
                            : "unchecked"
                        }
                        disabled={disableStart}
                        onPress={() =>
                          handleCheckboxToggle(
                            question,
                            each,
                            questionCode,
                            questionType,
                            checkboxData[question]?.status == "" || undefined
                              ? "Y"
                              : "N",
                          )
                        }
                      />
                    </>
                  )}
                </View>
              </View>
            ) : null}
          </>
        );
      });
    } //safety checklist end
  };

  const getTextboxQuestions = (each, data) => {
    return data?.map(ques => {
      let code = ques?.code;
      let questionType = ques?.questions?.question?.style
        ? ques?.questions?.question?.style
        : ques?.questions?.question[0]?.style;
      let question = ques?.shortText["__cdata"]
        ? ques?.shortText["__cdata"]
        : ques?.shortText;

      return (
        <>
          {questionType == "W1ST" && (
            <>
              <View>
                <Text style={styles.label}>{question}</Text>
                <TextInput
                  mode="outlined"
                  value={simpleTextData[question]?.answer}
                  onChangeText={quantity =>
                    handleInputChange(quantity, question, code)
                  }
                  disabled={disableTextInp}
                  style={styles.input}
                  theme={{
                    colors: {
                      text: GlobalStyles.colors.eDark.base,
                      primary: GlobalStyles.colors.eDark.base,
                      accent: GlobalStyles.colors.eDark.base,
                    },
                  }}
                />
              </View>
            </>
          )}
        </>
      );
    });
  };
  return (
    <View style={styles.containerChecklist}>
      <View style={styles.lineStyleInfoCheckList} />
      <SafeAreaProvider>
        <SafeAreaView style={styles.centeredView}>
          <Modal
            animationType="slide"
            transparent={true}
            visible={modalVisible}
            onRequestClose={() => {
              setModalVisible(!modalVisible);
            }}>
            <View style={styles.centeredView}>
              <View style={styles.modalView}>
                <View style={styles.modalText}>
                  <View>
                    <Text style={{ fontWeight: "bold" }}>
                      {workStatus == "CO" || workStatus == "CL"
                        ? t("CHECKLIST")
                        : t("COMPLETE_LIST_PROMPT")}
                    </Text>
                  </View>
                  <View>
                    <Icon
                      name="close"
                      size={20}
                      color={GlobalStyles.colors.ePrimary.base}
                      style={{
                        alignSelf: "flex-end",
                        alignItems: "flex-end",
                      }}
                      onPress={() => {
                        setModalVisible(false);
                      }}
                    />
                  </View>
                </View>
                <View style={styles.hr} />
                <ScrollView>
                  <Pressable>
                    <View style={{ paddingLeft: 20, overflow: "scroll" }}>
                      {items?.map(each => {
                        return (
                          <>
                            <View key={each.serviceHistoryType}>
                              {getRadioButtonQuestions(
                                each,
                                each?.questionnaireDetails?.sections?.section,
                              )}
                            </View>
                          </>
                        );
                      })}

                      <View
                        style={{
                          flexDirection: "row",
                          justifyContent: "flex-end", // Space buttons out or use 'center'
                          paddingVertical: 10,
                          marginRight: 20,
                        }}>
                        <Button
                          color={GlobalStyles.colors.eSecondary.base}
                          disabled={!showText}
                          title={t("DONE")}
                          onPress={() => setModalVisible(false)}
                          style={{
                            borderRadius: 15,
                          }}
                        />
                      </View>
                    </View>
                  </Pressable>
                </ScrollView>
              </View>
            </View>
          </Modal>
        </SafeAreaView>
      </SafeAreaProvider>
      {showText && modalVisible == false && (
        <View style={{ paddingLeft: 20 }}>
          {items?.map(each => {
            return (
              <>
                <View style={{ marginBottom: 10 }}>
                  {getTextboxQuestions(
                    each,
                    each?.questionnaireDetails?.sections?.section,
                  )}
                </View>
                <View>
                  {getRadioButtonQuestionsWithOutSafetyCheckList(
                    each,
                    each?.questionnaireDetails?.sections?.section,
                  )}
                </View>
              </>
            );
          })}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  hr: {
    borderBottomWidth: 1, // Thickness of the line
    borderBottomColor: "black", // Line color
    width: "100%", // Full width
    marginVertical: 10, // Space above and below the line
  },
  centeredView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  modalView: {
    maxHeight: "50%",
    width: "95%",
    margin: 2,
    backgroundColor: "white",
    borderRadius: 10,
    borderWidth: 1,
    borderStyle: "solid",
    borderColor: GlobalStyles.colors.ePrimary.base,
    padding: 5,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  button: {
    borderRadius: 5,
    padding: 10,
    elevation: 2,
  },
  buttonOpen: {
    backgroundColor: GlobalStyles.colors.ePrimary.base,
  },
  buttonClose: {
    backgroundColor: GlobalStyles.colors.ePastelColor2.base,
  },
  textStyle: {
    color: "white",
    fontWeight: "bold",
    textAlign: "center",
  },
  modalText: {
    fontSize: 13,
    paddingTop: 10,
    paddingHorizontal: 10,
    width: "100%",
    marginBottom: 15,
    textAlign: "flex-start",
    //backgroundColor: GlobalStyles.colors.ePrimary.base,
    color: GlobalStyles.colors.ePrimary.base,
    fontWeight: "bold",
    flexDirection: "row", // Horizontal alignment
    justifyContent: "space-between", // Space items evenly,
    fontWeight: "bold",
  },
  label: {
    //fontSize: 18, // Change font size
    // color: GlobalStyles.colors.ePrimary.base, // Change label color
    fontWeight: "bold", // Make the label bold
    // marginLeft: 10, // Add space between checkbox and label
  },
  labelCheck: {
    fontSize: 13, // Set font size,
    position: "relative",
    left: -15,
    right: -1,
  },
  card: {
    borderTopEndRadius: 10,
    borderTopStartRadius: 10,
    borderBottomEndRadius: 0,
    borderBottomStartRadius: 0,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    padding: 15,
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    margin: 10,
  },
  imgContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 0,
  },
  imgSideBySideView: {
    flex: 1,
    padding: 0,
  },
  imgSideBySideView2: {
    flex: 1,
    marginBottom: 10,
  },
  wrapDirection: {
    flexDirection: "row",
    flexWrap: "wrap",
    alignContent: "space-between",
  },
  bigTextHeader: {
    color: GlobalStyles.colors.eRich.base,
    textAlign: "left",
    fontSize: 14,
    justifyContent: "flex-start",
    // wordBreak: "break-all",
  },
  titleCard: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontWeight: "700",
    fontFamily: "NotoSans-Bold",
  },
  backButtonWrapper: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 3,
  },
  paddingRight: {
    paddingRight: 10,
  },
  scrollViewContent: {
    flexGrow: 1,
    padding: 20, // Adjust the padding as per your design
  },
  checkListDescription: {
    fontSize: 14,
    color: GlobalStyles.colors.ePrimary.base,
  },
  checkListDoneBtnStyle: {
    marginVertical: 10,
    marginTop: 20,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  checkListTextInput: {
    // minWidth: 150,
    flex: 1,
    height: 30,
    justifyContent: "center",
    marginRight: 10,
  },
  checkListIdLabel: {
    fontFamily: "NotoSans-SemiBold",
    fontSize: 13,
  },
  checkListStatus: {
    fontSize: 13,
    fontFamily: "NotoSans-SemiBold",
  },
  checkListItemContainer: {
    marginLeft: -25,
    marginVertical: -5,
  },
  camClass: {
    marginLeft: 25,
  },
  container: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: 10,
    borderRadius: 5,
  },
  containerEquip: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    // marginHorizontal: 10,
    // borderRadius: 5,
  },
  containerDiv: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: 10,
    marginTop: 0,
    borderRadius: 5,
  },
  containerChecklist: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    marginHorizontal: 10,
    // marginTop: 15,
    borderRadius: 5,
  },
  containerNoChecklist: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    alignItems: "center",
    marginVertical: 15,
    borderRadius: 5,
  },
  containerSubmit: {
    marginHorizontal: 10,
    marginBottom: 150,
    marginTop: 15,
  },
  containerStyle: {
    backgroundColor: "white",
    padding: 70,
    marginHorizontal: 20,
  },
  displayFlex: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 20,
  },
  ePrimary: {
    color: GlobalStyles.colors.ePrimary.base,
  },
  flexRowCenter: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  flexWrap: {
    flex: 1,
  },
  workActivityDetailContainer: {
    paddingHorizontal: 8,
    paddingVertical: 10,
  },
  labelHeader: {
    fontFamily: "NotoSans-Bold",
    fontSize: 14,
    color: GlobalStyles.colors.ePrimary.base,
  },
  nolabelHeader: {
    fontFamily: "NotoSans-Bold",
    fontSize: 14,
    color: GlobalStyles.colors.ePrimary.base,
  },
  labelStyle: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eRich.base,
  },
  labelStyleError: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eDanger.dark,
  },
  inputContainer: {
    marginRight: 10,
    marginVertical: -20,
  },
  valueStyle: {
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  labelWidth: {
    flex: 1,
  },
  labelWidthMeter: {
    marginLeft: -25,
  },
  labelWidthDuration: {
    flex: 1,
  },
  labelWidthTime: {
    width: 150,
    marginHorizontal: 20,
    marginTop: 15,
    marginBottom: 10,
  },
  opacityDimmed: {
    opacity: 0.5,
  },
  otpTextInputStyle: {
    borderBottomWidth: 2,
    borderBottomColor: GlobalStyles.colors.ePrimary.base,
    color: GlobalStyles.colors.ePrimary.base,
  },
  overdueStyle: {
    color: GlobalStyles.colors.eTertiary.base,
  },
  pastDueStyle: {
    paddingVertical: 10,
    paddingHorizontal: 8,
    borderWidth: 1,
    borderColor: "#d5d5d5",
    backgroundColor: "#fef9e8",
    display: "flex",
    flexDirection: "row",
    gap: 10,
    alignItems: "center",
  },

  rowFlexContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 5,
  },
  rowFlexContainerError: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  rowFlexContainerDate: {
    marginHorizontal: 20,
    marginTop: 10,
  },
  snackbarWrapper: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
  textInputWrapper: {
    paddingVertical: 3,
    flex: 1,
    // width: Dimensions.get("screen").width - 100,
  },
  workOrderCompletionTextStyle: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 13,
  },
  activityItem: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    paddingHorizontal: 10,
    // marginTop: -5,
  },
  activityItemInner: {
    display: "flex",
    flexDirection: "row",
    flex: 1,
    gap: 20,
  },
  iconContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 10,
  },
  contentContainer: {
    flex: 1,
  },
  displayFlex: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  input: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    color: GlobalStyles.colors.eDark.base,
    height: 50,
  },
  headerStyleNumber: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderRow: {
    display: "flex",
    flexDirection: "row",
    paddingVertical: 1,
    justifyContent: "space-between",
    paddingRight: 20,
    marginTop: -5,
    // textAlign: 'left'
  },
  subHeaderRowMinWidth: {
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderPriority: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eRich.base,
    textTransform: "capitalize",
    marginTop: -5,
    textAlign: "right",
  },
  subHeaderRowStatus: {
    display: "flex",
    flexDirection: "row",
    paddingVertical: 1,
    justifyContent: "space-between",
    paddingRight: 20,
    marginBottom: 10,
  },
  subHeaderRowMinWidth: {
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderRowMinWidth: {
    fontSize: 10,
    fontFamily: "NotoSans-Medium",
    color: GlobalStyles.colors.eRich.base,
  },
  subHeaderStatus: {
    fontSize: 10,
    fontFamily: "NotoSans-SemiBold",
    color: GlobalStyles.colors.eRich.base,
    textTransform: "capitalize",
    marginTop: -15,
    textAlign: "right",
  },
  arrowIconStyle: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
  lineStyle: {
    // borderWidth: 0.3,
    borderColor: GlobalStyles.colors.eMedium.hover,
    marginTop: 1,
    width: "90%",
  },
  lineStyleInfo: {
    borderWidth: 0.3,
    borderColor: GlobalStyles.colors.eSeparationLine.base,
    marginTop: 0,
    width: "100%",
  },
  lineStyleInfoCheckList: {
    borderWidth: 0.3,
    borderColor: GlobalStyles.colors.eSeparationLine.base,
    marginBottom: 5,
    width: "100%",
  },
  btnContainer: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 0,
    marginBottom: 15,
  },
  disabledCancleStyle: {
    // opacity: 0.5,
    backgroundColor: GlobalStyles.colors.ePage.base,
    borderColor: GlobalStyles.colors.eLight.selected,
    borderWidth: 1,
  },
  disableColor: {
    color: GlobalStyles.colors.eLight.selected,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  cancelText: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  bgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    marginLeft: 10,
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginLeft: 10,
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.ePrimary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 10,
  },
  disabledStyle: {
    opacity: 0.5,
    backgroundColor: GlobalStyles.colors.eBackground.selected,
  },
});

export default ServiceHostoryTableGrid;

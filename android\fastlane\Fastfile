


# before_all do
#   # ensure_git_branch(branch: "develop")
#   # ensure_git_status_clean
#   # git_pull
# end

# PACKAGE_NAME = "com.abjayon.ImpresaCxFieldWork"

# platform :android do
#   desc "Build Apk file for QA/testing"
#   lane :build_apk do
#     gradle(task: 'clean', project_dir: "../android")
#     gradle(task: 'assemble', build_type: 'Release', project_dir: "../android")
#     UI.message "file has been generated inside android/app/build/output/release directory..."
#   end

#   desc "Android build and release to beta"
#   lane :beta_release do
#       generate_android_apk_build  
#       #NOTE: you need to run fastlane supply init
#       supply(track: 'beta', aab: '../android/app/build/outputs/bundle/release/app-release.aab',
#         skip_upload_metadata: true,
#         skip_upload_changelogs: true,
#         skip_upload_images: true,
#         skip_upload_screenshots: true
#       )
#       commit_version_change_and_push_to_git_remote(track: "beta")
#   end

#   lane :alpha_release do
#     # this would invoke the build app private lane declared build
#     generate_android_apk_build

#     #NOTE: you need to run fastlane supply init
#     supply(track: 'alpha', aab: '../android/app/build/outputs/bundle/release/app-release.aab',
#       skip_upload_metadata: true,
#       skip_upload_changelogs: true,
#       skip_upload_images: true,
#       skip_upload_screenshots: true,
#     )
#     commit_version_change_and_push_to_git_remote(track: "alpha")
#   end

#   lane :internal_release do
#     # this would invoke the build app private lane declared build
#     generate_android_apk_build

#     #NOTE: you need to run fastlane supply init
#     supply(track: 'internal', aab: '../android/app/build/outputs/bundle/release/app-release.aab',
#       skip_upload_metadata: true,
#       skip_upload_changelogs: true,
#       skip_upload_images: true,
#       skip_upload_screenshots: true,
#       release_status: 'draft',
#     )
#     commit_version_change_and_push_to_git_remote(track: "internal")
#   end

#   lane :promote_latest_build do |options|
#     upload_to_play_store(
#       track: options[:track] , #"internal"
#       track_promote_to: options[:track_promote_to], #"alpha"
#       package_name: PACKAGE_NAME,
#       skip_upload_apk: true,
#       skip_upload_aab: true,
#       skip_upload_metadata: true,
#       skip_upload_changelogs: true,
#       skip_upload_images: true,
#       skip_upload_screenshots: true
#     )
#   end

#   lane :generate_android_apk_build do
#     increment_version_code(
#       gradle_file_path: "../android/app/build.gradle",  
#     )
#     gradle(task: 'clean', project_dir: "../android")
#     gradle(task: 'bundle', build_type: 'Release', project_dir: "../android")
#   end
  
#   lane :commit_version_change_and_push_to_git_remote do |options|
#     current_build_number = google_play_track_version_codes(
#       package_name: PACKAGE_NAME, #pull it from ENV
#       track: options[:track],
#       json_key: "secret.json",
#     )[0]
  
#     puts "Current build number #{current_build_number}"
  
#     add_git_tag(
#       grouping: "builds",
#       includes_lane: false,
#       prefix: "android",
#       build_number: current_build_number,
#       force: true,
#     )
#     git_add(path: ["../android/app/build.gradle"], shell_escape: false)
#     git_commit(path: ["../android/app/build.gradle"], message: "Bump version code # #{current_build_number}")
#     push_to_git_remote
#   end
  
#   # desc "Deploy a new version to the Google Play"
#   # lane :deploy do
#   #   gradle(task: "clean assembleRelease")
#   #   upload_to_play_store
#   # end


#   desc "Deploy a new version to the Google Play"
#   lane :deploy do
#     gradle(task: "clean bundleRelease")
#     upload_to_play_store
#   end
# end





PACKAGE_NAME = "com.abjayon.ImpresaCxFieldWork"


CURRENT_BRANCH = "develop"
before_all do
  sh "git checkout ."
  ensure_git_branch(branch: CURRENT_BRANCH)
  # ensure_git_status_clean
  sh "git pull origin #{CURRENT_BRANCH}"
  sh "yarn"
  sh "bundle install"
  sh "pwd"
end

platform :android do
  desc "building qa build"
  lane :qa do
    gradle(task: 'clean', project_dir: "../android")
    gradle(task: 'assemble', build_type: 'Release', project_dir: "../android")
    UI.message "file has been generated inside android/app/build/output/release directory..."
    # you can use any one of the approach
    #IF aws s3 sdk should be installed on the machine if using command line

    # sh "aws s3 cp ./app/build/outputs/apk/release/app-release.aab [path_in_s3_where you want to store]" 

  end

  desc "Android build and release to beta"
  lane :beta_release do
      generate_android_aab_build  
      #NOTE: you need to run fastlane supply init
      supply(track: 'beta', aab: '../android/app/build/outputs/bundle/release/app-release.aab',
        skip_upload_metadata: true,
        skip_upload_changelogs: true,
        skip_upload_images: true,
        skip_upload_screenshots: true
      )
      commit_version_change_and_push_to_git_remote(track: "beta")
  end

  desc "performing alpha release"
  lane :alpha_release do
    generate_android_aab_build
    supply(track: 'alpha', aab: '../android/app/build/outputs/bundle/release/app-release.aab',
      skip_upload_metadata: true,
      skip_upload_changelogs: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
    commit_version_change_and_push_to_git_remote(track: "alpha")
  end

  desc "performing inter testing release"
  lane :internal_testing_release do
    generate_android_aab_build
    supply(track: 'internal', aab: '../android/app/build/outputs/bundle/release/app-release.aab',
      skip_upload_metadata: true,
      skip_upload_changelogs: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
    commit_version_change_and_push_to_git_remote(track: "internal")
  end

  desc "promoting latest build"
  lane :promote_latest_build do |options|
    upload_to_play_store(
      track: options[:track] , #"internal"
      track_promote_to: options[:track_promote_to], #"alpha"
      package_name: PACKAGE_NAME,
      skip_upload_apk: true,
      skip_upload_aab: true,
      skip_upload_metadata: true,
      skip_upload_changelogs: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
  end

  desc "Deploy a new version to the Google Play"
  lane :deploy do
    generate_android_aab_build
    upload_to_play_store(
      skip_upload_metadata: true,
      skip_upload_changelogs: true,
      skip_upload_images: true,
      skip_upload_screenshots: true
    )
    commit_version_change_and_push_to_git_remote
  end

  desc "bumping up the minor version"
  lane :bump_minor do
    UI.message "before bumping minor, version is #{get_version_name}"
    increment_version_name(app_folder_name: "../android/app", bump_type: "minor")
    UI.message "after bumping minor, version is #{get_version_name}"
  end

  desc "bumping up the major version"
  lane :bump_major do
    UI.message "before bumping major, version is #{get_version_name}"
    increment_version_name(app_folder_name: "../android/app", bump_type: "major")
    UI.message "after bumping major, version is #{get_version_name}"
  end

  desc "bumping up the patch version"
  lane :bump_patch do
    UI.message "before bumping patch, version is #{get_version_name}"
    increment_version_name(app_folder_name: "../android/app", bump_type: "patch")
    UI.message "after bumping patch, version is #{get_version_name}"
  end

  desc "performing version bump commit"
  lane :commit_version_change_and_push_to_git_remote do |options|
    current_build_number = google_play_track_version_codes(
      package_name: PACKAGE_NAME, #pull it from ENV
      track: options[:track],
      json_key: "impresa-playstore-secret.json",
    )[0]
  
    puts "Current build number #{current_build_number}"
  
    # add_git_tag(
    #   grouping: "builds",
    #   includes_lane: false,
    #   prefix: "android",
    #   build_number: current_build_number,
    #   force: true,
    # )
    git_add(path: ["../android/app/build.gradle"], shell_escape: false)
    git_commit(path: ["../android/app/build.gradle"], message: "Version Bump")
    push_to_git_remote(
      remote: "origin",         # optional, default: "origin"
      local_branch: "develop",  # optional, aliased by "branch", default is set to current branch
      remote_branch: "develop", # optional, default is set to local_branch
      force: true,    # optional, default: false
      force_with_lease: true,   # optional, default: false
      tags: false,    # optional, default: true
      no_verify: true,# optional, default: false
      set_upstream: true        # optional, default: false
    )
  end

  desc "generating aab build..."
  lane :generate_android_aab_build do
    increment_version_code(
      app_folder_name: "**/app",  
    )
    gradle(task: 'clean', project_dir: "../android")
    gradle(task: 'bundle', build_type: 'Release', project_dir: "../android")
  end

  desc "generating apk..."
  lane :generate_android_apk_build do
    increment_version_code(
      app_folder_name: "**/app",  
    )
    gradle(task: 'clean', project_dir: "../android")
    gradle(task: 'assemble', build_type: 'Release', project_dir: "../android")
  end
end




lane :commit_version_change_and_push_to_git_remote do |options|
  current_build_number = google_play_track_version_codes(
    package_name: PACKAGE_NAME, #pull it from ENV
    track: options[:track],
    json_key: "impresa-playstore-secret.json",
  )[0]

  puts "Current build number #{current_build_number}"

  add_git_tag(
    grouping: "builds",
    includes_lane: false,
    prefix: "android",
    build_number: current_build_number,
    force: true,
  )
  git_add(path: ["../android/app/build.gradle"], shell_escape: false)
  git_commit(path: ["../android/app/build.gradle"], message: "Version Bump")
  push_to_git_remote
end

desc "Deploy a new version to the Google Play"
lane :deploy do
  gradle(task: "clean assembleRelease")
  upload_to_play_store
end

desc "Deploy a new version to the Google Play"
lane :test_me do
  gradle(task: "clean assembleRelease")
  upload_to_play_store
end
import moment from "moment";
import React, { useContext, useEffect, useState } from "react";
import { ActivityIndicator } from "react-native";
import { View } from "react-native";
import { useSelector } from "react-redux";
import { drawerContext } from "../../../app/authenticated/authenticated_layout";
import { GlobalStyles } from "../../../app/global-styles";
import { EAutoPayServices } from "../model/auto_pay_service";
import AutoPayCard from "./_auto_pay_card";
import { config } from "../../../../environment";
import { drawerContextWO } from "../../../app/authenticated/authenticated_layout_wo";
import { stackContext } from "../../../app/get_stack";

export default function AutoPayData({
  autoData,
  setAutoData,
  setChecked,
  checked,
}) {
  const { workModelType } = React.useContext(stackContext);
  const accountId = useSelector(
    state => state?.meterDetails?.meterDetails?.accountId,
  );
  const [fetching, setFetching] = useState(false);
  const [initialData, setInitialData] = useState([]);
  const [sourceList, setSourceList] = useState([]);
  const [sourceDropdown, setSourceDropdown] = useState([]);
  const [deleteData, setDeleteData] = useState();
  let popup,
    setPopup,
    setTitle,
    setContent,
    setError,
    setButton,
    setIcon,
    popupCode,
    setPopupCode,
    yesDone,
    setYesDone;

  if (workModelType === "WA") {
    ({
      popup,
      setPopup,
      setTitle,
      setContent,
      setError,
      setButton,
      setIcon,
      popupCode,
      setPopupCode,
      yesDone,
      setYesDone,
    } = useContext(drawerContext));
  } else {
    ({
      popup,
      setPopup,
      setTitle,
      setContent,
      setError,
      setButton,
      setIcon,
      popupCode,
      setPopupCode,
      yesDone,
      setYesDone,
    } = useContext(drawerContextWO));
  }

  useEffect(() => {
    // fetchRecords();
    accountId &&
      EAutoPayServices.getSourceList(accountId)
        .then(sourceResponse => {
          setSourceList(sourceResponse?.data?.getSourceCdList["CM-ApaySrcLs"]);
          let dropdownData = [];
          sourceResponse?.data?.getSourceCdList?.["CM-ApaySrcLs"].map(item =>
            dropdownData.push(item.description),
          );
          setSourceDropdown(dropdownData);
        })
        .catch(err => setSourceList([]));
  }, [accountId]);

  useEffect(() => {
    if (
      deleteData &&
      popup === true &&
      popupCode === "DELETE_CONFIRMATION" &&
      yesDone
    ) {
      let newData = autoData.filter(
        item => item.accountAutopayId !== deleteData,
      );
      accountId &&
        deleteData &&
        EAutoPayServices.deleteAutoPayRecord(accountId, deleteData)
          .then(response => {
            setAutoData(newData);
            if (newData.length > 0) {
              var current_date = moment().format("MM-DD-YYYY");
              let haveData = newData.filter(item => {
                return moment(item.startDate).isAfter(current_date);
              });
              if (haveData.length === 0) {
                setChecked(false);
                accountId &&
                  EAutoPayServices.getUpdatePreference(false, accountId).then(
                    res => console.log("res"),
                  );
              }
            }
            setYesDone(false);
            setPopup(true);
            setTitle("Success");
            setContent("Your data is deleted successfully");
            setError("SUCCESS");
            setButton(false);
            setIcon("Sucess-icon");
            setPopupCode();
            fetchRecords();
          })
          .catch(error => {
            setYesDone(false);
            setPopup(true);
            setTitle("Failure");
            setContent("Something went wrong.Please try again later.");
            setError("ERROR");
            setButton(false);
            setIcon("Exclamationmark-fill-icon");
            setPopupCode();
          });
    }
  }, [popup, popupCode, deleteData, yesDone]);

  const fetchRecords = () => {
    setFetching(true);
    accountId &&
      EAutoPayServices.getAutoPayRecords(accountId)
        .then(response => {
          setFetching(false);
          let autoPayResponse = response?.data?.getAutoPayData?.autopayDetails;
          autoPayResponse = Array.isArray(autoPayResponse)
            ? [...response.data.getAutoPayData.autopayDetails]
            : autoPayResponse === null
            ? []
            : [response.data.getAutoPayData.autopayDetails];
          autoPayResponse.map(item => (item.editable = false));
          autoPayResponse && setInitialData(autoPayResponse);
          autoPayResponse && setAutoData(autoPayResponse);
        })
        .catch(err => {
          setFetching(false);
          setInitialData([]);
          setAutoData([]);
        });
  };

  useEffect(() => {
    if (popup && popupCode === "CANCEL_CLICK" && yesDone) {
      let newData = autoData.filter(item => item.editable !== true);
      setAutoData(newData);
      setPopup(false);
      setPopupCode();
      setYesDone(false);
    }
  }, [popup, popupCode, yesDone]);

  const cancelClick = () => {
    setPopup(true);
    setTitle("Confirmation");
    setContent("You have unsaved changes. Do you want to cancel?");
    setError("WARNING");
    setIcon();
    setButton(true);
    setPopupCode("CANCEL_CLICK");
  };

  const deleteItem = e => {
    let isActive = autoData.filter(
      item => item.accountAutopayId == e && item.status === "ACTIVE",
    );
    if (isActive.length > 0) {
      setPopup(true);
      setTitle("Confirmation");
      setContent(
        "You are deleting an active auto pay record. Are you sure you want to proceed?",
      );
      setError("WARNING");
      setIcon();
      setButton(true);
      setDeleteData(e);
      setPopupCode("DELETE_CONFIRMATION");
    } else {
      setPopup(true);
      setTitle("Confirmation");
      setContent("Are you sure you want to delete this record?");
      setError("WARNING");
      setIcon();
      setButton(true);
      setDeleteData(e);
      setPopupCode("DELETE_CONFIRMATION");
    }
  };

  const saveFunction = () => {
    let newData = autoData.map(item =>
      item.editable === true ? (item.editable = false) : "",
    );
    setAutoData(newData);
    setChecked(true);
    fetchRecords();
    accountId &&
      EAutoPayServices.getUpdatePreference(true, accountId).then(res =>
        console.log("res"),
      );
  };

  useEffect(() => {
    fetchRecords();
  }, [checked]);

  return (
    <View>
      {fetching ? (
        <ActivityIndicator
          align="center"
          size="small"
          color={GlobalStyles.colors.ePrimary.base}
        />
      ) : (
        autoData &&
        autoData.length > 0 &&
        autoData.map((item, k) => (
          <View key={k}>
            <AutoPayCard
              item={item}
              sourceDropdown={sourceDropdown}
              sourceList={sourceList}
              cancelClick={cancelClick}
              deleteItem={deleteItem}
              saveFunction={saveFunction}
              checked={checked}
            />
          </View>
        ))
      )}
    </View>
  );
}

import { createIconSetFromIcoMoon } from "react-native-vector-icons";
import icoMoonConfig from "../../../assets/fonts/selection.json";
import { Platform } from "react-native";

// We use the IcoMoon app (https://icomoon.io) to generate a custom font made up
// of SVG icons. The actual font file is loaded up-front in src/index.js.
export default createIconSetFromIcoMoon(
  icoMoonConfig,
  Platform.OS === "ios" ? null : "custom-icons",
);

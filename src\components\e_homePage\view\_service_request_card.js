import React, { useContext } from "react";
import { StyleSheet, View, ScrollView, Dimensions } from "react-native";
import { FAB, Text } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import LinearGradient from "react-native-linear-gradient";
import { useNavigation, useRoute } from "@react-navigation/native";
import { useEffect, useState } from "react";
import { servicePath } from "../../../redux/slices/servicePath";
import { useDispatch, useSelector } from "react-redux";
import FlatButton from "../../common/_flat_button";
import { Linking } from "react-native";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import Icon from "../../icon";
import { config } from "../../../environment";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { stackContext } from "../../app/get_stack";

export default function ServiceRequestCard({ title, data }) {
  const { workModelType } = React.useContext(stackContext);
  const [bearer, setbearer] = useState();
  let setOpenMenu, menuFlag, setmenuFlag, submitLoader, setSubmitLoader;

  if (workModelType === "WA") {
    ({ setOpenMenu, menuFlag, setmenuFlag, submitLoader, setSubmitLoader } =
      useContext(drawerContext));
  } else {
    ({ setOpenMenu, menuFlag, setmenuFlag, submitLoader, setSubmitLoader } =
      useContext(drawerContextWO));
  }

  const navigation = useNavigation();
  const [clickColor, setClickColor] = useState("");
  const pathName = useSelector(state => state?.servicePath?.servicePath);
  const dispatch = useDispatch();
  const route = useRoute();
  const accountId = useSelector(
    state => state?.meterDetails?.meterDetails?.accountId,
  );
  const saId = useSelector(
    state => state?.meterDetails?.meterDetails?.getSaInfo?.saId,
  );
  useEffect(() => {
    if (route.name === "Home") {
      dispatch(servicePath("Home"));
      setClickColor();
    }
    if (route.name === "Services" && clickColor === "") {
      setClickColor("ReportOutage");
    }
  }, [route]);

  useEffect(() => {
    if (pathName && pathName !== "PastTickets") {
      setClickColor(pathName);
    }
  }, [pathName]);

  useEffect(() => {
    AsyncStorage.getItem("bearer").then(bearer => {
      bearer = JSON.parse(bearer);
      setbearer(bearer);
    });
  }, []);

  const itemClick = path => {
    if (path === "MORE") {
      //Linking.openURL(config.constants.BASE_URL+"/nativeRedirect?nativeAppToken=" + bearer.acessToken+"&path=service");
      if (accountId && saId) {
        Linking.openURL(
          config.constants.BASE_URL +
            "/nativeRedirect?nativeAppToken=" +
            bearer.acessToken +
            "&path=Service" +
            "&accountNumber=" +
            accountId +
            "&serviceID=" +
            saId,
        );
      }
    } else {
      if (route.name === "Home") {
        navigation.navigate("Services");
      }
      dispatch(servicePath(path));
      // navigation.navigate(path);
      setClickColor(path);
    }
  };

  const pastTicketClick = () => {
    navigation.navigate("Services");
    dispatch(servicePath("PastTickets"));
    setClickColor("ReportOutage");
    setOpenMenu(true);
    setmenuFlag(false);
  };

  const [resposiveWidth, setResposiveWidth] = useState(0);
  useEffect(() => {
    const calculateViewWidth = () => {
      const windowWidth = Dimensions.get("window").width;
      if (windowWidth > 300 && windowWidth < 350) {
        setResposiveWidth(windowWidth / 9);
      } else if (windowWidth > 340 && windowWidth < 400) {
        setResposiveWidth(windowWidth / 7.5);
      } else if (windowWidth < 420) {
        setResposiveWidth(windowWidth / 7);
      } else if (windowWidth < 490) {
        setResposiveWidth(windowWidth / 7);
      } else {
        setResposiveWidth(windowWidth / 8);
      }
    };
    calculateViewWidth();
    Dimensions.addEventListener("change", calculateViewWidth);

    return () => {
      Dimensions.removeEventListener("change", calculateViewWidth);
    };
  }, []);
  return (
    <View style={styles.container}>
      <LinearGradient
        colors={[
          GlobalStyles.colors.ePrimary.hover,
          GlobalStyles.colors.ePrimary.selected,
        ]}
        style={styles.background}
      />
      <View style={styles.card}>
        <View style={{ flexDirection: "row", flexWrap: "wrap" }}>
          <View style={styles.leftView}>
            <Text style={styles.titleCard}>{title}</Text>
          </View>
        </View>
        <View style={styles.content}>
          {data &&
            data.map((item, k) => {
              return (
                <ScrollView
                  horizontal={true}
                  showsHorizontalScrollIndicator={false}
                  pagingEnabled={true}
                  contentContainerStyle={styles.contentContainer}>
                  <View
                    style={[styles.centerIcon, { width: resposiveWidth }]}
                    key={k}>
                    <FAB
                      icon={() => (
                        <Icon
                          name={item.icon}
                          size={22}
                          color={
                            clickColor === "ReportOutage" &&
                            item.path === "ReportOutage"
                              ? GlobalStyles.colors.eWhite.base
                              : GlobalStyles.colors.ePrimary.base
                          }
                        />
                      )}
                      style={[
                        styles.fabSize,
                        clickColor === item.path
                          ? item.path === "ReportOutage"
                            ? { backgroundColor: "#D35056" }
                            : styles.clickFab
                          : styles.iconFab,
                      ]}
                      disabled={submitLoader}
                      onPress={() => itemClick(item.path)}
                      animated={false}
                    />
                    <Text style={styles.blueText}>{item.label}</Text>
                  </View>
                </ScrollView>
              );
            })}
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  contentContainer: {
    paddingHorizontal: 10,
  },
  card: {
    borderRadius: 20,
    width: "100%",
    paddingHorizontal: 15,
    paddingTop: "3%",
    height: 145,
  },
  titleCard: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
  },
  content: {
    flex: 1,
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
    marginTop: "5%",
    justifyContent: "space-between",
  },

  iconFab: {
    backgroundColor: GlobalStyles.colors.eFaint.hover,
  },
  clickFab: {
    backgroundColor: GlobalStyles.colors.ePastelColor2.base,
  },
  btnPress: {
    backgroundColor: "red",
  },
  fabSize: {
    width: 46,
    height: 46,
    justifyContent: "center",
    alignItems: "center",
  },
  blueText: {
    color: GlobalStyles.colors.eWhite.base,
    marginTop: "5%",
    fontSize: 10,
    width: 60,
    textAlign: "center",
    fontFamily: "NotoSans-Regular",
  },
  centerIcon: {
    alignItems: "center",
  },
  container: {
    flex: 1,
    alignItems: "center",
    height: "100%",
    // paddingBottom: "2%",
    justifyContent: "center",
  },
  background: {
    position: "absolute",
    left: 0,
    right: 0,
    top: 0,
    borderWidth: 1,
    borderRadius: 20,
    height: "100%",
    borderColor: GlobalStyles.colors.eWhite.base,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
  },
  linkWhite: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 10,
    fontFamily: "NotoSans-Regular",
    textDecorationLine: "underline",
    alignSelf: "flex-end",
    paddingRight: 0,
  },
  leftView: {
    float: "left",
    width: "50%",
    marginTop: 0,
  },
  rightView: {
    float: "right",
    width: "50%",
    marginTop: 0,
  },
});

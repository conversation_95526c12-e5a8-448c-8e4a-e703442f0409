fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

### commit_version_change_and_push_to_git_remote

```sh
[bundle exec] fastlane commit_version_change_and_push_to_git_remote
```



### deploy

```sh
[bundle exec] fastlane deploy
```

Deploy a new version to the Google Play

----


## Android

### android qa

```sh
[bundle exec] fastlane android qa
```

building qa build

### android beta_release

```sh
[bundle exec] fastlane android beta_release
```

Android build and release to beta

### android alpha_release

```sh
[bundle exec] fastlane android alpha_release
```

performing alpha release

### android internal_testing_release

```sh
[bundle exec] fastlane android internal_testing_release
```

performing inter testing release

### android promote_latest_build

```sh
[bundle exec] fastlane android promote_latest_build
```

promoting latest build

### android deploy

```sh
[bundle exec] fastlane android deploy
```

Deploy a new version to the Google Play

### android bump_minor

```sh
[bundle exec] fastlane android bump_minor
```

bumping up the minor version

### android bump_major

```sh
[bundle exec] fastlane android bump_major
```

bumping up the major version

### android bump_patch

```sh
[bundle exec] fastlane android bump_patch
```

bumping up the patch version

### android commit_version_change_and_push_to_git_remote

```sh
[bundle exec] fastlane android commit_version_change_and_push_to_git_remote
```

performing version bump commit

### android generate_android_aab_build

```sh
[bundle exec] fastlane android generate_android_aab_build
```

generating aab build...

### android generate_android_apk_build

```sh
[bundle exec] fastlane android generate_android_apk_build
```

generating apk...

----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).

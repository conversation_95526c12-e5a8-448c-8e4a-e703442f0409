import React, { useEffect, useMemo, useState, useContext } from "react";
import { View, Text, StyleSheet, ActivityIndicator } from "react-native";
import { ScrollView } from "react-native-gesture-handler";
import CustomTextInput from "./CustomTextInput";
import { consumerPaginationContext } from "./new-consumer-index";
import _ from "lodash";
import { GlobalStyles } from "../../app/global-styles";
import { Dimensions } from "react-native";
import { consumerIndexContext } from "../e_consumer-index";
import Button from "../../common/_button";
import Geolocation from "@react-native-community/geolocation";
import { useTranslation } from 'react-i18next';

const windowWidth = Dimensions.get("window").width;
const windowHeight = Dimensions.get("window").height;
const OtherInfoPage = () => {
  const { t } = useTranslation();
  const { tempCIData, revert, setRevert, newCIData, setNewCIData } =
    useContext(consumerIndexContext);
  const { IndexArray, currentPage, setIndexArray } = useContext(
    consumerPaginationContext,
  );
  const [loader, setLoader] = React.useState(false);

  useEffect(() => {
    Geolocation.requestAuthorization();
  }, []);

  const locationUpdate = () => {
    setLoader(true);
    Geolocation.getCurrentPosition(
      position => {
        const { latitude, longitude } = position.coords;
        const newLocation = "Lat:" + latitude + "," + "Long:" + longitude;
        setNewCIData(prev => ({ ...prev, LatitudeLongitude: newLocation }));
        setLoader(false);
      },
      error => {
        console.log(error);
        setLoader(false);
      },
      { enableHighAccuracy: true, timeout: 20000, maximumAge: 1000 },
    );
  };
  useEffect(() => {
    if (newCIData) {
      if (!newCIData.Remarks || !newCIData.LatitudeLongitude) {
        const updatedIndexArray = IndexArray.map((item, index) =>
          index === currentPage - 1 ? { ...item, filled: false } : item,
        );
        setIndexArray(updatedIndexArray);
      } else {
        const updatedIndexArray = IndexArray.map((item, index) =>
          index === currentPage - 1 ? { ...item, filled: true } : item,
        );
        setIndexArray(updatedIndexArray);
      }
    }
  }, [newCIData, currentPage]);
  useEffect(() => {
    if (revert) {
      setNewCIData(tempCIData);
      setRevert(false);
    }
  }, [revert]);
  return (
    <View style={styles.container}>
      <View style={styles.section}>
        <View style={styles.headerWrapper}>
          <Text style={styles.sectionHeaderText}>
            {IndexArray[currentPage - 1].name}
          </Text>
        </View>
        <View style={styles.sectionWrapper}>
          <ScrollView
            persistentScrollbar={true}
            contentContainerStyle={{ flexGrow: 1 }}>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t('REMARKS')}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={newCIData?.Remarks ? `${newCIData?.Remarks}` : ""}
                value={newCIData?.Remarks ? `${newCIData?.Remarks}` : ""}
                placeholder={t('ENTER_REMARKS')}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, Remarks: value }));
                }}
              />
            </View>

            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>
                {t('LAT_LONG')} : {newCIData?.LatitudeLongitude}
              </Text>
            </View>

            <View style={styles.sectionItemButton}></View>
            <Button
              onPress={locationUpdate}
              buttonbgColor={[styles.buttonBgColor]}
              textColor={[styles.cancelText]}>
              {t('UPDATE_CURRENT_LOCATION')}
              {loader && (
                <ActivityIndicator
                  align="center"
                  size={13}
                  color={GlobalStyles.colors.eWhite.base}
                />
              )}
            </Button>
          </ScrollView>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingLeft: 10,
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eTertiary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    //marginLeft: 10,
  },
  cancelText: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 14,
    fontFamily: "NotoSans-SemiBold",
  },
  extendFlex: {
    width: windowWidth - 40,
    paddingBottom: 10,
  },
  headerWrapper: {
    paddingBottom: 20,
  },
  flexRowCenter: {
    paddingHorizontal: 40,
  },
  section: {},
  sectionHeaderText: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.ePrimary.base,
  },
  sectionItemHeader: {
    width: "100%",
  },
  sectionItemButton: {
    marginTop: 10,
  },
  sectionItemInputStyle: {
    height: 35,
  },
  sectionItemLabel: {
    fontFamily: "NotoSans-Medium",
    fontSize: 10,
    color: GlobalStyles.colors.eRich.hover,
  },
  sectionWrapper: {
    height: windowHeight - 450,
  },
  submitBtnWrapper: {
    paddingVertical: 16,
    marginBottom: 10,
  },
});

export default OtherInfoPage;

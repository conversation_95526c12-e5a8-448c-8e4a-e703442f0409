import { View, StyleSheet } from "react-native";
import { Text } from "react-native-paper";
import { GlobalStyles } from "../app/global-styles";
import <PERSON><PERSON> from "./_button";
// import DocumentPicker from "react-native-document-picker";
// import R<PERSON>etchBlob from "rn-fetch-blob"

export default function UploadImage() {
  const btnClick = () => {
    // try {
//       const res =  DocumentPicker.pick({
//        //by using allFiles type, you will able to pick any type of media from user device, 
//     //There can me more options as well
//     //DocumentPicker.types.images: All image types
//     //DocumentPicker.types.plainText: Plain text files
//     //DocumentPicker.types.audio: All audio types
//    //DocumentPicker.types.pdf: PDF documents
//    //DocumentPicker.types.zip: Zip files
//    //DocumentPicker.types.csv: Csv files
//    //DocumentPicker.types.doc: doc files
//    //DocumentPicker.types.docx: docx files
//   //DocumentPicker.types.ppt: ppt files
//   //DocumentPicker.types.pptx: pptx files
//   //DocumentPicker.types.xls: xls files
//   //DocumentPicker.types.xlsx: xlsx files
//   //For selecting more more than one options use the 
//  //type: [DocumentPicker.types.csv,DocumentPicker.types.xls]
//          type: [DocumentPicker.types.allFiles],
//       });

//       console.log(
//         "mimeeeeeeee"
// ,res,        
// res.uri,
//         res.type, // mime type
//         res.name,
//         res.size
//       );
      // this.uploadAPICall(res);//here you can call your API and send the data to that API
    } 
    // catch (err) {
    //   if (DocumentPicker.isCancel(err)) {
    //     console.log("error -----", err);
    //   } else {
    //     throw err;
    //   }
    
    
  // };
  return (
    <View style={styles.container}>
      <Text style={styles.textStyle}>Supported file formats</Text>
      <Text style={styles.orangeTextStyle}>
        .pdf .doc .docx .png .jpg .jpeg .txt .rtf
      </Text>
      <View style={styles.browserContainer}>
        <Button
          onPress={btnClick}
          buttonbgColor={styles.bgColor}
          textColor={styles.textColor}
        >
          Choose File
        </Button>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderColor: GlobalStyles.colors.eDark.hover,
    borderRadius: 4,
    width: "100%",
    borderWidth: 2,
    borderStyle: 'dashed',
  },
  textStyle: {
    paddingTop: 10,
    textAlign: "center",
    fontSize: 10
  },
  orangeTextStyle: {
    color: GlobalStyles.colors.eTertiary.base,
    textAlign: "center",
    fontSize: 10
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize:14
  },
  bgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    padding: 5,
  },
  browserContainer: {
    paddingTop: 20,
    paddingBottom: 10,
    alignItems: "center",
  },
});

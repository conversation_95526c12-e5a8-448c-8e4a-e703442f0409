import AsyncStorage from "@react-native-async-storage/async-storage";
import axios from "axios";
import { config } from "../environment";
import { updateLoggedInStatus } from "../redux/slices/authenticationReducer";
import { store } from "../redux/store";

export const commonService = {
  logoutUser,
  getAssestUrl,
};

async function logoutUser() {
  const rawBearer = await AsyncStorage.getItem("bearer");
  const bearer = JSON.parse(rawBearer);
  if (bearer) {
    let readTips = bearer.readTipId;
    await axios
      .post(
        config.urls.EXPIRE_SESSION,
        {
          sessionId: bearer.sessionId,
          email: bearer.email,
          readTipId: readTips,
          tenantCode: config.constants.BASE_TENANT_CODE,
        },
        {
          headers: {
            accessToken: bearer.acessToken,
          },
        },
      )
      .then(async function (response) {
        await AsyncStorage.removeItem("bearer");
        store.dispatch(updateLoggedInStatus(false));
      })
      .catch(async function (error) {
        await AsyncStorage.removeItem("bearer");
        store.dispatch(updateLoggedInStatus(false));
      });
  } else {
    await AsyncStorage.removeItem("bearer");
    store.dispatch(updateLoggedInStatus(false));
  }
}

async function getAssestUrl(assestCode) {
  return new Promise((resolve, reject) => {
    axios
      .get(config.urls.ASSEST_CODE_API + "/" + assestCode)
      .then(response => {
        let returnData = {
          assetCode: response?.data?.assetCode,
          assetPath:
            config?.urls?.ASSEST_URL_ENDPOINT + response?.data?.assetPath,
          fallBackLabelKey: response?.data?.fallBackLabelKey,
        };
        if (returnData) {
          resolve(returnData);
        }
      })
      .catch(function (error) {
        reject(error);
      });
  });
}

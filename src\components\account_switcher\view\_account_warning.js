import { View, StyleSheet } from "react-native";
import { Modal, Text, Card, IconButton } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import Button from "../../common/_button";
import Icon from "../../icon";

export default function AccountWarning({
  visible,
  hideModel,
  changeAccount,
  finalMeter,
}) {
  return (
    <Modal visible={visible} onDismiss={hideModel} style={styles.modal}>
      <Card style={styles.container}>
        <View style={styles.headerContainer}>
          <Text style={styles.title}>Switch Account?</Text>
          <Icon
            name="Close-icon-stroke"
            color={GlobalStyles.colors.eWhite.base}
            onPress={hideModel}
            style={styles.closeIcon}
            size={24}
          />
        </View>
        <View style={styles.contentContainer}>
          <View style={styles.imgViewCls}>
            <IconButton
              icon="alert"
              size={50}
              iconColor={GlobalStyles.colors.eTertiary.base}
            />
            <Text style={styles.content}>
              This will reload the page and all your changes will be lost.
            </Text>
            <Text style={styles.content1}>Do you want to continue?</Text>
          </View>
        </View>
        <View style={styles.lineStyleBottom}></View>
        <View style={styles.centerButtons}>
          <Button
            buttonbgColor={styles.cancelBg}
            textColor={styles.greenText}
            onPress={hideModel}>
            No
          </Button>
          <Button
            buttonbgColor={styles.buttonBgColor}
            textColor={styles.whiteText}
            onPress={() => changeAccount(finalMeter)}>
            Yes
          </Button>
        </View>
      </Card>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flext: 1,
    height: 240,
    position: "relative",
    marginHorizontal: "5%",
    justifyContent: "space-between",
  },

  title: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 14,
  },
  closeIcon: {
    position: "absolute",
    top: 10,
    right: 10,
  },
  imgViewCls: {
    justifyContent: "center",
    alignItems: "center",
    // marginTop: -15
  },
  headerContainer: {
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    height: 40,
    display: "flex",
    alignItems: "center",
    flexDirection: "row",
    gap: 1,
    paddingHorizontal: 5,
    justifyContent: "space-between",
  },
  buttonContainer: {
    backgroundColor: GlobalStyles.colors.eLight.base,
    padding: 12,
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
    justifyContent: "flex-end",
  },
  contentContainer: {
    paddingHorizontal: 30,
  },
  cancelBtnBgStyle: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.ePage.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 10,
  },
  bgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 5,
    marginLeft: 10,
  },
  greenText: {
    color: GlobalStyles.colors.eSecondary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  whiteText: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginLeft: 10,
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.ePage.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 10,
  },
  centerButtons: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: 15,
    marginBottom: 5,
    marginRight: 10,
  },
  okBtnTextStyle: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 13,
    fontWeight: 600,
  },
  warningTitle: {
    textAlign: "center",
    fontSize: 13,
    marginVertical: 5,
    fontWeight: 500,
  },
  nobtnBg: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
  },
  noText: {
    color: GlobalStyles.colors.eSecondary.base,
  },
  greenButton: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    marginLeft: 20,
    paddingVertical: 10,
    paddingHorizontal: 12,
  },
  whiteText: {
    color: GlobalStyles.colors.eWhite.base,
  },
  content: {
    marginTop: -10,
    textAlign: "center",
  },
  content1: {
    paddingBottom: 10,
    textAlign: "center",
  },
  lineStyleBottom: {
    borderWidth: 0.6,
    borderColor: "#DEE2E4",
  },
});

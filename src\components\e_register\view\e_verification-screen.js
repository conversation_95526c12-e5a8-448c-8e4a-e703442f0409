import React from "react";
import {
  StyleSheet,
  View,
  Dimensions,
  ScrollView,
  TouchableWithoutFeedback,
  Platform,
} from "react-native";
import { Text} from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import Icon from "../../icon";
import Button from "../../common/_button";

const { height } = Dimensions.get("window");
const width = Dimensions.get("window").width;
export default function VerificationScreen({ verificationDetails,  onPressBack = () => {} }) {
  return (
    <ScrollView>
      <View
        style={{
          flex: 1,
          width: "100%",
          paddingHorizontal: 12,
          elevation: 2,
          alignContent: "center",
          justifyContent: "center",
        }}>
        <>
          <View style={styles.heading}>
            <Text style={styles.titleCard}>Verification Failed</Text>
            <View style={styles.imgViewCls}>
              <Icon
                name="V-failed2close"
                style={styles.greenBg}
                color={GlobalStyles.colors.eDanger.dark}
                size={200}
              />
              <Icon
                name="V-failed1close"
                style={styles.closeIcon}
                color={GlobalStyles.colors.eDanger.dark}
                size={80}
              />
            </View>
          </View>
          <View style={styles.container}>
            <View>
              <View style={styles.subtitle}>
                <Text style={styles.singleHeader}>
                  Unfortunately, the details you have entered was not sufficient
                  to verify with any existing account.
                </Text>
              </View>

              <View style={styles.subheadingView}>
                {/* <Text style={styles.subheading}> */}
                <View
                  style={{
                    marginLeft: 50,
                    paddingTop: 10,
                    marginBottom: 10,
                    width: width / 13,
                  }}>
                  <Icon
                    name="info-icon-filled"
                    style={styles.blueBg}
                    color={GlobalStyles.colors.ePrimary.base}
                    size={18}
                  />
                </View>
                <View
                  style={{
                    paddingTop: 17,
                  }}>
                  <Text style={styles.subheading3}> MATCH FOUND</Text>
                </View>
              </View>
              <View>
                <Text style={styles.subheading2}>
                  Atleast one of the details you provided, matches with an
                  existing account with these details:
                </Text>
              </View>
              <View>
                <View style={styles.details}>
                  <View style={{ flex: 1 }}>
                    <Text style={styles.detailstitle}>Name</Text>
                  </View>
                  <View style={{ flex: 2 }}>
                    <Text style={styles.detailsvalue}>
                      {verificationDetails.name}
                    </Text>
                  </View>
                </View>
                <View style={styles.details}>
                  <View style={{ flex: 1 }}>
                    <Text style={styles.detailstitle}>Email ID</Text>
                  </View>
                  <View style={{ flex: 2 }}>
                    <Text style={styles.detailsvalue}>
                      {verificationDetails.email}
                    </Text>
                  </View>
                </View>
                <View style={styles.details}>
                  <View style={{ flex: 1 }}>
                    <Text style={styles.detailstitle}>Mobile Number</Text>
                  </View>
                  <View style={{ flex: 2 }}>
                    <Text style={styles.detailsvalue}>
                      {verificationDetails.mobileNumber}
                    </Text>
                  </View>
                </View>
                <View style={styles.details}>
                  <View style={{ flex: 1 }}>
                    <Text style={styles.detailstitle}>Account ID</Text>
                  </View>
                  <View style={{ flex: 2 }}>
                    <Text style={styles.detailsvalue}>
                      {verificationDetails.accountId}
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
          { Platform.OS === "ios" && (
            <View>
              <View 
                style={styles.backToRegisterBtnContainer}
              >
                <Button
                  buttonbgColor={{
                    backgroundColor: GlobalStyles.colors.eSecondary.base,
                    alignItems: "center",
                    paddingHorizontal: 30,
                  }}
                  textColor={{
                      color: GlobalStyles.colors.eWhite.base,
                      fontSize: 12,
                      fontFamily: "NotoSans-Medium",
                  }}
                onPress={onPressBack}>
                Back to Register
              </Button>
              </View>
            </View>
          )}
        </>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // padding: 20,
  },
  contentContainer: {
    flexGrow: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  subText: {
    color: GlobalStyles.colors.eMedium.hover,
    fontSize: 12,
    textAlign: "center",
    fontFamily: "NotoSans-Medium",
  },
  details: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
    borderRadius: 4,
    justifyContent: "flex-start",
    width: "100%",
    textAlign: "right",
    textColor: GlobalStyles.colors.eWhite.base,
    marginLeft: 30,
  },
  detailstitle: {
    color: GlobalStyles.colors.eRich.base,
    textAlign: "left",
    fontFamily: "NotoSans-Medium",
    fontSize: 12,
    marginTop: 10,
    marginLeft: 20,
    justifyContent: "flex-start",
  },
  detailsvalue: {
    color: GlobalStyles.colors.ePrimary.base,
    textAlign: "left",
    fontFamily: "NotoSans-SemiBold",
    fontSize: 12,
    marginTop: 10,
    marginLeft: 20,
    justifyContent: "flex-start",
  },
  titleCard: {
    color: GlobalStyles.colors.eDanger.dark,
    fontFamily: "NotoSans-Bold",
    fontSize: 20,
    textAlign: "center",
  },
  imgViewCls: {
    justifyContent: "center",
    alignItems: "center",
    marginTop: height / 30,
  },
  greenBg: {
    //backgroundColor: GlobalStyles.colors.eDanger.dark,
  },
  closeIcon: {
    marginTop: 0,
    position: "absolute",
    // backgroundColor: GlobalStyles.colors.eDanger.dark,
  },
  blueBg: {
    backgroundColor: GlobalStyles.colors.eWhite.base,
    top: 10,
  },
  subtitle: {
    textAlign: "center",
    color: GlobalStyles.colors.eRich.base,
    fontSize: 14,
    fontFamily: "NotoSans-Bold",
    marginTop: height - (height + 5),
  },
  subheadingView: {
    marginTop: height - (height - 30),
    flexDirection: "row",
    justifyContent: "flex-start",
    paddingBottom: 10,
  },
  subheading: {
    fontFamily: "NotoSans-Bold",
    fontSize: 12,
    textAlign: "left",
    color: GlobalStyles.colors.ePrimary.base,
    marginLeft: 40,
    marginBottom: 10,
    paddingTop: -10,
  },
  subheading3: {
    fontFamily: "NotoSans-Bold",
    fontSize: 12,
    color: GlobalStyles.colors.ePrimary.base,
  },
  subheading2: {
    fontFamily: "NotoSans-Medium",
    fontSize: 12,
    textAlign: "left",
    color: GlobalStyles.colors.eRich.base,
    marginLeft: 50,
    marginRight: 10,
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 16,
  },
  singleHeader: {
    color: GlobalStyles.colors.eRich.base,
    textAlign: "center",
    fontFamily: "NotoSans-Bold",
    fontSize: 14,
    marginHorizontal: "17%",
  },
  scrollStyle: {
    height: "100%",
    flex: 1
  },
  backToRegisterBtnContainer: {
    flexDirection: "row",
    justifyContent: "center",
    display: "flex",
    zIndex: 2,
    position: "absolute",
    bottom: -30,
    left: width/3.3
    }
});

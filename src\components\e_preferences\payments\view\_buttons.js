import { ActivityIndicator, StyleSheet, View } from "react-native";
import { Card } from "react-native-paper";
import { GlobalStyles } from "../../../app/global-styles";
import Button from "../../../common/_button";

export default function AutoPayButton({ disableButton, cancelClick ,saveClick, isLoading}) {
  return (
    <View style={styles.centerButtons}>
      <Button
        buttonbgColor={styles.cancelBg}
        textColor={styles.cancelText}
        onPress={cancelClick}
      >
        Cancel
      </Button>
      <Button
        buttonbgColor={[
          styles.buttonBgColor,
          disableButton && styles.disabledStyle,
        ]}
        textColor={styles.textColor}
        onPress={!disableButton && !isLoading && saveClick}
        disabled={disableButton}
      >
        Save{" "}
        {isLoading && (
          <ActivityIndicator
            align="center"
            size="small"
            color={GlobalStyles.colors.eWhite.base}
          />
        )}
      </Button>
    </View>
  );
}

const styles = StyleSheet.create({
  centerButtons: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 15,
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 10,
  },
  cancelText: {
    color: GlobalStyles.colors.eSecondary.base,
    fontFamily: "NotoSans-Medium",
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontFamily: "NotoSans-Medium",
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginLeft: 10,
  },
  disabledStyle: {
    opacity: 0.5,
  },
});

import { View } from "react-native";
import { ActivityIndicator } from "react-native-paper";
import { GlobalStyles } from "../app/global-styles";

export const ActivityLoader = () => {
  return (
    <View
      style={{
        display: "flex",
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        padding: "40%",
      }}>
      {
        <ActivityIndicator
          size="large"
          color={GlobalStyles.colors.ePrimary.base}
        />
      }
    </View>
  );
};

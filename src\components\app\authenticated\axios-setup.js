// import axios from "axios";
// import { commonService } from "../../../services/common.service";
// import { config } from "../../../environment";
// import { refreshBearerService } from "../../../services/refresh.bearer.service";
// import AsyncStorage from "@react-native-async-storage/async-storage";
// import { decode as atob, encode as btoa } from "base-64";

// const TIMEOUT = 1 * 60 * 1000;
// axios.defaults.timeout = TIMEOUT;
// const envConfig = config;

// const getExpirationDate = (jwtToken?: string): number | null => {
//   if (!jwtToken) {
//     return null;
//   }
//   const jwt = JSON.parse(atob(jwtToken.split(".")[1]));
//   // multiply by 1000 to convert seconds into milliseconds
//   return (jwt && jwt.exp && jwt.exp * 1000) || null;
// };

// const isExpired = (exp?: number) => {
//   if (!exp) {
//     return false;
//   }
//   return Date.now() > exp;
// };

// const setupAxiosInterceptors = () => {
//   const onRequestSuccess = async config => {
//     let bearer = await AsyncStorage.getItem("bearer");

//     if (bearer) {
//       bearer = JSON.parse(bearer);
//       let accessToken = bearer.acessToken;
//       let dataExpiration = getExpirationDate(accessToken);
//       let hasExpired = isExpired(dataExpiration);

//       if (config.url !== envConfig.urls.REFRESH_BEARER) {
//         if (hasExpired) {
//           console.log("only bearer expired... refreshing");
//           await refreshBearerService.refreshBearerToken();
//           delete config.headers["accessToken"];
//           bearer = await AsyncStorage.getItem("bearer");
//           bearer = JSON.parse(bearer);
//         }
//       }

//       if (bearer) {
//         const sessionId = bearer.sessionId;
//         config.headers.SessionId = `${sessionId}`;
//         // config.headers['Accept-Encoding']='gzip, deflate, br';
//         config.headers.tenantCode = envConfig.constants.BASE_TENANT_CODE;
//         config.headers.personId = bearer.accountDetails.indentifiers.personId;
//         config.headers.username = bearer.userName;
//         config.headers["x-client-tenantCode"] =
//           envConfig.constants.BASE_TENANT_CODE;

//         if (config.url !== envConfig.urls.EXPIRE_SESSION) {
//           config.headers.accessToken = `${accessToken}`;
//         }
//       } else {
//         commonService.logoutUser();
//       }
//     }
//     return config;
//   };

//   const onResponseSuccess = response => response;
//   const onResponseError = async err => {
//     const status = err.status || (err.response ? err.response.status : 0);
//     if (
//       (err.config.url && err.config.url === envConfig.urls.AUTH_SERVICE) ||
//       err.config.url === envConfig.urls.PROFILE_DETAILS_DATA ||
//       err.config.url === envConfig.urls.LABELS ||
//       err.config.url === envConfig.urls.USAGE_SERVICE_BASE_URL ||
//       err.config.url === envConfig.urls.ADMIN_SERVICE_BASE_URL
//     ) {
//       if (status === 500 || status === 0) {
//         // history.push(routePaths.RETRY);
//       }
//     }

//     if (status === 403) {
//       console.log("got 403 only bearer token, refreshing token");
//       refreshBearerService.refreshBearerToken();
//     }

//     let errMessage = "";
//     if (err.response) {
//       if (err?.response?.data?.message) {
//         errMessage = err.response.data.message;
//       } else {
//         errMessage = Array.isArray(err.response.data.errors)
//           ? err.response.data.errors[0]["message"]
//           : "";
//       }
//     }
//     return Promise.reject(err);
//   };
//   axios.interceptors.request.use(onRequestSuccess);
//   axios.interceptors.response.use(onResponseSuccess, onResponseError);
// };
// export default setupAxiosInterceptors;

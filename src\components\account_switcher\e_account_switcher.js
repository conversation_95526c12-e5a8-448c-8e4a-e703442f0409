import React, { useRef } from "react";
import { StyleSheet, View } from "react-native";
import { GlobalStyles } from "../app/global-styles";
import { Text, IconButton } from "react-native-paper";
import { useContext, useEffect, useState } from "react";
import { drawerContext } from "../app/authenticated/authenticated_layout";
import { useSelector } from "react-redux";
import Button from "../common/_button";
import DropDown from "../common/_dropdown";
import { SERVICE_TYPE_IDENTIFIER } from "./constants";
import Icon from "../icon";
import _ from "lodash";
import { config } from "../../environment";
import { drawerContextWO } from "../app/authenticated/authenticated_layout_wo";
import { stackContext } from "../app/get_stack";

export default function AccountSwitcher({ setShowpopup, setFinalMeter }) {
  let setAccountSwitcher;
  const { workModelType } = React.useContext(stackContext);


  if (workModelType === "WA") {
    ({ setAccountSwitcher } = useContext(drawerContext));
  } else {
    ({ setAccountSwitcher } = useContext(drawerContextWO));
  }

  const [accountList, setAccountList] = useState([]);
  const [saList, setsaList] = useState([]);
  const [selectedAccount, setAccount] = useState(null);
  const [selectedSa, setSelectedSa] = useState(null);
  const [close, setClose] = useState(false);
  const accountData = useSelector(
    store => store?.accountDetails?.accountDetails,
  );
  const meterData = useSelector(store => store?.meterDetails?.meterDetails);

  useEffect(() => {
    if (accountData?.accountSummary?.accountDetails) {
      let jsArray = accountData?.accountSummary?.accountDetails;
      jsArray = jsArray.filter((ele, index, arr) => {
        if (arr.indexOf(ele) === index) {
          return ele;
        }
      });
      let uniqueArray = [];
      let uniqueValues = new Set();
      for (let emp of jsArray) {
        if (!uniqueValues.has(emp.accountId)) {
          uniqueArray.push({
            value: emp.accountId,
            key: emp.accountId,
          });
          uniqueValues.add(emp.accountId);
        }
      }
      uniqueArray && setAccountList(uniqueArray);
      setAccount({ value: meterData.accountId, key: meterData.accountId });
    }
  }, [accountData]);

  useEffect(() => {
    if (accountData?.accountSummary?.accountDetails) {
      let saId = [];
      accountData.accountSummary.accountDetails.map(
        item =>
          item.accountId === selectedAccount?.value &&
          item.saDetails.map(i => {
            if (i.serviceType === SERVICE_TYPE_IDENTIFIER.GAS) {
              saId.push({
                value: i.saId,
                key: i.saId,
                image: "SA-Gas",
                type: i.serviceType,
              });
            }
            if (i.serviceType === SERVICE_TYPE_IDENTIFIER.WATER) {
              saId.push({
                value: i.saId,
                key: i.saId,
                image: "SA-Water",
                type: i.serviceType,
              });
            }
            if (i.serviceType === SERVICE_TYPE_IDENTIFIER.ELECTRIC) {
              saId.push({
                value: i.saId,
                key: i.saId,
                image: "SA-Electricity",
                type: i.serviceType,
              });
            }
            if (i.serviceType === SERVICE_TYPE_IDENTIFIER.WASTE_WATER) {
              saId.push({
                value: i.saId,
                key: i.saId,
                image: "SA-Wastewater",
                type: i.serviceType,
              });
            }
            if (
              [
                SERVICE_TYPE_IDENTIFIER.COOL_WATER,
                SERVICE_TYPE_IDENTIFIER.COOL_WATER_RESIDENTIAL,
                SERVICE_TYPE_IDENTIFIER.COOLING_SERVICE,
              ].includes(i.serviceType)
            ) {
              saId.push({
                value: i.saId,
                key: i.saId,
                image: "SA-CoolingFan",
                type: i.serviceType,
              });
            }
          }),
      );
      saId && setsaList(saId);
    }
  }, [accountData, selectedAccount]);

  const initServiceAgreement = async () => {
    if (!_.isEmpty(saList) && saList.length === 1) {
      const firstSa = saList[0];
      setSelectedSa(firstSa);
    } else if (saList.length > 1) {
      const found = saList.find(
        item => item.value === meterData.getSaInfo.saId,
      );
      if (found) {
        setSelectedSa(found);
      } else {
        setSelectedSa(saList[0]);
      }
    }
  };
  useEffect(() => {
    initServiceAgreement();
  }, [saList]);

  const okClick = async () => {
    if (meterData.accountId) {
      if (
        meterData.accountId !== selectedAccount.value ||
        selectedSa.value !== meterData.getSaInfo.saId
      ) {
        let meterInfo =
          accountData.accountSummary.personAcccountDetail.personAccountList.filter(
            item => item.accountId === selectedAccount.value,
          );
        let saIdArray = meterInfo[0].accountSaList.filter(
          item => item.saId === selectedSa.value,
        );
        const sampObj = { getSaInfo: saIdArray[0] };
        let finalMeter = { ...meterInfo[0], ...sampObj };
        setShowpopup(true);
        setFinalMeter(finalMeter);
      } else {
        setAccountSwitcher(false);
      }
    }
  };

  const cancelPopup = () => {
    setAccountSwitcher(false);
  };
  const onChangeAccount = account => {
    setAccount(account);
  };
  const onChangeServiceAgreement = async selectedAgreement => {
    setSelectedSa(selectedAgreement);
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}> account switcher</Text>
        <IconButton
          icon={() => (
            <Icon
              name="Close-icon-stroke"
              color={GlobalStyles.colors.eDark.base}
              style={{ fontFamily: "NotoSans-Bold" }}
              size={24}
            />
          )}
          color={GlobalStyles.colors.ePrimary.base}
          onPress={cancelPopup}
          style={styles.closeIcon}
          size={18}
        />
        <View style={styles.dropdownContainer}>
          <View style={{ paddingVertical: 10 }}>
            {accountList &&
              selectedAccount &&
              (accountList.length > 1 ? (
                <>
                  <DropDown
                    data={accountList}
                    onChange={onChangeAccount}
                    reset="reset"
                    title="Account ID"
                    afterSelection="Account ID"
                    defaultvalue={selectedAccount}
                    close={close}
                    setClose={setClose}
                    customSelectButton={true}
                    dropdownType="account"
                    options="keyValuePair"
                    canRenderIcon="true"
                  />
                </>
              ) : (
                selectedAccount && (
                  <View style={styles.singleId}>
                    <Text style={styles.idLabel}>Account ID </Text>
                    <Text style={styles.accountId}>
                      {selectedAccount.value}
                    </Text>
                  </View>
                )
              ))}
          </View>
          <View style={styles.saStyle}>
            {saList &&
              (saList.length > 1 ? (
                <DropDown
                  data={saList}
                  onChange={onChangeServiceAgreement}
                  setClose={setClose}
                  close={close}
                  title="SA ID"
                  afterSelection="SA ID"
                  defaultvalue={selectedSa}
                  customSelectButton={true}
                  dropdownType="said"
                  options="keyValuePair"
                  canRenderIcon="true"
                />
              ) : (
                <View style={styles.singleId}>
                  <Text style={styles.idLabel}>SA ID</Text>
                  <View style={styles.singleSaIdIconContainer}>
                    <Icon
                      name={selectedSa?.image || ""}
                      color={GlobalStyles.colors.ePrimary.base}
                      size={14}
                      style={{
                        fontFamily: "NotoSans-Bold",
                      }}
                    />
                    <View>
                      <Text style={styles.selectedItem}>
                        {" "}
                        {selectedSa?.value}{" "}
                      </Text>
                    </View>
                  </View>
                </View>
              ))}
          </View>
        </View>
      </View>
      <View style={styles.lineStyleBottom}></View>
      <View style={styles.centerButtons}>
        <View style={styles.leftButton}>
          <Button
            buttonbgColor={styles.whiteBgColor}
            textColor={styles.greenText}
            customBtnStyle={styles.customBtnStyle}
            customTextStyle={styles.buttonText}
            onPress={cancelPopup}>
            Cancel
          </Button>
        </View>
        <Button
          buttonbgColor={styles.buttonBgColor}
          textColor={styles.whiteText}
          customBtnStyle={styles.customBtnStyle}
          customTextStyle={styles.buttonText}
          onPress={okClick}>
          Ok
        </Button>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    height: "100%",
    position: "relative",
  },
  content: {
    padding: 30,
  },
  title: {
    alignSelf: "center",
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 16,
    fontFamily: "NotoSans-Bold",
    textTransform: "capitalize",
  },
  closeIcon: {
    position: "absolute",
    top: 10,
    right: 18,
    backgroundColor: GlobalStyles.colors.eFaint.selected,
    fontFamily: "NotoSans-Bold",
  },
  lineStyleBottom: {
    borderWidth: 0.5,
    borderColor: "#DEE2E4",
    marginVertical: 10,
  },
  buttonContainer: {
    position: "fixed",
    width: "100%",
  },
  centerButtons: {
    flexDirection: "row",
    flexWrap: "wrap",
    width: "100%",
    justifyContent: "center",
    marginVertical: 10,
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 30,
  },
  whiteBgColor: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.ePage.base,
    paddingHorizontal: 20,
  },
  greenText: {
    color: GlobalStyles.colors.eSecondary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  whiteText: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  leftButton: {
    marginRight: 20,
  },
  dropdownContainer: {
    paddingTop: 30,
    // overflow: "scroll",
    // zIndex: 100,
  },
  saStyle: {
    paddingTop: 30,
    paddingBottom: 15,
  },
  singleId: {
    backgroundColor: GlobalStyles.colors.eBackground.base,
    borderRadius: 8,
    zIndex: 0,
    width: "100%",
    marginTop: 0,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    borderWidth: 0,
    padding: 12,
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  idLabel: {
    color: GlobalStyles.colors.eRich.base,
    fontSize: 14,
    justifyContent: "flex-start",
    fontFamily: "NotoSans-Bold",
  },
  idLabelDropdown: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 14,
    width: "100%",
  },
  value: {
    fontSize: 14,
  },
  accountId: {
    fontSize: 14,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.ePrimary.base,
  },
  selectedItem: {
    color: GlobalStyles.colors.ePrimary.base,
    fontFamily: "NotoSans-Bold",
  },
  singleSaIdIconContainer: {
    display: "flex",
    justifyContent: "center",
    flexDirection: "row",
    alignItems: "center",
  },
  customBtnStyle: {
    borderRadius: 5,
    paddingVertical: 4,
    paddingHorizontal: 6,
  },
  customTextStyle: {
    textAlign: "center",
    fontSize: 16,
  },
});

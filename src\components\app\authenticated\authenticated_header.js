import React, { useState, useEffect, useContext } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Card,
  IconButton,
  Menu,
  PaperProvider,
} from "react-native-paper";
import {
  Pressable,
  StyleSheet,
  View,
  Image,
  TouchableOpacity,
  Text,
  Modal,
} from "react-native";
import { GlobalStyles } from "../global-styles";
import CustomDrawerContent from "../../e_drawerContent/e_drawer_content";
import AccountSwitcher from "../../account_switcher/e_account_switcher";
import AccountWarning from "../../account_switcher/view/_account_warning";
import { useDispatch, useSelector } from "react-redux";
import { meterDetails } from "../../../redux/slices/selectedAccount";
import { drawerContext } from "./authenticated_layout";
import PastTicketDrawer from "../../e_services/past_Tickets/view/_past_ticket_drawer";
import { ticketID } from "../../../redux/slices/pastTicketId";
import { turnOnOffID } from "../../../redux/slices/pastTurnOnOff";
import Icon from "../../icon";
import { updateLoggedInStatus } from "../../../redux/slices/authenticationReducer";
import { commonService } from "../../../services/common.service";
import IconBell from "react-native-vector-icons/MaterialCommunityIcons";
import Button from "../../common/_button";
import { useNavigation } from "@react-navigation/native";
import { servicePath } from "../../../redux/slices/servicePath";
import { WorkOrderService } from "../../e_workactivities/model/work-order-service";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { config } from "../../../environment";
import { drawerContextWO } from "./authenticated_layout_wo";

export default function AuthenticatedHeader() {

  const {
    openDrawer,
    setOpenMenu,
    menuFlag,
    setmenuFlag,
    submitLoader,
    allWOList,
    allCIList,
    newNotification,
    setNewNotification,
    selectedItem,
    setSelectedItem,
    newWorkOrder,
    setAllWOList,
    setNewWorkOrderExists,
    setWOList,
    setSingleWO,
    setConfirmationModal,
    setOTPModal,
    accountSwitcher,
    setAccountSwitcher,
  } =  useContext(drawerContext);
  const [showPopup, setShowpopup] = useState(false);
  const [finalMeter, setFinalMeter] = useState();
  const [searchIcon, setSearchIcon] = useState(true);
  const dispatch = useDispatch();
  const pastTicketID = useSelector(store => store?.ticketID.ticketID);
  const pastturnOnOffID = useSelector(store => store?.turnOnOffID.turnOnOffID);
  const [logoutDrawer, setLogoutDrawer] = useState(false);
  const [notificationDrawer, setNotificationDrawer] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const navigation = useNavigation();
  const { t } = useTranslation();
  const closePopup = () => {
    setShowpopup(false);
    setAccountSwitcher(false);
  };

  const changeAccount = e => {
    dispatch(meterDetails(e));
    setShowpopup(false);
    setAccountSwitcher(false);
  };

  const setOpenMenuFunction = () => {
    setOpenMenu(true);
    setmenuFlag(true);
    if (pastTicketID || pastturnOnOffID) {
      let ticket = null;
      dispatch(ticketID(ticket));
      dispatch(turnOnOffID(ticket));
    }
  };

  const onPressPastTicketDrawerMenu = () => {
    if (ticketID) {
      dispatch(ticketID(null));
    }
    if (turnOnOffID) {
      dispatch(turnOnOffID(null));
    }
    setOpenMenu(false);
  };

  useEffect(() => {
    console.log(allCIList);
  }, [newWorkOrder]);
  const notificationClick = () => {
    setNewNotification(!newNotification);
    setModalVisible(!modalVisible);
  };
  const toggleNotificationDrawer = () => {
    setNotificationDrawer(prevState => !prevState);
  };
  const toggleModal = () => {
    setNewWorkOrderExists(false);
    fetchWorkOrderList();
    //setModalVisible(!modalVisible);
  };
  //going to field Activities on clicking notification bell.
  const goToActivitiesTab = () => {
    const ROUTE = "WorkActivities";
    setWOList(true);
    setSingleWO(false);
    setConfirmationModal(false);
    setOTPModal(false);
    setSelectedItem(ROUTE);
    dispatch(servicePath(ROUTE));
    navigation.navigate(ROUTE);
    setNewNotification(false);
  };
  // const acceptFn = () => {
  //   console.log("/////////////");
  //   dispatch(servicePath("WorkActivities"));
  //   navigation.navigate("WorkActivities");
  // };
  const acceptFn = () => {
    const updatedBy = 2;
    const status = "Y";
    console.log("newWorkOrder header", newWorkOrder);
    WorkOrderService.acceptUpdate(newWorkOrder?.WorkOrderId, status, updatedBy)
      .then(res => {
        console.log("accepted ", res);
        if (res === 201) {
          fetchWorkOrderList();
          setNewNotification(!newNotification);
          dispatch(servicePath("WorkActivities"));
          navigation.navigate("WorkActivities");
          setModalVisible(!modalVisible);
          setSelectedItem("WorkActivities");
        }
      })
      .catch(error => {
        console.log(error.response.data.message);
      });
  };
  const declineFn = () => {
    const updatedBy = 2;
    const status = "N";
    WorkOrderService.acceptUpdate(newWorkOrder.WorkOrderId, status, updatedBy)
      .then(res => {
        if (res === 201) {
          fetchWorkOrderList();
          setModalVisible(!modalVisible);
          setNewNotification(!newNotification);
        }
      })
      .catch(error => {
        console.log(error.response.data.message);
      });
  };
  const fetchWorkOrderList = async () => {
    try {
      //setLoading(true);

      const res = await WorkOrderService.getAllWorkOrderList();
      res.workActivities = res?.workActivities?.filter(
        each => each["WamRefNum"] != undefined,
      );
      res.workActivities?.sort((a, b) => {
        // Sort by WorkActivityId in descending order
        return b.WorkActivityId - a.WorkActivityId;
      });
      console.log("res", res);
      setAllWOList(res);
    } catch (err) {
      console.log(err, "Error in fetching consumer index data");
    } finally {
      //setLoading(false);
    }
  };
  return (
    <>
      <Appbar.Header style={styles.backgroundHeader}>
        {/* <Icon
          name="FS-NA-Notification-icon"
          onPress={() => setOpenMenuFunction(true)}
          size={30}
          color={GlobalStyles.colors.ePrimary.base}
          style={{ marginHorizontal: 15 }}
          disabled={submitLoader}
        /> */}
        {/* <Image
          style={styles.clientLogo}
          source={require("../../../../assets/e_client_logo.png")}
        /> */}
        <Icon
          size={30}
          color={GlobalStyles.colors.ePrimary.base}
          name="FS-NA-ICX-logo-Symbol"
          diabled={submitLoader}
          style={{ marginHorizontal: 15 }}
        />
        <Appbar.Content color={GlobalStyles.colors.ePrimary.base} />
        {newNotification ? (
          <Pressable onPress={goToActivitiesTab}>
            <Image
              style={styles.notificationIcon}
              source={require("../../../../assets/dashboard-icons/fs-notification.png")}
            />
          </Pressable>
        ) : (
          <Pressable onPress={goToActivitiesTab}>
            <Icon
              name="FS-NA-Notification-icon"
              color={GlobalStyles.colors.ePrimary.base}
              size={35}
              disabled={false}
            />
          </Pressable>
        )}
        <Modal
          animationType="none" // You can change animationType as per your preference
          transparent={true}
          visible={modalVisible}
          onRequestClose={goToActivitiesTab}>
          <View style={styles.centeredView}>
            <View style={styles.modalView}>
              <Text style={styles.modalText}>
                {t("WORK_ORDER_NOTIFICATION")}
              </Text>
              <View style={styles.buttonContainer}>
                <Button
                  buttonbgColor={styles.acceptBgColor}
                  textColor={styles.textColor}
                  customBtnStyle={styles.customBtnStyle}
                  customTextStyle={styles.buttonText}
                  onPress={acceptFn}>
                  {t("ACCEPT")}
                </Button>
                <Button
                  buttonbgColor={styles.declineBgColor}
                  textColor={styles.textColor}
                  customBtnStyle={styles.customBtnStyle}
                  customTextStyle={styles.buttonText}
                  onPress={declineFn}>
                  {t("DECLINE")}
                </Button>
              </View>
            </View>
          </View>
        </Modal>
        {/* <Icon
          name="FS-NA-Profile-icon-circle"
          color={GlobalStyles.colors.ePrimary.base}
          size={35}
          style={{ marginHorizontal: 15 }}
          disabled={submitLoader}
          onPress={() => setAccountSwitcher(true)}
        /> */}

        <Menu
          visible={logoutDrawer}
          onDismiss={() => setLogoutDrawer(false)}
          style={{
            position: "absolute",
            top: "10%",
            right: 0,
            left: "70%",
            width: 100,
            overflow: "hidden",
          }}
          anchor={
            <Pressable
              onPress={() => {
                setLogoutDrawer(prev => !prev);
              }}>
              <Image
                style={styles.profileIcon}
                source={require("../../../../assets/dashboard-icons/fs-profile.png")}
              />
            </Pressable>
          }>
          <Menu.Item
            onPress={() => {
              dispatch(updateLoggedInStatus(false));
              dispatch(meterDetails());
              commonService.logoutUser();
            }}
            title={t("LOGOUT")}
            style={{
              paddingVertical: 0,
              marginVertical: 0,
              paddingLeft: 20,
            }}
          />
        </Menu>
      </Appbar.Header>
      {openDrawer && menuFlag && (
        <View style={styles.drawerMenu}>
          <View style={styles.drawerMenuContent}>
            <CustomDrawerContent />
          </View>
          <Pressable
            style={styles.drawerOpacity}
            onPress={() => setOpenMenu(false)}></Pressable>
        </View>
      )}
      {openDrawer && !menuFlag && (
        <View style={styles.drawerMenu}>
          <View style={styles.drawerMenuContentTicket}>
            <PastTicketDrawer />
          </View>

          <Pressable
            style={styles.drawerOpacityTicket}
            onPress={onPressPastTicketDrawerMenu}>
            <View style={styles.iconContainer}>
              <IconButton
                icon="menu"
                iconColor={GlobalStyles.colors.eWhite.base}
                style={[
                  {
                    transform: [{ rotateZ: "270deg" }],
                  },
                ]}
                size={15}
                onPress={onPressPastTicketDrawerMenu}
              />
            </View>
          </Pressable>
        </View>
      )}

      {accountSwitcher && (
        <View style={styles.drawerMenu}>
          <Pressable
            style={styles.accountDrawerOpacity}
            onPress={() => setAccountSwitcher(false)}></Pressable>
          <Card style={styles.accountStyle}>
            <AccountSwitcher
              setShowpopup={setShowpopup}
              setFinalMeter={setFinalMeter}
            />
          </Card>
          <AccountWarning
            visible={showPopup}
            hideModel={closePopup}
            changeAccount={changeAccount}
            finalMeter={finalMeter}
          />
        </View>
      )}
    </>
  );
}

const styles = StyleSheet.create({
  backgroundHeader: {
    backgroundColor: "#EDF4FC",
    height: 60,
    borderBottomWidth: 1,
    borderBottomColor: GlobalStyles.colors.ePastelColor2.hover,
  },
  drawerMenu: {
    position: "absolute",
    height: "100%",
    width: "100%",
    zIndex: 100,
    flexDirection: "row",
  },
  accountStyle: {
    position: "absolute",
    height: 370,
    width: "100%",
    bottom: 0,
    borderTopEndRadius: 25,
    borderTopLeftRadius: 25,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    backgroundColor: GlobalStyles.colors.eFaint.base,
  },
  accountDrawerOpacity: {
    height: "100%",
    width: "100%",
    backgroundColor: "black",
    opacity: 0.5,
  },
  drawerOpacity: {
    height: "100%",
    width: "100%",
    backgroundColor: "black",
    opacity: 0.5,
  },
  drawerOpacityTicket: {
    height: "100%",
    width: "15%",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  drawerMenuContent: {
    height: "100%",
    width: "85%",
  },
  drawerMenuContentTicket: {
    height: "100%",
    width: "85%",
  },
  appLogo: {
    height: 30,
    width: 30,
    marginLeft: 5,
  },
  iconContainer: {
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    borderTopWidth: 0,
    alignSelf: "center",
    zIndex: 100,
    top: "50%",
    marginLeft: -30,
  },
  clientLogo: { height: 33, width: 148 },
  notificationIcon: {
    height: 33,
    width: 28,
  },
  profileIcon: {
    marginHorizontal: 15,
    height: 40,
    width: 40,
  },
  centeredView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    top: "-35%",
    //backgroundColor: "rgba(0, 0, 0, 0.5)", // Semi-transparent background
  },
  modalView: {
    backgroundColor: "#fff",
    borderRadius: 10,
    paddingHorizontal: 20,
    paddingVertical: 10,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    width: "80%", // Adjust width as per your preference
    height: "18%", // Adjust maxHeight to limit height
  },
  modalText: {
    fontSize: 14,
    fontFamily: "NotoSans-Medium",
    marginBottom: 20,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: -5,
    marginBottom: 5,
    marginRight: 5,
    gap: 10,
  },
  acceptBgColor: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    paddingHorizontal: 20,
  },
  declineBgColor: {
    borderColor: GlobalStyles.colors.eTertiary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eTertiary.base,
    paddingHorizontal: 20,
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  customBtnStyle: {
    borderRadius: 15,
    paddingVertical: 1,
    paddingHorizontal: 0,
    //marginLeft: 5,
  },
  declinedRow: {
    backgroundColor: GlobalStyles.colors.eLight.base,
    color: GlobalStyles.colors.eLight.base,
  },
});

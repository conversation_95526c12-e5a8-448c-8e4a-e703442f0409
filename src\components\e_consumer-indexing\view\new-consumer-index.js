import React, { useEffect, useMemo, useState, useContext } from "react";
import {
  Dimensions,
  StyleSheet,
  View,
  TouchableOpacity,
  ActivityIndicator,
} from "react-native";
import { Text, TouchableRipple, Card } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import { ScrollView } from "react-native-gesture-handler";
import Button from "../../common/_button";
import _ from "lodash";
import { useDispatch, useSelector } from "react-redux";
import { useIsFocused } from "@react-navigation/native";
import moment from "moment/moment";
// import DatePicker from "react-native-date-picker";
import { getInitialConsumerIndexingValues } from "../../common/util";
import { setSnackbarData } from "../../../redux/slices/activitySlices";
import FontAwesome5Icon from "react-native-vector-icons/FontAwesome";
import { consumerIndexContext } from "../e_consumer-index";
import FontAwesome from "react-native-vector-icons/FontAwesome";
import PaginationExample from "./pagination";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import { useTranslation } from "react-i18next";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { config } from "../../../environment";
import { stackContext } from "../../app/get_stack";

const windowHeight = Dimensions.get("window").height;
const toLabelValue = arr => arr.map(item => ({ label: item, value: item }));

export const consumerPaginationContext = React.createContext();
export default function ConsumerIndexing({ route, navigation }) {
  const { workModelType } = React.useContext(stackContext);
  const {
    CIList,
    list,
    setCIList,
    singleCI,
    setSingleCI,
    CIDetails,
    reviewScreen,
    setReviewScreen,
    tempCIData,
    setTempCIData,
    filled,
    setfilled,
    rawData,
    setConfirmation,
    setVisible,
    revert,
    setRevert,
    setNewCIData,
  } = useContext(consumerIndexContext);
  let setConfirmationModal;

  if (workModelType === "WA") {
    ({ setConfirmationModal } = useContext(drawerContext));
  } else {
    ({ setConfirmationModal } = useContext(drawerContextWO));
  }

  const { t } = useTranslation();
  const [showDateModal, setShowDateModal] = useState(false);
  const [isSurveyDateAdded, setIsSurveyDateAdd] = useState(false);
  const PageIndexArray = [
    { no: 1, name: t("CONSUMER_INFORMATION"), filled: false },
    { no: 2, name: t("UTILITY_DIVISION_DETAILS"), filled: false },
    { no: 3, name: t("NETWORK_DETAILS"), filled: false },
    { no: 4, name: t("METER_INFORMATION"), filled: false },
    { no: 5, name: t("OTHER_INFORMATION"), filled: false },
  ];
  const [IndexArray, setIndexArray] = useState(PageIndexArray);

  const dispatch = useDispatch();

  const consumerIndexingValues = useMemo(() =>
    getInitialConsumerIndexingValues(),
  );
  const [initialValues, setInitialValues] = useState(consumerIndexingValues);

  const focus = useIsFocused(); // useIsFocused as shown
  const workOrderType = _.get(route?.params || {}, "workOrderType");
  const workOrderNumber = _.get(route?.params || {}, "workOrderNumber");

  const organization = _.get(route?.params || {}, "organization");
  const isTiedToAWorkOrder = workOrderNumber && workOrderType;

  const [isLoading, setisLoading] = useState(false);
  const [disableCancle, setDisableCancle] = useState(false);
  const [disableSubmit, setDisableSubmit] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  const pageArray = [1, 2, 3, 4, 5];

  useEffect(() => {
    if (!focus) {
      return;
    }

    if (workOrderType && workOrderNumber) {
      setInitialValues(prev => ({
        ...prev,
        generalInfo: {
          ...prev.generalInfo,
          workOrderType,
          workOrderNumber,
          organization,
        },
      }));
    } else {
      setInitialValues(prev => ({
        ...prev,
        generalInfo: {
          ...prev.generalInfo,
          workOrderType: "",
          workOrderNumber: "",
          organization: "",
        },
      }));
    }
  }, [focus, workOrderType, workOrderNumber]);
  const handleBack = () => {
    setCIList(true);
    setSingleCI(false);
  };
  const okClick = e => {};
  const nextClick = e => {
    if (currentPage < pageArray.length) {
      setCurrentPage(currentPage + 1);
    } else {
      setReviewScreen(true);
      setSingleCI(false);
      setCIList(false);
    }
  };
  const previousClick = e => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    } else {
    }
  };
  const cancleHandler = () => {
    setVisible(true);
    setConfirmation(true);
  };
  useEffect(() => {
    setNewCIData(tempCIData);
  }, [tempCIData]);
  return (
    <>
      <consumerPaginationContext.Provider
        value={{
          IndexArray,
          setIndexArray,
          currentPage,
          setCurrentPage,
          pageArray,
          filled,
          setfilled,
        }}>
        <View>
          <Card style={styles.card}>
            <TouchableOpacity
              style={[styles.backButtonWrapper, styles.paddingRight]}
              onPress={handleBack}>
              <FontAwesome
                name="chevron-left"
                color={GlobalStyles.colors.eWhite.base}
                style={{ fontFamily: "NotoSans-Thin", marginTop: 2 }}
                size={18}
              />
              <Text
                style={{
                  color: GlobalStyles.colors.eWhite.base,
                  fontSize: 12,
                  fontWeight: "700",
                  fontFamily: "NotoSans-Bold",
                }}>
                {t("CONSUMER_INDEXING")}
              </Text>
            </TouchableOpacity>
          </Card>
          <View>
            <Card style={[styles.cardStyle]}>
              <PaginationExample />
            </Card>
          </View>
          {currentPage === 1 ? (
            <View style={styles.btnContainer}>
              <Button
                onPress={cancleHandler}
                buttonbgColor={[
                  styles.cancelBg,
                  disableCancle && styles.disabledCancleStyle,
                ]}
                textColor={[
                  disableCancle ? styles.disableColor : styles.cancelText,
                ]}
                disabled={disableCancle}>
                {t("CANCEL")}
              </Button>
              <Button
                buttonbgColor={[
                  styles.buttonBgColor,
                  disableSubmit && styles.disabledStyle,
                ]}
                textColor={[
                  disableSubmit ? styles.disableColor : styles.textColor,
                ]}
                onPress={nextClick}
                disabled={disableSubmit}>
                {t("NEXT")}
                {isLoading && (
                  <ActivityIndicator
                    align="center"
                    size={13}
                    color={GlobalStyles.colors.eWhite.base}
                  />
                )}
              </Button>
            </View>
          ) : (
            <View style={styles.btnContainer}>
              <Button
                buttonbgColor={[
                  styles.buttonBgColor,
                  disableSubmit && styles.disabledStyle,
                ]}
                textColor={[
                  disableSubmit ? styles.disableColor : styles.textColor,
                ]}
                onPress={previousClick}
                disabled={disableSubmit}>
                {t("PREVIOUS")}
                {isLoading && (
                  <ActivityIndicator
                    align="center"
                    size={13}
                    color={GlobalStyles.colors.eWhite.base}
                  />
                )}
              </Button>
              <Button
                onPress={cancleHandler}
                buttonbgColor={[
                  styles.cancelBg,
                  disableCancle && styles.disabledCancleStyle,
                ]}
                textColor={[
                  disableCancle ? styles.disableColor : styles.cancelText,
                ]}
                disabled={disableCancle}>
                {t("CANCEL")}
              </Button>
              <Button
                buttonbgColor={[
                  styles.buttonBgColor,
                  disableSubmit && styles.disabledStyle,
                ]}
                textColor={[
                  disableSubmit ? styles.disableColor : styles.textColor,
                ]}
                onPress={nextClick}
                disabled={disableSubmit}>
                {t("NEXT")}
                {isLoading && (
                  <ActivityIndicator
                    align="center"
                    size={13}
                    color={GlobalStyles.colors.eWhite.base}
                  />
                )}
              </Button>
            </View>
          )}
        </View>
      </consumerPaginationContext.Provider>
    </>
  );
}

const styles = StyleSheet.create({
  card: {
    // borderTopEndRadius: 10,
    // borderTopStartRadius: 10,
    // borderBottomEndRadius: 10,
    // borderBottomStartRadius: 10,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    padding: 15,
    borderColor: "white",
    // boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
    margin: 10,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  backButtonWrapper: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 3,
  },
  paddingRight: {
    paddingRight: 10,
  },
  extendFlex: {
    width: 180,
  },
  headerWrapper: {
    paddingBottom: 20,
  },
  flexRowCenter: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginVertical: 10,
  },
  section: {
    paddingTop: 5,
    paddingBottom: 5,
  },
  sectionHeaderText: {
    fontSize: 17,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.ePrimary.base,
  },
  sectionItemHeader: {
    // width: 140,
  },
  sectionItemInputStyle: {
    height: 35,
    backgroundColor: "white",
  },
  sectionItemLabel: {
    fontFamily: "NotoSans-Medium",
    fontSize: 14,
    color: GlobalStyles.colors.ePrimary.base,
  },
  sectionWrapper: {
    paddingHorizontal: 16,
    paddingVertical: 5,
    backgroundColor: "white",
  },
  submitBtnWrapper: {
    paddingVertical: 16,
    marginBottom: 10,
  },
  cardStyle: {
    marginHorizontal: 10,
    //borderBottomLeftRadius: 10,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    // boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    //paddingVertical: 20,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    marginTop: -10,
    height: windowHeight - 290,
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  cancelText: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 12,
    fontFamily: "NotoSans-Medium",
  },
  btnContainer: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 20,
    marginBottom: 0,
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    //marginLeft: 10,
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.ePrimary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginHorizontal: 20,
  },
  disabledStyle: {
    opacity: 0.5,
    backgroundColor: GlobalStyles.colors.eBackground.selected,
  },
});

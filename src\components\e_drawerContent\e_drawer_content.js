import {
  Pressable,
  StyleSheet,
  View,
  ScrollView,
  Image,
  ImageBackground,
} from "react-native";
import { GlobalStyles } from "../app/global-styles";
import { commonService } from "../../services/common.service";
import { Text } from "react-native-paper";
import DrawerMenuItems from "./view/_drawerMenu";
import React, { useContext, useEffect, useState } from "react";
import { drawerContext } from "../app/authenticated/authenticated_layout";
import { useDispatch, useSelector } from "react-redux";
import { updateLoggedInStatus } from "../../redux/slices/authenticationReducer";
import { IconButton } from "react-native-paper";
import Icon from "../icon";
import { meterDetails } from "../../redux/slices/selectedAccount";
import { drawerContextWO } from "../app/authenticated/authenticated_layout_wo";
import { config } from "../../environment";
import { stackContext } from "../app/get_stack";

export default function CustomDrawerContent(props) {
  const { workModelType } = React.useContext(stackContext);
  let setOpenMenu;

  if (workModelType === "WA") {
    ({ setOpenMenu } = useContext(drawerContext));
  } else {
    ({ setOpenMenu } = useContext(drawerContextWO));
  }

  const [name, setName] = useState();
  const [email, setEmail] = useState();
  const dispatch = useDispatch();
  let accountDetails = useSelector(
    store => store?.accountDetails?.accountDetails,
  );
  useEffect(() => {
    if (accountDetails) {
      setName(
        accountDetails?.accountSummary?.personDetailList?.["C1-Person"]
          ?.personName?.entityName,
      );
      let emailAdd = accountDetails?.accountSummary?.personDetailList?.[
        "C1-Person"
      ]?.emailAddress
        ? accountDetails?.accountSummary?.personDetailList?.["C1-Person"]
            ?.emailAddress
        : accountDetails?.accountSummary?.personDetailList?.[
            "C1-Person"
          ]?.personContactDetail.map(
            item =>
              item.personContactType === "PRIMARYEMAIL" &&
              item.contactDetailValue,
          );
      setEmail(emailAdd);
    }
  }, [accountDetails]);
  const logout = () => {
    dispatch(updateLoggedInStatus(false));
    dispatch(meterDetails());
    commonService.logoutUser();
  };
  const closeMenu = () => {
    setOpenMenu(false);
  };
  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Icon
          name="Close-icon-stroke"
          color={GlobalStyles.colors.ePrimary.base}
          size={35}
          style={styles.closeLogo}
          onPress={closeMenu}
        />

        <View style={styles.appLogoView}>
          <Image
            style={styles.appLogo}
            source={require("../../../assets/e_client_logo.png")}
          />
        </View>
      </View>
      <View style={styles.contentPart}>
        <View style={styles.bgImageView}>
          <ImageBackground
            style={styles.bgImage}
            source={require("../../../assets/e_profile_bg.png")}>
            <View style={styles.profilePicView}>
              <Icon
                name="User-stroke-icon"
                size={40}
                color={GlobalStyles.colors.ePrimary.base}
              />
            </View>
            <View style={styles.profileDetails}>
              <Text style={styles.profileName}>
                {name && name.replace(",", " ")}
              </Text>
              <Text style={styles.profileEmail}>{email}</Text>
            </View>
          </ImageBackground>
        </View>
        <View style={styles.scrollView}>
          {/* <View style={styles.categoriesView}>
          <Text style={styles.categories}>CATEGORIES</Text>
          </View> */}

          <DrawerMenuItems />
        </View>
        <Pressable onPress={logout}>
          <View style={styles.footerContainer}>
            <Text style={styles.logoutText}>Log out</Text>
          </View>
        </Pressable>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#EDF4FC",
    marginTop: "11%",
  },
  contentPart: {
    justifyContent: "center",
    flex: 1,
    alignItems: "center",
  },
  appLogoView: {
    height: 60,
    alignItems: "flex-start",
    width: "70%",
    justifyContent: "center",
    marginRight: 5,
  },
  appLogo: {
    height: "80%",
    width: "100%",
    resizeMode: "contain",
  },
  closeLogo: {
    marginLeft: 15,
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  scrollView: {
    width: "90%",
    marginVertical: 10,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    borderRadius: 15,
    elevation: 2,
    shadowColor: "black",
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    flex: 1,
  },
  profile: {
    height: 100,
    width: "90%",
  },
  footerContainer: {
    justifyContent: "center",
    alignItems: "center",
    paddingBottom: 25,
    paddingTop: 5,
    fontFamily: "NotoSans-Bold",
  },
  logoutText: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 14,
    fontFamily: "NotoSans-Medium",
  },
  bgImage: {
    height: "100%",
    width: "100%",
    alignItems: "center",
    flexDirection: "row",
  },
  bgImageView: {
    width: "100%",
    height: 120,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 8,
  },
  closeView: {
    marginRight: 10,
    marginLeft: 20,
    height: "40%",
    width: "7%",
    resizeMode: "contain",
  },
  close: {
    color: GlobalStyles.colors.ePrimary.base,
    fontSize: 40,
    marginBottom: 7,
  },
  profilePicView: {
    height: 60,
    width: 60,
    borderRadius: 100,
    marginLeft: 25,
    backgroundColor: GlobalStyles.colors.eWhite.base,
    borderWidth: 10,
    borderColor: GlobalStyles.colors.eWhite.base,
  },
  profilePic: {
    height: "100%",
    width: "100%",
    borderRadius: "100%",
  },
  profileDetails: {
    marginLeft: 10,
  },
  profileName: {
    color: GlobalStyles.colors.eWhite.base,
    fontFamily: "NotoSans-Bold",
    fontSize: 16,
    maxWidth: 180,
    overflow: "hidden",
  },
  profileEmail: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 10,
    maxWidth: 180,
    overflow: "hidden",
    marginTop: -1,
  },
  categories: {
    color: GlobalStyles.colors.eRich.base,
    fontFamily: "NotoSans-SemiBold",
    fontSize: 11,
    textAlign: "left",
  },
  categoriesView: {
    marginTop: 18,
    marginLeft: 20,
    marginRight: 20,
  },
});

import React, { useState, useEffect, useContext, useRef } from "react";
import {
  StyleSheet,
  View,
  ActivityIndicator,
  Dimensions,
  ScrollView,
  Platform,
} from "react-native";
import { Text } from "react-native-paper";
import { GlobalStyles } from "../../app/global-styles";
import OTPTextInput from "react-native-otp-textinput";
import FlatButton from "../../common/_flat_button";
import { registerService } from "../model/_register-service";
import Button from "../../common/_button";
import { CountdownCircleTimer } from "react-native-countdown-circle-timer";
import { registerContext } from "../../e_authPages/e_auth_pages";
import Icon from "../../icon";
import { registerContextWO } from "../../e_authPages/e_auth_pages_wo";
import { stackContext } from "../../app/get_stack";

const { height } = Dimensions.get("window");
const width = Dimensions.get("window").width;
export default function OTPVerification({ onPressBack = () => {} }) {
  const { workModelType } = React.useContext(stackContext);
  let setRegisterModal, setRegisterSuccess, email, mobileNumber;

  if (workModelType === "WA") {
    ({ setRegisterModal, setRegisterSuccess, email, mobileNumber } =
      useContext(registerContext));
  } else {
    ({ setRegisterModal, setRegisterSuccess, email, mobileNumber } =
      useContext(registerContextWO));
  }

  let otpInput = useRef(null);
  const [loading, setLoading] = useState();
  const [isLoading, setisLoading] = useState(false);
  const [countdownTime, setCountDownTime] = useState(30);
  const [showResend, setShowResend] = useState(false);
  const [showTimer, setShowTimer] = useState(true);
  const [error, setError] = useState(false);
  const [errorMSG, setErrorMSG] = useState(false);
  const [disableButton, setDisableButton] = useState(true);
  const [disableResend, setDisableResend] = useState(true);
  const [key, setKey] = useState(0);
  const [OTP, setOTP] = useState();
  const [reachedMax, setReachedMax] = useState(false);
  const [isPlaying, setIsPlaying] = useState(true);
  const { height } = Dimensions.get("window");
  const [timerCount, setTimer] = useState();
  const [timerNeed, setTimerNeed] = useState(false);
  const [timeErrorMSG, setTimeErrorMSG] = useState(false);

  useEffect(() => {
    let interval = setInterval(() => {
      setTimer(lastTimerCount => {
        lastTimerCount <= 1 && clearInterval(interval);
        return lastTimerCount - 1;
      });
    }, 60000);
    return () => clearInterval();
  }, []);

  useEffect(() => {
    if (timerCount === 0) {
    }
  }, timerCount);

  const resend = () => {
    setShowTimer(true);
    setIsPlaying(true);
    setDisableResend(true);
    setError(false);
    registerService
      .ResendOTPDetails(email)
      .then(res => {
        if (res.data) {
          setLoading(false);
          otpInput = null;
        }
      })
      .catch(error => {
        if (error.response.status === 500) {
          setError(true);
          setErrorMSG(error.response.data.message);
          setShowResend(false);
        }
        if (error.response.status === 400) {
          setError(true);
          setErrorMSG(error.response.data.message);
        }
        if (error.response.status === 403) {
          setError(true);
          setReachedMax(true);
          setErrorMSG("You have reached the maximum number of OTP attempts");
          setDisableResend(true);
          setIsPlaying(false);
          setShowTimer(false);
        }
        setLoading(false);
      });
    // }
  };

  const otpSubmit = e => {
    // setRegisterModal(false);
    // setRegisterSuccess(true);
    setisLoading(true);
    let otp = "";
    otpInput.state.otpText.forEach(element => {
      otp += element;
    });
    setOTP(otp);
    if (otp.length !== 0) {
      let flowType = "SIGNUP";
      registerService
        .OTPSubmitDetails(otp, flowType, email)
        .then(res => {
          setisLoading(false);
          if (res.data) {
            setRegisterModal(false);
            setRegisterSuccess(true);
          }
        })
        .catch(error => {
          setisLoading(false);
          if (error.response.data.statusCode === 409) {
            setError(true);
            setErrorMSG(error.response.data.message);
          }
          if (error.response.status === 400) {
            setError(true);
            setErrorMSG(error.response.data.message);
            setShowResend(false);
          }
          if (error.response.status === 403) {
            setError(true);
            setTimerNeed(true);
            setErrorMSG("");
            setTimeErrorMSG(
              "Account is blocked. Please try again after " +
                timerCount +
                " minutes",
            );
            setDisableResend(true);
            setTimer(15);
            setDisableButton(true);
            setShowResend(false);
          }
        });
    }
  };
  const countDown = totalElapsedTime => {
    if (!reachedMax) {
      setKey(prevKey => prevKey + 1);
      setIsPlaying(false);
      if (timerNeed) {
        setDisableResend(true);
      } else {
        setDisableResend(false);
      }
      setShowTimer(false);
    } else {
      setIsPlaying(false);
      setShowTimer(false);
    }

    // setKey(prevKey => prevKey +1)
    // setShowTimer(false);
    // setDisable(false);
    // setIsPlaying(false)
  };
  const handleTextChange = input => {
    if (input.length === 6) {
      setDisableButton(false);
    } else {
      setDisableButton(true);
    }
  };
  const cancel = () => {
    setRegisterModal(false);
  };
  return (
    <>
      {/* <View style={{flex: 1}}> */}
      <View>
        <ScrollView style={[styles.pressable]}>
          <View>
            <View style={styles.imgViewCls}>
              <Icon
                name="OTP-v1"
                style={styles.greenBg}
                color={GlobalStyles.colors.ePrimary.selected}
                size={100}
              />
              <Icon
                name="OTP-v2"
                style={styles.otpv2}
                color={GlobalStyles.colors.eSecondary.selected}
                size={70}
              />
            </View>
            <Text style={styles.titleCard}>OTP Verification</Text>
            <View style={styles.subtitle}>
              <Text style={styles.singleHeader}>Enter OTP sent to your </Text>
              <Text style={styles.singleHeader}>
                mobile number to{" "}
                <Text style={styles.mobileNumber}>{mobileNumber}</Text>
              </Text>
            </View>

            <View style={styles.otpInput}>
              <OTPTextInput
                containerStyle={styles.textInputContainer}
                textInputStyle={styles.roundedTextInput}
                inputCellLength={1}
                inputCount={6}
                ref={e => (otpInput = e)}
                tintColor={GlobalStyles.colors.ePrimary.hover}
                offTintColor={GlobalStyles.colors.ePrimary.hover}
                handleTextChange={input => handleTextChange(input)}
                returnKeyType="done"
              />
            </View>
            {timerCount !== 0 ? (
              <View style={styles.contentFlex}>
                <Text style={styles.singleHeader}>
                  Didn’t receive OTP?{" "}
                  {loading ? (
                    <ActivityIndicator
                      size="small"
                      color={GlobalStyles.colors.ePrimary.base}
                    />
                  ) : null}
                </Text>
                <FlatButton
                  textStyles={
                    disableResend ? styles.disableLike : styles.linkWhite
                  }
                  onPress={resend}
                  disable={disableResend}>
                  RESEND OTP
                </FlatButton>
              </View>
            ) : null}
            {showTimer ? (
              <>
                <View style={styles.contentSub}>
                  <Text style={styles.subText}>Time Remaining</Text>
                </View>
                <View
                  style={[
                    styles.contentSubText,
                    !showTimer ? styles.contentSubText2 : null,
                  ]}>
                  <CountdownCircleTimer
                    isPlaying={isPlaying}
                    duration={countdownTime}
                    colors={["#004777", "#A30000", "#A30000", "#F7B801"]}
                    key={key}
                    colorsTime={[7, 5, 2, 0]}
                    size={50}
                    strokeWidth={5}
                    onComplete={totalElapsedTime => countDown(totalElapsedTime)}
                    //      onComplete={() =>{
                    //       setKey(prevKey => prevKey +1)
                    //       setIsPlaying(false)
                    //  }}
                  >
                    {({ remainingTime }) => (
                      <>
                        <Text
                          color={GlobalStyles.colors.eSecondary.base}
                          style={styles.countdown}>
                          00:{remainingTime}
                        </Text>
                      </>
                    )}
                  </CountdownCircleTimer>
                </View>
              </>
            ) : null}
            {error ? (
              <>
                <View style={styles.contentError}>
                  {timerNeed && timerCount !== 0 ? (
                    <Text style={styles.error}>
                      {" "}
                      Account is blocked. Please try again after {
                        timerCount
                      }{" "}
                      minutes
                    </Text>
                  ) : (
                    <Text style={styles.error}>{errorMSG}</Text>
                  )}
                </View>
              </>
            ) : null}
          </View>
        </ScrollView>
      </View>
      <View style={styles.buttons}>
        {timerCount === 0 ? (
          <Button
            onPress={cancel}
            buttonbgColor={styles.buttonbgColor}
            textColor={styles.textColor}>
            Cancel
          </Button>
        ) : (
          <Button
            onPress={otpSubmit}
            buttonbgColor={
              disableButton ? styles.diableCls : styles.buttonbgColor
            }
            textColor={styles.textColor}
            disabled={disableButton}
            // style={disable ? styles.diableCls : styles.enableCls}
          >
            Verify & Proceed
            {isLoading && (
              <ActivityIndicator
                align="center"
                size="small"
                color={GlobalStyles.colors.eWhite.base}
              />
            )}
          </Button>
        )}
        {Platform.OS === "ios" && (
          <Button
            buttonbgColor={{
              backgroundColor: GlobalStyles.colors.eSecondary.base,
              alignItems: "center",
            }}
            textColor={{
              color: GlobalStyles.colors.eWhite.base,
              fontSize: 12,
              fontFamily: "NotoSans-Medium",
            }}
            customBtnStyle={{
              marginTop: 15,
              paddingVertical: 9,
              borderRadius: 6,
            }}
            onPress={onPressBack}>
            Back to Register
          </Button>
        )}
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 20,
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    // boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
  },
  pressable: {
    // flex: 9,
  },
  textInputContainer: {
    marginBottom: 20,
  },
  roundedTextInput: {
    borderRadius: 2,
    borderTopWidth: 2,
    borderLeftWidth: 2,
    borderRightWidth: 2,
    borderBottomWidth: 3,
    width: width / 9,
    height: height / 13,
  },
  otpStyle: {
    borderWidth: 1,
  },
  countdown: {
    fontSize: 10,
  },
  subText: {
    color: GlobalStyles.colors.eMedium.hover,
    fontSize: 12,
    textAlign: "center",
    fontFamily: "NotoSans-Medium",
  },
  titleCard: {
    color: GlobalStyles.colors.ePrimary.base,
    fontFamily: "NotoSans-Bold",
    fontSize: 18,
    textAlign: "center",
  },
  otpInput: {
    marginHorizontal: 5,
    marginVertical: 20,
  },
  linkWhite: {
    color: GlobalStyles.colors.eTertiary.base,
    fontSize: 12,
    textDecorationLine: "underline",
    marginLeft: 0,
    marginTop: 3,
  },
  disableLike: {
    color: GlobalStyles.colors.eLight.selected,
    fontSize: 12,
    textDecorationLine: "underline",
    marginLeft: 0,
    marginTop: 3,
  },
  contentFlex: {
    marginVertical: height / 80,
    flexDirection: "row",
    justifyContent: "center",
  },
  contentError: {
    flexDirection: "row",
    justifyContent: "center",
  },
  error: {
    color: GlobalStyles.colors.eDanger.dark,
    paddingHorizontal: 10,
  },
  contentSubText: {
    marginVertical: 10,
    flexDirection: "row",
    justifyContent: "center",
  },
  contentSubText2: {
    marginVertical: 0,
    flexDirection: "row",
    justifyContent: "center",
  },
  contentSub: {
    flexDirection: "row",
    justifyContent: "center",
  },
  buttonbgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    paddingVertical: "3%",
    paddingHorizontal: "3%",
    width: "100%",
    alignItems: "center",
    color: GlobalStyles.colors.eWhite.base,
    fontFamily: "NotoSans-Medium",
  },
  imgViewCls: {
    justifyContent: "center",
    alignItems: "center",
    marginTop: height - (height + 0),
    marginBottom: height - (height - 40),
  },
  buttons: {
    paddingHorizontal: "10%",
    width: "100%",
    bottom: height - (height + 20),
  },
  greenBg: {
    marginTop: 20,
  },
  otpv2: {
    position: "absolute",
    marginTop: 20,
    top: 4,
    right: width / 3,
  },
  subtitle: {
    textAlign: "center",
    color: GlobalStyles.colors.eSecondary.base,
    fontSize: 12,
    fontFamily: "NotoSans-SemiBold",
    marginTop: 20,
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 14,
    fontFamily: "NotoSans-Medium",
  },
  singleHeader: {
    color: GlobalStyles.colors.eRich.base,
    textAlign: "center",
    fontFamily: "NotoSans-SemiBold",
    fontSize: 12,
  },
  mobileNumber: {
    color: GlobalStyles.colors.ePrimary.base,
    textAlign: "center",
    fontFamily: "NotoSans-SemiBold",
    fontSize: 12,
  },
  diableCls: {
    backgroundColor: GlobalStyles.colors.eLight.selected,
    color: GlobalStyles.colors.eWhite.base,
    fontFamily: "NotoSans-Medium",
    paddingVertical: "3%",
    paddingHorizontal: "3%",
    width: "100%",
    alignItems: "center",
  },
});

import { Pressable, StyleSheet, View } from "react-native";
import { GlobalStyles } from "../app/global-styles";
import { Text } from "react-native-paper";

export default function Button({
  children,
  onPress,
  buttonbgColor,
  textColor,
  disabled,
  customBtnStyle,
  customTextStyle,
}) {
  return (
    <Pressable
      style={({ pressed }) => [
        customBtnStyle ? customBtnStyle : styles.button,
        buttonbgColor,
        pressed && styles.pressed,
      ]}
      onPress={onPress}
      disabled={disabled}>
      <View>
        <Text
          style={[
            customTextStyle ? customTextStyle : styles.buttonText,
            textColor,
          ]}>
          {children}
        </Text>
      </View>
    </Pressable>
  );
}

const styles = StyleSheet.create({
  button: {
    borderRadius: 100,
    elevation: 2,
    shadowColor: "black",
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    minWidth: 45,
    alignSelf: "center",
    paddingHorizontal: 20,
    boxShadow: "none",
    paddingTop: 7,
    paddingBottom: 8,
  },
  pressed: {
    opacity: 0.7,
  },
  buttonText: {
    textAlign: "center",
    fontSize: 16,
  },
});

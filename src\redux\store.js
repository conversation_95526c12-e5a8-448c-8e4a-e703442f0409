import { configureStore } from "@reduxjs/toolkit";
import authenticationReducer from "./slices/authenticationReducer";
import accountDetails from "./slices/accountDetails";
import selectedAccount from "./slices/selectedAccount";
import servicePath from "./slices/servicePath";
import parameter from "./slices/parameterWidgets";
import ticketID from "./slices/pastTicketId";
import turnOnOffID from "./slices/pastTurnOnOff";
import billRouting from "./slices/handleChangeBillRouting";
import defaultBillRouting from "./slices/defaultBillRouting";
import defaultAlertsPreference from "./slices/defaultAlertsPreference";
import alertsPreference from "./slices/handleChangeAlertsPreference";
import unreadMeterInfo from "./slices/unreadMeterNotification";
import unreadBillInfo from "./slices/unreadBillNotification";
import unreadAnnouncementInfo from "./slices/unreadAnnouncementNotification";
import activityReducer from "./slices/activitySlices";

export const store = configureStore({
    reducer: {
        authentication: authenticationReducer,
        accountDetails: accountDetails,
        meterDetails:selectedAccount,
        servicePath,
        parameter,
        ticketID,
        turnOnOffID,
        billRouting,
        defaultBillRouting,
        defaultAlertsPreference,
        alertsPreference,
        unreadMeterInfo,
        unreadBillInfo,
        unreadAnnouncementInfo,
        activity: activityReducer,
  }
})
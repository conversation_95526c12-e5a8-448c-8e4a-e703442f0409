import axios from "axios";
import { config } from "../../../../environment";

export default function getParametersWidgets() {
    return new Promise((resolve, reject) => {
      axios
        .get(config.urls.PARAMETER_WIDGET_CONTROL)
        .then(response => {
          if(response){
          resolve(response.data);
          }else{
            // const callRefresh = await refreshBearerService.refreshBearerToken();
            // getParametersWidgets();
          }
        })
        .catch(function(error) {
          reject(error);
        });
    });
  }
  
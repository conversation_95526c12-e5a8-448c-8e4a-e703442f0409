import axios from "axios";
import { config } from "../../../environment";

export const WorkOrderService = {
  getAllWorkOrderList,
  updateCIList,
  getAllWorkActivities,
  getAddress,
  getAssetAddress,
  requestOTP,
  OTPSubmit,
  UpdateWA,
  UpdateWrokActivity,
  UpdateWAStatus,
  workActitityAPIcall,
  getMeterInspectionAddress,
  getConsumerDetails,
  acceptUpdate,
  UpdateServiceHistory,
  UpdateWAStaus,
  updateIFSdatabase,
  updateWAM,
  updateWAMEquipments,
  addExpenseDetails,
  UpdateIFSdbLogs,
};

async function getAllWorkOrderList() {
  try {
    let url = config.urls.GET_WORK_ORDERS;
    const response = await axios.get(
      `${config.urls.WORK_MANAGEMENT}/work-activity?workStatus=R&includeStatus=false&pageSize=1000`,
      {
        // params: {
        //   pageSize: 2000,
        //   getWorkActivities: true,
        //   getWorkOrderResources: true,
        //   getActivityChecklist: true,
        //   //orgHierarchyId: 1, - Discom Corporation - Testing Org Id
        //   //Division 1 - 21 - QA Test 21
        //   orgHierarchyId: 9,
        //   crewResourceId: 2,
        // },
      },
    );
    console.log(response.data, "KKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK");
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error(" 111111111111error occurred:", error);
    throw error;
  }
}

async function updateCIList(CIdata) {
  const consumerNumber = CIdata.ConsumerNumber;
  let tempCIData = CIdata;
  let key = "ConsumerIndexingHistoryId";
  let key2 = "WorkorderId";
  delete tempCIData[key];
  delete tempCIData[key2];
  let url = config.urls.GET_CONSUMER_LIST;
  try {
    const response = await axios.put(
      url + consumerNumber + "",
      tempCIData,
      // Add your request data here if needed
    );
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function getAllWorkActivities(id) {

  try {
    let url = config.urls.GET_WORK_ORDERS;
    const response = await axios.get(
      `${config.urls.WORK_MANAGEMENT}/work-order?pageNumber=1&pageSize=1000&getWorkActivities=true&getWorkOrderResources=true&getActivityChecklist=true&getWorkOrderAssets=true&workOrderId=${id}`,
    );
    console.log("response getAllWorkActivities", response);
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function getAddress(meterNo, workorderid) {
  try {
    let params = {};
    if (meterNo) {
      params["MeterNumber"] = meterNo;
    }
    if (workorderid) {
      params["WorkOrderId"] = workorderid;
    }
    const response = await axios.get(
      "https://impresa-fieldwork-demo.abjayon.com/asset/consumer-indexing/cih",
      { params: params },
    );
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function getMeterInspectionAddress(assetID) {
  try {
    const response = await axios.get(
      "https://impresa-fieldwork-demo.abjayon.com/asset/assets-managemenet/details?assetId=" +
        assetID +
        "",
    );

    return response.data.assetsDetails[0];
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}
async function getAssetAddress(assetID, workorderid) {
  try {
    const response = await axios.get(
      "https://impresa-fieldwork-demo.abjayon.com/asset/assets-managemenet/historydetails?assetId=" +
        assetID +
        "&WorkOrderId=" +
        workorderid +
        "",
    );
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}
async function requestOTP(customerNumber) {
  try {
    let customerNumber = "";
    if (customerNumber === undefined || null) {
      customerNumber = "8333011316";
    }
    const response = await axios.get(
      `${config.urls.WORK_MANAGEMENT}/work-order/otp?mobileNumber=` +
        customerNumber +
        "",
    );
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error.response.data);
    throw error;
  }
}

async function OTPSubmit(otp, customerNumber) {
  try {
    const response = await axios.get(
      `${config.urls.WORK_MANAGEMENT}/work-order/validate-otp?mobileNumber=` +
        customerNumber +
        "&otp=" +
        otp +
        "",
    );
    return response.data;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function UpdateWA(data) {
  try {
    const response = await axios.post(
      `${config.urls.WORK_MANAGEMENT}/work-order/updateWorkActivityAndChecklist`,
      data,
    );
    console.log(response);
    return response;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function UpdateServiceHistory(activityId, data) {
  console.log(
    activityId,
    data,
    "UpdateWrokActivity--------------------------189",
  );
  try {
    const response = await axios.post(
      `${config.urls.WORK_MANAGEMENT}/work-activity/updateChecklistViaActivity`,
      {
        workActivityId: activityId,
        serviceHistory: data,
      },
    );
    console.log(
      response,
      "YYYYYYYYYYYYYYYUUUUUUUUUUUUUUUUUUTTTTTTTTTTTTTTTTTTTTT",
    );
    return response;
  } catch (error) {
    // Handle any errors
    console.error(
      "YYYYYYYYYYYYYYYUUUUUUUUUUUUUUUUUUTTTTTTTTTTTTTTTTTTTTT-An error occurred:",
      error,
    );
    throw error;
  }
}

async function UpdateWAStaus(activityId, data) {
  console.log(
    activityId,
    data,
    "UpdateWrokActivity--------------------------189",
  );
  try {
    const response = await axios.post(
      `${config.urls.WORK_MANAGEMENT}/work-activity/updateChecklistViaActivity`,
      {
        workActivityId: activityId,
        serviceHistory: data,
      },
    );
    console.log(
      response,
      "YYYYYYYYYYYYYYYUUUUUUUUUUUUUUUUUUTTTTTTTTTTTTTTTTTTTTT",
    );
    return response;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function UpdateWrokActivity(data) {
  console.log(data, "UpdateWrokActivity--------------------------189");
  try {
    const response = await axios.post(
      `${config.urls.WORK_MANAGEMENT}/work-activity/completeMobileActivity`,
      data,
    );
    console.log(response, "OKKKKKKKKKKKKKK");
    return response;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function UpdateWAStatus(data) {
  console.log(data, "TYUIOIUYTRTYUI");
  try {
    const response = await axios.post(
      `${config.urls.WORK_MANAGEMENT}/work-activity/updateActivity`,
      data,
    );
    return response.status;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function UpdateIFSdbLogs(data) {
  console.log(data, "TYUIOQQQQQQQQQQQQQQQIUYTRTYUI");
  try {
    const response = await axios.post(
      `${config.urls.WORK_MANAGEMENT}/work-activity/addActivityTransactionHistory`,
      data,
    );
    return response;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function workActitityAPIcall(data) {
  try {
    const response = await axios.post(
      `${config.urls.WORK_MANAGEMENT}/work-order/updateWorkActivityAndChecklist`,
      data,
    );
    return response;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

async function updateIFSdatabase(data) {
  try {
    const response = await axios.post(
      `${config.urls.WORK_MANAGEMENT}/work-activity/updateResourceDetails`,
      JSON.stringify(data),
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    );

    console.log(response, "response=-----------303");
    return response;
  } catch (error) {
    // Handle any errors
    console.log("An error occurred:", error);
    return error;
  }
}

async function addExpenseDetails(data) {
  try {
    const response = await axios.post(
      `${config.urls.WORK_MANAGEMENT}/work-activity/addResourceDetails`,
      JSON.stringify(data),
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    );

    console.log(response, "response=-----------303");
    return response;
  } catch (error) {
    // Handle any errors
    console.log("An error occurred:", error);
    return error;
  }
}

async function updateWAM(data) {
  console.log(data, "WAM DATA__________________________318");
  try {
    const response = await axios.post(
      `${config.urls.WORK_MANAGEMENT}/work-activity/addTimeSheet`,
      // JSON.stringify(data),
      data,
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    );

    console.log(response, "response=-----------303");
    return response;
  } catch (error) {
    // Handle any errors
    console.log("An error occurred:", error);
    return error;
  }
}

async function updateWAMEquipments(data) {
  console.log(data, "WAM DATA__________________________318");
  try {
    const response = await axios.post(
      `${config.urls.WORK_MANAGEMENT}/work-activity/modifyResources`,
      // JSON.stringify(data),
      data,
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    );

    console.log(response, "response=-----------303");
    return response;
  } catch (error) {
    // Handle any errors
    console.log("An error occurred:", error);
    return error;
  }
}

async function getConsumerDetails(id) {
  try {
    let url = config.urls.GET_CONSUMER_DETAILS;
    const response = await axios.get(url + "ConsumerIndexingId=" + id);
    return response.data[0];
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}
async function acceptUpdate(workOrderId, status, updatedBy) {
  try {
    //console.log("in service", workOrderId, status, updatedBy);
    let url = `${config.urls.WORK_MANAGEMENT}/work-order/addupdatewo`;
    const response = await axios.post(url, {
      WorkOrderId: workOrderId,
      FtAcceptanceStatus: status,
      updatedBy: updatedBy,
    });
    return response.status;
  } catch (error) {
    // Handle any errors
    console.error("An error occurred:", error);
    throw error;
  }
}

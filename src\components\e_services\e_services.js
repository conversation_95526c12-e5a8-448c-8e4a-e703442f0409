import React, { useEffect, useState, useContext } from "react";
import {
  Pressable,
  StyleSheet,
  View,
  Dimensions,
  SafeAreaView,
} from "react-native";
import { useSelector } from "react-redux";
import ServiceRequest from "../e_homePage/view/_service_request";
import PastTickets from "./past_Tickets/e_past_tickets";
import ServiceContent from "./view/_service_content";
import { drawerContext } from "../app/authenticated/authenticated_layout";
import { drawerContextWO } from "../app/authenticated/authenticated_layout_wo";
import { config } from "../../environment";
import { stackContext } from "../app/get_stack";

export const ticketContext = React.createContext();
export const turnOffAndOn = React.createContext();
const { height } = Dimensions.get("window");
export default function Services() {
  const { workModelType } = React.useContext(stackContext);
  const [open, setOpen] = React.useState(false);
  if (workModelType == "WA") {
    const { showPopup, setShowPopup } = useContext(drawerContext);
  } else {
    const { showPopup, setShowPopup } = useContext(drawerContextWO);
  }

  const [setTicketNumber] = React.useState(null);
  const [setClosePopup] = React.useState(false);
  const [title, setTitle] = React.useState(null);
  const { height } = Dimensions.get("window");

  const servicePath = useSelector(state => state?.servicePath?.servicePath);
  useEffect(() => {
    setShowPopup(false);
  }, [servicePath]);

  const [viewHeight1, setViewHeight] = useState(0);
  const [viewHeight2, setViewHeight2] = useState(0);
  const [requiredHeight, setrequiredHeight] = useState(0);
  const [requiredCardHeight, setrequiredCardHeight] = useState(0);
  useEffect(() => {
    const calculateViewHeight = () => {
      const windowHeight = Dimensions.get("window").height;
      const maxHeight = windowHeight * 0.2;
      const minHeight = windowHeight * 0.1;
      const maxHeight2 = windowHeight * 0.6;
      const minHeight2 = windowHeight * 0.4;
      const contentHeight = 60;

      let calculatedHeight = windowHeight - contentHeight;
      let calculatedHeight2 = windowHeight - contentHeight;

      if (calculatedHeight > maxHeight) {
        calculatedHeight = maxHeight;
      } else if (calculatedHeight < minHeight) {
        calculatedHeight = minHeight;
      }

      if (calculatedHeight2 > maxHeight2) {
        calculatedHeight2 = maxHeight2 - 20;
      } else if (calculatedHeight2 < minHeight2) {
        calculatedHeight2 = minHeight2;
      }
      setViewHeight(calculatedHeight);
      setViewHeight2(calculatedHeight2);
      setrequiredHeight(calculatedHeight2 - 100);
      setrequiredCardHeight(calculatedHeight2 - 55);
    };
    calculateViewHeight();

    Dimensions.addEventListener("change", calculateViewHeight); // Update the view height when the screen dimensions change

    return () => {
      Dimensions.removeEventListener("change", calculateViewHeight); // Clean up the event listener when the component is unmounted
    };
  }, []);

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <ticketContext.Provider
        value={{
          setOpen,
          setShowPopup,
          setTicketNumber,
          setClosePopup,
          showPopup,
          setTitle,
          title,
        }}>
        <View style={styles.container}>
          <View style={[styles.view, { height: viewHeight1 }]}>
            <ServiceRequest />
          </View>
          <View style={[styles.view2, { height: viewHeight2 }]}>
            <ServiceContent
              requiredHeight={requiredHeight}
              requiredCardHeight={requiredCardHeight}
            />
          </View>
        </View>
        <PastTickets open={open} />
      </ticketContext.Provider>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  view: {
    borderRadius: 8,
    marginTop: height - 10 - height,
  },
  view2: {
    borderRadius: 8,
    marginTop: height + 20 - height,
    marginBottom: height - 10 - height,
  },
  pressable: {
    justifyContent: "center",
    backgroundColor: "red",
  },
  spaceAroundCard: {
    width: "100%",
    marginTop: 15,
    backgroundColor: "green",
  },
  contentCard: {
    width: "100%",
    backgroundColor: "yellow",
    marginVertical: 15,
  },
  drawerMenu: {
    position: "absolute",
    height: "100%",
    width: "100%",
    zIndex: 100,
    flexDirection: "row",
  },
  drawerOpacity: {
    height: "100%",
    width: "15%",
    backgroundColor: "black",
    opacity: 0.5,
  },
});

import { Card, Text } from "react-native-paper";
import {
  StyleSheet,
  View,
} from "react-native";
import { GlobalStyles } from "../../app/global-styles";
import { useTranslation } from 'react-i18next';


export default function TitleCard() {
  const { t } = useTranslation();

  return (
    <Card style={styles.card}>
          <View>
            <View style={styles.wrapDirection}>
              <View style={{ width: "65%" }}>
                <Text style={styles.titleCard}>{t('MY_TASKS_OVERVIEW')}</Text>
              </View>
            </View>
          </View>
    </Card>
  );
}

const styles = StyleSheet.create({
  card: {
    borderTopEndRadius: 10,
    borderTopStartRadius: 10,
    borderBottomEndRadius: 0,
    borderBottomStartRadius: 0,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    width: "100%",
    padding: 15,
    borderColor: "white",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    backgroundColor: GlobalStyles.colors.ePrimary.base,
  },
  titleCard: {
    color: GlobalStyles.colors.eWhite.base,
    fontSize: 12,
    fontWeight: "700",
    fontFamily: "NotoSans-Bold",
  },
  cardHeight: {
    alignSelf: "center",
    marginTop: "8%",
    fontSize: 12,
  },
  wrapDirection: {
    flexDirection: "row",
    flexWrap: "wrap",
    alignContent: "space-between",
  },
});

import {
  ApolloClient,
  gql,
  ApolloLink,
  InMemoryCache,
  HttpLink,
  concat,
} from "@apollo/client";
import { persistCache } from "apollo3-cache-persist";
import { config } from "../environment";
import { refreshBearerService } from "../services/refresh.bearer.service";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { decode as atob, encode as btoa } from "base-64";

export const apolloClientService = {
  ApolloClientgqls,
  ApolloClientgqlsUsage,
  ApolloClientgqlsAuth,
  ApolloClientgqlsCommunication,
};

let cache = new InMemoryCache();
const init = async () => {
  await persistCache({
    cache,
    storage: AsyncStorage,
  });
};

const authMiddleware = new ApolloLink(async (operation, forward) => {
  let authbearer = false;
  let bearer = await AsyncStorage.getItem("bearer");
  if (!bearer) {
    authbearer = true;
    bearer = await AsyncStorage.getItem("authbearer");
  }
  bearer = JSON.parse(bearer);
  let accessToken = bearer.acessToken;
  let userName = bearer.userName;
  let dataExpiration = getExpirationDate(accessToken);
  let timeCheck = isExpired(dataExpiration);
  if (timeCheck && wait) {
    wait = false;
    const callRefresh = authbearer
      ? await refreshBearerService.refreshAuthBearerToken()
      : await refreshBearerService.refreshBearerToken();
    wait = true;
  }
  operation.setContext(({ headers = {} }) => ({
    headers: {
      ...headers,
      tenantCode: config.constants.BASE_TENANT_CODE,
      userName: userName,
      accesstoken: bearer.acessToken || null,
    },
  }));
  return forward(operation);
});
const endPoints = config.urls;
let httplink = new HttpLink({
  uri: `${endPoints.ADMIN_SERVICE_BASE_URL}`,
});
let httplinkUsage = new HttpLink({
  uri: `${endPoints.USAGE_SERVICE_BASE_URL}`,
  headers: {},
});
let httplinkAuth = new HttpLink({
  uri: `${endPoints.OAUTH_SERVICE_BASE_URL}`,
  headers: {},
});
let httplinkCommunication = new HttpLink({
  uri: `${endPoints.COMMUNICATION_SERVICE_BASE_URL}`,
});
const defaultOptions = {
  watchQuery: {
    fetchPolicy: "cache-only",
  },
  query: {
    fetchPolicy: "cache-only",
    errorPolicy: "ignore",
  },
};
init();
const client = new ApolloClient({
  link: concat(authMiddleware, httplink),
  cache,
  queryDeduplication: false,
  defaultOptions: defaultOptions,
});

const clientUsage = new ApolloClient({
  link: concat(authMiddleware, httplinkUsage),
  cache,
  queryDeduplication: false,
  defaultOptions: defaultOptions,
});
const clientAuth = new ApolloClient({
  link: concat(authMiddleware, httplinkAuth),
  cache,
  queryDeduplication: false,
  defaultOptions: defaultOptions,
});
const clientCommunication = new ApolloClient({
  link: concat(authMiddleware, httplinkCommunication),
  cache,
  queryDeduplication: false,
  defaultOptions: defaultOptions,
});

function ApolloClientgqls(reguestQuery) {
  return new Promise((resolve, reject) => {
    client
      .query({
        query: gql`
          ${reguestQuery}
        `,
        fetchPolicy: "cache-first",
      })
      .then(res => {
        return resolve(res);
      })
      .catch(function (error) {
        return reject(error);
      });
  });
}

function ApolloClientgqlsUsage(reguestQuery, headers, network) {
  let fetchPolicy = "cache-first";
  headers["saId"] = headers["saId"]
    ? headers["saId"]
    : httplinkUsage.options.headers["saId"];
  if (
    httplinkUsage.options.headers["saId"] !== headers["saId"] ||
    httplinkUsage.options.headers["accountId"] !== headers["accountId"]
  ) {
    httplinkUsage.options.headers["saId"] = headers["saId"];
    httplinkUsage.options.headers["accountId"] = headers["accountId"];
    fetchPolicy = "network-only";
  }
  if (network) {
    fetchPolicy = network;
  }
  return new Promise((resolve, reject) => {
    clientUsage
      .query({
        query: gql`
          ${reguestQuery}
        `,
        fetchPolicy: fetchPolicy,
        context: {
          headers: headers,
        },
      })
      .then(res => {
        return resolve(res);
      })
      .catch(function (error) {
        return reject(error);
      });
  });
}

function ApolloClientgqlsAuth(reguestQuery, headers, fetchPolicy) {
  if (fetchPolicy != "network-only") fetchPolicy = "cache-first";
  if (headers) {
    httplinkAuth.options.headers = Object.assign(
      httplinkUsage.options.headers,
      headers,
    );
  }
  return new Promise((resolve, reject) => {
    clientAuth
      .query({
        query: gql`
          ${reguestQuery}
        `,
        fetchPolicy: fetchPolicy,
        context: {
          headers: headers,
        },
      })
      .then(res => {
        return resolve(res);
      })
      .catch(function (error) {
        return reject(error);
      });
  });
}
function ApolloClientgqlsCommunication(reguestQuery, headers, fetchPolicy) {
  if (fetchPolicy != "network-only") fetchPolicy = "cache-first";
  if (headers) {
    httplinkCommunication.options.headers = Object.assign(
      httplinkUsage.options.headers,
      headers,
    );
  }
  return new Promise((resolve, reject) => {
    clientCommunication
      .query({
        query: gql`
          ${reguestQuery}
        `,
        fetchPolicy: fetchPolicy,
        context: {
          headers: headers,
        },
      })
      .then(res => {
        return resolve(res);
      })
      .catch(function (error) {
        return reject(error);
      });
  });
}
let wait = true;
const getExpirationDate = jwtToken => {
  if (!jwtToken) {
    return null;
  }
  const jwt = JSON.parse(atob(jwtToken.split(".")[1]));
  return (jwt && jwt.exp && jwt.exp * 1000) || null;
};
const isExpired = exp => {
  if (!exp) {
    return false;
  }
  return Date.now() > exp;
};

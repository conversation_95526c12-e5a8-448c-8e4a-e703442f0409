diff --git a/ios/RNDateTimePickerShadowView.m b/ios/RNDateTimePickerShadowView.m
index 4ff336234e32e8626cb608393d470efd4c7ff75d..c139440cc915ff1b7adf0ddde9d3a7b629d88a8c 100644
--- a/ios/RNDateTimePickerShadowView.m
+++ b/ios/RNDateTimePickerShadowView.m
@@ -41,7 +41,7 @@ - (void)setTimeZoneName:(NSString *)timeZoneName {
   YGNodeMarkDirty(self.yogaNode);
 }
 
-static YGSize RNDateTimePickerShadowViewMeasure(YGNodeConstRef node, float width, YGMeasureMode widthMode, float height, YGMeasureMode heightMode)
+static YGSize RNDateTimePickerShadowViewMeasure(YGNodeRef node, float width, YGMeasureMode widthMode, float height, YGMeasureMode heightMode)
 {
   RNDateTimePickerShadowView *shadowPickerView = (__bridge RNDateTimePickerShadowView *)YGNodeGetContext(node);
 

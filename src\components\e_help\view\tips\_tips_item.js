import { ScrollView, Image, StyleSheet, View } from "react-native";
import { Text } from "react-native-paper";
import RenderHtml, { defaultSystemFonts } from "react-native-render-html";
import TextLink from "react-native-text-link";
import { GlobalStyles } from "../../../app/global-styles";
import { useState, useEffect } from "react";
import { useWindowDimensions } from "react-native";
import { commonService } from "../../../../services/common.service";

export default function TipsItem({ item }) {
  const systemFonts = [
    ...defaultSystemFonts,
    "Montserrat-Regular",
    "Montserrat-Bold",
  ];
  const [moreRead, setMoreRead] = useState(false);
  const [open, setOpen] = useState([]);
  const { width } = useWindowDimensions();
  const [imgUrlCode, setImgUrlCode] = useState();

  useEffect(() => {
    if (item.tipIconName) {
      commonService
        .getAssestUrl(item.tipIconName)
        .then(res => setImgUrlCode(res));
    }
  }, [item]);

  return (
    <>
      <ScrollView style={styles.content}>
        <Text style={styles.titleStyle2}>{item.tipShortDescription}</Text>
        <RenderHtml
          baseStyle={{
            color: GlobalStyles.colors.ePrimary.base,
            fontSize: 12,
            textAlign: "left",
            backgroundColor: GlobalStyles.colors.eFaint.base,
            width: 300,
            fontFamily: "NotoSans-Medium",
          }}
          systemFonts={systemFonts}
          enableExperimentalMarginCollapsing={true}
          contentWidth={width / 2}
          source={{ html: item.tipLongDescription }}
          ignoredStyles={["fontFamily", "marginBottom"]}
          defaultTextProps={{
            numberOfLines: open.includes(item.tipId) ? 0 : 2,
          }}
        />

        {!open.includes(item.tipId) && (
          <TextLink
            textStyle={styles.textLinkStyle}
            textLinkStyle={{
              color: GlobalStyles.colors.ePrimary.base,
            }}
            pressingLinkStyle={{ color: GlobalStyles.colors.ePrimary.base }}
            links={[
              {
                text: "Read more",
                onPress: () => {
                  setOpen(prev => [...prev, item.tipId]);
                  setMoreRead(true);
                },
              },
            ]}>
            Read more
          </TextLink>
        )}

        {open.includes(item.tipId) === true && (
          <>
            {imgUrlCode?.assetPath && (
              <Image
                style={{ width: "100%", height: 200 }}
                source={{
                  uri: imgUrlCode?.assetPath,
                }}
                resizeMode="contain"
              />
            )}
            <TextLink
              textStyle={styles.textLinkStyle}
              textLinkStyle={{
                color: GlobalStyles.colors.ePrimary.base,
              }}
              pressingLinkStyle={{ color: GlobalStyles.colors.ePrimary.base }}
              links={[
                {
                  text: "Show less",
                  onPress: () => {
                    let remain = open;
                    remain.pop(item.tipId);
                    setOpen(remain);
                    setMoreRead(false);
                  },
                },
              ]}>
              Show less
            </TextLink>
          </>
        )}
      </ScrollView>
      <View style={styles.lineStyle} />
    </>
  );
}

const styles = StyleSheet.create({
  lineStyle: {
    borderBottomWidth: 1,
    borderBottomColor: GlobalStyles.colors.eOutline.base,
    marginHorizontal: 10,
  },

  titleStyle2: {
    color: GlobalStyles.colors.eSecondary.base,
    fontSize: 14,
    fontFamily: "NotoSans-Bold",
    textAlign: "left",
    backgroundColor: GlobalStyles.colors.eFaint.base,
  },
  html: {
    paddingHorizontal: 20,
  },
  textLinkStyle: {
    textAlign: "right",
    fontSize: 9,
    width: "100%",
    textDecorationLine: "underline",
  },
  content: {
    paddingVertical: 10,
    paddingHorizontal: 25,
  },
});

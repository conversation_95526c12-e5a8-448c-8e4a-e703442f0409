import React, { useContext } from "react";
import { useEffect, useState } from "react";
import { ActivityIndicator } from "react-native";
import { StyleSheet, View } from "react-native";
import { batch, useDispatch, useSelector } from "react-redux";
import { defaultAlertsPreference } from "../../../../redux/slices/defaultAlertsPreference";
import { defaultBillRouting } from "../../../../redux/slices/defaultBillRouting";
import { alertsPreference } from "../../../../redux/slices/handleChangeAlertsPreference";
import { billRouting } from "../../../../redux/slices/handleChangeBillRouting";
import { drawerContext } from "../../../app/authenticated/authenticated_layout";
import { GlobalStyles } from "../../../app/global-styles";
import Button from "../../../common/_button";
import { billPreferenceService } from "../model/billPreferenceService";
import { drawerContextWO } from "../../../app/authenticated/authenticated_layout_wo";
import { config } from "../../../../environment";
import { stackContext } from "../../../app/get_stack";

export default function SaveButton({ billRoutingLoading }) {
  const { workModelType } = React.useContext(stackContext);
  let setPopup,
    setTitle,
    setContent,
    setError,
    setButton,
    setIcon,
    setPopupCode;

  if (workModelType === "WA") {
    ({
      setPopup,
      setTitle,
      setContent,
      setError,
      setButton,
      setIcon,
      setPopupCode,
    } = useContext(drawerContext));
  } else {
    ({
      setPopup,
      setTitle,
      setContent,
      setError,
      setButton,
      setIcon,
      setPopupCode,
    } = useContext(drawerContextWO));
  }

  const billRoutingData = useSelector(state => state?.billRouting?.billRouting);
  const defaultBillRoutingData = useSelector(
    state => state?.defaultBillRouting?.defaultBillRouting,
  );
  const profileDetails = useSelector(
    store =>
      store?.accountDetails?.accountDetails?.accountSummary?.personDetailList?.[
        "C1-Person"
      ]?.personContactDetail,
  );
  const [email, setEmail] = useState();
  const [disableButton, setDisable] = useState(false);
  const accountId = useSelector(
    state => state?.meterDetails?.meterDetails?.accountId,
  );
  const [isAlertsLoading, setIsAlertsLoading] = useState(false);
  const [isBillsLoading, setIsBillsLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const defaultAlertsPreferenceData = useSelector(
    state => state?.defaultAlertsPreference?.defaultAlertsPreference,
  );
  const alertsPreferenceData = useSelector(
    state => state?.alertsPreference?.alertsPreference,
  );
  useEffect(() => {
    if (profileDetails) {
      profileDetails.map(
        item =>
          item.personContactType === "PRIMARYEMAIL" &&
          setEmail(item?.contactDetailValue),
      );
    }
  }, [profileDetails]);
  const dispatch = useDispatch();
  const cancelClick = () => {
    batch(() => {
      dispatch(billRouting(defaultBillRoutingData));
      dispatch(alertsPreference(defaultAlertsPreferenceData));
    });
  };
  useEffect(() => {
    if (
      alertsPreferenceData === defaultAlertsPreferenceData &&
      defaultBillRoutingData === billRoutingData
    ) {
      setDisable(true);
    } else {
      setDisable(false);
    }
  }, [
    defaultBillRoutingData,
    billRoutingData,
    alertsPreferenceData,
    defaultAlertsPreferenceData,
  ]);
  const okClick = async () => {
    let billRoutingErr = "";
    setIsLoading(true);
    if (defaultBillRoutingData !== billRoutingData) {
      setIsBillsLoading(true);
      let billRouteType = billRoutingData?.billRoutingInfo?.billRouteType;
      await billPreferenceService
        .updateBEbill(accountId, billRouteType, email)
        .then(res => {
          setIsBillsLoading(false);
          billRoutingErr = "";
        })
        .catch(er => {
          setIsBillsLoading(false);
          billRoutingErr = er;
        });
      dispatch(defaultBillRouting(billRoutingData));
    } else {
      billRoutingErr = "";
    }
    let alertsErr = "";
    if (alertsPreferenceData !== defaultAlertsPreferenceData) {
      setIsAlertsLoading(true);
      await billPreferenceService
        .updateBillNotifyPreference(alertsPreferenceData, accountId)
        .then(res => {
          setIsAlertsLoading(false);
          alertsErr = "";
        })
        .catch(err => {
          setIsAlertsLoading(false);
          alertsErr = err;
        });
      dispatch(defaultAlertsPreference(alertsPreferenceData));
    } else {
      alertsErr = "";
    }
    if (isAlertsLoading === false && isBillsLoading === false) {
      if (alertsErr === "" && billRoutingErr === "") {
        setPopup(true);
        setTitle("Success");
        setContent("Your changes were saved successfully!");
        setError("SUCCESS");
        setButton(false);
        setIcon("Sucess-icon");
        setPopupCode();
        setIsLoading(false);
      } else {
        setPopup(true);
        setTitle("Failure");
        setContent("Something went wrong.Please try again later.");
        setError("ERROR");
        setButton(false);
        setIcon("Exclamationmark-fill-icon");
        setPopupCode();
        setIsLoading(false);
      }
    }
  };
  return (
    <View style={styles.centerButtons}>
      <Button
        buttonbgColor={[styles.cancelBg, disableButton && styles.disabledStyle]}
        textColor={styles.cancelText}
        onPress={!disableButton && cancelClick}
        disabled={disableButton}>
        Cancel
      </Button>
      <Button
        buttonbgColor={[
          styles.buttonBgColor,
          disableButton && styles.disabledStyle,
        ]}
        textColor={styles.textColor}
        onPress={!disableButton && okClick}
        disabled={disableButton}>
        Save{" "}
        {isLoading && (
          <ActivityIndicator
            align="center"
            size="small"
            color={GlobalStyles.colors.eWhite.base}
          />
        )}
      </Button>
    </View>
  );
}

const styles = StyleSheet.create({
  centerButtons: {
    flexDirection: "row",
    alignSelf: "center",
    justifyContent: "space-between",
    marginTop: 15,
  },
  cancelBg: {
    borderColor: GlobalStyles.colors.eSecondary.base,
    borderWidth: 1,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginRight: 10,
  },
  cancelText: {
    color: GlobalStyles.colors.eSecondary.base,
    fontFamily: "NotoSans-Medium",
  },
  textColor: {
    color: GlobalStyles.colors.eWhite.base,
    fontFamily: "NotoSans-Medium",
  },
  buttonBgColor: {
    backgroundColor: GlobalStyles.colors.eSecondary.base,
    alignItems: "center",
    paddingHorizontal: 20,
    paddingTop: 3,
    paddingBottom: 4,
    marginLeft: 10,
  },
  disabledStyle: {
    opacity: 0.5,
  },
});

import { useEffect, useState } from "react";
import { ActivityIndicator } from "react-native";
import { ScrollView, StyleSheet } from "react-native";
import { Card, Checkbox, Text } from "react-native-paper";
import { useDispatch, useSelector } from "react-redux";
import { alertsPreference } from "../../../../redux/slices/handleChangeAlertsPreference";
import { GlobalStyles } from "../../../app/global-styles";

export default function BillAlertsNotifications({ billRoutingLoading }) {
  const [alertsData, setAlertsData] = useState();
  const data = useSelector(state => state?.alertsPreference?.alertsPreference);
  const isPrepaid = useSelector(
    state => state?.meterDetails?.meterDetails?.getSaInfo?.isPrepaidSa,
  );
  useEffect(() => {
    if (data) {
      let newData = JSON.parse(JSON.stringify(data));
      setAlertsData(newData);
    }
  }, [data]);
  const dispatch = useDispatch();
  const handleChange = ssNtfTypCode => {
    alertsData.map((item, k) => {
      if (item.ssNtfTypCode === ssNtfTypCode) {
        item.shouldNotify === "true"
          ? (item.shouldNotify = "false")
          : (item.shouldNotify = "true");
      }
    });
    setAlertsData(alertsData);
    dispatch(alertsPreference(alertsData));
  };
  return (
    <Card style={styles.cardStyle}>
      <ScrollView style={styles.scrollStyle}>
        <Text style={styles.blueTextTitle}>
          {isPrepaid === "N"
            ? "Bill Alerts & Notifications"
            : "Alerts and Notifications"}
        </Text>
        {data || billRoutingLoading === false ? (
          alertsData?.map((item, k) => (
            <Checkbox.Item
              key={k}
              mode="android"
              label={item.notfTypShortDesc}
              position="leading"
              status={item.shouldNotify === "true" ? "checked" : "unchecked"}
              onPress={() => {
                handleChange(item.ssNtfTypCode);
              }}
              labelStyle={styles.bigTextHeader}
              style={{ padding: 0, marginLeft: -15 }}
              color={GlobalStyles.colors.eSecondary.base}
              uncheckedColor={GlobalStyles.colors.eRich.hover}
            />
          ))
        ) : (
          <ActivityIndicator
            size="large"
            color={GlobalStyles.colors.ePrimary.base}
          />
        )}
      </ScrollView>
    </Card>
  );
}

const styles = StyleSheet.create({
  cardStyle: {
    marginHorizontal: "3%",
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
    paddingTop: 15,
  },
  scrollStyle: {
    paddingHorizontal: 20,
    paddingVertical: 5,
    paddingBottom: 10,
    overflow: "scroll",
    zIndex: 100,
  },
  blueTextTitle: {
    color: GlobalStyles.colors.ePrimary.base,
    fontFamily: "NotoSans-SemiBold",
    fontSize: 14,
    marginBottom: "3%",
  },
  bigTextHeader: {
    color: GlobalStyles.colors.eRich.base,
    textAlign: "left",
    fontSize: 12,
    justifyContent: "flex-start",
    // wordBreak: "break-all",
  },
});

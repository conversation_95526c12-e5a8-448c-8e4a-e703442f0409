import { createSlice } from "@reduxjs/toolkit";

const billRoutingReducer = createSlice({
    name: "billRouting",
    initialState: {
       billRouting :"",
    },
    reducers: {
        billRouting: (state, action) => {
            state.billRouting= action.payload
        },
    
    }
})

export const billRouting = billRoutingReducer.actions.billRouting;
export default billRoutingReducer.reducer;
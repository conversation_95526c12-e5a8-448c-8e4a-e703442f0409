import { Text, ScrollView, Dimensions, Platform } from "react-native";
import { useSelector } from "react-redux";
import Bills from "./bills/billsPreference";
import PaymentsPreference from "./payments/paymentsPreference";
import PrepaidAccountPreference from "./prepaid_account/prepaid_account";

export default function PreferenceContent() {
  const pathName = useSelector(state => state?.servicePath?.servicePath);
  const { height } = Dimensions.get("window");
  const isPrepaid = useSelector(
    store => store?.meterDetails?.meterDetails?.getSaInfo?.isPrepaidSa,
  );

  return (
    <ScrollView
      style={
        Platform.OS === "ios"
          ? { height: height - 355 }
          : { height: height - 300 }
      }>
      {pathName === "payments_preference" ? (
        <PaymentsPreference />
      ) : pathName === "Others" ? (
        <Text>others</Text>
      ) : pathName === "prepaid_account_preference" ? (
        <PrepaidAccountPreference />
      ) : isPrepaid === "N" ? (
        <Bills />
      ) : (
        <PaymentsPreference />
      )}
    </ScrollView>
  );
}

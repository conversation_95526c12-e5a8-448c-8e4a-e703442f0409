import moment from "moment";
import { useContext, useEffect, useState } from "react";
import { View } from "react-native";
import { ActivityIndicator } from "react-native-paper";
import { useSelector, useDispatch } from "react-redux";
import { GlobalStyles } from "../../app/global-styles";
import TicketWarning from "../createdTicket_warning/ticket_warning";
import { ticketContext } from "../e_services";
import { ticketService } from "../model/ticket_service";
import Relocate from "./relocate";
import { turnOnOffID } from "../../../redux/slices/pastTurnOnOff";

export default function RelocateMain({ requiredHeight, requiredCardHeight }) {
  const [loading, setLoading] = useState(true);
  const [hasActiveTicket, setHasActiveTicket] = useState(false);
  const { showPopup } = useContext(ticketContext);
  const [readOnly, setReadOnly] = useState(true);
  const [pastTicketDetails, setPastTicketDetails] = useState({});
  const dispatch = useDispatch();

  const accountId = useSelector(
    state => state?.meterDetails?.meterDetails?.accountId,
  );
  const saId = useSelector(
    state => state?.meterDetails?.meterDetails?.getSaInfo?.saId,
  );
  const saDate = useSelector(
    store => store?.meterDetails?.meterDetails?.getSaInfo?.saStartDate,
  );
  const pastTicketID = useSelector(store => store?.ticketID.ticketID);
  const pastturnOnOffID = useSelector(store => store?.turnOnOffID.turnOnOffID);

  useEffect(() => {
    if (pastturnOnOffID) {
      let ticket = null;
      dispatch(turnOnOffID(ticket));
    }
    if (pastTicketID) {
      setReadOnly(false);
    }
    if (accountId && saId && saDate) {
      let dateFrom = moment().format("DD-MM-YYYY");
      let dateTo = moment(saDate).format("DD-MM-YYYY");
      let ticketType = "CM-RELOCSVC";
      setLoading(true);

      ticketService
        .getUserServiceTickets(dateTo, dateFrom, ticketType, accountId, saId)
        .then(res => {
          let tempActive =
            res?.data.getUserServiceTickets.serviceTicketList.filter(
              ticket =>
                ticket?.ticketTypeCd === "CM-RELOCSVC" &&
                ticket?.ticketStatusCd === "ACTIVE" &&
                ticket?.userSubmittedValues.SA_ID === saId,
            );
          const pastTicket =
            res?.data.getUserServiceTickets.serviceTicketList.find(
              ticket =>
                ticket?.ticketTypeCd === "CM-RELOCSVC" &&
                ticket?.ticketId === pastTicketID &&
                ticket?.userSubmittedValues.SA_ID === saId,
            );
          if (tempActive.length > 0 || pastTicket) {
            setHasActiveTicket(true);
            setLoading(false);
          } else {
            setHasActiveTicket(false);
            setLoading(false);
          }
          if (pastTicket) {
            setPastTicketDetails(pastTicket);
          }
        })
        .catch(err => setLoading(false));
    }
  }, [accountId, saId, saDate, showPopup, pastTicketID]);
  return (
    <View style={{ marginHorizontal: "5%", height: requiredHeight }}>
      {loading ? (
        <ActivityIndicator
          size="large"
          color={GlobalStyles.colors.ePrimary.base}
        />
      ) : (
        <>
          {pastTicketID && hasActiveTicket ? (
            <Relocate
              accountId={accountId}
              saId={saId}
              readOnly={readOnly}
              pastTicketDetails={pastTicketDetails}
              requiredCardHeight={requiredCardHeight}
            />
          ) : hasActiveTicket ? (
            <TicketWarning />
          ) : (
            <Relocate accountId={accountId} saId={saId} readOnly={readOnly} />
          )}
        </>
      )}
    </View>
  );
}

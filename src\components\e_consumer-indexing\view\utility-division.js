import React, { useEffect, useMemo, useState, useContext } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { ScrollView } from "react-native-gesture-handler";
import CustomTextInput from "./CustomTextInput";
import { consumerPaginationContext } from "./new-consumer-index";
import { useDispatch, useSelector } from "react-redux";
import { getInitialConsumerIndexingValues } from "../../common/util";
import { useIsFocused } from "@react-navigation/native";
import _ from "lodash";
import { GlobalStyles } from "../../app/global-styles";
import { Dimensions } from "react-native";
import { consumerIndexContext } from "../e_consumer-index";
import { drawerContext } from "../../app/authenticated/authenticated_layout";
import { useTranslation } from "react-i18next";
import { drawerContextWO } from "../../app/authenticated/authenticated_layout_wo";
import { config } from "../../../environment";
import { stackContext } from "../../app/get_stack";

const windowWidth = Dimensions.get("window").width;
const windowHeight = Dimensions.get("window").height;
const UtilityInfoPage = () => {
  const { workModelType } = React.useContext(stackContext);
  const { IndexArray, currentPage, setIndexArray } = useContext(
    consumerPaginationContext,
  );
  let createCI;

  if (workModelType === "WA") {
    ({ createCI } = useContext(drawerContext));
  } else {
    ({ createCI } = useContext(drawerContextWO));
  }

  const { t } = useTranslation();
  const { tempCIData, revert, setRevert, newCIData, setNewCIData } =
    useContext(consumerIndexContext);

  useEffect(() => {
    if (newCIData) {
      if (
        !newCIData.DiscomUtilityCode ||
        !newCIData.ZoneName ||
        !newCIData.DivisionCode ||
        !newCIData.SubDivisionName ||
        !newCIData.DiscomUtilityName ||
        !newCIData.WardCode ||
        !newCIData.DivisionName ||
        !newCIData.DivisionName ||
        !newCIData.DivisionName ||
        !newCIData.DivisionName ||
        !newCIData.DivisionName ||
        !newCIData.DivisionName ||
        !newCIData.DivisionName
      ) {
        const updatedIndexArray = IndexArray.map((item, index) =>
          index === currentPage - 1 ? { ...item, filled: false } : item,
        );
        setIndexArray(updatedIndexArray);
      } else {
        const updatedIndexArray = IndexArray.map((item, index) =>
          index === currentPage - 1 ? { ...item, filled: true } : item,
        );
        setIndexArray(updatedIndexArray);
      }
    }
  }, [newCIData, currentPage]);

  useEffect(() => {
    if (revert) {
      setNewCIData(tempCIData);
      setRevert(false);
    }
  }, [revert]);

  useEffect(() => {}, [tempCIData]);

  return (
    <View style={styles.container}>
      <View style={styles.section}>
        <View style={styles.headerWrapper}>
          <Text style={styles.sectionHeaderText}>
            {IndexArray[currentPage - 1].name}
          </Text>
        </View>
        <View style={styles.sectionWrapper}>
          <ScrollView
            persistentScrollbar={true}
            contentContainerStyle={{ flexGrow: 1 }}>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>
                {t("DISCOM_UTILITY_CODE")}
              </Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.DiscomUtilityCode
                    ? `${newCIData?.DiscomUtilityCode}`
                    : ""
                }
                value={
                  newCIData?.DiscomUtilityCode
                    ? `${newCIData?.DiscomUtilityCode}`
                    : ""
                }
                placeholder={t("ENTER_DISCOM_UTILITY_CODE")}
                keyboardType="numeric"
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, DiscomUtilityCode: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t("ZONE_NAME")}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.ZoneName ? `${newCIData?.ZoneName}` : ""
                }
                value={newCIData?.ZoneName ? `${newCIData?.ZoneName}` : ""}
                placeholder={"Enter Zone Name"}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, ZoneName: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t("DIVISION_CODE")}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.DivisionCode ? `${newCIData?.DivisionCode}` : ""
                }
                value={
                  newCIData?.DivisionCode ? `${newCIData?.DivisionCode}` : ""
                }
                placeholder={t("ENTER_DIVISION_CODE")}
                keyboardType="numeric"
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, DivisionCode: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>
                {t("SUB_DIVISION_NAME")}
              </Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.SubDivisionName
                    ? `${newCIData?.SubDivisionName}`
                    : ""
                }
                keyboardType="numeric"
                value={
                  newCIData?.SubDivisionName
                    ? `${newCIData?.SubDivisionName}`
                    : ""
                }
                placeholder={t("ENTER_SUB_DIVISION_NAME")}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, SubDivisionName: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>
                {t("DISCOM_UTILITY_NAME")}
              </Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.DiscomUtilityName
                    ? `${newCIData?.DiscomUtilityName}`
                    : ""
                }
                value={
                  newCIData?.DiscomUtilityName
                    ? `${newCIData?.DiscomUtilityName}`
                    : ""
                }
                placeholder={t("ENTER_DISCOM_UTILITY_NAME")}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, DiscomUtilityName: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t("WARD_CODE")}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.WardCode ? `${newCIData?.WardCode}` : ""
                }
                value={newCIData?.WardCode ? `${newCIData?.WardCode}` : ""}
                placeholder={t("ENTER_WARD_CODE")}
                keyboardType="numeric"
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, WardCode: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t("DIVISION_NAME")}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.DivisionName ? `${newCIData?.DivisionName}` : ""
                }
                value={
                  newCIData?.DivisionName ? `${newCIData?.DivisionName}` : ""
                }
                placeholder={t("ENTER_DIVISION_NAME")}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, DivisionName: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t("ZONE_CODE")}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.ZoneCode ? `${newCIData?.ZoneCode}` : ""
                }
                value={newCIData?.ZoneCode ? `${newCIData?.ZoneCode}` : ""}
                keyboardType="numeric"
                placeholder={t("ENTER_ZONE_CODE")}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, ZoneCode: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t("WARD_NAME")}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.WardName ? `${newCIData?.WardName}` : ""
                }
                value={newCIData?.WardName ? `${newCIData?.WardName}` : ""}
                placeholder={t("ENTER_WARD_NAME")}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, WardName: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>
                {t("SUB_DIVISION_CODE")}
              </Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.SubDivisionCode
                    ? `${newCIData?.SubDivisionCode}`
                    : ""
                }
                value={
                  newCIData?.SubDivisionCode
                    ? `${newCIData?.SubDivisionCode}`
                    : ""
                }
                keyboardType="numeric"
                placeholder={t("ENTER_SUB_DIVISION_CODE")}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, SubDivisionCode: value }));
                }}
              />
            </View>
          </ScrollView>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingLeft: 10,
  },
  extendFlex: {
    width: windowWidth - 40,
    paddingBottom: 10,
  },
  headerWrapper: {
    paddingBottom: 20,
  },
  flexRowCenter: {
    paddingHorizontal: 40,
  },
  section: {},
  sectionHeaderText: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.ePrimary.base,
  },
  sectionItemHeader: {
    width: "100%",
  },
  sectionItemInputStyle: {
    height: 35,
  },
  sectionItemLabel: {
    fontFamily: "NotoSans-Medium",
    fontSize: 10,
    color: GlobalStyles.colors.eRich.hover,
  },
  sectionWrapper: {
    height: windowHeight - 450,
  },
  submitBtnWrapper: {
    paddingVertical: 16,
    marginBottom: 10,
  },
});

export default UtilityInfoPage;

{"name": "ifs_ui_mobile", "version": "1.0.0", "private": true, "scripts": {"android": "set ENVFILE=.env.development && react-native run-android --port=1234", "ios": "react-native run-ios --scheme \"icxnativeui\"", "ios-dev": "react-native run-ios --scheme \"icxnativeui-dev\"", "ios-stage": "react-native run-ios --scheme \"icxnativeui-stage\"", "ios-qa": "react-native run-ios --scheme \"icxnativeui-qa\"", "lint": "eslint .", "start": "react-native start", "test": "jest", "android-dev": "set ENVFILE=.env.development && react-native run-android --port=1234", "android-demo": "set ENVFILE=.env.staging && react-native run-android --port=1234", "android-qa": "set ENVFILE=.env.qa && react-native run-android --port=1234", "android-prod": "set ENVFILE=.env.production && react-native run-android --port=1234", "build-apk": "set ENVFILE=.env.development && react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res", "apk-dev": "set ENVFILE=.env.development && cd android && ./gradlew assembleRelease", "apk-demo": "set ENVFILE=.env.staging && cd android && ./gradlew assembleRelease", "apk-prod": "set ENVFILE=.env.production && cd android && ./gradlew assembleRelease", "apk-qa": "set ENVFILE=.env.qa && cd android && ./gradlew assembleRelease", "aab-dev": "set ENVFILE=.env.development && cd android && ./gradlew bundleRelease", "aab-demo": "set ENVFILE=.env.staging && cd android && ./gradlew bundleRelease", "aab-qa": "set ENVFILE=.env.qa && cd android && ./gradlew bundleRelease", "aab-prod": "set ENVFILE=.env.production && cd android && ./gradlew bundleRelease", "postinstall": "patch-package"}, "dependencies": {"@apollo/client": "3.6.9", "@formatjs/intl-relativetimeformat": "^11.2.7", "@react-native-async-storage/async-storage": "^1.17.11", "@react-native-community/datetimepicker": "^7.7.0", "@react-native-community/geolocation": "^3.1.0", "@react-native-firebase/app": "^21.7.1", "@react-native-firebase/messaging": "^21.7.1", "@react-navigation/bottom-tabs": "6.3.2", "@react-navigation/drawer": "6.4.3", "@react-navigation/native": "6.0.10", "@react-navigation/native-stack": "6.6.2", "@reduxjs/toolkit": "1.8.3", "accordion-collapse-react-native": "^1.1.1", "apollo3-cache-persist": "0.14.1", "axios": "^1.5.1", "base-64": "^1.0.0", "geolib": "^3.3.4", "graphql": "^16.6.0", "i18next": "^22.4.9", "i18next-http-backend": "^2.1.1", "intl-pluralrules": "^2.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-grecaptcha": "^1.2.5", "react-i18next": "13.0.1", "react-native": "^0.71.2", "react-native-calendars": "^1.1299.0", "react-native-chart-kit": "^6.12.0", "react-native-config": "^1.5.0", "react-native-countdown-circle-timer": "^3.2.1", "react-native-date-picker": "^4.3.3", "react-native-document-picker": "^8.1.2", "react-native-dropdown": "^0.0.6", "react-native-dropdown-select-list": "^1.0.23", "react-native-element-dropdown": "^2.10.0", "react-native-elements": "^3.4.2", "react-native-gesture-handler": "^2.9.0", "react-native-globalize": "^4.5.1", "react-native-linear-gradient": "^2.6.2", "react-native-localize": "^3.0.2", "react-native-maps": "^1.8.0", "react-native-maps-directions": "^1.8.0", "react-native-modal-datetime-picker": "^17.1.0", "react-native-otp-textinput": "^1.0.0", "react-native-paper": "^5.2.0", "react-native-paper-dates": "^0.9.0", "react-native-permissions": "^3.9.3", "react-native-push-notification": "^8.1.1", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "^2.14.4", "react-native-recaptcha-that-works": "^1.3.2", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^4.2.4", "react-native-screens": "^3.19.0", "react-native-select-dropdown": "^2.0.4", "react-native-select-dropdown-android": "^2.0.4", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^13.14.0", "react-native-svg-uri": "^1.2.3", "react-native-text-link": "^1.0.0", "react-native-vector-icons": "^9.2.0", "react-native-web": "0.17.7", "react-redux": "8.0.2", "reactotron-react-native": "^5.0.3", "rn-fetch-blob": "^0.12.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/eslint-config": "^3.2.0", "@tsconfig/react-native": "^2.0.2", "@types/jest": "^29.2.1", "@types/lodash": "^4.14.192", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.73.7", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "jest": {"preset": "react-native"}, "resolutions": {"@react-native-community/datetimepicker@^7.7.0": "patch:@react-native-community/datetimepicker@npm%3A7.7.0#./.yarn/patches/@react-native-community-datetimepicker-npm-7.7.0-5cd616709f.patch"}, "packageManager": "yarn@3.6.4"}
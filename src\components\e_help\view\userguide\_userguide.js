import React, { useRef } from "react";
import { Card } from "react-native-paper";
import { GlobalStyles } from "../../../app/global-styles";
import {
  StyleSheet,
  View,
  Dimensions,
  ScrollView,
  ActivityIndicator,
  Text,
  Platform,
} from "react-native";
import SupportCard from "../links/_support-card";
import ExpandUserGuide from "./_expand-userguide";

export default function UserGuides({ UserguideData, CallUSDescription }) {
  const { height } = Dimensions.get("window");
  const expandRef = useRef();

  return (
    <View style={styles.aroundMargin}>
      <Card
        style={[
          styles.card,
          Platform.OS === "ios"
            ? { height: height - 405 }
            : { height: height - 350 },
        ]}>
        {UserguideData === "NODATA" ? (
          <Text>No Content Found</Text>
        ) : UserguideData && UserguideData.length > 0 ? (
          <ScrollView nestedScrollEnabled={true} style={styles.scrollStyle}>
            {UserguideData.map((item, key) => {
              if (item.contentGroup === "HTML") {
                return (
                  <View
                    ref={expandRef}
                    style={
                      UserguideData.length === key + 1 && {
                        marginBottom: 20,
                      }
                    }>
                    <ExpandUserGuide key={key} item={item} i={key} />
                  </View>
                );
              }
            })}
          </ScrollView>
        ) : (
          <ActivityIndicator
            size="large"
            color={GlobalStyles.colors.ePrimary.base}
          />
        )}
      </Card>
      <SupportCard CallUSDescription={CallUSDescription} />
    </View>
  );
}

const styles = StyleSheet.create({
  aroundMargin: {
    marginLeft: "5%",
    marginRight: "5%",
  },
  card: {
    borderRadius: 20,
    backgroundColor: GlobalStyles.colors.eFaint.base,
    width: "100%",
    padding: "1%",
    borderColor: "white",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    boxShadow: "rgb(0 0 0 / 15%) 1px 5px 13px 0px",
  },
  view: {
    backgroundColor: GlobalStyles.colors.eBackground.base,
    boxShadow: "rgb(0 0 0 / 15%) 1px 2px 1px -1px",
    borderRadius: 10,
    marginHorizontal: 0,
    marginVertical: 5,
  },
  item: {
    paddingHorizontal: "5%",
    color: GlobalStyles.colors.eDark.base,
  },
  lineStyle: {
    borderWidth: 0.5,
    borderColor: GlobalStyles.colors.eLight.base,
    marginBottom: 5,
    width: "100%",
  },
  titleStyle: {
    color: GlobalStyles.colors.eRich.base,
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    textAlign: "left",
    paddingVertical: -5,
    backgroundColor: GlobalStyles.colors.eBackground.base,
  },
  html: {
    paddingHorizontal: 20,
  },
  scrollStyle: {
    padding: 10,
  },
});

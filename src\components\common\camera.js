// import React, { useState } from "react";
// import { View, Text, TouchableOpacity, Image, StyleSheet } from "react-native";
// import ImagePicker from "react-native-image-picker";
// import { RNCamera } from "react-native-camera";

// const CameraComponent = () => {
//   const [imageUri, setImageUri] = useState(null);

//   const openCamera = () => {
//     ImagePicker.launchCamera(
//       {
//         mediaType: "photo",
//         includeBase64: false,
//       },
//       response => {
//         if (!response.didCancel) {
//           setImageUri(response.uri);
//         }
//       },
//     );
//   };

//   return (
//     <View style={styles.container}>
//       <TouchableOpacity onPress={openCamera}>
//         <Image
//           source={require("path_to_your_camera_icon.png")}
//           style={styles.cameraIcon}
//         />
//       </TouchableOpacity>
//       {imageUri && (
//         <Image source={{ uri: imageUri }} style={styles.capturedImage} />
//       )}
//     </View>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     alignItems: "center",
//     marginTop: 20,
//   },
//   cameraIcon: {
//     width: 50,
//     height: 50,
//     resizeMode: "contain",
//   },
//   capturedImage: {
//     marginTop: 20,
//     width: 200,
//     height: 200,
//     resizeMode: "cover",
//   },
// });

// export default CameraComponent;

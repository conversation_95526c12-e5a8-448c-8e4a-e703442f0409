import React, { useEffect, useMemo, useState, useContext } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { ScrollView } from "react-native-gesture-handler";
import CustomTextInput from "./CustomTextInput";
import CustomDropDown from "./CustomDropdown";
import {
  CONSUMER_INDEXING_DROPDOWN_VALUES,
  MESSAGES,
} from "../../common/constants";
import _ from "lodash";
import { GlobalStyles } from "../../app/global-styles";
import { Dimensions } from "react-native";
import { consumerIndexContext } from "../e_consumer-index";
import { consumerPaginationContext } from "./new-consumer-index";
import { useTranslation } from 'react-i18next';

const windowWidth = Dimensions.get("window").width;
const windowHeight = Dimensions.get("window").height;
const MeterInfoPage = () => {
  const { t } = useTranslation();
  const { tempCIData, revert, setRevert, newCIData, setNewCIData } =
    useContext(consumerIndexContext);
  const { IndexArray, currentPage, setIndexArray } = useContext(
    consumerPaginationContext,
  );
  useEffect(() => {
    if (newCIData) {
      if (
        !newCIData.ExistingMeterNo ||
        !newCIData.ExistingMeterType ||
        !newCIData.MeterReadingKWH ||
        !newCIData.MeterMF ||
        !newCIData.MeterMake ||
        !newCIData.ExistingMeterBoxStatus ||
        !newCIData.ExisitingMeterBoxSealStatus ||
        !newCIData.MeterLocation ||
        !newCIData.EnerygyMeterNamePlateDetails
      ) {
        const updatedIndexArray = IndexArray.map((item, index) =>
          index === currentPage - 1 ? { ...item, filled: false } : item,
        );
        setIndexArray(updatedIndexArray);
      } else {
        const updatedIndexArray = IndexArray.map((item, index) =>
          index === currentPage - 1 ? { ...item, filled: true } : item,
        );
        setIndexArray(updatedIndexArray);
      }
    }
  }, [newCIData, currentPage]);

  useEffect(() => {
    if (revert) {
      setNewCIData(tempCIData);
      setRevert(false);
    }
  }, [revert]);

  return (
    <View style={styles.container}>
      <View style={styles.section}>
        <View style={styles.headerWrapper}>
          <Text style={styles.sectionHeaderText}>
            {t('EXISTING_METER_INFORMATION')}
          </Text>
        </View>
        <View style={styles.sectionWrapper}>
          <ScrollView
            persistentScrollbar={true}
            contentContainerStyle={{ flexGrow: 1 }}>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t('METER_NUMBER')}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.ExistingMeterNo
                    ? `${newCIData?.ExistingMeterNo}`
                    : ""
                }
                placeholder={t('METER_NUMBER')}
                value={
                  newCIData?.ExistingMeterNo
                    ? `${newCIData?.ExistingMeterNo}`
                    : ""
                }
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, ExistingMeterNo: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t('METER_TYPE')}</Text>
            </View>
            <View style={[styles.extendFlex]}>
              <CustomDropDown
                data={CONSUMER_INDEXING_DROPDOWN_VALUES.EXISTING_METER_TYPE}
                selectedValue={
                  newCIData?.ExistingMeterType
                    ? `${newCIData?.ExistingMeterType}`
                    : ""
                }
                handleOnChange={item => {
                  setNewCIData(prev => ({
                    ...prev,
                    ExistingMeterType: item.value,
                  }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t('METER_READING_KWH')}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.MeterReadingKWH
                    ? `${newCIData?.MeterReadingKWH}`
                    : ""
                }
                placeholder={t('ENTER_METER_READING_KWH')}
                value={
                  newCIData?.MeterReadingKWH
                    ? `${newCIData?.MeterReadingKWH}`
                    : ""
                }
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, MeterReadingKWH: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t('METER_MF')}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={newCIData?.MeterMF ? `${newCIData?.MeterMF}` : ""}
                value={newCIData?.MeterMF ? `${newCIData?.MeterMF}` : ""}
                placeholder={t('ENTER_METER_MF')}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, MeterMF: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t('METER_READING_KVAH')}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.MeterReadingKVAH
                    ? `${newCIData?.MeterReadingKVAH}`
                    : ""
                }
                placeholder={t('ENTER_METER_READING_KVAH')}
                value={
                  newCIData?.MeterReadingKVAH
                    ? `${newCIData?.MeterReadingKVAH}`
                    : ""
                }
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, MeterReadingKVAH: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t('METER_MAKE')}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.MeterMake ? `${newCIData?.MeterMake}` : ""
                }
                placeholder={t('ENTER_METER_MAKE')}
                value={newCIData?.MeterMake ? `${newCIData?.MeterMake}` : ""}
                onChange={value => {
                  setNewCIData(prev => ({ ...prev, MeterMake: value }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t('METER_BOX_STATUS')}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomDropDown
                data={
                  CONSUMER_INDEXING_DROPDOWN_VALUES.EXISTING_METER_BOX_STATUS
                }
                selectedValue={
                  newCIData?.ExistingMeterBoxStatus
                    ? `${newCIData?.ExistingMeterBoxStatus}`
                    : ""
                }
                handleOnChange={item => {
                  setNewCIData(prev => ({
                    ...prev,
                    ExistingMeterBoxStatus: item.value,
                  }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t('METER_BOX_SEAL_STATUS')}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomDropDown
                data={
                  CONSUMER_INDEXING_DROPDOWN_VALUES.EXISTING_METER_SEAL_STATUS
                }
                selectedValue={
                  newCIData?.ExisitingMeterBoxSealStatus
                    ? `${newCIData?.ExisitingMeterBoxSealStatus}`
                    : ""
                }
                handleOnChange={item => {
                  setNewCIData(prev => ({
                    ...prev,
                    ExisitingMeterBoxSealStatus: item.value,
                  }));
                }}
              />
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>{t('METER_LOCATION')}</Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomDropDown
                data={CONSUMER_INDEXING_DROPDOWN_VALUES.METER_LOCATION}
                selectedValue={
                  newCIData?.MeterLocation ? `${newCIData?.MeterLocation}` : ""
                }
                handleOnChange={item => {
                  setNewCIData(prev => ({
                    ...prev,
                    MeterLocation: item.value,
                  }));
                }}
              />
            </View>

            <View style={styles.headerWrapper}>
              <Text style={styles.sectionHeaderText}>
                {t('NEW_METER_INFORMATION')}
              </Text>
            </View>
            <View style={styles.sectionItemHeader}>
              <Text style={styles.sectionItemLabel}>
                {t('ENERGY_METER_NAME_PLATE_DETAILS')}
              </Text>
            </View>
            <View style={styles.extendFlex}>
              <CustomTextInput
                defaultValue={
                  newCIData?.EnerygyMeterNamePlateDetails
                    ? `${newCIData?.EnerygyMeterNamePlateDetails}`
                    : ""
                }
                placeholder={t('ENTER_ENERGY_METER_NAME_PLATE_DETAILS')}
                value={
                  newCIData?.EnerygyMeterNamePlateDetails
                    ? `${newCIData?.EnerygyMeterNamePlateDetails}`
                    : ""
                }
                onChange={value => {
                  setNewCIData(prev => ({
                    ...prev,
                    EnerygyMeterNamePlateDetails: value,
                  }));
                }}
              />
            </View>
          </ScrollView>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingLeft: 10,
  },
  extendFlex: {
    width: windowWidth - 40,
    paddingBottom: 10,
  },
  headerWrapper: {
    paddingBottom: 20,
  },
  flexRowCenter: {
    paddingHorizontal: 40,
  },
  section: {},
  sectionHeaderText: {
    fontSize: 12,
    fontFamily: "NotoSans-Bold",
    color: GlobalStyles.colors.ePrimary.base,
  },
  sectionItemHeader: {
    width: "100%",
  },
  sectionItemInputStyle: {
    height: 35,
  },
  sectionItemLabel: {
    fontFamily: "NotoSans-Medium",
    fontSize: 10,
    color: GlobalStyles.colors.eRich.hover,
  },
  sectionWrapper: {
    height: windowHeight - 450,
  },
  submitBtnWrapper: {
    paddingVertical: 16,
    marginBottom: 10,
  },
});

export default MeterInfoPage;
